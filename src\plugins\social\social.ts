import { KeepTrackApiEvents } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { TopMenu } from '../top-menu/top-menu';

export class SocialMedia extends KeepTrackPlugin {
  readonly id = 'SocialMedia';
  dependencies_ = [TopMenu.name];
  addHtml() {
    super.addHtml();
    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      SocialMedia.uiManagerFinal_,
    );
  }

  private static uiManagerFinal_(): void {
    /*
    // Bottom Icon
    const githubShareElement = document.createElement('li');

    githubShareElement.innerHTML = keepTrackApi.html`
          <a id="github-share1" class="top-menu-icons" rel="noreferrer" href="www.spacedefense.cn" target="_blank">
            <img
            src="${githubPng}"
            />
          </a>
          `;
    getEl('nav-mobile2', true)?.insertBefore(githubShareElement, getEl('nav-mobile2')?.firstChild ?? null);
    */
  }
}
