#!/usr/bin/env node

/**
 * 模块解析诊断脚本
 * 检查模块导入路径是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 模块解析诊断工具\n');

// 检查文件是否存在
function checkFile(filePath) {
  const fullPath = path.resolve(filePath);
  const exists = fs.existsSync(fullPath);
  
  console.log(`${exists ? '✅' : '❌'} ${filePath}`);
  if (exists) {
    console.log(`   完整路径: ${fullPath}`);
  }
  
  return exists;
}

// 检查目录结构
function checkDirectoryStructure() {
  console.log('📁 检查目录结构...');
  
  const paths = [
    'src/api/server.ts',
    'src/api/auth.routes.ts',
    'src/api/auth.service.ts',
    'src/auth/auth.service.ts',
    'src/api/es-history.routes.ts',
    'src/api/satellite-history.routes.ts'
  ];
  
  let allExist = true;
  paths.forEach(p => {
    if (!checkFile(p)) {
      allExist = false;
    }
  });
  
  return allExist;
}

// 检查导入语句
function checkImports() {
  console.log('\n📋 检查导入语句...');
  
  try {
    const authRoutesPath = 'src/api/auth.routes.ts';
    if (!fs.existsSync(authRoutesPath)) {
      console.log('❌ auth.routes.ts 不存在');
      return false;
    }
    
    const content = fs.readFileSync(authRoutesPath, 'utf8');
    const lines = content.split('\n');
    
    console.log('🔍 auth.routes.ts 中的导入语句:');
    lines.forEach((line, index) => {
      if (line.trim().startsWith('import')) {
        console.log(`   第${index + 1}行: ${line.trim()}`);
        
        // 检查是否导入 auth.service
        if (line.includes('auth.service')) {
          console.log('   📌 这是auth.service的导入语句');
          
          // 尝试解析路径
          const match = line.match(/from\s+['"]([^'"]+)['"]/);
          if (match) {
            const importPath = match[1];
            console.log(`   📍 导入路径: ${importPath}`);
            
            // 检查相对路径
            if (importPath.startsWith('.')) {
              const basePath = path.dirname(authRoutesPath);
              const resolvedPath = path.resolve(basePath, importPath);
              
              // 尝试不同的扩展名
              const extensions = ['', '.ts', '.js'];
              let found = false;
              
              for (const ext of extensions) {
                const testPath = resolvedPath + ext;
                if (fs.existsSync(testPath)) {
                  console.log(`   ✅ 解析为: ${testPath}`);
                  found = true;
                  break;
                }
              }
              
              if (!found) {
                console.log(`   ❌ 无法解析路径: ${resolvedPath}`);
                
                // 建议可能的路径
                console.log('   💡 可能的正确路径:');
                const suggestions = [
                  '../auth/auth.service',
                  '../auth/auth.service.ts',
                  './auth.service',
                  './auth.service.ts'
                ];
                
                suggestions.forEach(suggestion => {
                  const suggestionPath = path.resolve(basePath, suggestion);
                  if (fs.existsSync(suggestionPath) || fs.existsSync(suggestionPath + '.ts')) {
                    console.log(`      ✅ ${suggestion}`);
                  }
                });
              }
            }
          }
        }
      }
    });
    
    return true;
  } catch (error) {
    console.log(`❌ 检查导入语句时出错: ${error.message}`);
    return false;
  }
}

// 检查TypeScript配置
function checkTsConfig() {
  console.log('\n⚙️  检查TypeScript配置...');
  
  const configs = [
    'tsconfig.json',
    'tsconfig.base.json',
    'src/api/tsconfig.json'
  ];
  
  configs.forEach(config => {
    if (fs.existsSync(config)) {
      console.log(`✅ ${config}`);
      
      try {
        const content = fs.readFileSync(config, 'utf8');
        const parsed = JSON.parse(content.replace(/\/\*[\s\S]*?\*\//g, '').replace(/\/\/.*$/gm, ''));
        
        if (parsed.compilerOptions && parsed.compilerOptions.paths) {
          console.log(`   📍 路径映射:`);
          Object.entries(parsed.compilerOptions.paths).forEach(([key, value]) => {
            console.log(`      ${key} -> ${JSON.stringify(value)}`);
          });
        }
        
        if (parsed.compilerOptions && parsed.compilerOptions.baseUrl) {
          console.log(`   📂 基础URL: ${parsed.compilerOptions.baseUrl}`);
        }
      } catch (error) {
        console.log(`   ⚠️  解析配置文件时出错: ${error.message}`);
      }
    } else {
      console.log(`❌ ${config}`);
    }
  });
}

// 生成修复建议
function generateFixSuggestions() {
  console.log('\n💡 修复建议:');
  
  console.log('1. 确保文件路径正确:');
  console.log('   - src/auth/auth.service.ts 应该存在');
  console.log('   - 从 src/api/auth.routes.ts 导入应该使用 "../auth/auth.service"');
  
  console.log('\n2. 如果使用tsx运行，尝试:');
  console.log('   - 添加 .ts 扩展名: "../auth/auth.service.ts"');
  console.log('   - 或使用路径别名: "@app/auth/auth.service"');
  
  console.log('\n3. 确保在项目根目录运行:');
  console.log('   cd /path/to/keeptrack.space');
  console.log('   npx tsx src/api/server.ts');
  
  console.log('\n4. 检查Node.js模块解析:');
  console.log('   - 确保使用Node.js 16+');
  console.log('   - 检查package.json中的"type"字段');
}

// 主函数
function main() {
  console.log(`📅 检查时间: ${new Date().toISOString()}`);
  console.log(`📂 当前目录: ${process.cwd()}\n`);
  
  const structureOk = checkDirectoryStructure();
  const importsOk = checkImports();
  checkTsConfig();
  generateFixSuggestions();
  
  console.log('\n📊 诊断结果:');
  console.log(`   目录结构: ${structureOk ? '✅ 正常' : '❌ 有问题'}`);
  console.log(`   导入语句: ${importsOk ? '✅ 正常' : '❌ 有问题'}`);
  
  if (structureOk && importsOk) {
    console.log('\n🎉 模块解析应该正常工作！');
  } else {
    console.log('\n⚠️  发现问题，请根据建议进行修复。');
  }
}

// 运行诊断
main();
