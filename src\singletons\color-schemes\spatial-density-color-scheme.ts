/* eslint-disable complexity */
import { ColorInformation, Pickable, rgbaArray } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { BaseObject, DetailedSatellite, Star } from 'ootk';
import { DensityBin } from '../catalog-manager';
import { ColorScheme, ColorSchemeColorMap } from './color-scheme';

export interface SourceColorSchemeColorMap extends ColorSchemeColorMap {
  spatialDensityLow: rgbaArray;
  densityMed: rgbaArray;
  densityHi: rgbaArray;
  densityOther: rgbaArray;
  sensorAlt: rgbaArray;
}

export class SpatialDensityColorScheme extends ColorScheme {
  colorTheme: Record<string, rgbaArray>;
  objectTypeFlags: Record<string, boolean>;
  readonly label = '空间密度';
  readonly id = 'SpatialDensityColorScheme';
  static readonly id = 'SpatialDensityColorScheme';

  static readonly uniqueObjectTypeFlags = {
    spatialDensityHi: true,
    spatialDensityMed: true,
    spatialDensityLow: true,
    spatialDensityOther: true,
  };

  static readonly uniqueColorTheme = {
    spatialDensityHi: [1, 0, 0, 1] as rgbaArray,
    spatialDensityMed: [1, 0.4, 0, 1] as rgbaArray,
    spatialDensityLow: [1, 1, 0, 0.9] as rgbaArray,
    spatialDensityOther: [0.8, 0.8, 0.8, 0.3] as rgbaArray,
    sensorAlt: [0.0, 0.0, 1.0, 1.0] as rgbaArray,
  };

  private readonly lowDensityThreshold = 1.0e-8;
  private readonly mediumDensityThreshold = 1.0e-7;
  private readonly highDensityThreshold = 1.5e-7;

  constructor() {
    super(SpatialDensityColorScheme.uniqueColorTheme);
    this.objectTypeFlags = {
      ...this.objectTypeFlags, ...SpatialDensityColorScheme.uniqueObjectTypeFlags,
    };
  }

  calculateParams() {
    const catalogManagerInstance = keepTrackApi.getCatalogManager();

    return {
      orbitDensity: catalogManagerInstance.orbitDensity,
      orbitDensityMax: catalogManagerInstance.orbitDensityMax,
    };
  }

  update(obj: BaseObject, params?: {
    orbitDensity: DensityBin[];
    orbitDensityMax: number;
  }): ColorInformation {
    /*
     * NOSONAR
     * Hover and Select code might not pass params, so we will handle that here
     * TODO: Hover and select code should be refactored to pass params
     */
    if (!params) {
      const catalogManagerInstance = keepTrackApi.getCatalogManager();

      params = {
        orbitDensity: catalogManagerInstance.orbitDensity,
        orbitDensityMax: catalogManagerInstance.orbitDensityMax,
      };
    }

    if (obj.isStar()) {
      return this.starColor_(obj as Star);
    }

    const checkFacility = this.checkFacility_(obj);

    if (checkFacility) {
      return checkFacility;
    }

    if (obj.isSensor()) {
      return {
        color: this.colorTheme.sensorAlt,
        pickable: Pickable.Yes,
      };
    }
    if (obj.isMissile()) {
      return {
        color: this.colorTheme.transparent,
        pickable: Pickable.No,
      };
    }

    const sat = obj as DetailedSatellite;
    const altitude = (sat.apogee + sat.perigee) / 2;
    /*
     * orbit density bins are 25km apart starting at 75 and end at 1050. Find the bin
     * that is closest to the altitude
     */

    const bin = Math.floor((altitude - 75) / 25);
    let orbitDensity = 0;

    if (bin <= 76) {
      if (altitude < params.orbitDensity[bin].minAltitude || altitude > params.orbitDensity[bin].maxAltitude) {
        throw new Error(`Altitude ${altitude} is out of range for bin ${bin}`);
      }

      orbitDensity = params.orbitDensity[bin].density;

      if (this.objectTypeFlags.spatialDensityHi && orbitDensity > this.highDensityThreshold) {
        return {
          color: settingsManager.colors.spatialDensityHi,
          pickable: Pickable.Yes,
        };
      } else if (this.objectTypeFlags.spatialDensityMed && orbitDensity > this.mediumDensityThreshold && orbitDensity <= this.highDensityThreshold) {
        return {
          color: settingsManager.colors.spatialDensityMed,
          pickable: Pickable.Yes,
        };
      } else if (this.objectTypeFlags.spatialDensityLow && orbitDensity > this.lowDensityThreshold && orbitDensity <= this.mediumDensityThreshold) {
        return {
          color: settingsManager.colors.spatialDensityLow,
          pickable: Pickable.Yes,
        };
      }
    }

    if (this.objectTypeFlags.spatialDensityOther && orbitDensity <= this.lowDensityThreshold) {
      return {
        color: settingsManager.colors.spatialDensityOther,
        pickable: Pickable.Yes,
      };
    }

    // Deselected
    return {
      color: this.colorTheme.deselected,
      pickable: Pickable.No,
    };
  }

  updateGroup(obj: BaseObject): ColorInformation {
    // 检查是否有选中的组
    const selectedGroup = keepTrackApi.getGroupsManager().selectedGroup;
    if (!selectedGroup) {
      // 没有选中组时，使用正常的颜色方案
      return this.update(obj);
    }

    // 有选中组时，检查对象是否在组中
    if (!selectedGroup.hasObject(obj.id)) {
      // 不在组中的对象强制设为完全透明（隐藏）
      return {
        color: [0.0, 0.0, 0.0, 0.0] as rgbaArray, // 强制透明
        pickable: Pickable.No,
      };
    }

    // 在组中的对象使用正常的颜色方案
    return this.update(obj);
  }

  static readonly legendHtml = keepTrackApi.html`
  <ul id="legend-list-spatialDensityHi">
    <li>
      <div class="Square-Box legend-spatialDensityHi-box"></div>
      高轨道密度
    </li>
    <li>
      <div class="Square-Box legend-spatialDensityMed-box"></div>
      中等轨道密度
    </li>
    <li>
      <div class="Square-Box legend-spatialDensityLow-box"></div>
      低轨道密度
    </li>
    <li>
      <div class="Square-Box legend-spatialDensityOther-box"></div>
      其他轨道密度
    </li>
  </ul>
  `.trim();
}
