/**
 * Array of Russian missile data. Each missile is 4 elements long.
 *
 * 1: Latitude
 * 2: Longitude
 * 3: Name
 * 4: Range
 */
export const RussianICBM = [
  52 + 30 * 0.01666667,
  82 + 45 * 0.01666667,
  'Aleysk (SS-18)',
  16000,
  50 + 45 * 0.01666667,
  59 + 30 * 0.01666667,
  '<PERSON><PERSON><PERSON><PERSON><PERSON> (SS-18)',
  16000,
  55 + 20 * 0.01666667,
  89 + 48 * 0.01666667,
  '<PERSON><PERSON><PERSON> (SS-18)',
  16000,
  53 + 58 * 0.01666667,
  57 + 50 * 0.01666667,
  '<PERSON><PERSON><PERSON> (SS-18)',
  16000,
  52 + 19 * 0.01666667,
  104 + 14 * 0.01666667,
  '<PERSON><PERSON><PERSON><PERSON> (SS-25)',
  10500,
  56 + 22 * 0.01666667,
  95 + 28 * 0.01666667,
  '<PERSON><PERSON><PERSON> (SS-25)',
  10500,
  54 + 2 * 0.01666667,
  35 + 46 * 0.01666667,
  '<PERSON><PERSON><PERSON>sk (SS-19)',
  10000,
  56 + 22 * 0.01666667,
  92 + 25 * 0.01666667,
  '<PERSON><PERSON><PERSON><PERSON><PERSON> (SS-25)',
  10500,
  58 + 4 * 0.01666667,
  60 + 33 * 0.01666667,
  'Nizhniy Tagil (SS-25)',
  10500,
  55 + 20 * 0.01666667,
  83 + 0 * 0.01666667,
  'Nov<PERSON>ibirsk (SS-25)',
  10500,
  51 + 40 * 0.01666667,
  45 + 34 * 0.01666667,
  'Tatishchevo (SS-19)',
  10000,
  51 + 40 * 0.01666667,
  45 + 34 * 0.01666667,
  'Tatishchevo (SS-27)',
  10500,
  56 + 51 * 0.01666667,
  40 + 32 * 0.01666667,
  'Teykovo (SS-25)',
  10500,
  56 + 38 * 0.01666667,
  47 + 51 * 0.01666667,
  'Yoshkar Ola (SS-25)',
  10500,
  72.039545,
  42.696683,
  'Verkhoturye (SS-N-23A)',
  8300,
  73.902056,
  3.133463,
  'Ekaterinburg (SS-N-23A)',
  8300,
  76.502284,
  -158.871984,
  'Tula (SS-N-23A)',
  8300,
  82.25681,
  -10.161045,
  'Bryansk (SS-N-23A)',
  8300,
  81.564646,
  32.553796,
  'Karelia (SS-N-23A)',
  8300,
  74.67366,
  6.538173,
  'Novomoskovsk (SS-N-23A)',
  8300,
  71.920763,
  41.039876,
  'Borei Sub (Bulava)',
  9300,
  71.920763,
  41.039876,
  'Delta IV Sub (Sineva)',
  8300,
  71.920763,
  41.039876,
  'Delta IV Sub (Layner)',
  12000, // Sub
];

/**
 * Array of Chinese missile data. Each missile is 4 elements long.
 *
 * 1: Latitude
 * 2: Longitude
 * 3: Name
 * 4: Range
 */
export const ChinaICBM = [
  32.997534,
  112.537904,
  'Nanyang (DF-31)',
  8000,
  36.621398,
  101.773908,
  'Xining (DF-31)',
  8000,
  37.797257,
  97.079547,
  'Delingha (DF-31A)',
  11000,
  37.07045,
  100.805779,
  'Haiyan (DF-31A)',
  11000,
  40.079969,
  113.29994,
  'Datong (DF-31A)',
  11000,
  34.583156,
  105.724525,
  'Tainshui (DF-31A)',
  11000,
  38.552936,
  106.020538,
  'Xixia (DF-31A)',
  11000,
  27.242253,
  111.465223,
  'Shaoyang (DF-31A)',
  11000,
  24.34658,
  102.527838,
  'Yuxi (DF-31A)',
  11000,
  34.345845,
  111.491062,
  'Luoyang (DF-5A/B)',
  13000,
  38.917086,
  111.847057,
  'Wuzhai (DF-5A/B)',
  13000,
  40.615707,
  115.107604,
  'Xuanhua (DF-5A/B)',
  13000,
  26.163848,
  109.790408,
  'Tongdao (DF-5A/B)',
  13000,
  34.061291,
  111.054379,
  'Lushi (DF-5A/B)',
  13000,
  30.691542,
  118.437169,
  'Jingxian (DF-5A/B)',
  13000,
  37.707532,
  116.271994,
  'Jingxian (DF-5A/B)',
  13000,
  27.415932,
  111.792471,
  'Hunan (DF-5A/B)',
  13000,
  46.585153,
  125.104037,
  'Daqing City (DF-41)',
  13500,
  32.154153,
  114.099875,
  'Xinyang City (DF-41)',
  13500,
  40.4417,
  85.530745,
  'Xinjiang Province (DF-41)',
  13500,
  31.271257,
  88.699152,
  'Tibet Province (DF-41)',
  13500,
  29.573548,
  122.923151,
  'Type 092 Sub (JL-2)',
  8000,
];

/**
 * Array of North Korean missile data. Each missile is 4 elements long.
 *
 * 1: Latitude
 * 2: Longitude
 * 3: Name
 * 4: Range
 */
export const NorthKoreanBM = [
  40,
  128.3,
  'Sinpo Sub (Pukkŭksŏng-1)',
  2500,
  40.019,
  128.193,
  'Sinpo (KN-14)',
  8000,
  39.365,
  126.165,
  'P`yong`an (KN-20)',
  10000,
  39.046,
  125.667,
  'Pyongyang (KN-22)',
  13000,
];

/**
 * Array of US missile data. Each missile is 4 elements long.
 *
 * 1: Latitude
 * 2: Longitude
 * 3: Name
 * 4: Range
 */
export const UsaICBM = [
  48.420079,
  -101.33356,
  'Ohio Sub (Trident II)',
  12000,
  48.420079,
  -101.33356,
  'Minot (Minuteman III)',
  13000,
  47.505958,
  -111.181776,
  'Malmstrom (Minuteman III)',
  13000,
  41.149931,
  -104.860645,
  'F.E. Warren (Minuteman III)',
  13000,
];

/**
 * Array of French missile data. Each missile is 4 elements long.
 *
 * 1: Latitude
 * 2: Longitude
 * 3: Name
 * 4: Range
 */
export const FraSLBM = [47.878, -4.263, 'Triomphant Sub (M51)', 10000, 47.878, -4.263, 'Triomphant Sub (M51)', 10000];

/**
 * Array of British missile data. Each missile is 4 elements long.
 *
 * 1: Latitude
 * 2: Longitude
 * 3: Name
 * 4: Range
 */
export const ukSLBM = [56.066111, -4.8175, 'Vanguard Sub (Trident II)', 12000, 56.066111, -4.8175, 'HMNB Clyde (Trident II)', 12000];

/**
 * Array of global target data. Each target is 3 elements long.
 *
 * 1: Latitude
 * 2: Longitude
 * 3: Name
 */
export const globalBMTargets = [
  38.951,
  -77.013,
  'Washington DC',
  40.679,
  -73.947,
  'New York City',
  34.073,
  -118.248,
  'Los Angeles',
  41.877,
  -87.622,
  'Chicago',
  42.361,
  -71.058,
  'Boston',
  47.749,
  -122.317,
  'Seattle',
  25.784,
  -80.196,
  'Miami',
  32.828,
  -96.759,
  'Dallas',
  38.765,
  -104.837,
  'Colorado Springs',
  41.33,
  -96.054,
  'Omaha',
  19.832,
  -155.491,
  'Hawaii',
  13.588,
  144.922,
  'Guam',
  51.50634,
  -0.097485,
  'London',
  48.874195,
  2.378987,
  'Paris',
  24.503,
  -66.127,
  'French Caribean',
  40.449889,
  -3.717309,
  'Madrid',
  41.931955,
  12.520198,
  'Rome',
  52.501746,
  13.416486,
  'Berlin',
  43.706946,
  -79.423854,
  'Toronto',
  55.750246,
  37.691525,
  'Moscow',
  59.887535,
  30.38409,
  'St. Petersburg',
  55.017165,
  82.965879,
  'Novosibirsk',
  39.974338,
  116.396057,
  'Beijing',
  39.044051,
  125.735244,
  'Pyongyang',
];

/**
 * Array of US target data. Each target is 3 elements long.
 *
 * 1: Latitude
 * 2: Longitude
 * 3: Name
 */
export const USATargets = [
  40.679,
  -73.947,
  42.361,
  -71.058,
  41.755,
  -70.539,
  41.763,
  -72.684,
  42.101,
  -72.59,
  39.408,
  -74.441,
  39.191,
  -75.534,
  39.331,
  -76.671,
  38.951,
  -77.013,
  37.608,
  -77.378,
  42.36,
  -83.048,
  39.844,
  -86.172,
  40.008,
  -83,
  40.538,
  -79.934,
  40.034,
  -75.131,
  47.749,
  -122.317,
  45.7,
  -122.581,
  47.732,
  -117.389,
  37.889,
  -122.562,
  36.257,
  -115.159,
  48.034,
  -101.295,
  49.134,
  -101.495,
  48.234,
  -100.295,
  48.334,
  -101.095,
  48.434,
  -101.295,
  47.948,
  -97.027,
  45.107,
  -93.306,
  47.092,
  -110.334,
  47.292,
  -111.834,
  47.592,
  -111.934,
  46.792,
  -111.334,
  47.992,
  -111.534,
  47.792,
  -110.734,
  48.592,
  -111.534,
  47.292,
  -111.334,
  46.092,
  -111.134,
  47.592,
  -110.034,
  40.21,
  -104.811,
  41.51,
  -105.811,
  41.21,
  -104.211,
  40.51,
  -104.211,
  41.21,
  -105.611,
  41.51,
  -104.611,
  41.21,
  -103.011,
  42.21,
  -104.011,
  41.91,
  -104.811,
  41.91,
  -104.811,
  34.048,
  -118.28,
  19.832,
  -155.491,
  13.588,
  144.922,
  36.318,
  -86.718,
  32.782,
  -97.343,
  32.584,
  -99.707,
  35.208,
  -101.837,
  35.188,
  -106.595,
  33.603,
  -111.965,
  38.765,
  -104.837,
  38.737,
  -104.883,
  39.847,
  -104.902,
  40.684,
  -105.059,
  40.852,
  -111.827,
  61.343,
  -150.187,
  64.94,
  -147.881,
  58.488,
  -134.238,
  30.46,
  -86.549,
  41.33,
  -96.054,
  39.113276,
  -121.356137,
  64.303735,
  -149.148768,
  76.534322,
  -68.718288,
  41.875523,
  -87.634038,
  35.145865,
  -89.979153,
  43.663448,
  -70.278127,
  43.612156,
  -116.231845, // Boise ID
];
