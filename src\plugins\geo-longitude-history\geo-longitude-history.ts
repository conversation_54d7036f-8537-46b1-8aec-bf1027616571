import { MenuMode, ToastMsgType } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import waterfall2Png from '@public/img/icons/waterfall2.png';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import * as echarts from 'echarts';

export class GeoLongitudeHistoryPlugin extends KeepTrackPlugin {
  // ECharts实例用于滑动控件
  private echartsSlider: echarts.ECharts | null = null;
  // 标志：是否正在进行过滤操作（防止重新初始化滑动控件）
  private isFiltering: boolean = false;
  // 标志：是否正在加载数据
  private _isLoading: boolean = false;

  constructor() {
    super();
  }

  init(): void {
    super.init();
  }

  // 当点击底部图标时的回调
  bottomIconCallback = (): void => {
    this.updateNoradFromSelectedSat();

    // 修复日期选择器
    setTimeout(() => {
      this.fixDatePickers();
      // 强制重置输入框样式，防止自动填充变色
      this.forceResetInputStyles_();



      // 只在输入框获得焦点时重置样式
      const noradInput = document.getElementById('geo-longitude-norad-id') as HTMLInputElement;
      if (noradInput) {
        noradInput.addEventListener('focus', () => this.forceResetInputStyles_());
        noradInput.addEventListener('input', () => this.forceResetInputStyles_());
      }
    }, 200);
  };

  private fixDatePickers(): void {
    const startDateInput = document.getElementById('geo-longitude-start-date') as HTMLInputElement;
    const endDateInput = document.getElementById('geo-longitude-end-date') as HTMLInputElement;

    if (startDateInput && endDateInput) {
      // 设置默认日期：结束日期为今天，开始日期为6个月前
      const today = new Date();
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(today.getMonth() - 6);

      const formatDate = (date: Date) => {
        return date.toISOString().split('T')[0];
      };

      if (!endDateInput.value) {
        endDateInput.value = formatDate(today);
      }
      if (!startDateInput.value) {
        startDateInput.value = formatDate(sixMonthsAgo);
      }

      // 移除可能存在的旧事件监听器
      startDateInput.removeEventListener('focus', this.handleDateInputFocus);
      endDateInput.removeEventListener('focus', this.handleDateInputFocus);

      // 添加新的事件监听器
      startDateInput.addEventListener('focus', this.handleDateInputFocus);
      endDateInput.addEventListener('focus', this.handleDateInputFocus);
    }
  }

  private updateNoradFromSelectedSat(): void {
    try {
      const selectSatManager = keepTrackApi.getPluginByName('SelectSatManager') as any;
      if (selectSatManager?.selectedSat > -1) {
        const selectedSat = selectSatManager.getSelectedSat();
        if (selectedSat?.isSatellite()) {
          const sat = selectedSat as any;
          const noradInput = document.getElementById('geo-longitude-norad-id') as HTMLInputElement;
          if (noradInput && sat.sccNum) {
            noradInput.value = sat.sccNum.toString();
          }
        }
      }
    } catch (error) {
      // 忽略错误，可能是插件还未初始化
    }
  }

  readonly id = 'GeoLongitudeHistoryPlugin';
  protected dependencies_: string[] = [];
  menuMode: MenuMode[] = [MenuMode.ANALYSIS, MenuMode.ALL];
  bottomIconImg = waterfall2Png;
  bottomIconLabel = '历史经度';
  bottomIconElementName = 'menu-geo-longitude-history';
  sideMenuElementName = 'geo-longitude-history-menu';
  sideMenuElementHtml = keepTrackApi.html`
  <style>
  #geo-longitude-chart-container {
    position: relative;
    overflow: hidden;
    height: calc(100vh - 200px);
  }
  #geo-longitude-chart {
    cursor: crosshair;
  }
  .geo-longitude-download-btn {
    position: fixed;
    bottom: calc(20px / var(--system-scale-factor, 1));
    right: calc(20px / var(--system-scale-factor, 1));
    z-index: 9999;
    background: #2196f3;
    color: white;
    border: none;
    border-radius: calc(6px / var(--system-scale-factor, 1));
    padding: calc(12px / var(--system-scale-factor, 1)) calc(20px / var(--system-scale-factor, 1));
    font-size: calc(14px / var(--system-scale-factor, 1));
    cursor: pointer;
    box-shadow: 0 calc(2px / var(--system-scale-factor, 1)) calc(8px / var(--system-scale-factor, 1)) rgba(0,0,0,0.3);
    transition: all 0.2s ease;
  }
  .geo-longitude-download-btn:hover {
    background: #1976d2;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
  }

  /* 🔥🔥🔥 强制历史经度页面输入框透明 - 最高优先级 🔥🔥🔥 */
  #geo-longitude-history-menu input,
  #geo-longitude-norad-id,
  #geo-longitude-start-date,
  #geo-longitude-end-date,
  #geo-longitude-range {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    border: none !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
    border-radius: 0 !important;
    color: white !important;
    outline: none !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
  }

  /* 聚焦状态 */
  #geo-longitude-history-menu input:focus,
  #geo-longitude-norad-id:focus,
  #geo-longitude-start-date:focus,
  #geo-longitude-end-date:focus,
  #geo-longitude-range:focus {
    background: transparent !important;
    background-color: transparent !important;
    border-bottom-color: rgba(255, 255, 255, 0.8) !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
  }
  </style>
  <div id="geo-longitude-history-menu" class="side-menu-parent start-hidden text-select" style="width: 100vw; min-width: 100vw; max-width: 100vw;">
    <!-- 右上角关闭按钮 -->
    <div id="geo-longitude-close-btn" style="position: fixed; top: 20px; right: 20px; z-index: 99999; width: 50px; height: 50px; background: rgba(220, 53, 69, 0.8); border: none; border-radius: 6px; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 32px; color: #fff; font-weight: bold; user-select: none;" title="关闭">×</div>
    <div id="geo-longitude-inner-menu" class="side-menu" style="width: 100vw;">
      <!-- 使用与历史轨道数据相同的标题布局 -->
      <div class="row" style="margin-bottom: 0;">
        <div class="col s12" style="padding: 0;">
          <form id="geo-longitude-form" style="display: flex; flex-direction: row; align-items: center; justify-content: center; width: 100vw; gap: 0;">
            <div style="display: flex; align-items: center; margin-right: 24px;">
              <label for="geo-longitude-norad-id" style="margin-bottom: 0; margin-right: 12px; white-space: nowrap; font-size: 16px; color: #ffffff; line-height: 32px;">NORAD编号</label>
              <input value="" id="geo-longitude-norad-id" type="text" placeholder="留空查询所有GEO卫星" title="NORAD编号输入规则：&#10;• 单个卫星：25544&#10;• 多个卫星：25544,25545,25546&#10;• 空格分隔：25544 25545 25546&#10;• 留空：查询所有GEO卫星" style="width: 220px; height: 32px; margin-right: 16px; font-size: 16px; line-height: 32px; padding: 0 8px; box-sizing: border-box; background: transparent !important; background-color: transparent !important; background-image: none !important; border: none; border-bottom: 1px solid rgba(255, 255, 255, 0.5); color: white; outline: none; box-shadow: 0 0 0 30px transparent inset !important;" />
            </div>
            <div style="display: flex; align-items: center; margin-right: 24px;">
              <label for="geo-longitude-start-date" style="margin-bottom: 0; margin-right: 12px; white-space: nowrap; font-size: 16px; color: #ffffff; line-height: 32px;">开始日期</label>
              <input id="geo-longitude-start-date" type="date" style="width: 160px; height: 32px; margin-right: 16px; font-size: 16px; line-height: 32px; padding: 0 8px; box-sizing: border-box;" />
            </div>
            <div style="display: flex; align-items: center; margin-right: 24px;">
              <label for="geo-longitude-end-date" style="margin-bottom: 0; margin-right: 12px; white-space: nowrap; font-size: 16px; color: #ffffff; line-height: 32px;">结束日期</label>
              <input id="geo-longitude-end-date" type="date" style="width: 160px; height: 32px; margin-right: 16px; font-size: 16px; line-height: 32px; padding: 0 8px; box-sizing: border-box;" />
            </div>
            <div style="display: flex; align-items: center; margin-right: 24px;">
              <label for="geo-longitude-range" style="margin-bottom: 0; margin-right: 12px; white-space: nowrap; font-size: 16px; color: #ffffff; line-height: 32px;">经度范围</label>
              <input value="" id="geo-longitude-range" type="text" placeholder="如: 85~100 或 85-100 或 85 100" title="经度范围输入规则：&#10;• 波浪号：85~100&#10;• 短横线：85-100&#10;• 空格：85 100&#10;• 范围：-180到180度&#10;• 留空或0：不过滤经度" style="width: 200px; height: 32px; margin-right: 16px; font-size: 16px; line-height: 32px; padding: 0 8px; box-sizing: border-box; background: transparent; border: none; border-bottom: 1px solid rgba(255, 255, 255, 0.5); color: white; outline: none;" />
            </div>
            <button id="geo-longitude-get-data" class="blue-btn" type="button" style="height: 32px; padding: 0 16px; font-size: 16px; line-height: 32px; cursor: pointer; white-space: nowrap;">获取数据 &#9658;</button>
          </form>
        </div>
      </div>
      <div id="geo-longitude-results" class="row" style="display: none;">
        <div class="col s12">
          <!-- 下载按钮移动到图表外左上方 -->
          <div style="position: relative; margin-bottom: 10px;">
            <button id="geo-longitude-download-btn" class="geo-longitude-download-btn" style="display: none; position: absolute; top: 0; left: 0; z-index: 10; background: rgba(0, 0, 0, 0.7); color: white; border: 2px solid white; border-radius: 6px; padding: 8px 16px; font-size: 14px; cursor: pointer; width: auto; min-width: 100px; max-width: 150px; white-space: nowrap;">下载数据</button>
          </div>
          <div id="geo-longitude-stats-title" class="center-align" style="font-size: 14px; font-weight: normal; margin: 12px 0 8px 0; color: #888888;"></div>
          <div id="geo-longitude-chart-container" style="width: 100%; margin: 10px 0; position: relative;">
            <canvas id="geo-longitude-chart" style="width: 100%; height: calc(100% - 80px); display: block;"></canvas>
            <!-- ECharts滑动控件容器 -->
            <div id="geo-longitude-echarts-container" style="width: 100%; height: 80px; position: absolute; bottom: 0; left: 0; display: none;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>`;

  addJs(): void {
    super.addJs();

    // 添加关闭按钮事件监听器
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target && target.id === 'geo-longitude-close-btn') {
        e.preventDefault();
        e.stopPropagation();

        // 隐藏菜单
        const menu = document.getElementById('geo-longitude-history-menu');
        if (menu) {
          menu.classList.add('start-hidden');
          menu.style.display = 'none';
        }

        // 取消底部图标选中状态
        const bottomIcon = document.getElementById(this.bottomIconElementName);
        if (bottomIcon) {
          bottomIcon.classList.remove('bmenu-item-selected');
        }

        // 重置插件状态
        this.isMenuButtonActive = false;
      }
    });

    // 初始化日期选择器
    setTimeout(() => {
      this.initializeDatePickers_();
      // 🔥🔥🔥 强制重置输入框样式
      this.forceResetInputStyles_();
    }, 100);

    // 添加获取数据按钮事件监听器
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target && target.id === 'geo-longitude-get-data') {
        e.preventDefault();
        e.stopPropagation();
        this.handleGetData_();
      }
    });

    // 添加下载按钮事件监听器
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target && target.id === 'geo-longitude-download-btn') {
        e.preventDefault();
        e.stopPropagation();
        this.downloadData_();
      }
    });
  }

  // 存储查询结果数据
  private lastQueryData: any[] = [];

  // 性能优化：缓存处理后的数据
  private dataProcessingCache = new Map<string, any>();
  private lastDataHash = '';
  private lastMouseMoveTime = 0;
  private mouseHoverThrottle = 16; // 约60fps的节流
  private lastRenderHash = '';
  private renderCache: ImageData | null = null;

  // 数据视图管理（完全复制自历史轨道数据）
  private dataView = {
    yMin: 0,
    yMax: 1000,
    yRange: 1000,
    xMin: 0,
    xMax: 1,
    xRange: 1,
  };

  private scale = 1;
  private globalXMin = 0;
  private globalXMax = 1;
  private _onWheelBound: ((e: WheelEvent) => void) | null = null;
  private selectedSatelliteName: string | null = null; // 存储选中的卫星名字
  private dragUpdateTimer: number | null = null; // 拖拽更新定时器

  // 修复日期选择器（复制自历史轨道数据的实现）
  private initializeDatePickers_(): void {
    this.fixDatePickers();
  }

  private handleDateInputFocus = (event: Event): void => {
    const input = event.target as HTMLInputElement;
    // 尝试显示日期选择器，但只在用户手势触发时才有效
    try {
      // 只有在事件是由用户交互触发时才调用 showPicker
      if (event.isTrusted) {
        input.showPicker?.();
      }
    } catch (error) {
      // 忽略 showPicker 错误，这通常是因为缺少用户手势
    }
  };

  private async handleGetData_(): Promise<void> {
    const rawInput = (getEl('geo-longitude-norad-id') as HTMLInputElement)?.value || '';
    const startDate = (getEl('geo-longitude-start-date') as HTMLInputElement)?.value;
    const endDate = (getEl('geo-longitude-end-date') as HTMLInputElement)?.value;
    const rangeInput = (getEl('geo-longitude-range') as HTMLInputElement)?.value || '';

    if (!startDate || !endDate) {
      keepTrackApi.getUiManager()?.toast('请填写开始日期和结束日期', ToastMsgType.error);
      return;
    }

    // 解析经度范围
    const longitudeRange = this.parseLongitudeRange_(rangeInput);
    if (rangeInput && !longitudeRange) {
      keepTrackApi.getUiManager()?.toast('经度范围格式错误，请使用如：85~100 或 85-100 或 85 100', ToastMsgType.error);
      return;
    }

    // 立即显示"数据正在获取中"的图表
    this.showLoadingChart_();

    // 开始获取数据时立即显示结果容器
    const resultsContainer = document.getElementById('geo-longitude-results');
    if (resultsContainer) {
      resultsContainer.style.display = 'block';
    }

    // 显示空图表
    this.clearChart_();

    try {
      // 使用配置加载器获取API地址
      let apiBaseUrl: string;
      if ((window as any).configLoader) {
        try {
          apiBaseUrl = await (window as any).configLoader.getApiBaseUrl();
        } catch (error) {
          console.warn('无法从配置加载器获取API地址，使用默认地址:', error);
          const currentHost = window.location.hostname;
          apiBaseUrl = `http://${currentHost}:5001`;
        }
      } else {
        console.warn('配置加载器不可用，使用默认地址');
        const currentHost = window.location.hostname;
        apiBaseUrl = `http://${currentHost}:5001`;
      }
      
      // 构建查询参数
      const params = new URLSearchParams();
      if (rawInput.trim()) {
        // 如果有NORAD输入，查询特定卫星
        const noradIds = rawInput
          .split(/[\s,]+/)
          .map(id => id.trim())
          .filter(id => id.length > 0);
        noradIds.forEach(id => params.append('norad_id', id));
      }
      params.append('start', startDate!);
      params.append('end', endDate!);
      params.append('geo_only', 'true'); // 只查询GEO卫星

      // 如果有经度范围，添加到查询参数
      if (longitudeRange) {
        params.append('lon_min', longitudeRange.min.toString());
        params.append('lon_max', longitudeRange.max.toString());
      }

      const apiUrl = `${apiBaseUrl}/api/satellite-history?${params.toString()}`;
      
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        this.clearChart_('API请求失败，请检查网络连接');
        keepTrackApi.getUiManager()?.toast('API请求失败', ToastMsgType.error);
        return;
      }
      
      const data = await response.json();



      if (data.data && data.data.length > 0) {

        // 过滤和处理数据，只保留有效的GEO卫星经度数据
        // 注意：经度范围过滤现在在后端ES查询中处理，这里只需要基本验证
        const geoData = data.data
          .filter((record: any) => {
            const isGeo = record.object_type === 'GEO';
            const hasValidLongitude = record.subsat_long !== null &&
                                    record.subsat_long !== undefined &&
                                    record.subsat_long !== '' &&
                                    !isNaN(parseFloat(record.subsat_long));

            return isGeo && hasValidLongitude;
          })
          .map((record: any) => {
            const objectName = record.satellite_name || record.object_name || `SAT-${record.norad_id}`;

            return {
              norad_id: record.norad_id,
              time: record.time,
              longitude: parseFloat(record.subsat_long),
              object_type: record.object_type,
              object_name: objectName
            };
          });



        if (geoData.length > 0) {
          // 停止加载动画
          this._isLoading = false;

          this.lastQueryData = geoData;

          // 显示统计信息
          this.displayStats_(geoData);

          // 显示图表
          this.displayChart_(geoData);

          // 显示下载按钮
          const downloadBtn = document.getElementById('geo-longitude-download-btn');
          if (downloadBtn) {
            downloadBtn.style.display = 'block';
          }

          keepTrackApi.getUiManager()?.toast(`成功获取 ${geoData.length} 条GEO卫星记录`, ToastMsgType.normal);
        } else {
          // 停止加载动画
          this._isLoading = false;
          this.clearChart_('未找到符合条件的GEO卫星经度数据');
          keepTrackApi.getUiManager()?.toast('未找到有效的GEO卫星经度数据', ToastMsgType.error);
        }
      } else {
        // 停止加载动画
        this._isLoading = false;
        this.clearChart_('未找到符合条件的GEO卫星数据');
        keepTrackApi.getUiManager()?.toast('未找到数据', ToastMsgType.error);
      }
    } catch (error) {
      // 停止加载动画
      this._isLoading = false;
      this.clearChart_('网络请求失败，请检查网络连接');
      keepTrackApi.getUiManager()?.toast('网络请求失败', ToastMsgType.error);

    }
  }

  // 显示统计信息
  private displayStats_(data: any[]): void {
    const statsTitle = document.getElementById('geo-longitude-stats-title');
    if (!statsTitle) return;

    // 计算统计信息
    const uniqueSatellites = new Set(data.map(d => d.norad_id));
    const longitudes = data.map(d => d.longitude);
    const minLong = Math.min(...longitudes);
    const maxLong = Math.max(...longitudes);
    const timeRange = {
      min: new Date(Math.min(...data.map(d => new Date(d.time).getTime()))),
      max: new Date(Math.max(...data.map(d => new Date(d.time).getTime())))
    };

    // 检查是否有经度范围过滤
    const rangeInput = (getEl('geo-longitude-range') as HTMLInputElement)?.value || '';
    const longitudeRange = this.parseLongitudeRange_(rangeInput);

    let statsText = `共 ${uniqueSatellites.size} 颗GEO卫星，${data.length} 条记录 | ` +
                   `经度范围：${minLong.toFixed(1)}° ~ ${maxLong.toFixed(1)}° | ` +
                   `时间范围：${timeRange.min.toLocaleDateString()} ~ ${timeRange.max.toLocaleDateString()}`;

    // 如果有经度范围过滤，显示过滤条件
    if (longitudeRange) {
      statsText += ` | 过滤条件：${longitudeRange.min.toFixed(1)}° ~ ${longitudeRange.max.toFixed(1)}°`;
    }

    statsTitle.textContent = statsText;
    statsTitle.style.display = 'block';
  }

  // 显示"数据正在获取中"的图表
  private showLoadingChart_(): void {
    this._isLoading = true;

    const canvas = getEl('geo-longitude-chart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d', { willReadFrequently: true });
    if (!ctx) return;

    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    const width = rect.width;
    const height = 1000;

    canvas.width = width * dpr;
    canvas.height = height * dpr;
    ctx.scale(dpr, dpr);

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 绘制坐标轴框架
    const padding = 60;
    const bottomPadding = 50;
    const rightPadding = 30;

    ctx.strokeStyle = '#444';
    ctx.lineWidth = 1;
    ctx.beginPath();
    // 左边框
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - bottomPadding);
    // 底边框
    ctx.lineTo(width - rightPadding, height - bottomPadding);
    ctx.stroke();

    // 显示加载消息
    ctx.fillStyle = '#ffffff';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('数据正在获取中...', width / 2, height / 2);

    // 显示加载动画点
    const dots = Math.floor(Date.now() / 500) % 4;
    ctx.fillText('.'.repeat(dots), width / 2 + 80, height / 2);

    // 每500ms更新一次动画，但只有在还在加载状态时才继续
    setTimeout(() => {
      if (this._isLoading) {
        this.showLoadingChart_();
      }
    }, 500);
  }

  // 🔥 已删除动态CSS注入，完全依赖静态CSS文件

  private clearChart_(message?: string): void {
    const canvas = getEl('geo-longitude-chart') as HTMLCanvasElement;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d', { willReadFrequently: true });
    if (!ctx) return;
    
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    const width = rect.width;
    const height = 1000;
    
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    canvas.style.width = width + 'px';
    canvas.style.height = height + 'px';
    ctx.setTransform(1, 0, 0, 1, 0, 0);
    ctx.scale(dpr, dpr);
    
    // 清空画布
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, width, height);
    
    if (message) {
      ctx.fillStyle = '#ffffff';
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(message, width / 2, height / 2);
    }
  }

  private displayChart_(data: any[]): void {
    const canvas = getEl('geo-longitude-chart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d', { willReadFrequently: true });
    if (!ctx) return;

    // 生成渲染哈希，检查是否需要重绘
    const renderHash = this.generateRenderHash_(data);
    if (this.lastRenderHash === renderHash && this.renderCache) {
      // 使用缓存的渲染结果
      ctx.putImageData(this.renderCache, 0, 0);
      return;
    }

    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    const width = rect.width;
    const height = 1000;

    canvas.width = width * dpr;
    canvas.height = height * dpr;
    canvas.style.width = width + 'px';
    canvas.style.height = height + 'px';
    ctx.setTransform(1, 0, 0, 1, 0, 0);
    ctx.scale(dpr, dpr);

    const canvasWidth = canvas.width / dpr;
    const canvasHeight = canvas.height / dpr;
    const padding = 80;
    const bottomPadding = 60;
    const rightPadding = 40;

    // 计算数据范围
    const timeRange = this.calculateTimeRange_(data);
    const longitudeRange = this.calculateLongitudeRange_(data);

    // 初始化数据视图（如果是第一次显示）
    if (this.scale === 1) {
      // X轴对应时间范围（使用实际时间戳）
      this.globalXMin = timeRange.min.getTime();
      this.globalXMax = timeRange.max.getTime();
      this.dataView.xMin = timeRange.min.getTime();
      this.dataView.xMax = timeRange.max.getTime();
      this.dataView.xRange = timeRange.max.getTime() - timeRange.min.getTime();

      // Y轴对应经度范围
      this.dataView.yMin = longitudeRange.min;
      this.dataView.yMax = longitudeRange.max;
      this.dataView.yRange = longitudeRange.max - longitudeRange.min;
    }

    // 清空画布
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // 绘制网格
    ctx.strokeStyle = '#333333';
    ctx.lineWidth = 1;
    const gridStep = 50;
    for (let x = padding; x <= canvasWidth - rightPadding; x += gridStep) {
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, canvasHeight - bottomPadding);
      ctx.stroke();
    }
    for (let y = padding; y <= canvasHeight - bottomPadding; y += gridStep) {
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(canvasWidth - rightPadding, y);
      ctx.stroke();
    }

    // 绘制数据曲线
    this.drawDataCurves_(ctx, data, canvasWidth, canvasHeight, padding, bottomPadding, rightPadding);

    // 绘制坐标轴和刻度标签
    this.drawAxesWithLabels_(ctx, canvasWidth, canvasHeight, padding, bottomPadding, rightPadding);

    // 绘制统计信息（在图表左上角，完全模仿历史轨道数据页面）
    this.drawStatistics_(ctx, data, canvasWidth, canvasHeight, padding, rightPadding);

    // 缓存渲染结果
    try {
      this.renderCache = ctx.getImageData(0, 0, canvas.width, canvas.height);
      this.lastRenderHash = renderHash;
    } catch (error) {
      // 忽略缓存错误，继续正常渲染
    }

    // 绑定事件（确保每次都绑定）
    setTimeout(() => {
      this.bindCanvasEvents_(canvas);
      // 只在滑动控件不存在且不是过滤操作时才初始化
      if (!this.echartsSlider && !this.isFiltering) {
        this.initializeEChartsSlider_(data);
      }
    }, 100);
  }

  // 生成渲染哈希，用于判断是否需要重绘
  private generateRenderHash_(data: any[]): string {
    const viewHash = `${this.dataView.xMin}-${this.dataView.xMax}-${this.dataView.yMin}-${this.dataView.yMax}`;
    const dataHash = this.generateDataHash_(data);
    return `${viewHash}-${dataHash}`;
  }

  private downloadData_(): void {
    if (this.lastQueryData.length === 0) {
      keepTrackApi.getUiManager()?.toast('没有可下载的数据', ToastMsgType.error);
      return;
    }

    const jsonData = JSON.stringify(this.lastQueryData, null, 2);
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `geo-longitude-history-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    keepTrackApi.getUiManager()?.toast('数据下载完成', ToastMsgType.normal);
  }

  // 绘制坐标轴和刻度标签
  private drawAxesWithLabels_(ctx: CanvasRenderingContext2D, width: number, height: number,
                             padding: number, bottomPadding: number, rightPadding: number): void {
    const chartWidth = width - padding - rightPadding;
    const chartHeight = height - padding - bottomPadding;

    // 绘制坐标轴框架
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - bottomPadding);
    ctx.lineTo(width - rightPadding, height - bottomPadding);
    ctx.stroke();

    // 绘制横坐标刻度和标签（经度）
    ctx.fillStyle = '#cccccc';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.strokeStyle = '#666666';
    ctx.lineWidth = 1;

    // 计算经度刻度间隔
    const lonRange = this.dataView.yRange;
    const lonStep = this.calculateLongitudeStep_(lonRange);
    const startLon = Math.ceil(this.dataView.yMin / lonStep) * lonStep;
    const endLon = Math.floor(this.dataView.yMax / lonStep) * lonStep;

    for (let lon = startLon; lon <= endLon; lon += lonStep) {
      if (lon >= this.dataView.yMin && lon <= this.dataView.yMax) {
        const lonRatio = (lon - this.dataView.yMin) / this.dataView.yRange;
        const x = padding + lonRatio * chartWidth;

        // 绘制刻度线
        ctx.beginPath();
        ctx.moveTo(x, height - bottomPadding);
        ctx.lineTo(x, height - bottomPadding + 5);
        ctx.stroke();

        // 绘制标签（保留2位小数）
        const label = lon === 0 ? '0.00°' : lon > 0 ? `${lon.toFixed(2)}°E` : `${Math.abs(lon).toFixed(2)}°W`;
        ctx.fillText(label, x, height - bottomPadding + 20);
      }
    }

    // 绘制纵坐标刻度和标签（时间，最新时间在底部）
    ctx.textAlign = 'right';
    const timeSteps = 8;
    const currentTimeSpan = this.dataView.xRange;

    for (let i = 0; i <= timeSteps; i++) {
      const timeRatio = i / timeSteps;
      // 底部（i=0）为最新时间，顶部（i=timeSteps）为最早时间
      const time = new Date(this.dataView.xMax - timeRatio * currentTimeSpan);
      const y = height - bottomPadding - timeRatio * chartHeight;

      // 绘制刻度线
      ctx.beginPath();
      ctx.moveTo(padding - 5, y);
      ctx.lineTo(padding, y);
      ctx.stroke();

      // 绘制时间标签（只显示日期）
      const timeLabel = time.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
      ctx.fillText(timeLabel, padding - 10, y + 4);
    }
  }

  // 计算经度刻度间隔
  private calculateLongitudeStep_(range: number): number {
    if (range > 180) return 30;
    if (range > 90) return 15;
    if (range > 45) return 10;
    if (range > 20) return 5;
    if (range > 10) return 2;
    if (range > 5) return 1;
    if (range > 2) return 0.5;
    return 0.1;
  }

  // 绘制数据曲线（性能优化版本）
  private drawDataCurves_(ctx: CanvasRenderingContext2D, data: any[], width: number, height: number,
                         padding: number, bottomPadding: number, rightPadding: number): void {
    const chartWidth = width - padding - rightPadding;
    const chartHeight = height - padding - bottomPadding;

    // 按卫星分组数据（使用缓存）
    const satelliteData = this.groupDataBySatellite_(data);

    // 设置裁剪区域
    ctx.save();
    ctx.beginPath();
    ctx.rect(padding, padding, chartWidth, chartHeight);
    ctx.clip();

    // 预计算常用值，避免重复计算
    const yRange = this.dataView.yRange;
    const xRange = this.dataView.xRange;
    const yMin = this.dataView.yMin;
    const yMax = this.dataView.yMax;
    const xMin = this.dataView.xMin;
    const xMax = this.dataView.xMax;

    // 绘制每个卫星的数据曲线
    const satelliteArray: any[] = [];
    satelliteData.forEach((satellite: any) => {
      satelliteArray.push(satellite); // 收集卫星数据用于图例

      // 预过滤数据点，只处理在视图范围内的点
      const visiblePoints: any[] = [];
      const satelliteDataArray = satellite.data;

      for (let i = 0; i < satelliteDataArray.length; i++) {
        const record = satelliteDataArray[i];
        const timeStamp = new Date(record.time).getTime();

        // 检查时间和经度是否在当前视图范围内
        if (record.longitude >= yMin && record.longitude <= yMax &&
            timeStamp >= xMin && timeStamp <= xMax) {

          // 预计算坐标
          const lonRatio = (record.longitude - yMin) / yRange;
          const timeRatio = (timeStamp - xMin) / xRange;
          const x = padding + lonRatio * chartWidth;
          const y = height - bottomPadding - timeRatio * chartHeight;

          visiblePoints.push({ x, y, record });
        }
      }

      // 只有在有可见点时才绘制
      if (visiblePoints.length > 0) {
        ctx.strokeStyle = satellite.color;
        ctx.fillStyle = satellite.color;
        ctx.lineWidth = 2;
        ctx.globalAlpha = 0.8;

        ctx.beginPath();

        // 绘制线条
        ctx.moveTo(visiblePoints[0].x, visiblePoints[0].y);
        for (let i = 1; i < visiblePoints.length; i++) {
          ctx.lineTo(visiblePoints[i].x, visiblePoints[i].y);
        }
        ctx.stroke();

        // 批量绘制数据点
        for (let i = 0; i < visiblePoints.length; i++) {
          const point = visiblePoints[i];
          ctx.fillRect(point.x - 1, point.y - 1, 2, 2);
        }
      }
    });

    ctx.restore(); // 结束裁剪

    // 绘制图例（卫星名字和颜色）
    this.drawLegend_(ctx, satelliteArray, width);
  }

  // 绘制卫星名字在对应线条上方（倾斜显示）
  private drawLegend_(ctx: CanvasRenderingContext2D, satellites: any[], canvasWidth: number): void {
    ctx.save();
    ctx.font = '12px Arial';
    ctx.shadowColor = 'rgba(0,0,0,0.7)';
    ctx.shadowBlur = 2;
    ctx.textAlign = 'left';

    const padding = 80;
    const rightPadding = 40;
    const chartWidth = canvasWidth - padding - rightPadding;

    satellites.forEach((satellite, index) => {
      if (satellite.data && satellite.data.length > 0) {
        // 找到卫星数据的中间位置作为标签位置
        const midIndex = Math.floor(satellite.data.length / 2);
        const midRecord = satellite.data[midIndex];

        // 检查经度是否在当前视图范围内
        if (midRecord.longitude >= this.dataView.yMin && midRecord.longitude <= this.dataView.yMax) {
          // 计算标签位置
          const lonRatio = (midRecord.longitude - this.dataView.yMin) / this.dataView.yRange;
          const x = padding + lonRatio * chartWidth;

          // 标签位置在线条上方，根据卫星索引错开
          const y = padding + 20 + index * 25; // 自上而下排列，每个卫星间隔25px

          // 保存当前状态
          ctx.save();

          // 移动到标签位置并旋转
          ctx.translate(x, y);
          ctx.rotate(-Math.PI / 6); // 倾斜30度（-30度）

          // 绘制卫星名字，避免重复显示NORAD编号
          let satelliteName = satellite.object_name || `卫星${satellite.norad_id}`;

          // 检查卫星名是否已经包含NORAD编号，如果包含则只使用卫星名部分
          const noradStr = satellite.norad_id.toString();
          if (satelliteName.includes(noradStr) && satelliteName.includes('(') && satelliteName.includes(')')) {
            // 如果卫星名已经包含NORAD编号格式，提取卫星名部分
            const match = satelliteName.match(/^(.+?)\s*\(/);
            if (match) {
              satelliteName = match[1].trim();
            }
          }

          const satelliteText = `${satelliteName} (${satellite.norad_id})`;

          ctx.fillStyle = satellite.color;
          ctx.fillText(satelliteText, 0, 0);

          // 恢复状态
          ctx.restore();
        }
      }
    });

    ctx.restore();
  }

  // 按卫星分组数据（性能优化版本）
  private groupDataBySatellite_(data: any[]): Map<number, any> {
    // 生成数据哈希用于缓存
    const dataHash = this.generateDataHash_(data);

    // 如果数据没有变化，返回缓存的结果
    if (this.lastDataHash === dataHash && this.dataProcessingCache.has('satelliteMap')) {
      return this.dataProcessingCache.get('satelliteMap');
    }

    const satelliteMap = new Map();

    // 批量处理数据，减少函数调用开销
    for (let i = 0; i < data.length; i++) {
      const record = data[i];
      const noradId = record.norad_id;

      if (!satelliteMap.has(noradId)) {
        satelliteMap.set(noradId, {
          norad_id: noradId,
          object_name: record.object_name,
          data: [],
          color: this.generateSatelliteColor_(noradId),
          highlighted: false
        });
      }
      satelliteMap.get(noradId).data.push(record);
    }

    // 批量排序，使用更高效的排序
    satelliteMap.forEach(satellite => {
      satellite.data.sort((a: any, b: any) => new Date(a.time).getTime() - new Date(b.time).getTime());
    });

    // 缓存结果
    this.dataProcessingCache.set('satelliteMap', satelliteMap);
    this.lastDataHash = dataHash;

    return satelliteMap;
  }

  // 生成数据哈希用于缓存判断
  private generateDataHash_(data: any[]): string {
    if (data.length === 0) return '';
    // 使用数据长度和第一个、最后一个记录的时间戳作为简单哈希
    const first = data[0];
    const last = data[data.length - 1];
    return `${data.length}-${first?.time}-${last?.time}`;
  }

  // 生成卫星颜色
  private generateSatelliteColor_(noradId: number): string {
    const colors = [
      '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57',
      '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43',
      '#10ac84', '#ee5a24', '#0984e3', '#6c5ce7', '#a29bfe'
    ];
    return colors[noradId % colors.length];
  }

  // 计算时间范围
  private calculateTimeRange_(data: any[]): { min: Date, max: Date } {
    const times = data.map(d => new Date(d.time));
    return {
      min: new Date(Math.min(...times.map(t => t.getTime()))),
      max: new Date(Math.max(...times.map(t => t.getTime())))
    };
  }

  // 计算经度范围（根据实际数据自适应）
  private calculateLongitudeRange_(data: any[]): { min: number, max: number } {
    const longitudes = data.map(d => d.longitude);
    const min = Math.min(...longitudes);
    const max = Math.max(...longitudes);

    // 添加一些边距
    const range = max - min;
    const margin = Math.max(5, range * 0.05); // 至少5度边距，或者5%的范围

    return {
      min: Math.max(-180, min - margin),
      max: Math.min(180, max + margin)
    };
  }





  // 绑定画布事件（复制自历史轨道数据）
  private bindCanvasEvents_(canvas: HTMLCanvasElement): void {
    const self = this;

    // 移除之前的事件监听器
    if (this._onWheelBound) {
      canvas.removeEventListener('wheel', this._onWheelBound, true);
    }

    // 缩放
    this._onWheelBound = (e: WheelEvent) => this._onWheel(e, canvas);
    canvas.addEventListener('wheel', this._onWheelBound, true);

    // 拖拽平移数据区间
    let isDragging = false;
    let lastMouseX = 0;
    let lastMouseY = 0;

    canvas.addEventListener('mousedown', function (e: MouseEvent) {
      if (e.button === 0) { // 左键
        // 检查是否点击了曲线
        const clickedSatellite = self.checkCurveClick_(e, canvas);
        if (clickedSatellite) {
          // 点击了曲线，选中卫星
          self.selectedSatelliteName = clickedSatellite;
          if (self.lastQueryData) {
            self.displayChart_(self.lastQueryData); // 重绘以显示选中状态
          }
        } else {
          // 没有点击曲线，开始拖拽
          isDragging = true;
          lastMouseX = e.clientX;
          lastMouseY = e.clientY;
          canvas.style.cursor = 'grabbing';
        }
      }
    });

    canvas.addEventListener('mousemove', function (e: MouseEvent) {
      if (isDragging) {
        const deltaX = e.clientX - lastMouseX;
        const deltaY = e.clientY - lastMouseY;

        // 计算像素到数据区间的转换比例
        const padding = 80;
        const bottomPadding = 60;
        const rightPadding = 40;
        const dpr = window.devicePixelRatio || 1;
        const chartWidth = (canvas.width / dpr) - padding - rightPadding;
        const chartHeight = (canvas.height / dpr) - padding - bottomPadding;

        // 横坐标=经度，纵坐标=时间
        const lonDataPerPixel = self.dataView.yRange / chartWidth * 0.7;
        const timeDataPerPixel = self.dataView.xRange / chartHeight * 0.7;
        const lonDataStep = deltaX * lonDataPerPixel;
        const timeDataStep = deltaY * timeDataPerPixel;

        // 经度拖拽：左右移动对应经度变化
        self.dataView.yMin -= lonDataStep;
        self.dataView.yMax -= lonDataStep;
        self.dataView.yRange = self.dataView.yMax - self.dataView.yMin;

        // 时间拖拽：上下移动对应时间变化（注意：向上拖拽应该显示更早的时间）
        self.dataView.xMin -= timeDataStep;
        self.dataView.xMax -= timeDataStep;
        self.dataView.xRange = self.dataView.xMax - self.dataView.xMin;

        lastMouseX = e.clientX;
        lastMouseY = e.clientY;

        // 使用防抖优化拖拽性能
        if (self.dragUpdateTimer) {
          clearTimeout(self.dragUpdateTimer);
        }
        self.dragUpdateTimer = window.setTimeout(() => {
          if (self.lastQueryData) {
            self.displayChart_(self.lastQueryData);
          }
        }, 16); // 约60fps的更新频率
      } else {
        // 当不在拖拽时，显示鼠标悬停信息
        self.handleMouseHover_(e, canvas);
      }
    });

    canvas.addEventListener('mouseup', function (e: MouseEvent) {
      if (e.button === 0) {
        isDragging = false;
        canvas.style.cursor = 'default';
      }
    });
  }

  // 缩放功能（修复版本）
  private _onWheel(e: WheelEvent, canvas: HTMLCanvasElement) {
    e.preventDefault();
    const minScale = 0.01, maxScale = 2.0; // 增加最大缩放范围
    const isZoomIn = e.deltaY < 0;

    // 更新缩放比例
    if (isZoomIn) this.scale = Math.max(minScale, this.scale * 0.95);
    else this.scale = Math.min(maxScale, this.scale / 0.95);

    // 检查是否需要重置到原始状态
    if (this.scale >= 0.99 && !isZoomIn) {
      this.resetToOriginalView_();
      return;
    }
    const rect = canvas.getBoundingClientRect();
    const mouseX = (e.clientX - rect.left) / (rect.width) * (canvas.width / (window.devicePixelRatio || 1));
    const mouseY = (e.clientY - rect.top) / (rect.height) * (canvas.height / (window.devicePixelRatio || 1));
    const padding = 80;
    const bottomPadding = 60;
    const rightPadding = 40;
    const chartWidth = (canvas.width / (window.devicePixelRatio || 1)) - padding - rightPadding;
    const chartHeight = (canvas.height / (window.devicePixelRatio || 1)) - padding - bottomPadding;

    // 计算鼠标位置对应的数据坐标
    // 横坐标比例（经度）
    const lonRatio = (mouseX - padding) / chartWidth;
    const lonData = this.dataView.yMin + lonRatio * this.dataView.yRange;

    // 纵坐标比例（时间，注意方向：底部=最新时间）
    const timeRatio = 1 - (mouseY - padding) / chartHeight;
    const timeData = this.dataView.xMin + timeRatio * this.dataView.xRange;

    // 缩放灵敏度
    const scaleStep = e.deltaY < 0 ? 0.7 : 1.3;

    // 缩放经度范围（横坐标，dataView.y系列）
    let newLonRange = this.dataView.yRange * scaleStep;
    let newLonMin = lonData - lonRatio * newLonRange;
    let newLonMax = lonData + (1 - lonRatio) * newLonRange;
    if (newLonRange < 1e-6) newLonRange = 1e-6;

    // 缩放时间范围（纵坐标，dataView.x系列）
    let newTimeRange = this.dataView.xRange * scaleStep;
    let newTimeMin = timeData - timeRatio * newTimeRange;
    let newTimeMax = timeData + (1 - timeRatio) * newTimeRange;
    if (newTimeRange < 1e-6) newTimeRange = 1e-6;

    // 更新数据视图
    this.dataView.yMin = newLonMin;    // 经度最小值
    this.dataView.yMax = newLonMax;    // 经度最大值
    this.dataView.yRange = newLonRange; // 经度范围
    this.dataView.xMin = newTimeMin;   // 时间最小值
    this.dataView.xMax = newTimeMax;   // 时间最大值
    this.dataView.xRange = newTimeRange; // 时间范围


    // 边界检查和限制
    this.applyViewBoundaries_();

    if (this.lastQueryData) {
      this.displayChart_(this.lastQueryData);
    }
  }

  // 处理鼠标悬停显示数据信息（性能优化版本）
  private handleMouseHover_(e: MouseEvent, canvas: HTMLCanvasElement): void {
    // 节流处理，避免过于频繁的计算
    const now = Date.now();
    if (now - this.lastMouseMoveTime < this.mouseHoverThrottle) {
      return;
    }
    this.lastMouseMoveTime = now;

    if (!this.lastQueryData || this.lastQueryData.length === 0) return;

    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;
    const mouseX = (e.clientX - rect.left) / (rect.width) * (canvas.width / dpr);
    const mouseY = (e.clientY - rect.top) / (rect.height) * (canvas.height / dpr);

    const padding = 80;
    const bottomPadding = 60;
    const rightPadding = 40;
    const chartWidth = (canvas.width / dpr) - padding - rightPadding;
    const chartHeight = (canvas.height / dpr) - padding - bottomPadding;

    // 检查鼠标是否在图表区域内
    if (mouseX < padding || mouseX > padding + chartWidth ||
        mouseY < padding || mouseY > padding + chartHeight) {
      this.hideTooltip_();
      return;
    }

    // 预计算常用值
    const yRange = this.dataView.yRange;
    const xRange = this.dataView.xRange;
    const yMin = this.dataView.yMin;
    const yMax = this.dataView.yMax;
    const xMin = this.dataView.xMin;
    const xMax = this.dataView.xMax;

    // 查找最近的数据点（优化版本）
    let closestPoint: any = null;
    let minDistance = Infinity;
    const tolerance = 10; // 像素容差
    const toleranceSquared = tolerance * tolerance; // 避免开方运算

    const satelliteData = this.groupDataBySatellite_(this.lastQueryData);
    satelliteData.forEach((satellite: any) => {
      const satelliteDataArray = satellite.data;

      // 使用for循环替代forEach，性能更好
      for (let i = 0; i < satelliteDataArray.length; i++) {
        const record = satelliteDataArray[i];

        // 快速过滤：只检查在视图范围内的点
        if (record.longitude >= yMin && record.longitude <= yMax) {
          const timeStamp = new Date(record.time).getTime();
          if (timeStamp >= xMin && timeStamp <= xMax) {
            const timeRatio = (timeStamp - xMin) / xRange;
            const lonRatio = (record.longitude - yMin) / yRange;
            const x = padding + lonRatio * chartWidth;
            const y = (canvas.height / dpr) - bottomPadding - timeRatio * chartHeight;

            // 使用平方距离避免开方运算
            const distanceSquared = Math.pow(mouseX - x, 2) + Math.pow(mouseY - y, 2);
            if (distanceSquared < toleranceSquared && distanceSquared < minDistance) {
              minDistance = distanceSquared;
              // 处理卫星名，避免重复显示NORAD编号
              let satelliteName = satellite.object_name || `卫星${satellite.norad_id}`;
              const noradStr = satellite.norad_id.toString();
              if (satelliteName.includes(noradStr) && satelliteName.includes('(') && satelliteName.includes(')')) {
                // 如果卫星名已经包含NORAD编号格式，提取卫星名部分
                const match = satelliteName.match(/^(.+?)\s*\(/);
                if (match) {
                  satelliteName = match[1].trim();
                }
              }

              closestPoint = {
                ...record,
                satelliteName: satelliteName,
                color: satellite.color,
                x: x,
                y: y
              };
            }
          }
        }
      }
    });

    if (closestPoint) {
      this.showTooltip_(e, closestPoint);
    } else {
      this.hideTooltip_();
    }
  }

  // 显示数据提示框
  private showTooltip_(e: MouseEvent, dataPoint: any): void {
    let tooltip = document.getElementById('geo-longitude-tooltip');
    if (!tooltip) {
      tooltip = document.createElement('div');
      tooltip.id = 'geo-longitude-tooltip';
      tooltip.style.cssText = `
        position: fixed;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        pointer-events: none;
        z-index: 10000;
        max-width: 200px;
        border: 2px solid white;
      `;
      document.body.appendChild(tooltip);
    }

    const time = new Date(dataPoint.time);
    const timeStr = time.toLocaleString('zh-CN');

    tooltip.innerHTML = `
      <div style="color: ${dataPoint.color}; font-weight: bold;">${dataPoint.satelliteName} (${dataPoint.norad_id})</div>
      <div>经度: ${dataPoint.longitude.toFixed(2)}°</div>
      <div>时间: ${timeStr}</div>
    `;

    tooltip.style.left = (e.clientX + 10) + 'px';
    tooltip.style.top = (e.clientY - 10) + 'px';
    tooltip.style.display = 'block';
  }

  // 隐藏数据提示框
  private hideTooltip_(): void {
    const tooltip = document.getElementById('geo-longitude-tooltip');
    if (tooltip) {
      tooltip.style.display = 'none';
    }
  }

  // 重置到原始视图状态
  private resetToOriginalView_(): void {
    if (!this.lastQueryData || this.lastQueryData.length === 0) return;

    // 重新计算原始数据范围
    const timeRange = this.calculateTimeRange_(this.lastQueryData);
    const longitudeRange = this.calculateLongitudeRange_(this.lastQueryData);

    // 重置数据视图到原始状态
    this.globalXMin = timeRange.min.getTime();
    this.globalXMax = timeRange.max.getTime();
    this.dataView.xMin = timeRange.min.getTime();
    this.dataView.xMax = timeRange.max.getTime();
    this.dataView.xRange = timeRange.max.getTime() - timeRange.min.getTime();

    this.dataView.yMin = longitudeRange.min;
    this.dataView.yMax = longitudeRange.max;
    this.dataView.yRange = longitudeRange.max - longitudeRange.min;

    // 重置缩放比例
    this.scale = 1;

    // 重新绘制图表
    this.displayChart_(this.lastQueryData);
  }

  // 应用视图边界限制
  private applyViewBoundaries_(): void {
    // 时间轴边界检查
    if (this.dataView.xMin < this.globalXMin) {
      this.dataView.xMin = this.globalXMin;
      this.dataView.xMax = this.dataView.xMin + this.dataView.xRange;
    }
    if (this.dataView.xMax > this.globalXMax) {
      this.dataView.xMax = this.globalXMax;
      this.dataView.xMin = this.dataView.xMax - this.dataView.xRange;
    }
    if (this.dataView.xRange > this.globalXMax - this.globalXMin) {
      this.dataView.xRange = this.globalXMax - this.globalXMin;
      this.dataView.xMin = this.globalXMin;
      this.dataView.xMax = this.globalXMax;
    }

    // 经度轴边界检查（防止过度缩放）
    const maxLonRange = 360; // 最大经度范围
    const minLonRange = 0.1; // 最小经度范围

    if (this.dataView.yRange > maxLonRange) {
      const center = (this.dataView.yMin + this.dataView.yMax) / 2;
      this.dataView.yRange = maxLonRange;
      this.dataView.yMin = center - maxLonRange / 2;
      this.dataView.yMax = center + maxLonRange / 2;
    }

    if (this.dataView.yRange < minLonRange) {
      const center = (this.dataView.yMin + this.dataView.yMax) / 2;
      this.dataView.yRange = minLonRange;
      this.dataView.yMin = center - minLonRange / 2;
      this.dataView.yMax = center + minLonRange / 2;
    }

    // 经度范围限制在合理范围内
    if (this.dataView.yMin < -180) {
      const offset = -180 - this.dataView.yMin;
      this.dataView.yMin = -180;
      this.dataView.yMax += offset;
    }
    if (this.dataView.yMax > 180) {
      const offset = this.dataView.yMax - 180;
      this.dataView.yMax = 180;
      this.dataView.yMin -= offset;
    }
  }

  // 解析经度范围输入
  private parseLongitudeRange_(rangeInput: string): { min: number; max: number } | null {
    if (!rangeInput || rangeInput.trim() === '' || rangeInput.trim() === '0') {
      return null; // 空值或0表示不过滤
    }

    const input = rangeInput.trim();
    let min: number, max: number;

    // 支持多种分隔符：~ - 空格
    if (input.includes('~')) {
      const parts = input.split('~').map(p => p.trim());
      if (parts.length === 2) {
        min = parseFloat(parts[0]);
        max = parseFloat(parts[1]);
      } else {
        return null;
      }
    } else if (input.includes('-')) {
      const parts = input.split('-').map(p => p.trim());
      if (parts.length === 2) {
        min = parseFloat(parts[0]);
        max = parseFloat(parts[1]);
      } else {
        return null;
      }
    } else if (input.includes(' ')) {
      const parts = input.split(/\s+/).filter(p => p.length > 0);
      if (parts.length === 2) {
        min = parseFloat(parts[0]);
        max = parseFloat(parts[1]);
      } else {
        return null;
      }
    } else {
      return null; // 单个数字不支持
    }

    // 验证数字有效性
    if (isNaN(min) || isNaN(max)) {
      return null;
    }

    // 确保min <= max
    if (min > max) {
      [min, max] = [max, min];
    }

    // 经度范围限制在-180到180之间
    min = Math.max(-180, Math.min(180, min));
    max = Math.max(-180, Math.min(180, max));

    return { min, max };
  }

  // 检查鼠标点击是否命中曲线
  private checkCurveClick_(e: MouseEvent, canvas: HTMLCanvasElement): string | null {
    if (!this.lastQueryData || this.lastQueryData.length === 0) return null;

    const rect = canvas.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    const dpr = window.devicePixelRatio || 1;
    const padding = 80;
    const bottomPadding = 60;
    const rightPadding = 40;
    const chartWidth = (canvas.width / dpr) - padding - rightPadding;
    const chartHeight = (canvas.height / dpr) - padding - bottomPadding;

    // 检查点击是否在图表区域内
    if (mouseX < padding || mouseX > padding + chartWidth ||
        mouseY < padding || mouseY > padding + chartHeight) {
      return null;
    }

    // 按卫星分组数据
    const satelliteGroups = new Map<string, any[]>();
    this.lastQueryData.forEach(point => {
      const key = `${point.norad_id}-${point.satellite_name || 'Unknown'}`;
      if (!satelliteGroups.has(key)) {
        satelliteGroups.set(key, []);
      }
      satelliteGroups.get(key)!.push(point);
    });

    const clickTolerance = 8; // 点击容差，像素

    // 检查每条曲线
    for (const [, points] of satelliteGroups) {
      const sortedPoints = points.sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime());

      for (let i = 0; i < sortedPoints.length - 1; i++) {
        const point1 = sortedPoints[i];
        const point2 = sortedPoints[i + 1];

        // 计算点在画布上的位置
        const x1 = padding + ((point1.longitude - this.dataView.yMin) / this.dataView.yRange) * chartWidth;
        const y1 = padding + ((new Date(point1.time).getTime() - this.dataView.xMin) / this.dataView.xRange) * chartHeight;
        const x2 = padding + ((point2.longitude - this.dataView.yMin) / this.dataView.yRange) * chartWidth;
        const y2 = padding + ((new Date(point2.time).getTime() - this.dataView.xMin) / this.dataView.xRange) * chartHeight;

        // 检查鼠标是否接近线段
        const distance = this.pointToLineDistance_(mouseX, mouseY, x1, y1, x2, y2);
        if (distance <= clickTolerance) {
          // 使用格式：卫星名 (NORAD编号)，避免重复显示NORAD编号
          let satelliteName = point1.satellite_name || point1.object_name || `卫星${point1.norad_id}`;

          // 检查卫星名是否已经包含NORAD编号，如果包含则只使用卫星名部分
          const noradStr = point1.norad_id.toString();
          if (satelliteName.includes(noradStr) && satelliteName.includes('(') && satelliteName.includes(')')) {
            // 如果卫星名已经包含NORAD编号格式，提取卫星名部分
            const match = satelliteName.match(/^(.+?)\s*\(/);
            if (match) {
              satelliteName = match[1].trim();
            }
          }

          return `${satelliteName} (${point1.norad_id})`;
        }
      }
    }

    return null;
  }

  // 计算点到线段的距离
  private pointToLineDistance_(px: number, py: number, x1: number, y1: number, x2: number, y2: number): number {
    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) return Math.sqrt(A * A + B * B);

    let param = dot / lenSq;
    param = Math.max(0, Math.min(1, param));

    const xx = x1 + param * C;
    const yy = y1 + param * D;

    const dx = px - xx;
    const dy = py - yy;
    return Math.sqrt(dx * dx + dy * dy);
  }







  // 绘制统计信息（完全模仿历史轨道数据页面）
  private drawStatistics_(ctx: CanvasRenderingContext2D, data: any[], _canvasWidth: number, _canvasHeight: number, padding: number, _rightPadding: number): void {
    if (!data || data.length === 0) return;

    ctx.save();
    ctx.font = '13px Arial'; // 使用与历史轨道数据页面相同的字体
    ctx.shadowColor = 'rgba(0,0,0,0.7)';
    ctx.shadowBlur = 2;
    ctx.textAlign = 'left';

    // 计算统计信息
    const longitudes = data.map(d => d.longitude).filter(lon => typeof lon === 'number' && !isNaN(lon));
    if (longitudes.length === 0) {
      ctx.restore();
      return;
    }

    const count = longitudes.length;
    const max = Math.max(...longitudes);
    const min = Math.min(...longitudes);
    const mean = longitudes.reduce((sum, val) => sum + val, 0) / count;
    const range = max - min;

    const infoY = padding / 2;
    let infoX = padding - 29; // 左移约5mm，与历史轨道数据页面一致

    // 如果有选中的卫星，显示卫星信息
    if (this.selectedSatelliteName) {
      // 先画蓝色卫星名（使用蓝色，与历史轨道数据页面一致）
      ctx.fillStyle = '#4FC3F7'; // 蓝色
      ctx.fillText(this.selectedSatelliteName, infoX, infoY);

      // 计算卫星名宽度，用于后续文字定位
      const satelliteNameWidth = ctx.measureText(this.selectedSatelliteName).width;
      infoX += satelliteNameWidth;
    }

    // 再画白色统计信息
    const statsText = `经度 数据点: ${count}    最大值: ${max.toFixed(2)}    最小值: ${min.toFixed(2)}    平均值: ${mean.toFixed(2)}    峰值差: `;
    ctx.fillStyle = '#fff';
    ctx.fillText(statsText, infoX, infoY);

    // 画橘黄色峰值差
    const statsWidth = ctx.measureText(statsText).width;
    ctx.fillStyle = '#FFA500'; // 橘黄色
    ctx.fillText(`${range.toFixed(2)}`, infoX + statsWidth, infoY);

    ctx.restore();
  }



  // 调试输入框样式方法已移除

  // 强制重置输入框样式，防止自动填充变色
  private forceResetInputStyles_(): void {
    const inputIds = ['geo-longitude-norad-id', 'geo-longitude-start-date', 'geo-longitude-end-date', 'geo-longitude-range'];

    inputIds.forEach(id => {
      const input = getEl(id) as HTMLInputElement;
      if (input) {
      }
    });

    // 🔥 删除所有JavaScript样式设置，完全依赖CSS
    // 历史轨道页面证明了纯CSS方案是可行的，不需要JavaScript干预

    // 🔥 移除JavaScript样式设置，完全依赖CSS规则
    // 历史轨道页面证明了纯CSS方案是可行的
  }

  // 初始化ECharts滑动控件
  private initializeEChartsSlider_(data: any[]): void {
    if (!data || data.length === 0) return;

    const container = document.getElementById('geo-longitude-echarts-container');
    if (!container) return;

    // 显示ECharts容器
    container.style.display = 'block';

    // 计算经度范围
    const longitudes = data.map(d => d.longitude);
    const minLon = Math.min(...longitudes);
    const maxLon = Math.max(...longitudes);

    // 如果ECharts实例已存在，先销毁
    if (this.echartsSlider) {
      this.echartsSlider.dispose();
    }

    // 初始化ECharts实例
    this.echartsSlider = echarts.init(container);

    // 配置ECharts选项
    const option = {
      backgroundColor: 'transparent',
      grid: {
        left: 80,
        right: 40,
        top: 10,
        bottom: 50,
        height: 20
      },
      xAxis: {
        type: 'value',
        min: -180,
        max: 180,
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'value',
        show: false
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: Math.max(0, Math.min(100, ((minLon + 180) / 360) * 100)),
          end: Math.max(0, Math.min(100, ((maxLon + 180) / 360) * 100)),
          height: 20,
          bottom: 10,
          left: 80,
          right: 40,
          handleStyle: {
            color: '#2196F3',
            borderColor: '#2196F3'
          },
          textStyle: {
            color: '#fff'
          },
          borderColor: '#444',
          fillerColor: 'rgba(33, 150, 243, 0.2)',
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
          showDetail: false,
          showDataShadow: false
        }
      ],
      series: [{
        type: 'line',
        data: [[-180, 0], [180, 0]], // 添加一条简单的线来激活dataZoom
        symbolSize: 0,
        lineStyle: {
          opacity: 0
        }
      }]
    };

    this.echartsSlider.setOption(option);

    // 确保ECharts容器尺寸正确
    setTimeout(() => {
      if (this.echartsSlider) {
        this.echartsSlider.resize();
      }
    }, 100);

    // 监听滑动事件
    this.echartsSlider.on('dataZoom', (params: any) => {
      // ECharts的dataZoom事件参数结构可能不同，尝试多种方式获取参数
      let start: number, end: number;

      if (params.batch && params.batch[0]) {
        // 批量操作模式
        start = params.batch[0].start;
        end = params.batch[0].end;
      } else if (params.start !== undefined && params.end !== undefined) {
        // 直接参数模式
        start = params.start;
        end = params.end;
      } else {
        return;
      }

      // 将百分比转换为经度值
      const minLonFilter = (start / 100) * 360 - 180;
      const maxLonFilter = (end / 100) * 360 - 180;

      // 应用经度过滤
      this.applyEChartsLongitudeFilter_(minLonFilter, maxLonFilter);
    });
  }

  // 应用ECharts滑动控件的经度过滤
  private applyEChartsLongitudeFilter_(minLon: number, maxLon: number): void {
    if (!this.lastQueryData) return;

    // 过滤数据
    const filteredData = this.lastQueryData.filter(record =>
      record.longitude >= minLon && record.longitude <= maxLon
    );

    if (filteredData.length > 0) {
      // 直接重绘图表，不重新初始化滑动控件
      this.displayChartOnly_(filteredData);
    } else {
      this.clearChart_('所选经度范围内无数据');
    }
  }

  // 只重绘图表，不重新初始化滑动控件
  private displayChartOnly_(data: any[]): void {
    // 设置过滤标志，防止重新初始化滑动控件
    this.isFiltering = true;

    // 调用原始的displayChart_方法
    this.displayChart_(data);

    // 重置过滤标志
    this.isFiltering = false;
  }

}

