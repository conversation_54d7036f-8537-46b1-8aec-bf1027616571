# Elasticsearch 配置说明

## 安全配置方式

为了避免在代码中硬编码敏感信息，请使用以下方式配置ES连接：

### 方式1：环境变量（推荐）

1. 复制 `env.example` 为 `.env` 文件：
```bash
cp env.example .env
```

2. 编辑 `.env` 文件，填入实际的ES连接信息：
```bash
ES_URL=http://your-es-server:9200
ES_USERNAME=your_username
ES_PASSWORD=your_password
ES_INDEX=your_index_name
```

3. 确保 `.env` 文件已添加到 `.gitignore` 中，避免提交到版本控制。

### 方式2：配置文件

编辑 `config.json` 文件（仅包含非敏感信息）：
```json
{
  "elasticsearch": {
    "url": "http://your-es-server:9200",
    "index": "your_index_name"
  }
}
```

敏感信息（用户名、密码）仍需要通过环境变量设置。

## 配置优先级

1. **环境变量**：`ES_USERNAME`, `ES_PASSWORD`, `ES_URL`, `ES_INDEX`
2. **config.json**：非敏感配置项
3. **默认值**：仅包含基本连接信息

## 安全建议

- ✅ 使用环境变量存储敏感信息
- ✅ 将 `.env` 文件添加到 `.gitignore`
- ✅ 定期更换ES密码
- ❌ 不要在代码中硬编码密码
- ❌ 不要将包含密码的配置文件提交到版本控制

## 验证配置

启动应用后，查看控制台输出：
- "Using ES config from environment variables" - 成功从环境变量读取
- "Loaded ES config from config.json" - 成功从配置文件读取
- "ES credentials not found" - 需要设置认证信息 