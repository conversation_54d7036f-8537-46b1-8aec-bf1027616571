/*
 * This file is auto-generated from the translation JSON file
 * Do not edit manually
 */
/* eslint-disable guard-for-in */
/* eslint-disable @typescript-eslint/no-explicit-any */
import i18next from 'i18next';

export const Keys = [
  'time.days.0',
  'time.days.1',
  'time.days.2',
  'time.days.3',
  'time.days.4',
  'time.days.5',
  'time.days.6',
  'time.days-short.0',
  'time.days-short.1',
  'time.days-short.2',
  'time.days-short.3',
  'time.days-short.4',
  'time.days-short.5',
  'time.days-short.6',
  'time.months.1',
  'time.months.2',
  'time.months.3',
  'time.months.4',
  'time.months.5',
  'time.months.6',
  'time.months.7',
  'time.months.8',
  'time.months.9',
  'time.months.10',
  'time.months.11',
  'time.months.12',
  'time.calendar.time',
  'time.calendar.hour',
  'time.calendar.minute',
  'time.calendar.second',
  'time.calendar.now',
  'time.calendar.done',
  'time.calendar.pause',
  'time.calendar.propagation',
  'errorMsgs.catalogNotFullyInitialized',
  'errorMsgs.sensorGroupsApiEmpty',
  'errorMsgs.SelectSensorFirst',
  'errorMsgs.SelectSatelliteFirst',
  'errorMsgs.SelectSecondarySatellite',
  'errorMsgs.SatelliteNotDetailedSatellite',
  'errorMsgs.SensorNotFound',
  'errorMsgs.Scene.disablingGodrays',
  'errorMsgs.Scene.disablingAurora',
  'errorMsgs.Scene.disablingAtmosphere',
  'errorMsgs.Scene.disablingMoon',
  'errorMsgs.Scene.disablingMilkyWay',
  'errorMsgs.Scene.disablingSun',
  'errorMsgs.Breakup.SatelliteNotFound',
  'errorMsgs.Breakup.CannotCreateBreakupForNonCircularOrbits',
  'errorMsgs.Breakup.CannotCalcDirectionOfSatellite',
  'errorMsgs.Breakup.ErrorCreatingBreakup',
  'errorMsgs.Breakup.InvalidStartNum',
  'errorMsgs.Breakup.BreakupGeneratorFailed',
  'errorMsgs.Collisions.noCollisionsData',
  'errorMsgs.Collisions.errorProcessingCollisions',
  'errorMsgs.EditSat.errorReadingFile',
  'errorMsgs.EditSat.satelliteNotFound',
  'errorMsgs.CreateSat.errorCreatingSat',
  'errorMsgs.Reports.popupBlocker',
  'errorMsgs.SensorManager.errorUpdatingUi',
  'hoverManager.launched',
  'hoverManager.launchedPlanned',
  'hoverManager.launchedUnknown',
  'loadingScreenMsgs.math',
  'loadingScreenMsgs.science',
  'loadingScreenMsgs.science2',
  'loadingScreenMsgs.dots',
  'loadingScreenMsgs.satIntel',
  'loadingScreenMsgs.painting',
  'loadingScreenMsgs.coloring',
  'loadingScreenMsgs.elsets',
  'loadingScreenMsgs.models',
  'loadingScreenMsgs.cunningPlan',
  'loadingScreenMsgs.copyrightNotice',
  'loadingScreenMsgs.copyrightNoticeMobile',
  'splashScreens.1',
  'splashScreens.2',
  'splashScreens.3',
  'splashScreens.4',
  'splashScreens.5',
  'splashScreens.6',
  'splashScreens.7',
  'splashScreens.8',
  'splashScreens.9',
  'splashScreens.10',
  'splashScreens.11',
  'splashScreens.12',
  'splashScreens.13',
  'splashScreens.14',
  'splashScreens.15',
  'splashScreens.16',
  'splashScreens.17',
  'splashScreens.18',
  'splashScreens.19',
  'splashScreens.20',
  'splashScreens.21',
  'splashScreens.22',
  'splashScreens.23',
  'splashScreens.24',
  'splashScreens.25',
  'splashScreens.26',
  'splashScreens.27',
  'splashScreens.28',
  'splashScreens.29',
  'plugins.SensorListPlugin.bottomIconLabel',
  'plugins.SensorListPlugin.title',
  'plugins.SensorListPlugin.helpBody',
  'plugins.SensorInfoPlugin.bottomIconLabel',
  'plugins.SensorInfoPlugin.title',
  'plugins.SensorInfoPlugin.helpBody',
  'plugins.CustomSensorPlugin.bottomIconLabel',
  'plugins.CustomSensorPlugin.title',
  'plugins.CustomSensorPlugin.helpBody',
  'plugins.LookAnglesPlugin.bottomIconLabel',
  'plugins.LookAnglesPlugin.title',
  'plugins.LookAnglesPlugin.helpBody',
  'plugins.MultiSiteLookAnglesPlugin.bottomIconLabel',
  'plugins.MultiSiteLookAnglesPlugin.title',
  'plugins.MultiSiteLookAnglesPlugin.helpBody',
  'plugins.SensorTimeline.bottomIconLabel',
  'plugins.SensorTimeline.title',
  'plugins.SensorTimeline.helpBody',
  'plugins.SatelliteTimeline.bottomIconLabel',
  'plugins.SatelliteTimeline.title',
  'plugins.SatelliteTimeline.helpBody',
  'plugins.WatchlistPlugin.bottomIconLabel',
  'plugins.WatchlistPlugin.title',
  'plugins.WatchlistPlugin.helpBody',
  'plugins.WatchlistOverlay.bottomIconLabel',
  'plugins.WatchlistOverlay.title',
  'plugins.WatchlistOverlay.helpBody',
  'plugins.ReportsPlugin.bottomIconLabel',
  'plugins.ReportsPlugin.title',
  'plugins.ReportsPlugin.helpBody',
  'plugins.PolarPlotPlugin.bottomIconLabel',
  'plugins.PolarPlotPlugin.title',
  'plugins.PolarPlotPlugin.helpBody',
  'plugins.NextLaunchesPlugin.bottomIconLabel',
  'plugins.NextLaunchesPlugin.title',
  'plugins.NextLaunchesPlugin.helpBody',
  'plugins.FindSatPlugin.bottomIconLabel',
  'plugins.FindSatPlugin.title',
  'plugins.FindSatPlugin.helpBody',
  'plugins.ShortTermFences.bottomIconLabel',
  'plugins.ShortTermFences.title',
  'plugins.ShortTermFences.helpBody',
  'plugins.Collisions.bottomIconLabel',
  'plugins.Collisions.title',
  'plugins.Collisions.helpBody',
  'plugins.TrackingImpactPredict.bottomIconLabel',
  'plugins.TrackingImpactPredict.title',
  'plugins.TrackingImpactPredict.helpBody',
  'plugins.Breakup.bottomIconLabel',
  'plugins.Breakup.title',
  'plugins.Breakup.helpBody',
  'plugins.DebrisScreening.bottomIconLabel',
  'plugins.DebrisScreening.title',
  'plugins.DebrisScreening.helpBody',
  'plugins.TransponderChannelData.bottomIconLabel',
  'plugins.TransponderChannelData.title',
  'plugins.TransponderChannelData.helpBody',
  'plugins.EditSat.bottomIconLabel',
  'plugins.EditSat.title',
  'plugins.EditSat.helpBody',
  'plugins.NewLaunch.bottomIconLabel',
  'plugins.NewLaunch.title',
  'plugins.NewLaunch.helpBody',
  'plugins.MissilePlugin.bottomIconLabel',
  'plugins.MissilePlugin.title',
  'plugins.MissilePlugin.helpBody',
  'plugins.StereoMap.bottomIconLabel',
  'plugins.StereoMap.title',
  'plugins.StereoMap.helpBody',
  'plugins.SensorFov.bottomIconLabel',
  'plugins.SensorSurvFence.bottomIconLabel',
  'plugins.SatelliteViewPlugin.bottomIconLabel',
  'plugins.SatelliteFov.bottomIconLabel',
  'plugins.SatelliteFov.title',
  'plugins.SatelliteFov.helpBody',
  'plugins.Planetarium.bottomIconLabel',
  'plugins.NightToggle.bottomIconLabel',
  'plugins.SatConstellations.bottomIconLabel',
  'plugins.SatConstellations.title',
  'plugins.SatConstellations.helpBody',
  'plugins.CountriesMenu.bottomIconLabel',
  'plugins.CountriesMenu.title',
  'plugins.CountriesMenu.helpBody',
  'plugins.ColorMenu.bottomIconLabel',
  'plugins.ColorMenu.title',
  'plugins.ColorMenu.helpBody',
  'plugins.Screenshot.bottomIconLabel',
  'plugins.LaunchCalendar.bottomIconLabel',
  'plugins.TimeMachine.bottomIconLabel',
  'plugins.SatellitePhotos.bottomIconLabel',
  'plugins.SatellitePhotos.title',
  'plugins.SatellitePhotos.helpBody',
  'plugins.ScreenRecorder.bottomIconLabel',
  'plugins.Astronomy.bottomIconLabel',
  'plugins.Calculator.bottomIconLabel',
  'plugins.Calculator.title',
  'plugins.Calculator.helpBody',
  'plugins.AnalysisMenu.bottomIconLabel',
  'plugins.AnalysisMenu.title',
  'plugins.AnalysisMenu.helpBody',
  'plugins.SettingsMenuPlugin.bottomIconLabel',
  'plugins.SettingsMenuPlugin.title',
  'plugins.SettingsMenuPlugin.helpBody',
  'plugins.VideoDirectorPlugin.bottomIconLabel',
  'plugins.VideoDirectorPlugin.title',
  'plugins.VideoDirectorPlugin.helpBody',
  'plugins.CreateSat.bottomIconLabel',
  'plugins.CreateSat.title',
  'plugins.CreateSat.helpBody',
  'plugins.DopsPlugin.bottomIconLabel',
  'plugins.DopsPlugin.title',
  'plugins.DopsPlugin.helpBody',
  'plugins.EciPlot.bottomIconLabel',
  'plugins.EciPlot.title',
  'plugins.EciPlot.helpBody',
  'plugins.EcfPlot.bottomIconLabel',
  'plugins.EcfPlot.title',
  'plugins.EcfPlot.helpBody',
  'plugins.RicPlot.bottomIconLabel',
  'plugins.RicPlot.title',
  'plugins.RicPlot.helpBody',
  'plugins.Time2LonPlots.bottomIconLabel',
  'plugins.Time2LonPlots.title',
  'plugins.Time2LonPlots.helpBody',
  'plugins.Lat2LonPlots.bottomIconLabel',
  'plugins.Lat2LonPlots.title',
  'plugins.Lat2LonPlots.helpBody',
  'plugins.Inc2AltPlots.bottomIconLabel',
  'plugins.Inc2AltPlots.title',
  'plugins.Inc2AltPlots.helpBody',
  'plugins.Inc2LonPlots.bottomIconLabel',
  'plugins.Inc2LonPlots.title',
  'plugins.Inc2LonPlots.helpBody',
  'plugins.GraphicsMenuPlugin.bottomIconLabel',
  'plugins.GraphicsMenuPlugin.title',
  'plugins.GraphicsMenuPlugin.helpBody',
  'plugins.ProximityOps.bottomIconLabel',
  'plugins.ProximityOps.title',
  'plugins.ProximityOps.titleSecondary',
  'plugins.ProximityOps.helpBody',
  'plugins.ProximityOps.noradId',
  'plugins.ProximityOps.maxDistThreshold',
  'plugins.ProximityOps.maxRelativeVelocity',
  'plugins.ProximityOps.searchDuration',
  'plugins.ProximityOps.geoText',
  'plugins.ProximityOps.leoText',
  'plugins.ProximityOps.orbitType',
  'plugins.ProximityOps.geoAllVsAll',
  'plugins.ProximityOps.geoAllVsAllTooltip',
  'plugins.ProximityOps.comparePayloadsOnly',
  'plugins.ProximityOps.comparePayloadsOnlyTooltip',
  'plugins.ProximityOps.ignoreVimpelRso',
  'plugins.ProximityOps.ignoreVimpelRsoTooltip',
] as const;

// Type for all valid translation keys
export type TranslationKey = typeof Keys[number];

/**
 * Translates a given key into the corresponding localized string.
 * The name t7e represents t + 7 + e, where 7 is the number of letters in the word "translate".
 *
 * @param key - The translation key to be localized. This should correspond to a valid key in the translation files.
 * @param options - An optional object containing variables to interpolate into the localized string.
 * @returns The localized string corresponding to the provided key. If no localization is found, the key itself is returned.
 *
 * @remarks
 * This function is a placeholder and should be replaced with the actual `t7e` integration
 * or another localization library implementation.
 */
export function t7e(key: TranslationKey, options?: Record<string, any>): string {
  return i18next.t(key, options) as string;
}
