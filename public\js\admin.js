class AdminManager {
    constructor() {
        this.apiBaseUrl = this.getApiBaseUrl();
        this.token = localStorage.getItem('authToken');
        this.init();
    }

    getApiBaseUrl() {
        const currentHost = window.location.hostname;
        const protocol = window.location.protocol;
        const port = window.location.hostname === 'localhost' ? '3001' : (protocol === 'https:' ? '443' : '3001');
        return `${protocol}//${currentHost}:${port}/api/auth`;
    }

    async init() {
        if (!this.token) {
            window.location.href = '/login.html';
            return;
        }

        // 验证token和管理员权限
        if (!await this.verifyAdmin()) {
            alert('需要管理员权限');
            window.location.href = '/login.html';
            return;
        }

        this.showLoading(true);
        await this.loadData();
        this.showLoading(false);
        this.setupEventListeners();
    }

    async verifyAdmin() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/verify`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ token: this.token })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.user.role === 'admin') {
                    document.getElementById('currentUser').textContent = data.user.username;
                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error('Token verification failed:', error);
            return false;
        }
    }

    setupEventListeners() {
        // 修改密码表单
        const changePasswordForm = document.getElementById('changePasswordForm');
        if (changePasswordForm) {
            changePasswordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleChangePassword();
            });
        }
    }

    async loadData() {
        try {
            await Promise.all([
                this.loadUsers(),
                this.loadRegistrations(),
                this.loadLogs(),
                this.loadStats(),
                this.loadAccountInfo()
            ]);
        } catch (error) {
            console.error('Error loading data:', error);
            this.showError('加载数据失败：' + error.message);
        }
    }

    async makeAuthenticatedRequest(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        return fetch(url, { ...options, ...defaultOptions });
    }

    async loadUsers() {
        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/users`);
            const users = await response.json();
            
            const tableBody = document.getElementById('users-table');
            tableBody.innerHTML = '';
            
            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="user-id">${user.id.substring(0, 8)}...</td>
                    <td>${user.username}</td>
                    <td>${user.email || '-'}</td>
                    <td><span class="badge ${user.role}">${user.role === 'admin' ? '管理员' : '普通用户'}</span></td>
                    <td><span class="badge ${user.isActive ? 'active' : 'inactive'}">${user.isActive ? '活跃' : '禁用'}</span></td>
                    <td>${new Date(user.createdAt).toLocaleString()}</td>
                    <td>${user.lastLogin ? new Date(user.lastLogin).toLocaleString() : '-'}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn ${user.isActive ? 'danger' : 'success'}" onclick="toggleUser('${user.id}')" title="${user.isActive ? '禁用用户' : '启用用户'}">
                                ${user.isActive ? '禁用' : '启用'}
                            </button>
                            ${user.role !== 'admin' ? `
                                <button class="btn" onclick="updateUserRole('${user.id}', 'admin')" title="设为管理员">
                                    升级
                                </button>
                            ` : user.username !== 'admin' ? `
                                <button class="btn" onclick="updateUserRole('${user.id}', 'user')" title="设为普通用户">
                                    降级
                                </button>
                            ` : ''}
                            ${user.username !== 'admin' ? `
                                <button class="btn danger" onclick="deleteUser('${user.id}')" title="删除用户">
                                    删除
                                </button>
                            ` : ''}
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        } catch (error) {
            console.error('Failed to load users:', error);
            this.showError('加载用户数据失败');
        }
    }

    async loadRegistrations() {
        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/pending-registrations`);
            const registrations = await response.json();
            
            const tableBody = document.getElementById('registrations-table');
            if (!tableBody) return; // 如果表格不存在就跳过
            
            tableBody.innerHTML = '';
            
            registrations.forEach(reg => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${reg.username}</td>
                    <td>${reg.email}</td>
                    <td>${new Date(reg.requestedAt).toLocaleString()}</td>
                    <td><span class="badge ${reg.status}">${reg.status === 'pending' ? '待审批' : reg.status}</span></td>
                    <td class="action-buttons">
                        ${reg.status === 'pending' ? `
                            <button class="btn success" onclick="adminManager.approveRegistration('${reg.id}')">批准</button>
                            <button class="btn danger" onclick="adminManager.rejectRegistration('${reg.id}')">拒绝</button>
                        ` : '-'}
                    </td>
                `;
                tableBody.appendChild(row);
            });
        } catch (error) {
            console.error('Failed to load registrations:', error);
            this.showError('加载注册申请失败');
        }
    }

    async loadLogs() {
        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/login-attempts?limit=50`);
            const logs = await response.json();
            
            const tableBody = document.getElementById('logs-table');
            if (!tableBody) return; // 如果表格不存在就跳过
            
            tableBody.innerHTML = '';
            
            logs.forEach(log => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${new Date(log.timestamp).toLocaleString()}</td>
                    <td>${log.username}</td>
                    <td>${log.ip}</td>
                    <td><span class="badge ${log.success ? 'success' : 'failed'}">${log.success ? '成功' : '失败'}</span></td>
                `;
                tableBody.appendChild(row);
            });
        } catch (error) {
            console.error('Failed to load logs:', error);
            this.showError('加载登录日志失败');
        }
    }

    async loadStats() {
        try {
            const [usersResponse, registrationsResponse, logsResponse] = await Promise.all([
                this.makeAuthenticatedRequest(`${this.apiBaseUrl}/users`),
                this.makeAuthenticatedRequest(`${this.apiBaseUrl}/pending-registrations`),
                this.makeAuthenticatedRequest(`${this.apiBaseUrl}/login-attempts?limit=1000`)
            ]);

            const users = await usersResponse.json();
            const registrations = await registrationsResponse.json();
            const logs = await logsResponse.json();

            const activeUsers = users.filter(u => u.isActive).length;
            const pendingRegs = registrations.filter(r => r.status === 'pending').length;
            
            const today = new Date().toDateString();
            const todayLogins = logs.filter(log => 
                log.success && new Date(log.timestamp).toDateString() === today
            ).length;

            document.getElementById('totalUsers').textContent = users.length;
            document.getElementById('activeUsers').textContent = activeUsers;
            document.getElementById('pendingRegistrations').textContent = pendingRegs;
            document.getElementById('todayLogins').textContent = todayLogins;
        } catch (error) {
            console.error('Failed to load stats:', error);
        }
    }

    async toggleUserStatus(userId) {
        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/toggle-user/${userId}`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showSuccess('用户状态已更新');
                this.loadUsers();
                this.loadStats();
            } else {
                const data = await response.json();
                this.showError(data.error || '操作失败');
            }
        } catch (error) {
            console.error('Toggle user status failed:', error);
            this.showError('操作失败');
        }
    }

    async changeUserRole(userId, currentRole) {
        const newRole = currentRole === 'admin' ? 'user' : 'admin';
        
        if (!confirm(`确定要将用户角色更改为${newRole === 'admin' ? '管理员' : '普通用户'}吗？`)) {
            return;
        }
        
        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/update-role/${userId}`, {
                method: 'POST',
                body: JSON.stringify({ role: newRole })
            });

            if (response.ok) {
                this.showSuccess('用户角色已更新');
                this.loadUsers();
            } else {
                const data = await response.json();
                this.showError(data.error || '操作失败');
            }
        } catch (error) {
            console.error('Change user role failed:', error);
            this.showError('操作失败');
        }
    }

    async deleteUser(userId) {
        if (!confirm('确定要删除这个用户吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/users/${userId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.showSuccess('用户已删除');
                this.loadUsers();
                this.loadStats();
            } else {
                const data = await response.json();
                this.showError(data.error || '删除失败');
            }
        } catch (error) {
            console.error('Delete user failed:', error);
            this.showError('删除失败');
        }
    }

    async approveRegistration(requestId) {
        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/approve-registration/${requestId}`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showSuccess('注册申请已批准');
                this.loadRegistrations();
                this.loadUsers();
                this.loadStats();
            } else {
                const data = await response.json();
                this.showError(data.error || '操作失败');
            }
        } catch (error) {
            console.error('Approve registration failed:', error);
            this.showError('操作失败');
        }
    }

    async rejectRegistration(requestId) {
        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/reject-registration/${requestId}`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showSuccess('注册申请已拒绝');
                this.loadRegistrations();
                this.loadStats();
            } else {
                const data = await response.json();
                this.showError(data.error || '操作失败');
            }
        } catch (error) {
            console.error('Reject registration failed:', error);
            this.showError('操作失败');
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        const successDiv = document.getElementById('successMessage');
        
        if (successDiv) successDiv.style.display = 'none';
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
    }

    showSuccess(message) {
        const errorDiv = document.getElementById('errorMessage');
        const successDiv = document.getElementById('successMessage');
        
        if (errorDiv) errorDiv.style.display = 'none';
        if (successDiv) {
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 5000);
        }
    }

    showLoading(show) {
        const loadingDiv = document.getElementById('mainLoading');
        if (loadingDiv) {
            loadingDiv.style.display = show ? 'block' : 'none';
        }
    }

    // 加载账户信息
    async loadAccountInfo() {
        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/verify`, {
                method: 'POST',
                body: JSON.stringify({ token: this.token })
            });

            if (response.ok) {
                const data = await response.json();
                const user = data.user;

                document.getElementById('accountUsername').textContent = user.username || '-';
                document.getElementById('accountRole').textContent = user.role === 'admin' ? '管理员' : '普通用户';
                document.getElementById('accountEmail').textContent = user.email || '-';
                document.getElementById('accountCreated').textContent = user.createdAt ? new Date(user.createdAt).toLocaleString() : '-';
                document.getElementById('accountLastLogin').textContent = user.lastLogin ? new Date(user.lastLogin).toLocaleString() : '-';
            }
        } catch (error) {
            console.error('Failed to load account info:', error);
        }
    }

    // 处理修改密码
    async handleChangePassword() {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // 验证输入
        if (!currentPassword || !newPassword || !confirmPassword) {
            this.showError('请填写所有字段');
            return;
        }

        if (newPassword !== confirmPassword) {
            this.showError('新密码和确认密码不匹配');
            return;
        }

        if (newPassword.length < 8) {
            this.showError('新密码至少需要8位字符');
            return;
        }

        // 检查密码强度
        const hasUpper = /[A-Z]/.test(newPassword);
        const hasLower = /[a-z]/.test(newPassword);
        const hasNumber = /\d/.test(newPassword);
        const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(newPassword);

        if (!hasUpper || !hasLower || !hasNumber || !hasSpecial) {
            this.showError('新密码必须包含大小写字母、数字和特殊字符');
            return;
        }

        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/change-password`, {
                method: 'POST',
                body: JSON.stringify({
                    oldPassword: currentPassword,
                    newPassword: newPassword
                })
            });

            if (response.ok) {
                this.showSuccess('密码修改成功');
                document.getElementById('changePasswordForm').reset();
            } else {
                const errorData = await response.json();
                this.showError(errorData.error || '密码修改失败');
            }
        } catch (error) {
            console.error('Change password error:', error);
            this.showError('密码修改失败：' + error.message);
        }
    }

    // 完善注册审批功能
    async loadRegistrations() {
        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/pending-registrations`);

            if (response.ok) {
                const registrations = await response.json();
                this.displayRegistrations(registrations);

                // 更新统计
                document.getElementById('pendingRegistrations').textContent = registrations.length;
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('Failed to load registrations:', error);
            this.showError('加载注册申请失败');
        }
    }

    displayRegistrations(registrations) {
        const tableBody = document.getElementById('registrations-table');
        if (!tableBody) return;

        tableBody.innerHTML = '';

        if (registrations.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #666;">暂无待审批的注册申请</td></tr>';
            return;
        }

        registrations.forEach(reg => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${reg.username}</td>
                <td>${reg.email}</td>
                <td>${new Date(reg.createdAt).toLocaleString()}</td>
                <td><span class="badge pending">待审批</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="btn success" onclick="adminManager.approveRegistration('${reg.id}')">批准</button>
                        <button class="btn danger" onclick="adminManager.rejectRegistration('${reg.id}')">拒绝</button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);
        });
    }

    // 批准注册
    async approveRegistration(registrationId) {
        if (!confirm('确定要批准这个注册申请吗？')) return;

        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/approve-registration/${registrationId}`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showSuccess('注册申请已批准');
                await this.loadRegistrations();
                await this.loadUsers();
                await this.loadStats();
            } else {
                const errorData = await response.json();
                this.showError(errorData.error || '批准失败');
            }
        } catch (error) {
            console.error('Approve registration error:', error);
            this.showError('批准失败：' + error.message);
        }
    }

    // 拒绝注册
    async rejectRegistration(registrationId) {
        if (!confirm('确定要拒绝这个注册申请吗？')) return;

        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/reject-registration/${registrationId}`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showSuccess('注册申请已拒绝');
                await this.loadRegistrations();
            } else {
                const errorData = await response.json();
                this.showError(errorData.error || '拒绝失败');
            }
        } catch (error) {
            console.error('Reject registration error:', error);
            this.showError('拒绝失败：' + error.message);
        }
    }

    // 完善登录日志功能
    async loadLogs() {
        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/login-attempts?limit=100`);

            if (response.ok) {
                const logs = await response.json();
                this.displayLogs(logs);

                // 计算今日登录次数
                const today = new Date().toDateString();
                const todayLogins = logs.filter(log =>
                    log.success && new Date(log.timestamp).toDateString() === today
                ).length;
                document.getElementById('todayLogins').textContent = todayLogins;
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('Failed to load logs:', error);
            this.showError('加载登录日志失败');
        }
    }

    displayLogs(logs) {
        const tableBody = document.getElementById('logs-table');
        if (!tableBody) return;

        tableBody.innerHTML = '';

        if (logs.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: #666;">暂无登录记录</td></tr>';
            return;
        }

        logs.slice(0, 50).forEach(log => { // 只显示最近50条
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(log.timestamp).toLocaleString()}</td>
                <td>${log.username || '-'}</td>
                <td>${log.ip || '-'}</td>
                <td><span class="badge ${log.success ? 'success' : 'failed'}">${log.success ? '成功' : '失败'}</span></td>
            `;
            tableBody.appendChild(row);
        });
    }

    // 切换用户状态（启用/禁用）
    async toggleUser(userId) {
        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/toggle-user/${userId}`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showSuccess('用户状态已更新');
                await this.loadUsers();
                await this.loadStats();
            } else {
                const errorData = await response.json();
                this.showError(errorData.error || '操作失败');
            }
        } catch (error) {
            console.error('Toggle user error:', error);
            this.showError('操作失败：' + error.message);
        }
    }

    // 删除用户
    async deleteUser(userId) {
        if (!confirm('确定要删除这个用户吗？此操作不可撤销！')) return;

        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/users/${userId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.showSuccess('用户已删除');
                await this.loadUsers();
                await this.loadStats();
            } else {
                const errorData = await response.json();
                this.showError(errorData.error || '删除失败');
            }
        } catch (error) {
            console.error('Delete user error:', error);
            this.showError('删除失败：' + error.message);
        }
    }

    // 更新用户角色
    async updateUserRole(userId, newRole) {
        if (!confirm(`确定要将用户角色更改为 ${newRole === 'admin' ? '管理员' : '普通用户'} 吗？`)) return;

        try {
            const response = await this.makeAuthenticatedRequest(`${this.apiBaseUrl}/update-role/${userId}`, {
                method: 'POST',
                body: JSON.stringify({ role: newRole })
            });

            if (response.ok) {
                this.showSuccess('用户角色已更新');
                await this.loadUsers();
                await this.loadStats();
            } else {
                const errorData = await response.json();
                this.showError(errorData.error || '更新失败');
            }
        } catch (error) {
            console.error('Update role error:', error);
            this.showError('更新失败：' + error.message);
        }
    }
}

// 全局函数
function switchTab(tabName) {
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => tab.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));

    // 找到对应的标签页索引
    const tabNames = ['users', 'registrations', 'logs', 'settings'];
    const tabIndex = tabNames.indexOf(tabName);
    if (tabIndex !== -1 && tabs[tabIndex]) {
        tabs[tabIndex].classList.add('active');
    }

    const tabContent = document.getElementById(`${tabName}-tab`);
    if (tabContent) {
        tabContent.classList.add('active');

        // 如果切换到注册审批或日志标签页，重新加载数据
        if (tabName === 'registrations' && adminManager) {
            adminManager.loadRegistrations();
        } else if (tabName === 'logs' && adminManager) {
            adminManager.loadLogs();
        }
    }
}

function filterUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const rows = document.querySelectorAll('#users-table tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
}

function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    window.location.href = '/login.html';
}

// 全局函数 - 用户管理
function toggleUser(userId) {
    if (adminManager) {
        adminManager.toggleUser(userId);
    } else {
        console.error('AdminManager not initialized');
    }
}

function deleteUser(userId) {
    if (adminManager) {
        adminManager.deleteUser(userId);
    } else {
        console.error('AdminManager not initialized');
    }
}

function updateUserRole(userId, newRole) {
    if (adminManager) {
        adminManager.updateUserRole(userId, newRole);
    } else {
        console.error('AdminManager not initialized');
    }
}

// 全局函数 - 注册审批
function approveRegistration(registrationId) {
    if (adminManager) {
        adminManager.approveRegistration(registrationId);
    } else {
        console.error('AdminManager not initialized');
    }
}

function rejectRegistration(registrationId) {
    if (adminManager) {
        adminManager.rejectRegistration(registrationId);
    } else {
        console.error('AdminManager not initialized');
    }
}

// 初始化
let adminManager;
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing AdminManager...');
    adminManager = new AdminManager();
});
