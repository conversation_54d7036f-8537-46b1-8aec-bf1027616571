#!/usr/bin/env node

/**
 * 端口迁移脚本
 * 帮助用户将API端口从3001迁移到5001
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 API端口迁移工具');
console.log('从端口3001迁移到端口5001\n');

// 需要检查和更新的文件列表
const filesToUpdate = [
  {
    path: 'config.json',
    description: '主配置文件'
  },
  {
    path: 'src/api/config.json',
    description: 'API配置文件'
  },
  {
    path: '.env',
    description: '环境变量文件'
  },
  {
    path: '.env.local',
    description: '本地环境变量文件'
  }
];

// 检查文件并更新端口
function updatePortInFile(filePath, description) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  ${description} (${filePath}) 不存在，跳过`);
    return false;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let updated = false;
    let newContent = content;

    // 检查是否包含3001端口
    if (content.includes('3001')) {
      console.log(`🔍 在 ${description} 中发现端口3001`);
      
      // 根据文件类型进行不同的替换
      if (filePath.endsWith('.json')) {
        // JSON文件
        newContent = content
          .replace(/"port":\s*3001/g, '"port": 5001')
          .replace(/3001/g, '5001');
      } else if (filePath.includes('.env')) {
        // 环境变量文件
        newContent = content
          .replace(/PORT=3001/g, 'PORT=5001')
          .replace(/API_PORT=3001/g, 'API_PORT=5001');
      }
      
      if (newContent !== content) {
        // 创建备份
        const backupPath = `${filePath}.backup.${Date.now()}`;
        fs.writeFileSync(backupPath, content);
        console.log(`📋 创建备份: ${backupPath}`);
        
        // 写入更新后的内容
        fs.writeFileSync(filePath, newContent);
        console.log(`✅ 已更新 ${description}`);
        updated = true;
      }
    } else {
      console.log(`✅ ${description} 已经是正确的端口配置`);
    }

    return updated;
  } catch (error) {
    console.log(`❌ 更新 ${description} 时出错: ${error.message}`);
    return false;
  }
}

// 检查浏览器本地存储提醒
function checkBrowserStorage() {
  console.log('\n🌐 浏览器本地存储检查:');
  console.log('请手动检查浏览器本地存储中是否有旧的API地址:');
  console.log('1. 打开浏览器开发者工具 (F12)');
  console.log('2. 切换到 Application/应用 标签');
  console.log('3. 在左侧找到 Local Storage');
  console.log('4. 查找包含 "3001" 的键值对');
  console.log('5. 删除或更新为 "5001"');
  console.log('');
  console.log('常见的键名:');
  console.log('- apiBaseUrl');
  console.log('- serverUrl');
  console.log('- authUrl');
}

// 生成迁移报告
function generateMigrationReport(updatedFiles) {
  console.log('\n📊 迁移报告:');
  console.log('================');
  
  if (updatedFiles.length === 0) {
    console.log('✅ 所有文件都已经使用正确的端口配置');
  } else {
    console.log(`✅ 成功更新了 ${updatedFiles.length} 个文件:`);
    updatedFiles.forEach(file => {
      console.log(`   - ${file}`);
    });
  }
  
  console.log('\n🔄 下一步操作:');
  console.log('1. 重启API服务器:');
  console.log('   npm run start:api');
  console.log('');
  console.log('2. 测试新端口:');
  console.log('   npm run test:api');
  console.log('   或访问: http://localhost:5001');
  console.log('');
  console.log('3. 更新防火墙规则 (如果需要):');
  console.log('   - 开放端口5001');
  console.log('   - 关闭端口3001 (如果不再使用)');
  console.log('');
  console.log('4. 更新反向代理配置 (如果使用):');
  console.log('   - Nginx: proxy_pass http://localhost:5001');
  console.log('   - Apache: ProxyPass / http://localhost:5001/');
}

// 主函数
function main() {
  console.log(`📅 迁移时间: ${new Date().toISOString()}`);
  console.log(`📂 当前目录: ${process.cwd()}\n`);
  
  // 检查是否在项目根目录
  if (!fs.existsSync('package.json')) {
    console.log('❌ 错误: 请在项目根目录中运行此脚本');
    console.log('   cd /path/to/keeptrack.space');
    console.log('   node scripts/migrate-port.js');
    process.exit(1);
  }
  
  const updatedFiles = [];
  
  // 更新配置文件
  console.log('🔧 检查和更新配置文件...\n');
  filesToUpdate.forEach(file => {
    if (updatePortInFile(file.path, file.description)) {
      updatedFiles.push(file.path);
    }
  });
  
  // 检查浏览器存储
  checkBrowserStorage();
  
  // 生成报告
  generateMigrationReport(updatedFiles);
  
  console.log('\n🎉 端口迁移完成！');
  console.log('如果遇到问题，可以使用备份文件恢复原始配置。');
}

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('端口迁移工具 - 将API端口从3001迁移到5001');
  console.log('');
  console.log('使用方法:');
  console.log('  node scripts/migrate-port.js');
  console.log('');
  console.log('功能:');
  console.log('  - 自动更新配置文件中的端口号');
  console.log('  - 创建配置文件备份');
  console.log('  - 提供迁移后的操作指南');
  console.log('');
  console.log('注意事项:');
  console.log('  - 请在项目根目录中运行');
  console.log('  - 会自动创建备份文件');
  console.log('  - 需要手动检查浏览器本地存储');
  process.exit(0);
}

// 运行迁移
main();
