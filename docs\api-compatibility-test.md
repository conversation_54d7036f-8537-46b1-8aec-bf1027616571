# API兼容性测试指南

## 概述

新的标准化API需要与现有的前端代码完全兼容。本文档提供了测试指南来验证兼容性。

## 测试场景

### 1. 历史轨道数据查询兼容性

**测试端点**: `/api/satellite-history/orbital`

**期望数据格式**:
```json
{
  "count": 763,
  "data": [
    {
      "norad_id": 25544,
      "time": "2025-01-01T00:00:00Z",
      "satellite_name": "ISS (ZARYA)",
      "orbital_elements": {
        "arg_alt_km": 415.2,
        "inc_deg": 51.6461,
        "raan_deg": 123.4567,
        "ecc": 0.0001234,
        "arg_peri_deg": 89.1234,
        "mean_anom_deg": 270.5678,
        "orbital_period_min": 92.68,
        "sema_km": 6793.45
      }
    }
  ]
}
```

**前端兼容性检查**:
- ✅ `record.orbital_elements[parameter]` 应该能正确获取轨道参数
- ✅ 轨道高度曲线应该正常显示
- ✅ 所有轨道参数按钮应该工作正常

### 2. 历史经度数据查询兼容性

**测试端点**: `/api/satellite-history/longitude`

**期望数据格式**:
```json
{
  "count": 1250,
  "data": [
    {
      "norad_id": 39084,
      "time": "2025-01-01T00:00:00Z",
      "satellite_name": "GOES-16",
      "object_name": "GOES-16",
      "object_type": "GEO",
      "subsat_long": -75.2,
      "subsat_lat": 0.1,
      "longitude": -75.2,
      "latitude": 0.1
    }
  ]
}
```

**前端兼容性检查**:
- ✅ `record.object_type === 'GEO'` 应该正确过滤GEO卫星
- ✅ `record.subsat_long` 应该能正确获取经度值
- ✅ `record.satellite_name` 或 `record.object_name` 应该能获取卫星名称
- ✅ 经度图表应该正常显示

## 兼容性修复

### 经度数据API修复

为了保持与现有代码的兼容性，新API返回的数据包含了双重字段名：

```javascript
// 原有字段名（保持兼容）
subsat_long: record.orbital_elements?.subsat_long,
subsat_lat: record.orbital_elements?.subsat_lat,
object_type: record.orbital_elements?.object_type,

// 新字段名（向前兼容）
longitude: record.orbital_elements?.subsat_long,
latitude: record.orbital_elements?.subsat_lat
```

### 轨道数据API兼容性

轨道数据API保持了完整的`orbital_elements`对象结构，与现有代码完全兼容：

```javascript
// 现有代码能够处理这种格式
if (record.orbital_elements && typeof record.orbital_elements === 'object') {
  v = record.orbital_elements[parameter];
}
```

## 测试步骤

### 1. 测试历史轨道数据

1. 打开历史轨道数据页面
2. 输入NORAD ID（如：25544）
3. 选择日期范围
4. 点击"获取数据"
5. 验证：
   - 轨道高度曲线正常显示
   - 参数按钮切换正常工作
   - 数据值合理（轨道高度约400-450km）

### 2. 测试历史经度数据

1. 打开GEO经度历史页面
2. 选择日期范围
3. 可选：设置经度范围过滤
4. 点击"获取数据"
5. 验证：
   - GEO卫星经度分布图正常显示
   - 卫星名称正确显示
   - 经度值在合理范围内（-180到180度）

## 故障排除

### 如果轨道高度曲线不显示

1. 检查浏览器控制台是否有错误
2. 验证API返回的数据中是否包含`orbital_elements.arg_alt_km`
3. 检查参数按钮是否正确选中

### 如果经度图表不显示

1. 检查API返回的数据中是否包含`object_type: 'GEO'`
2. 验证`subsat_long`字段是否存在且为有效数值
3. 检查是否有GEO卫星数据在指定时间范围内

## API切换

如果需要回退到原有API：

### 历史轨道数据
```javascript
// 新API
const apiUrl = `${apiBaseUrl}/api/satellite-history/orbital?${params.toString()}`;

// 原有API（回退）
const apiUrl = `${apiBaseUrl}/api/es-history?${params.toString()}`;
```

### 历史经度数据
```javascript
// 新API
const apiUrl = `${apiBaseUrl}/api/satellite-history/longitude?${params.toString()}`;

// 原有API（回退）
const apiUrl = `${apiBaseUrl}/api/es-history?${params.toString()}`;
```

## 性能对比

新API的预期改进：
- ✅ 更快的查询响应（专门优化的查询）
- ✅ 更准确的数据过滤（后端处理）
- ✅ 更好的错误处理
- ✅ 标准化的响应格式

## 结论

新的标准化API在保持完全向后兼容的同时，提供了更好的性能和更清晰的数据结构。所有现有功能都应该无缝工作。
