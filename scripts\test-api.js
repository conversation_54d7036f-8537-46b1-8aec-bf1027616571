#!/usr/bin/env node

/**
 * API服务器测试脚本
 * 快速验证API服务器是否正常工作
 */

const http = require('http');

console.log('🧪 API服务器测试工具\n');

// 测试端点配置
const tests = [
  {
    name: '根路径',
    path: '/',
    method: 'GET',
    expectedStatus: 200,
    expectedContent: '太空物体模拟平台API服务'
  },
  {
    name: '健康检查',
    path: '/api/health',
    method: 'GET',
    expectedStatus: 200,
    expectedContent: 'ok'
  },
  {
    name: '配置文件',
    path: '/api/config.json',
    method: 'GET',
    expectedStatus: [200, 404], // 可能不存在
    expectedContent: null
  },
  {
    name: '认证端点（应该返回404或405）',
    path: '/api/auth',
    method: 'GET',
    expectedStatus: [404, 405],
    expectedContent: null
  },
  {
    name: '不存在的路径（404测试）',
    path: '/nonexistent',
    method: 'GET',
    expectedStatus: 404,
    expectedContent: 'not found'
  }
];

// 执行HTTP请求
function makeRequest(host, port, path, method = 'GET') {
  return new Promise((resolve) => {
    const options = {
      hostname: host,
      port: port,
      path: path,
      method: method,
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          success: true,
          statusCode: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        error: '请求超时'
      });
    });

    req.end();
  });
}

// 运行单个测试
async function runTest(test, host, port) {
  console.log(`🔍 测试: ${test.name}`);
  console.log(`   URL: http://${host}:${port}${test.path}`);
  
  const result = await makeRequest(host, port, test.path, test.method);
  
  if (!result.success) {
    console.log(`   ❌ 失败: ${result.error}\n`);
    return false;
  }
  
  // 检查状态码
  const expectedStatuses = Array.isArray(test.expectedStatus) 
    ? test.expectedStatus 
    : [test.expectedStatus];
    
  if (!expectedStatuses.includes(result.statusCode)) {
    console.log(`   ❌ 状态码错误: 期望 ${test.expectedStatus}, 实际 ${result.statusCode}`);
    console.log(`   📄 响应: ${result.data.substring(0, 200)}...\n`);
    return false;
  }
  
  // 检查内容（如果指定）
  if (test.expectedContent && !result.data.toLowerCase().includes(test.expectedContent.toLowerCase())) {
    console.log(`   ❌ 内容检查失败: 未找到 "${test.expectedContent}"`);
    console.log(`   📄 响应: ${result.data.substring(0, 200)}...\n`);
    return false;
  }
  
  console.log(`   ✅ 成功: ${result.statusCode}`);
  
  // 显示响应内容（截断）
  if (result.data) {
    const preview = result.data.length > 100 
      ? result.data.substring(0, 100) + '...' 
      : result.data;
    console.log(`   📄 响应: ${preview}`);
  }
  
  console.log('');
  return true;
}

// 主测试函数
async function runTests() {
  const host = process.argv[2] || 'localhost';
  const port = parseInt(process.argv[3] || '5001', 10);
  
  console.log(`🎯 测试目标: http://${host}:${port}`);
  console.log(`📅 测试时间: ${new Date().toISOString()}\n`);
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    const passed = await runTest(test, host, port);
    if (passed) {
      passedTests++;
    }
    
    // 测试间隔
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('📊 测试结果:');
  console.log(`   通过: ${passedTests}/${totalTests}`);
  console.log(`   失败: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！API服务器运行正常。');
    process.exit(0);
  } else {
    console.log('\n⚠️  部分测试失败，请检查API服务器配置。');
    process.exit(1);
  }
}

// 显示使用说明
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('使用方法:');
  console.log('  node scripts/test-api.js [host] [port]');
  console.log('');
  console.log('参数:');
  console.log('  host    服务器地址 (默认: localhost)');
  console.log('  port    端口号 (默认: 5001)');
  console.log('');
  console.log('示例:');
  console.log('  node scripts/test-api.js');
  console.log('  node scripts/test-api.js localhost 5001');
  console.log('  node scripts/test-api.js your-server.com 5001');
  process.exit(0);
}

// 运行测试
runTests().catch(console.error);
