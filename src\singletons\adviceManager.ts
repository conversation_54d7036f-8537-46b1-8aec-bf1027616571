/*
 * /////////////////////////////////////////////////////////////////////////////
 *
 * @Copyright 北京星地探索科技有限公司
 *
 * advice-module.js manages all recommended actions to the user in a semi-tutorial
 * manner. It works closely with ui.js
 * https://spacedefense
 *
 * All rights reserved. No part of this web site may be reproduced, published,
 * distributed, displayed, performed, copied or stored for public or private
 * use, without written permission of the author.
 *
 * No part of this code may be modified or changed or exploited in any way used
 * for derivative works, or offered for sale, or used to construct any kind of database
 * or mirrored at any other location without the express written permission of the author.
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { KeepTrackApiEvents } from '@app/interfaces';
import { keepTrackApi } from '../keepTrackApi';
import { getEl } from '../lib/get-el';
import { PersistenceManager, StorageKey } from './persistence-manager';

export class AdviceManager {
  private helpHeaderDOM: HTMLElement;
  private helpOuterDOM: HTMLElement;
  private helpTextDOM: HTMLElement;
  private isAdviceEnabled = true;
  private isAdviceOpen = false;
  private tutIconDOM: HTMLElement;

  public clearAdvice(): void {
    this.helpHeaderDOM.classList.remove('help-header-sel');
    this.helpHeaderDOM.onclick = null;
  }

  public init() {
    this.helpOuterDOM = getEl('help-outer-container');
    this.helpHeaderDOM = getEl('help-header');
    this.helpTextDOM = getEl('help-text');
    this.tutIconDOM = getEl('tutorial-icon');

    this.tutIconDOM.addEventListener('click', () => {
      console.log('帮助按钮被点击');
      const helpHandled = keepTrackApi.emit(KeepTrackApiEvents.onHelpMenuClick);
      console.log('插件处理结果:', helpHandled);

      // 如果没有任何插件处理帮助事件（主页状态），显示系统快捷键
      if (!helpHandled) {
        console.log('显示默认帮助');
        this.showDefaultHelp();
      }
    });

    // TODO: This should be registered with the keyboard class
    window.onkeydown = (e: KeyboardEvent) => {
      // If Shift + F1
      if (e.shiftKey && e.code === 'F1') {
        if (this.isAdviceOpen) {
          this.isAdviceOpen = false;
          this.helpOuterDOM.style.display = 'none';
        } else {
          const helpHandled = keepTrackApi.emit(KeepTrackApiEvents.onHelpMenuClick);
          if (!helpHandled) {
            this.showDefaultHelp();
          }
        }
      }
    };
  }

  public isEnabled(): boolean {
    return this.isAdviceEnabled;
  }

  public off() {
    PersistenceManager.getInstance().saveItem(StorageKey.IS_ADVICE_ENABLED, 'false');
    this.isAdviceEnabled = false;
    this.isAdviceOpen = false;
    this.helpOuterDOM.style.display = 'none';
    this.tutIconDOM.classList.remove('bmenu-item-selected');
  }

  public on() {
    PersistenceManager.getInstance().saveItem(StorageKey.IS_ADVICE_ENABLED, 'true');
    this.isAdviceEnabled = true;
    this.tutIconDOM.classList.add('bmenu-item-selected');
  }

  public showAdvice(header: string, text: string): void {
    console.log('showAdvice 被调用:', header);
    console.log('isAdviceEnabled:', this.isAdviceEnabled);
    console.log('helpOuterDOM:', this.helpOuterDOM);

    if (!this.isAdviceEnabled) {
      console.log('帮助功能被禁用');
      return;
    }

    this.isAdviceOpen = true;

    this.clearAdvice();

    this.helpOuterDOM.style.display = 'block';
    this.helpHeaderDOM.innerHTML = header;
    this.helpTextDOM.innerHTML = text;
    this.helpOuterDOM.addEventListener('click', () => {
      this.isAdviceOpen = false;
      this.helpOuterDOM.style.display = 'none';
    });
  }

  /**
   * 显示默认帮助内容（系统快捷键）
   */
  public showDefaultHelp(): void {
    console.log('showDefaultHelp 被调用');
    const defaultHelpTitle = '系统快捷键';
    const defaultHelpContent = `
      <div style="text-align: left; line-height: 1.6;">
        <h6 style="color: #48f3e3; margin-bottom: 10px;">🎮 相机控制</h6>
        <div style="margin-bottom: 15px;">
          <strong>W/A/S/D</strong> - 相机移动<br>
          <strong>Q/E</strong> - 相机上升/下降<br>
          <strong>方向键</strong> - 相机旋转<br>
          <strong>数字键盘 8/2/4/6</strong> - 精确旋转<br>
          <strong>\`</strong> - 重置相机旋转<br>
          <strong>Shift + 移动键</strong> - 快速移动
        </div>

        <h6 style="color: #48f3e3; margin-bottom: 10px;">⏰ 时间控制</h6>
        <div style="margin-bottom: 15px;">
          <strong>T</strong> - 重置为实时<br>
          <strong>,</strong> - 减慢时间<br>
          <strong>.</strong> - 加快时间<br>
          <strong>/</strong> - 暂停/恢复时间<br>
          <strong>}</strong> - 选择下一颗卫星<br>
          <strong>{</strong> - 选择上一颗卫星
        </div>

        <h6 style="color: #48f3e3; margin-bottom: 10px;">🖥️ 界面控制</h6>
        <div style="margin-bottom: 15px;">
          <strong>B</strong> - 切换底部菜单<br>
          <strong>L</strong> - 显示/隐藏轨道<br>
          <strong>Shift + F</strong> - 打开搜索栏<br>
          <strong>Shift + F1</strong> - 显示帮助<br>
          <strong>Shift + F2</strong> - 隐藏界面<br>
          <strong>ESC</strong> - 清空选择
        </div>

        <h6 style="color: #48f3e3; margin-bottom: 10px;">💡 提示</h6>
        <div>
          点击任意菜单图标后再点击帮助按钮，可查看该菜单的详细说明。
        </div>
      </div>
    `;

    this.showAdvice(defaultHelpTitle, defaultHelpContent);
  }
}

export const adviceManagerInstance = new AdviceManager();
