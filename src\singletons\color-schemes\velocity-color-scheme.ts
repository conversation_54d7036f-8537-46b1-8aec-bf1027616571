/* eslint-disable complexity */
import { ColorInformation, Pickable, rgbaArray } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { BaseObject, Star } from 'ootk';
import { ColorScheme } from './color-scheme';

export class VelocityColorScheme extends ColorScheme {
  colorTheme: Record<string, rgbaArray>;
  objectTypeFlags: Record<string, boolean>;
  readonly label = '速度';
  readonly id = 'VelocityColorScheme';
  static readonly id = 'VelocityColorScheme';
  isOptionInRmbMenu = false;

  static readonly uniqueObjectTypeFlags = {
    velocitySlow: true,
    velocityMed: true,
    velocityFast: true,
  };

  static readonly uniqueColorTheme = {
    velocitySlow: [1.0, 0.0, 0.0, 1.0] as rgbaArray,
    velocityMed: [1.0, 0.5, 0.0, 1.0] as rgbaArray,
    velocityFast: [1.0, 1.0, 0.0, 1.0] as rgbaArray,
    deselected: [1.0, 1.0, 1.0, 1.0] as rgbaArray,
    inFOVAlt: [1.0, 1.0, 1.0, 1.0] as rgbaArray,
    selected: [1.0, 1.0, 1.0, 1.0] as rgbaArray,
    sensor: [1.0, 1.0, 1.0, 1.0] as rgbaArray,
  };

  constructor() {
    super(VelocityColorScheme.uniqueColorTheme);
    this.objectTypeFlags = {
      ...this.objectTypeFlags, ...VelocityColorScheme.uniqueObjectTypeFlags,
    };
  }

  update(obj: BaseObject): ColorInformation {
    if (obj.isStar()) {
      return this.starColor_(obj as Star);
    }

    const checkFacility = this.checkFacility_(obj);

    if (checkFacility) {
      return checkFacility;
    }

    // Sensors
    if (obj.isSensor()) {
      return {
        color: this.colorTheme.sensor,
        pickable: Pickable.Yes,
      };
    }

    const dotsManagerInstance = keepTrackApi.getDotsManager();

    if (dotsManagerInstance.inViewData?.[obj.id] === 1) {
      if (this.objectTypeFlags.inViewAlt === false) {
        return {
          color: this.colorTheme.deselected,
          pickable: Pickable.No,
        };
      }

      return {
        color: this.colorTheme.inFOVAlt,
        pickable: Pickable.Yes,
      };

    }
    if (obj.totalVelocity > 5.5 && this.objectTypeFlags.velocityFast === false) {
      return {
        color: this.colorTheme.deselected,
        pickable: Pickable.No,
      };
    }
    if (obj.totalVelocity >= 2.5 && obj.totalVelocity <= 5.5 && this.objectTypeFlags.velocityMed === false) {
      return {
        color: this.colorTheme.deselected,
        pickable: Pickable.No,
      };
    }
    if (obj.totalVelocity < 2.5 && this.objectTypeFlags.velocitySlow === false) {
      return {
        color: this.colorTheme.deselected,
        pickable: Pickable.No,
      };
    }

    return {
      color: [1.0 - Math.min(obj.totalVelocity / 15, 1.0), Math.min(obj.totalVelocity / 15, 1.0), 0.0, 1.0],
      pickable: Pickable.Yes,
    };
  }

  updateGroup(obj: BaseObject): ColorInformation {
    // 检查是否有选中的组
    const selectedGroup = keepTrackApi.getGroupsManager().selectedGroup;
    if (!selectedGroup) {
      // 没有选中组时，使用正常的颜色方案
      return this.update(obj);
    }

    // 有选中组时，检查对象是否在组中
    if (!selectedGroup.hasObject(obj.id)) {
      // 不在组中的对象强制设为完全透明（隐藏）
      return {
        color: [0.0, 0.0, 0.0, 0.0] as rgbaArray, // 强制透明
        pickable: Pickable.No,
      };
    }

    // 在组中的对象使用正常的颜色方案
    return this.update(obj);
  }

  static readonly legendHtml = keepTrackApi.html`
  <ul id="legend-list-velocity">
    <li>
      <div class="Square-Box legend-velocityFast-box"></div>
      ~7公里/秒速度
    </li>
    <li>
      <div class="Square-Box legend-velocityMed-box"></div>
      ~4公里/秒速度
    </li>
    <li>
      <div class="Square-Box legend-velocitySlow-box"></div>
      ~1公里/秒速度
    </li>
  </ul>
  `.trim();
}
