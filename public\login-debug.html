<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录调试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .test-results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 登录调试工具</h1>
        
        <div class="status info">
            <strong>说明:</strong> 此工具帮助诊断登录连接问题，检查API服务器状态和配置
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runDiagnostics()">🚀 运行诊断</button>
            <button onclick="testLogin()">🔐 测试登录</button>
            <button onclick="clearCache()">🧹 清理缓存</button>
        </div>
        
        <div id="results" class="test-results"></div>
    </div>

    <div class="container">
        <h2>🔐 登录测试</h2>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="SpaceDefense2025!" placeholder="输入密码">
            </div>
            <button type="submit">登录测试</button>
        </form>
        <div id="loginResults"></div>
    </div>

    <script src="js/config-loader.js"></script>
    <script>
        let apiBaseUrl = '';
        
        // 添加结果到页面
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }
        
        // 运行诊断
        async function runDiagnostics() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            addResult('🔍 开始诊断...', 'info');
            
            // 1. 检查配置加载器
            if (window.configLoader) {
                addResult('✅ 配置加载器已加载', 'success');
                
                try {
                    const config = await window.configLoader.loadConfig();
                    addResult(`✅ 配置加载成功: ${JSON.stringify(config.apiServer)}`, 'success');
                    
                    apiBaseUrl = await window.configLoader.getAuthUrl();
                    addResult(`✅ API地址: ${apiBaseUrl}`, 'success');
                } catch (error) {
                    addResult(`❌ 配置加载失败: ${error.message}`, 'error');
                }
            } else {
                addResult('❌ 配置加载器未加载', 'error');
            }
            
            // 2. 检查API服务器健康状态
            if (apiBaseUrl) {
                const healthUrl = apiBaseUrl.replace('/api/auth', '/api/health');
                addResult(`🔍 检查健康状态: ${healthUrl}`, 'info');
                
                try {
                    const response = await fetch(healthUrl);
                    if (response.ok) {
                        const data = await response.text();
                        addResult(`✅ API服务器健康检查通过: ${data}`, 'success');
                    } else {
                        addResult(`❌ API服务器健康检查失败: ${response.status}`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ 无法连接到API服务器: ${error.message}`, 'error');
                }
            }
            
            // 3. 检查CORS
            addResult('🔍 检查CORS配置...', 'info');
            try {
                const response = await fetch(apiBaseUrl.replace('/api/auth', '/'), {
                    method: 'OPTIONS'
                });
                addResult(`✅ CORS检查通过: ${response.status}`, 'success');
            } catch (error) {
                addResult(`⚠️ CORS可能有问题: ${error.message}`, 'warning');
            }
            
            // 4. 检查本地存储
            const savedApiUrl = localStorage.getItem('apiBaseUrl');
            if (savedApiUrl) {
                if (savedApiUrl.includes('3001')) {
                    addResult(`⚠️ 本地存储中有旧的API地址: ${savedApiUrl}`, 'warning');
                } else {
                    addResult(`✅ 本地存储API地址: ${savedApiUrl}`, 'success');
                }
            } else {
                addResult('ℹ️ 本地存储中没有保存API地址', 'info');
            }
        }
        
        // 测试登录
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultsDiv = document.getElementById('loginResults');
            
            if (!username || !password) {
                resultsDiv.innerHTML = '<div class="status error">请填写用户名和密码</div>';
                return;
            }
            
            if (!apiBaseUrl) {
                try {
                    apiBaseUrl = await window.configLoader.getAuthUrl();
                } catch (error) {
                    resultsDiv.innerHTML = `<div class="status error">无法获取API地址: ${error.message}</div>`;
                    return;
                }
            }
            
            resultsDiv.innerHTML = '<div class="status info">正在测试登录...</div>';
            
            try {
                const loginUrl = `${apiBaseUrl}/login`;
                console.log('登录URL:', loginUrl);
                console.log('登录数据:', { username, password: '***' });
                
                const response = await fetch(loginUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                console.log('响应状态:', response.status);
                console.log('响应头:', [...response.headers.entries()]);
                
                const data = await response.text();
                console.log('响应数据:', data);
                
                let jsonData;
                try {
                    jsonData = JSON.parse(data);
                } catch (e) {
                    jsonData = { rawResponse: data };
                }
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="status success">✅ 登录成功!</div>
                        <pre>${JSON.stringify(jsonData, null, 2)}</pre>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="status error">❌ 登录失败 (${response.status})</div>
                        <pre>${JSON.stringify(jsonData, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('登录错误:', error);
                resultsDiv.innerHTML = `
                    <div class="status error">❌ 登录请求失败: ${error.message}</div>
                    <pre>错误详情: ${error.stack}</pre>
                `;
            }
        }
        
        // 清理缓存
        function clearCache() {
            localStorage.removeItem('apiBaseUrl');
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            localStorage.removeItem('failedAttempts');
            localStorage.removeItem('lockoutUntil');
            
            alert('缓存已清理');
        }
        
        // 表单提交处理
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            testLogin();
        });
        
        // 页面加载时自动运行诊断
        window.addEventListener('load', () => {
            setTimeout(runDiagnostics, 1000);
        });
    </script>
</body>
</html>
