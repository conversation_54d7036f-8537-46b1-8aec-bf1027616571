#!/usr/bin/env node

/**
 * 创建AES-256-GCM加密的ES配置文件
 * 使用方法: node create-secure-config.js
 */

const fs = require('fs');
const crypto = require('crypto');

const ALGORITHM = 'aes-256-gcm';
// 确保密钥长度为32字节
const ENCRYPTION_KEY = process.env.ES_ENCRYPTION_KEY || 'your-secret-key-32-chars-long!!';

function encryptConfig(config) {
  // 确保密钥长度为32字节
  const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
  cipher.setAAD(Buffer.from(''));
  
  let encrypted = cipher.update(JSON.stringify(config), 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return {
    iv: iv.toString('hex'),
    authTag: cipher.getAuthTag().toString('hex'),
    encrypted: encrypted
  };
}

async function createSecureConfig() {
  try {
    console.log('=== 创建AES-256-GCM加密的ES配置文件 ===\n');
    
    // 读取config.json
    if (!fs.existsSync('config.json')) {
      console.error('config.json 文件不存在！');
      process.exit(1);
    }
    
    const jsonContent = fs.readFileSync('config.json', 'utf8');
    const jsonConfig = JSON.parse(jsonContent);
    const esConfig = jsonConfig.elasticsearch;
    
    console.log('从 config.json 读取的配置:');
    console.log(`  服务器: ${esConfig.url}`);
    console.log(`  用户名: ${esConfig.username}`);
    console.log(`  密码: ${'*'.repeat(esConfig.password.length)}`);
    console.log(`  索引: ${esConfig.index}\n`);
    
    // 创建配置对象
    const config = {
      elasticsearch: {
        url: esConfig.url,
        username: esConfig.username,
        password: esConfig.password,
        index: esConfig.index
      }
    };
    
    // 使用AES-256-GCM加密
    const encryptedData = encryptConfig(config);
    
    // 保存加密文件
    fs.writeFileSync('es-config.enc', JSON.stringify(encryptedData, null, 2));
    
    console.log('✅ AES-256-GCM加密配置文件已创建: es-config.enc');
    console.log('⚠️  请确保将此文件添加到 .gitignore 中');
    console.log('⚠️  请妥善保管此文件，不要分享给他人');
    console.log('⚠️  建议设置环境变量 ES_ENCRYPTION_KEY 来增强安全性\n');
    
    // 验证文件
    console.log('=== 验证配置 ===');
    const testContent = fs.readFileSync('es-config.enc', 'utf8');
    const testData = JSON.parse(testContent);
    
    // 解密验证
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
    const iv = Buffer.from(testData.iv, 'hex');
    const authTag = Buffer.from(testData.authTag, 'hex');
    const encrypted = Buffer.from(testData.encrypted, 'hex');
    
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    decipher.setAuthTag(authTag);
    decipher.setAAD(Buffer.from(''));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    const testConfig = JSON.parse(decrypted);
    console.log('✅ 配置文件验证成功');
    console.log(`   服务器: ${testConfig.elasticsearch.url}`);
    console.log(`   索引: ${testConfig.elasticsearch.index}`);
    console.log(`   用户名: ${testConfig.elasticsearch.username}`);
    console.log(`   密码: ${'*'.repeat(testConfig.elasticsearch.password.length)}`);
    console.log(`   加密算法: AES-256-GCM`);
    
  } catch (error) {
    console.error('创建配置文件时出错:', error);
  }
}

createSecureConfig(); 