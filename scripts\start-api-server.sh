#!/bin/bash

# API服务器启动脚本
# 确保在正确的目录中启动，并设置正确的环境

echo "🚀 启动太空物体模拟平台API服务器"
echo "=================================="

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录中运行此脚本"
    exit 1
fi

# 检查必要文件
echo "📋 检查必要文件..."
required_files=(
    "src/api/server.ts"
    "src/api/auth.routes.ts"
    "src/auth/auth.service.ts"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少文件: $file"
        exit 1
    else
        echo "✅ $file"
    fi
done

# 检查Node.js版本
echo ""
echo "🔍 检查Node.js版本..."
node_version=$(node --version)
echo "Node.js版本: $node_version"

# 检查npm依赖
echo ""
echo "📦 检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "⚠️  node_modules不存在，正在安装依赖..."
    npm install
fi

# 设置环境变量
export NODE_ENV=${NODE_ENV:-production}
export PORT=${PORT:-5001}
export HOST=${HOST:-0.0.0.0}

echo ""
echo "🔧 环境配置:"
echo "  NODE_ENV: $NODE_ENV"
echo "  PORT: $PORT"
echo "  HOST: $HOST"

# 创建数据目录
echo ""
echo "📁 创建数据目录..."
mkdir -p data
mkdir -p logs

# 启动服务器
echo ""
echo "🚀 启动API服务器..."
echo "访问地址: http://localhost:$PORT"
echo "按 Ctrl+C 停止服务器"
echo ""

# 使用tsx运行TypeScript文件
npx tsx src/api/server.ts
