/* eslint-disable max-lines */
import { CommLink, Degrees, DetailedSensor, Kilometers, Milliseconds, RfSensor, SpaceObjectType, ZoomValue } from 'ootk';

export interface SensorList {
  [key: string]: DetailedSensor;
}

// TODO: Add the aperture size of telescopes as an optional parameter

export enum Operators {
  USSF = 'USSF',
  USA = 'US ARMY',
  RAF = 'RAF',
  UKSA = 'UKSA',
  RAAF = 'RAAF',
  ESA = 'ESA',
  ROC = 'ROC',
  EISCAT = 'EISCAT',
  RUSSF = 'RUS SF', // Russian Space Forces
  MIT = 'MIT',
  NOR = 'NOR',
  PLA = 'PLA',
  CAS = 'CAS',
  LEOLABS = 'LEO LABS',
  CALTECH = 'CALTECH',
  COMMERCIAL = 'COMMERCIAL',
  ITAF = 'ITAF', // Italian Air Force
  OWLNET = 'OWL-Net',
}

export const sensors = <SensorList>{
  CODSFS: new RfSensor({
    objName: 'CODSFS',
    shortName: 'COD',
    id: 0,
    name: 'Cape Cod SFS, Massachusetts',
    uiName: 'Cape Cod SFS',
    system: 'PAVE PAWS UEWR',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>41.754785,
    lon: <Degrees>-70.539151,
    alt: <Kilometers>0.060966,
    minAz: <Degrees>347,
    maxAz: <Degrees>227,
    boresightAz: [47 as Degrees, 227 as Degrees],
    minEl: <Degrees>3,
    maxEl: <Degrees>85,
    boresightEl: [20 as Degrees, 20 as Degrees],
    minRng: <Kilometers>200,
    maxRng: <Kilometers>5556,
    changeObjectInterval: <Milliseconds>1000,
    beamwidth: <Degrees>2.0, // National Research Council 1979. Radiation Intensity of the PAVE PAWS Radar System. Washington, DC: The National Academies Press.
    commLinks: [CommLink.AEHF, CommLink.WGS],
    zoom: ZoomValue.LEO,
    url: 'https://www.radartutorial.eu/19.kartei/01.oth/karte004.en.html',
    country: 'United States',
    operator: Operators.USSF,
    sensorId: 0, // For Testing Only
  }),
  BLEAFB: new RfSensor({
    objName: 'BLEAFB',
    shortName: 'BLE',
    id: 0,
    name: 'Beale AFB, California',
    uiName: 'Beale AFB',
    system: 'PAVE PAWS UEWR',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>39.136064,
    lon: <Degrees>-121.351237,
    alt: <Kilometers>0.112, // Open Street Maps
    minAz: <Degrees>126,
    maxAz: <Degrees>6,
    boresightAz: [186 as Degrees, 306 as Degrees],
    minEl: <Degrees>3,
    maxEl: <Degrees>85,
    boresightEl: [20 as Degrees, 20 as Degrees],
    minRng: <Kilometers>200,
    maxRng: <Kilometers>5556,
    changeObjectInterval: <Milliseconds>1000,
    beamwidth: <Degrees>2.0, // National Research Council 1979. Radiation Intensity of the PAVE PAWS Radar System. Washington, DC: The National Academies Press.
    commLinks: [CommLink.AEHF, CommLink.WGS],
    zoom: ZoomValue.LEO,
    country: 'United States',
    operator: Operators.USSF,
  }),
  CLRSFS: new RfSensor({
    objName: 'CLRSFS',
    shortName: 'CLR',
    id: 0,
    name: 'Clear SFS, Alaska',
    uiName: 'Clear SFS',
    system: 'PAVE PAWS UEWR',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>64.290556,
    lon: <Degrees>-149.186944,
    alt: <Kilometers>0.175, // Open Street Maps
    minAz: <Degrees>184,
    maxAz: <Degrees>64,
    boresightAz: [244 as Degrees, 4 as Degrees],
    minEl: <Degrees>3,
    maxEl: <Degrees>85,
    boresightEl: [20 as Degrees, 20 as Degrees],
    minRng: <Kilometers>200,
    maxRng: <Kilometers>5556,
    changeObjectInterval: <Milliseconds>1000,
    beamwidth: <Degrees>2.0, // National Research Council 1979. Radiation Intensity of the PAVE PAWS Radar System. Washington, DC: The National Academies Press.
    commLinks: [CommLink.AEHF, CommLink.WGS],
    zoom: ZoomValue.LEO,
    country: 'United States',
    operator: Operators.USSF,
  }),
  EGLAFB: new RfSensor({
    objName: 'EGLAFB',
    shortName: 'EGL',
    id: 0,
    name: 'Eglin AFB, Florida',
    uiName: 'Eglin AFB',
    system: 'AN/FPS-85',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>30.572411,
    lon: <Degrees>-86.214836,
    alt: <Kilometers>0.039, // Open Street Maps
    minAz: <Degrees>120,
    maxAz: <Degrees>240,
    boresightAz: [180 as Degrees],
    minEl: <Degrees>3, // Should this be 0 for "horizon"?
    // TODO: Incorporate boresight into rae2ecf calculations for inclined sensors
    maxEl: <Degrees>90, // https://en.wikipedia.org/wiki/Eglin_AFB_Site_C-6 (an elevation range from the horizon to 15° past the zenith)
    // maxEl: <Degrees>105, // https://en.wikipedia.org/wiki/Eglin_AFB_Site_C-6 (an elevation range from the horizon to 15° past the zenith)
    boresightEl: [45 as Degrees],
    minRng: <Kilometers>200,
    maxRng: <Kilometers>50000,
    changeObjectInterval: <Milliseconds>1000,
    zoom: ZoomValue.GEO,
    beamwidth: <Degrees>1.1, // Tmx 1.4 and Rcv 0.8 for Avg of 1.1
    url: 'https://www.radartutorial.eu/19.kartei/01.oth/karte002.en.html',
    country: 'United States',
    operator: Operators.USSF,
  }),
  RAFFYL: new RfSensor({
    objName: 'RAFFYL',
    shortName: 'FYL',
    id: 0,
    name: 'RAF Fylingdales, United Kingdom',
    uiName: 'RAF Fylingdales',
    system: 'BMEWS UEWR',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>54.361758,
    lon: <Degrees>-0.670051,
    alt: <Kilometers>0.26, // Open Street Maps
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>3,
    maxEl: <Degrees>85,
    minRng: <Kilometers>200,
    maxRng: <Kilometers>5556,
    changeObjectInterval: <Milliseconds>1000,
    beamwidth: <Degrees>2.0, // National Research Council 1979. Radiation Intensity of the PAVE PAWS Radar System. Washington, DC: The National Academies Press.
    commLinks: [CommLink.AEHF, CommLink.WGS],
    boresightAz: [<Degrees>0],
    boresightEl: [<Degrees>20],
    zoom: ZoomValue.LEO,
    country: 'United Kingdom',
    operator: Operators.RAF,
  }),
  CAVSFS: new RfSensor({
    objName: 'CAVSFS',
    shortName: 'CAV',
    id: 0,
    name: 'Cavalier SFS, North Dakota',
    uiName: 'Cavalier SFS',
    system: 'PARCS',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>48.724567,
    lon: <Degrees>-97.899755,
    alt: <Kilometers>0.352, // Open Street Maps
    minAz: <Degrees>298,
    maxAz: <Degrees>78,
    boresightAz: [8 as Degrees],
    minEl: <Degrees>1.9,
    maxEl: <Degrees>90,
    /*
     * TODO: incorporate boresight into rae2ecf calculations for inclined sensors
     * maxEl: <Degrees>95,
     */
    boresightEl: [25 as Degrees], // https://satelliteobservation.files.wordpress.com/2018/11/missile-warning-systems.pdf
    minRng: <Kilometers>200,
    maxRng: <Kilometers>3300, // 1,780 Nm http://www.fortwiki.com/Cavalier_Air_Force_Station
    changeObjectInterval: <Milliseconds>1000,
    beamwidth: <Degrees>1.2, // National Research Council 1979. Radiation Intensity of the PAVE PAWS Radar System. Washington, DC: The National Academies Press.
    commLinks: [CommLink.AEHF],
    zoom: ZoomValue.LEO,
    url: 'https://mostlymissiledefense.com/2012/04/12/parcs-cavalier-radar-april-12-2012/',
    country: 'United States',
    volume: true,
    operator: Operators.USSF,
  }),
  PITSB: new RfSensor({
    objName: 'PITSB',
    shortName: 'THL',
    id: 0,
    name: 'Pituffik SB, Greenland',
    uiName: 'Pituffik SB',
    system: 'BMEWS UEWR',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>76.570322,
    lon: <Degrees>-68.299211,
    alt: <Kilometers>0.392, // Open Street Maps
    minAz: <Degrees>297,
    maxAz: <Degrees>177,
    boresightAz: [357 as Degrees, 117 as Degrees],
    minEl: <Degrees>3,
    maxEl: <Degrees>85,
    boresightEl: [20 as Degrees, 20 as Degrees],
    minRng: <Kilometers>200,
    maxRng: <Kilometers>5556,
    changeObjectInterval: <Milliseconds>1000,
    beamwidth: <Degrees>2.0, // National Research Council 1979. Radiation Intensity of the PAVE PAWS Radar System. Washington, DC: The National Academies Press.
    commLinks: [CommLink.AEHF, CommLink.WGS],
    zoom: ZoomValue.LEO,
    url: 'https://www.radartutorial.eu/19.kartei/01.oth/karte004.en.html',
    country: 'United States',
    operator: Operators.USSF,
  }),
  COBRADANE: new RfSensor({
    objName: 'COBRADANE',
    shortName: 'CDN',
    id: 0,
    name: 'Eareckson Air Station, Alaska',
    uiName: 'Eareckson AFS',
    system: 'COBRA DANE',
    freqBand: 'L-Band',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>52.737,
    lon: <Degrees>174.092,
    alt: <Kilometers>0.066, // Open Street Maps
    // boresight 319az https://mostlymissiledefense.com/2012/04/12/cobra-dane-radar-april-12-2012/
    minAz: <Degrees>259, // https://www.radartutorial.eu/19.kartei/01.oth/karte003.en.html
    maxAz: <Degrees>19,
    boresightAz: [319 as Degrees],
    minEl: <Degrees>2,
    maxEl: <Degrees>30,
    boresightEl: [20 as Degrees], // https://mostlymissiledefense.com/2012/04/12/cobra-dane-radar-april-12-2012/
    minRng: <Kilometers>200,
    maxRng: <Kilometers>14000, // https://mostlymissiledefense.com/2012/04/12/cobra-dane-radar-april-12-2012/
    minAz2: <Degrees>251,
    maxAz2: <Degrees>27,
    minEl2: <Degrees>30,
    maxEl2: <Degrees>80,
    minRng2: <Kilometers>200,
    maxRng2: <Kilometers>14000,
    changeObjectInterval: <Milliseconds>1000,
    beamwidth: <Degrees>0.6, // https://www.radartutorial.eu/19.kartei/01.oth/karte003.en.html
    commLinks: [CommLink.WGS],
    zoom: ZoomValue.LEO,
    url: 'https://www.radartutorial.eu/19.kartei/01.oth/karte004.en.html',
    country: 'United States',
    volume: true,
    operator: Operators.USSF,
  }),
  KWAJALT: new DetailedSensor({
    objName: 'KWAJALT',
    shortName: 'ALT',
    id: 0,
    name: 'ALTAIR, Kwajalein Atoll',
    uiName: 'Kwajalein Atoll (ALT)',
    system: 'ALTAIR',
    freqBand: 'VHF/UHF',
    type: SpaceObjectType.MECHANICAL,
    lat: <Degrees>8.716667,
    lon: <Degrees>167.733333,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>1,
    maxEl: <Degrees>90,
    minRng: <Kilometers>200,
    maxRng: <Kilometers>200000,
    changeObjectInterval: <Milliseconds>20000,
    commLinks: [CommLink.AEHF, CommLink.WGS],
    zoom: ZoomValue.GEO,
    url: 'https://www.radartutorial.eu/19.kartei/01.oth/karte005.en.html',
    country: 'United States',
    operator: Operators.USA,
  }),
  KWAJGBRP: new DetailedSensor({
    objName: 'KWAJGBRP',
    id: 0,
    name: 'Ground-Based Radar Prototype (GBR-P), Kwajalein Atoll',
    uiName: 'Kwajalein Atoll (GBR-P)',
    system: 'GBR-P',
    freqBand: 'X-Band',
    type: SpaceObjectType.MECHANICAL,
    lat: <Degrees>8.716668,
    lon: <Degrees>167.773334,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>1,
    maxEl: <Degrees>90,
    minRng: <Kilometers>200,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,

    url: '',
    country: 'United States',
    operator: Operators.USA,
  }),
  KWAJMMW: new DetailedSensor({
    objName: 'KWAJMMW',
    id: 0,
    name: 'Millimeter Wave Radar, Kwajalein Atoll',
    uiName: 'Kwajalein Atoll (MMW)',
    system: 'Millimeter Wave Radar',
    type: SpaceObjectType.MECHANICAL,
    lat: <Degrees>8.756668,
    lon: <Degrees>167.773334,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>1,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'United States',
    operator: Operators.USA,
  }),
  KWAJALC: new DetailedSensor({
    objName: 'KWAJALC',
    id: 0,
    name: 'ALCOR Radar, Kwajalein Atoll',
    uiName: 'Kwajalein Atoll (ALCOR)',
    system: 'ALCOR',
    freqBand: 'C-Band',
    type: SpaceObjectType.MECHANICAL,
    lat: <Degrees>8.716668,
    lon: <Degrees>167.773334,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>1,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,

    url: '',
    country: 'United States',
    operator: Operators.USA,
  }),
  KWAJTDX: new DetailedSensor({
    objName: 'KWAJTDX',
    id: 0,
    name: 'TRADEX Radar, Kwajalein Atoll',
    uiName: 'Kwajalein Atoll (TDX)',
    system: 'TRADEX',
    type: SpaceObjectType.MECHANICAL,
    lat: <Degrees>8.756668,
    lon: <Degrees>167.733334,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>1,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,

    url: '',
    country: 'United States',
    operator: Operators.USA,
  }),
  KWAJSPF: new DetailedSensor({
    objName: 'KWAJSPF',
    shortName: 'SF',
    id: 0,
    name: 'Space Fence, Kwajalein Atoll',
    uiName: 'Kwajalein Atoll (SF)',
    system: 'Space Fence',
    freqBand: 'S-Band',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>8.723, // SDC7-paper1014.pdf
    lon: <Degrees>167.719,
    alt: <Kilometers>0.007,
    minAz: <Degrees>268, // TODO This needs a better system (same as LEO Labs)
    maxAz: <Degrees>272,
    minEl: <Degrees>5, // ??
    maxEl: <Degrees>175, // ??
    minRng: <Kilometers>50,
    maxRng: <Kilometers>3057.754, // https://www.spaceforce.mil/News/Article/2142648/swinging-for-the-space-fence/
    zoom: ZoomValue.LEO,
    volume: true,
    changeObjectInterval: <Milliseconds>2000,
    url: '',
    country: 'United States',
    operator: Operators.USSF,
  }),
  MITMIL: new DetailedSensor({
    objName: 'MITMIL',
    shortName: 'MIL',
    id: 0,
    name: 'Westford, Massachusetts',
    uiName: 'Westford',
    system: 'Millstone',
    type: SpaceObjectType.MECHANICAL,
    lat: <Degrees>42.6233,
    lon: <Degrees>-71.4882,
    alt: <Kilometers>0.131,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>1,
    maxEl: <Degrees>90,
    minRng: <Kilometers>200,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: 'https://mostlymissiledefense.com/2012/05/05/space-surveillance-sensors-millstone-hill-radar/',
    country: 'United States',
    operator: Operators.MIT,
  }),
  GEODDSDGC: new DetailedSensor({
    objName: 'GEODDSDGC',
    id: 0,
    name: 'Diego Garcia',
    uiName: 'Diego Garcia',
    system: 'GEODSS',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>-7.29648,
    lon: <Degrees>72.390153,
    alt: <Kilometers>0.0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>20, // https://www.osti.gov/servlets/purl/1253293
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: 'https://mostlymissiledefense.com/2012/08/20/space-surveillance-sensors-geodss-ground-based-electro-optical-deep-space-surveillance-system-august-20-2012/',
    country: 'United States',
    operator: Operators.USSF,
  }),
  GEODDSMAU: new DetailedSensor({
    objName: 'GEODDSMAU',
    id: 0,
    name: 'Maui, Hawaii',
    uiName: 'Maui',
    system: 'GEODSS',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>20.70835,
    lon: <Degrees>-156.257595,
    alt: <Kilometers>3.0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>20, // https://www.osti.gov/servlets/purl/1253293
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: 'https://mostlymissiledefense.com/2012/08/20/space-surveillance-sensors-geodss-ground-based-electro-optical-deep-space-surveillance-system-august-20-2012/',
    country: 'United States',
    operator: Operators.USSF,
  }),
  GEODDSSOC: new DetailedSensor({
    objName: 'GEODDSSOC',
    id: 0,
    name: 'Socorro, New Mexico',
    uiName: 'Socorro',
    system: 'GEODSS',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>33.817233,
    lon: <Degrees>-106.659961,
    alt: <Kilometers>1.24,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>20, // https://www.osti.gov/servlets/purl/1253293
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: 'https://mostlymissiledefense.com/2012/08/20/space-surveillance-sensors-geodss-ground-based-electro-optical-deep-space-surveillance-system-august-20-2012/',
    country: 'United States',
    operator: Operators.USSF,
  }),
  RAFASC: new DetailedSensor({
    objName: 'RAFASC',
    shortName: 'ASC',
    id: 0,
    name: 'Ascension Island, United Kingdom',
    uiName: 'RAF Ascension Island',
    system: 'FPQ-15',
    freqBand: 'C-Band',
    type: SpaceObjectType.MECHANICAL,
    lat: <Degrees>-7.969444,
    lon: <Degrees>-14.393889,
    alt: <Kilometers>0.0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>1,
    maxEl: <Degrees>90,
    minRng: <Kilometers>200,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'United States',
    operator: Operators.USSF,
  }),
  GLBII: new DetailedSensor({
    objName: 'GLBII',
    id: 0,
    name: 'Vardø, Norway',
    uiName: 'Vardø',
    system: 'Globus II',
    type: SpaceObjectType.MECHANICAL,
    lat: <Degrees>70.3671,
    lon: <Degrees>31.1271,
    alt: <Kilometers>0.0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>1,
    maxEl: <Degrees>90,
    minRng: <Kilometers>200,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Norway',
    operator: Operators.NOR,
  }),
  HOLCBAND: new DetailedSensor({
    objName: 'HOLCBAND',
    id: 0,
    name: 'Holt Naval Communication Station, Australia',
    uiName: 'Holt Naval CS (C-Band)',
    system: 'C-Band',
    freqBand: 'C-Band',
    type: SpaceObjectType.MECHANICAL,
    lat: <Degrees>-21.816195,
    lon: <Degrees>114.165637,
    alt: <Kilometers>0.0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>1,
    maxEl: <Degrees>90,
    minRng: <Kilometers>200,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Australia',
    operator: Operators.RAAF,
  }),
  HOLSST: new DetailedSensor({
    objName: 'HOLSST',
    id: 0,
    name: 'Holt Naval Communication Station, Australia',
    uiName: 'Holt Naval CS (SST)',
    system: 'SST',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>-21.816195,
    lon: <Degrees>114.165637,
    alt: <Kilometers>0.04572, // https://www.osti.gov/servlets/purl/1253293
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>20, // https://amostech.com/TechnicalPapers/2015/SSA/Graham.pdf
    maxEl: <Degrees>90,
    minRng: <Kilometers>15000,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Australia',
    operator: Operators.RAAF,
  }),
  // TPY-2 RADARS
  HARTPY: new DetailedSensor({
    objName: 'HARTPY',
    id: 0,
    name: 'Har Keren TPY-2, Israel',
    uiName: 'Har Keren',
    system: 'TPY-2',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>30.995807,
    lon: <Degrees>34.496062,
    alt: <Kilometers>0.173,
    minAz: <Degrees>5,
    maxAz: <Degrees>125,
    minEl: <Degrees>5,
    maxEl: <Degrees>90,
    /*
     * TODO: incorporate boresight into rae2ecf calculations for inclined sensors
     * maxEl: <Degrees>95,
     */
    minRng: <Kilometers>0,
    maxRng: <Kilometers>3000, // https://missilethreat.csis.org/defsys/tpy-2/
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: '',
    country: 'United States',
    operator: Operators.USA,
  }),
  QTRTPY: new DetailedSensor({
    objName: 'QTRTPY',
    id: 0,
    name: 'Al Udeid, Qatar',
    uiName: 'Al Udeid',
    system: 'TPY-2',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>25.31598,
    lon: <Degrees>51.146515,
    alt: <Kilometers>0.01,
    minAz: <Degrees>335,
    maxAz: <Degrees>95,
    minEl: <Degrees>0,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>3000, // https://missilethreat.csis.org/defsys/tpy-2/
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: '',
    country: 'United States',
    operator: Operators.USA,
  }),
  KURTPY: new DetailedSensor({
    objName: 'KURTPY',
    id: 0,
    name: 'Kürecik Radar Station, Turkey',
    uiName: 'Kürecik',
    system: 'TPY-2',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>38.349444,
    lon: <Degrees>37.793611,
    alt: <Kilometers>1.969,
    minAz: <Degrees>40,
    maxAz: <Degrees>160,
    minEl: <Degrees>0,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>3000, // https://missilethreat.csis.org/defsys/tpy-2/
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: '',
    country: 'United States',
    operator: Operators.USA,
  }),
  SHATPY: new DetailedSensor({
    objName: 'SHATPY',
    id: 0,
    name: 'Shariki Communication Site, Japan',
    uiName: 'Shariki',
    system: 'TPY-2',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>40.88809,
    lon: <Degrees>140.337698,
    alt: <Kilometers>0.01,
    minAz: <Degrees>230,
    maxAz: <Degrees>350,
    minEl: <Degrees>0,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>3000, // https://missilethreat.csis.org/defsys/tpy-2/
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: '',
    country: 'United States',
    operator: Operators.USA,
  }),
  KCSTPY: new DetailedSensor({
    objName: 'KCSTPY',
    id: 0,
    name: 'Kyogamisaki Communication Site, Japan',
    uiName: 'Kyogamisaki',
    system: 'TPY-2',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>35.766667,
    lon: <Degrees>135.195278,
    alt: <Kilometers>0.01,
    minAz: <Degrees>210,
    maxAz: <Degrees>330,
    minEl: <Degrees>0,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>3000, // https://missilethreat.csis.org/defsys/tpy-2/
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: '',
    country: 'United States',
    operator: Operators.USA,
  }),
  SBXRDR: new DetailedSensor({
    objName: 'SBXRDR',
    id: 0,
    name: 'Sea-Based X-Band Radar, Pacific Ocean',
    uiName: 'Mobile (Ocean)',
    system: 'SBX',
    freqBand: 'X-Band',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>36.5012,
    lon: <Degrees>169.6941,
    alt: <Kilometers>0.0,
    minAz: <Degrees>275,
    maxAz: <Degrees>300,
    minEl: <Degrees>0,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>4025,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: '',
    country: 'United States',
    operator: Operators.USA,
  }),
  // LEO LABS
  LEOMSR: new DetailedSensor({
    objName: 'LEOMSR',
    id: 0,
    name: 'Midland Space Radar, Texas',
    uiName: 'Midland Space Radar',
    system: 'UHF',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>31.9643,
    lon: <Degrees>-103.233245,
    alt: <Kilometers>0.855,
    minAz: <Degrees>70,
    maxAz: <Degrees>72,
    minEl: <Degrees>30,
    maxEl: <Degrees>91, // 91 to ensure visual overlap
    minRng: <Kilometers>100,
    maxRng: <Kilometers>1800,
    minAz2: <Degrees>250,
    maxAz2: <Degrees>252,
    minEl2: <Degrees>30,
    maxEl2: <Degrees>91, // 91 to ensure visual overlap
    minRng2: <Kilometers>100,
    maxRng2: <Kilometers>1800,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: 'https://platform.leolabs.space/sites/msr',
    country: 'United States',
    volume: true,
    operator: Operators.LEOLABS,
  }),

  LEOPFISR: new DetailedSensor({
    objName: 'LEOPFISR',
    id: 0,
    name: 'Poker Flat Incoherent Scatter Radar, Alaska',
    uiName: 'Poker Flat ISR',
    system: 'Incoherent Scatter',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>65.12992,
    lon: <Degrees>-147.47104,
    alt: <Kilometers>0.23,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>45,
    maxEl: <Degrees>90, // 91 to ensure visual overlap
    minRng: <Kilometers>100,
    maxRng: <Kilometers>1800,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: 'https://platform.leolabs.space/sites/pfisr',
    country: 'United States',
    /*
     * "boresight": new DetailedSensor({ "azimuth": 15.0, "elevation": 74.0 }),
     * "transmitFrequency": 450000000.0,
     * "transmitPower": 2000000.0,
     */
    volume: true,
    operator: Operators.LEOLABS,
  }),

  /*
   * TODO: There needs to be a new method for doing FOV when
   * the radar is a fence but not aiming at 90 elevation
   */
  LEOKSR: new DetailedSensor({
    objName: 'LEOKSR',
    id: 0,
    name: 'Kiwi Space Radar, New Zealand',
    uiName: 'Kiwi Space Radar',
    system: 'S-Band',
    freqBand: 'S-Band',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>-45.03854,
    lon: <Degrees>170.09556,
    alt: <Kilometers>0.6,
    /*
     * minAz:<Degrees>(-13 - 45),
     * maxAz: <Degrees>(-13 + 45),
     * minEl: <Degrees>69,
     * maxEl: <Degrees>71,
     * minRng: <Kilometers>100,
     * maxRng: <Kilometers>1800,
     * minAz2: <Degrees>(167 - 45),
     * maxAz2: <Degrees>(167 + 45),
     * minEl2: <Degrees>69,
     * maxEl2: <Degrees>71,
     * minRng2: <Kilometers>100,
     * maxRng2: <Kilometers>1800,
     */
    minAz: <Degrees>269,
    maxAz: <Degrees>271,
    minEl: <Degrees>10,
    maxEl: <Degrees>170,
    minRng: <Kilometers>100,
    maxRng: <Kilometers>3000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: 'https://platform.leolabs.space/sites/ksr',
    country: 'United States',
    volume: true,
    operator: Operators.LEOLABS,
  }),
  LEOCRSR: new DetailedSensor({
    objName: 'LEOCRSR',
    id: 0,
    name: 'Costa Rica Space Radar, Costa Rica',
    uiName: 'Costa Rica SR',
    system: 'S-Band',
    freqBand: 'S-Band',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>10.611782670733335, // https://www.radartutorial.eu/19.kartei/02.surv/karte087.en.html
    lon: <Degrees>-85.52869380341954,
    alt: <Kilometers>0.0,
    minAz: <Degrees>209,
    maxAz: <Degrees>211,
    minEl: <Degrees>20,
    maxEl: <Degrees>180, // NOTE: Not sure why this looks correct, but 200 goes into the earth
    minRng: <Kilometers>100,
    maxRng: <Kilometers>3000,
    minAz2: <Degrees>119,
    maxAz2: <Degrees>121,
    minEl2: <Degrees>20,
    maxEl2: <Degrees>180, // NOTE: Not sure why this looks correct, but 200 goes into the earth
    minRng2: <Kilometers>100,
    maxRng2: <Kilometers>3000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: 'https://platform.leolabs.space/sites/crsr',
    country: 'United States',
    volume: true,
    operator: Operators.LEOLABS,
  }),
  LEOAZORES: new DetailedSensor({
    objName: 'LEOAZORES',
    id: 0,
    name: 'Azores Space Radar, Azores',
    uiName: 'Azores Space Radar',
    system: 'S-Band',
    freqBand: 'S-Band',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>36.9975,
    lon: <Degrees>-25.1384933,
    alt: <Kilometers>0.198,
    minAz: <Degrees>209,
    maxAz: <Degrees>211,
    minEl: <Degrees>20,
    maxEl: <Degrees>180, // NOTE: Not sure why this looks correct, but 200 goes into the earth
    minRng: <Kilometers>100,
    maxRng: <Kilometers>3000,
    minAz2: <Degrees>119,
    maxAz2: <Degrees>121,
    minEl2: <Degrees>20,
    maxEl2: <Degrees>180, // NOTE: Not sure why this looks correct, but 200 goes into the earth
    minRng2: <Kilometers>100,
    maxRng2: <Kilometers>3000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: 'https://platform.leolabs.space/sites/crsr',
    country: 'United States',
    volume: true,
    operator: Operators.LEOLABS,
  }),
  LEOWASR: new DetailedSensor({
    objName: 'LEOWASR',
    id: 0,
    name: 'Western Australia Space Radar, Australia',
    uiName: 'Western Australia Space Radar',
    system: 'S-Band',
    freqBand: 'S-Band',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>-33.308487,
    lon: <Degrees>116.030608,
    alt: <Kilometers>0.241,
    minAz: <Degrees>119,
    maxAz: <Degrees>121,
    minEl: <Degrees>10,
    maxEl: <Degrees>170, // NOTE: Not sure why this looks correct, but 200 goes into the earth
    minRng: <Kilometers>100,
    maxRng: <Kilometers>3000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    url: 'https://platform.leolabs.space/sites/crsr',
    country: 'United States',
    volume: true,
    operator: Operators.LEOLABS,
  }),
  // LEO LABS ARSR on hold as of 2023-11-12 due to national security concerns from Argentina

  // ESOC RADARS
  GRV: new DetailedSensor({
    objName: 'GRV',
    id: 0,
    name: 'Grand Réseau Adapté à la Veille Spatiale, France',
    uiName: 'Dijon',
    system: 'GRAVES',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>47.347778,
    lon: <Degrees>5.51638,
    alt: <Kilometers>0.0,
    minAz: <Degrees>90,
    maxAz: <Degrees>270,
    minEl: <Degrees>20,
    maxEl: <Degrees>40,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>1700, // http://emits.sso.esa.int/emits-doc/AO5059RD1.pdf
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'France',
    volume: true,
    operator: Operators.ESA,
  }),
  TIR: new DetailedSensor({
    objName: 'TIR',
    id: 0,
    name: 'Wachtberg, Germany',
    uiName: 'Wachtberg',
    system: 'TIRA',
    freqBand: 'L/Ku-Band',
    type: SpaceObjectType.MECHANICAL,
    lat: <Degrees>50.6166,
    lon: <Degrees>7.1296,
    alt: <Kilometers>0.0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>1.5, // http://www.issfd.org/ISSFD_2012/ISSFD23_CRSD2_3.pdf
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>2000, // http://emits.sso.esa.int/emits-doc/AO5059RD1.pdf
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Germany',
    operator: Operators.ESA,
  }),
  GES: new DetailedSensor({
    objName: 'GES',
    id: 0,
    name: 'Schmidtenhöhe, Germany',
    uiName: 'Schmidtenhöhe',
    system: 'GESTRA',
    freqBand: 'L-Band',
    type: SpaceObjectType.MECHANICAL,
    lat: <Degrees>50.335711,
    lon: <Degrees>7.6359085,
    alt: <Kilometers>0.0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>1.5,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>3000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Germany',
    operator: Operators.ESA,
  }),
  NRC: new DetailedSensor({
    objName: 'NRC',
    id: 0,
    name: 'Northern Cross Radiotelescope, Italy',
    uiName: 'Medicina',
    system: 'BIRALES',
    freqBand: 'UHF',
    type: SpaceObjectType.BISTATIC_RADIO_TELESCOPE,
    lat: <Degrees>44.5208,
    lon: <Degrees>11.6469,
    alt: <Kilometers>0.025,
    minAz: <Degrees>89.1,
    maxAz: <Degrees>90.9,
    minEl: <Degrees>45,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>1700,
    minAz2: <Degrees>179.1,
    maxAz2: <Degrees>180.9,
    minEl2: <Degrees>45,
    maxEl2: <Degrees>90,
    minRng2: <Kilometers>0,
    maxRng2: <Kilometers>1700,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Italy',
    operator: Operators.ESA,
  }),
  PDM: new DetailedSensor({
    objName: 'PDM',
    id: 0,
    name: 'Pratica di Mare Air Base, Italy',
    uiName: 'Pratica di Mare',
    system: 'MiTE',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>41.654444, // https://en.wikipedia.org/wiki/Pratica_di_Mare_Air_Base
    lon: <Degrees>12.445,
    alt: <Kilometers>0.0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>5, // 0 seems low - https://amostech.com/TechnicalPapers/2015/Adaptive_Optics_Imaging/DelGenio.pdf
    maxEl: <Degrees>90,
    minRng: <Kilometers>0, // https://amostech.com/TechnicalPapers/2015/Adaptive_Optics_Imaging/DelGenio.pdf
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Italy',
    operator: Operators.ITAF,
  }),
  TRO: new DetailedSensor({
    objName: 'TRO',
    id: 0,
    name: 'Royal Air Force Troödos, Cyprus',
    uiName: 'Royal Air Force Troödos',
    system: 'Starbrook',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>34.912778,
    lon: <Degrees>32.883889,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>3,
    maxEl: <Degrees>90,
    minRng: <Kilometers>15000,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'United Kingdom',
    operator: Operators.UKSA,
  }),
  SDT: new DetailedSensor({
    objName: 'SDT',
    id: 0,
    name: 'ESA Space Debris Telescope, Spain',
    uiName: 'Tenerife',
    system: 'SDT',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>28.3,
    lon: <Degrees>-16.5097,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>10,
    maxEl: <Degrees>90,
    minRng: <Kilometers>15000,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Spain',
    operator: Operators.ESA,
  }),
  ZimLAT: new DetailedSensor({
    objName: 'ZimLAT',
    id: 0,
    name: 'Zimmerwald Observatory, Switzerland',
    uiName: 'Zimmerwald',
    system: 'ZimLAT',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>46.877225,
    lon: <Degrees>7.465225,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>10,
    maxEl: <Degrees>90,
    minRng: <Kilometers>15000,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Switzerland',
    operator: Operators.ESA,
  }),
  ZimSMART: new DetailedSensor({
    objName: 'ZimSMART',
    id: 0,
    name: 'Zimmerwald Observatory, Switzerland',
    uiName: 'Zimmerwald',
    system: 'ZimSMART',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>46.876947,
    lon: <Degrees>7.465086,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>10,
    maxEl: <Degrees>90,
    minRng: <Kilometers>15000,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Switzerland',
    operator: Operators.ESA,
  }),
  Tromso: new DetailedSensor({
    objName: 'Tromso',
    id: 0,
    name: 'Tromso, Norway',
    uiName: 'Tromso',
    system: 'Incoherent Scatter',
    freqBand: 'UHF/VHF',
    type: SpaceObjectType.BISTATIC_RADIO_TELESCOPE,
    lat: <Degrees>69.586439,
    lon: <Degrees>19.226111,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>30, // https://www.eiscat.se/groups/Operations/ops-manual/ops-manual.htm
    maxEl: <Degrees>90,
    minRng: <Kilometers>500,
    maxRng: <Kilometers>1500,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Norway',
    operator: Operators.EISCAT,
  }),
  Kiruna: new DetailedSensor({
    objName: 'Kiruna',
    id: 0,
    name: 'Kiruna, Sweden',
    uiName: 'Kiruna',
    system: 'Incoherent Scatter',
    freqBand: 'UHF/VHF',
    type: SpaceObjectType.BISTATIC_RADIO_TELESCOPE,
    lat: <Degrees>67.860778,
    lon: <Degrees>20.433806,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>10,
    maxEl: <Degrees>90,
    minRng: <Kilometers>500,
    maxRng: <Kilometers>1500,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Sweden',
    operator: Operators.EISCAT,
  }),
  Sodankyla: new DetailedSensor({
    objName: 'Sodankyla',
    id: 0,
    name: 'Sodankyla, Finland',
    uiName: 'Sodankyla',
    system: 'Incoherent Scatter',
    freqBand: 'UHF/VHF',
    type: SpaceObjectType.BISTATIC_RADIO_TELESCOPE,
    lat: <Degrees>67.363903,
    lon: <Degrees>26.630417,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>10,
    maxEl: <Degrees>90,
    minRng: <Kilometers>500,
    maxRng: <Kilometers>1500,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Finland',
    operator: Operators.EISCAT,
  }),
  Svalbard: new DetailedSensor({
    objName: 'Svalbard',
    id: 0,
    name: 'Svalbard, Norway',
    uiName: 'Svalbard',
    system: 'Incoherent Scatter',
    freqBand: 'UHF/VHF',
    type: SpaceObjectType.BISTATIC_RADIO_TELESCOPE,
    lat: <Degrees>78.152669,
    lon: <Degrees>16.058706,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>10,
    maxEl: <Degrees>90,
    minRng: <Kilometers>500,
    maxRng: <Kilometers>1500,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Norway',
    operator: Operators.EISCAT,
  }),
  // RUSSIAN RADARS
  OLED: new DetailedSensor({
    objName: 'OLED',
    id: 0,
    name: 'Olenegorsk, Russia',
    uiName: 'Olenegorsk',
    system: 'Dnepr',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>68.1141,
    lon: <Degrees>33.9102,
    alt: <Kilometers>0.0,
    minAz: <Degrees>280, // All Information via russianforces.org
    maxAz: <Degrees>340,
    minEl: <Degrees>5.5,
    maxEl: <Degrees>34.5,
    minRng: <Kilometers>250,
    maxRng: <Kilometers>4600,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'Russia',
    operator: Operators.RUSSF,
  }),
  OLEV: new DetailedSensor({
    objName: 'OLEV',
    id: 0,
    name: 'Olenegorsk, Russia',
    uiName: 'Olenegorsk',
    system: 'Voronezh-VP', // AKA Voronezh-M
    freqBand: 'VHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>68.090694,
    lon: <Degrees>34.327539,
    alt: <Kilometers>0.0,
    minAz: <Degrees>250, // All Information via russianforces.org
    maxAz: <Degrees>10,
    minEl: <Degrees>7,
    maxEl: <Degrees>70,
    minRng: <Kilometers>250,
    maxRng: <Kilometers>6000, // 6000 horizontal, 10000 vertical
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'Russia',
    operator: Operators.RUSSF,
    // Under Contstruction
  }),
  PEC: new DetailedSensor({
    objName: 'PEC',
    id: 0,
    name: 'Pechora, Russia',
    uiName: 'Pechora',
    system: 'Dar\'yal',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>65.21,
    lon: <Degrees>57.295,
    alt: <Kilometers>0.0,
    minAz: <Degrees>305, // All Information via russianforces.org
    maxAz: <Degrees>55,
    minEl: <Degrees>2,
    maxEl: <Degrees>55,
    minRng: <Kilometers>300,
    maxRng: <Kilometers>7200,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'Russia',
    operator: Operators.RUSSF,
  }),
  // Vorkuta
  MISD: new DetailedSensor({
    objName: 'MISD',
    id: 0,
    name: 'Mishelevka, Russia',
    uiName: 'Mishelevka',
    system: 'Dnepr',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>52.8555,
    lon: <Degrees>103.2317,
    alt: <Kilometers>0.0,
    minAz: <Degrees>41, // All Information via russianforces.org
    maxAz: <Degrees>219,
    minEl: <Degrees>5.5,
    maxEl: <Degrees>34.5,
    minRng: <Kilometers>250,
    maxRng: <Kilometers>4600,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'Russia',
    operator: Operators.RUSSF,
  }),
  MISV: new DetailedSensor({
    // NOTE: There are two co-located here
    objName: 'MISV',
    id: 0,
    name: 'Mishelevka, Russia',
    uiName: 'Mishelevka',
    system: 'Voronezh-VP', // AKA Voronezh-M or Voronezh-SM
    freqBand: 'VHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>52.8555,
    lon: <Degrees>103.2317,
    alt: <Kilometers>0.0,
    // boresight 130
    minAz: <Degrees>10, // All Information via russianforces.org
    maxAz: <Degrees>250,
    minEl: <Degrees>2,
    maxEl: <Degrees>70,
    minRng: <Kilometers>250,
    maxRng: <Kilometers>6000, // 6000 horizontal, 10000 vertical
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'Russia',
    operator: Operators.RUSSF,
  }),
  LEKV: new DetailedSensor({
    objName: 'LEKV',
    id: 0,
    name: 'Lekhtusi, Russia',
    uiName: 'Lekhtusi',
    system: 'Voronezh-M',
    freqBand: 'VHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>60.275458,
    lon: <Degrees>30.546017,
    alt: <Kilometers>0.0,
    minAz: <Degrees>245,
    maxAz: <Degrees>355,
    minEl: <Degrees>2,
    maxEl: <Degrees>70,
    minRng: <Kilometers>250,
    maxRng: <Kilometers>6000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'Russia',
    operator: Operators.RUSSF,
  }),
  // Planned Voronezh-SM (Ragozinka-2)
  ARMV: new DetailedSensor({
    objName: 'ARMV',
    id: 0,
    name: 'Armavir, Russia',
    uiName: 'Armavir',
    system: 'Voronezh-DM',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>44.925106,
    lon: <Degrees>40.983894,
    alt: <Kilometers>0.0,
    // 2 Radars = 240az
    minAz: <Degrees>55, // All Information via russianforces.org
    maxAz: <Degrees>295,
    minEl: <Degrees>2,
    maxEl: <Degrees>70,
    minRng: <Kilometers>250,
    maxRng: <Kilometers>6000,
    changeObjectInterval: <Milliseconds>1000,
    country: 'Russia',
    zoom: ZoomValue.LEO,
    operator: Operators.RUSSF,
  }),
  /*
   * Decommissioned???
   * GAN: new DetailedSensor({
   *   objName: 'GAN',
   *   id: 0,
   *   name: 'Gantsevichi, Russia',
   *   uiName: 'GAN',
   *   system: 'GAN',
   *   type: SpaceObjectType.PHASED_ARRAY_RADAR,
   *   lat: <Degrees>52.85,
   *   lon: <Degrees>26.48,
   *   alt: <Kilometers>0.0,
   *   minAz:<Degrees>190, // All Information via russianforces.org
   *   maxAz: <Degrees>310,
   *   minEl: <Degrees>3,
   *   maxEl: <Degrees>80,
   *   minRng: <Kilometers>300,
   *   maxRng: <Kilometers>6500,
   *   zoom: ZoomValue.LEO,
   *   changeObjectInterval: <Milliseconds>1000,
   *   country: 'Russia',
   *   operator: Operators.RUSSF,
   * }),
   */
  KALV: new DetailedSensor({
    objName: 'KALV',
    id: 0,
    name: 'Kaliningrad, Russia', // Pionersky/Kaliningrad
    uiName: 'Kaliningrad',
    system: 'Voronezh-DM', // AKA Voronezh-M
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>54.857294,
    lon: <Degrees>20.18235,
    alt: <Kilometers>0.0,
    // boresight 240
    minAz: <Degrees>180, // All Information via russianforces.org
    maxAz: <Degrees>300,
    minEl: <Degrees>2,
    maxEl: <Degrees>70,
    minRng: <Kilometers>250,
    maxRng: <Kilometers>6000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'Russia',
    operator: Operators.RUSSF,
  }),
  BARV: new DetailedSensor({
    objName: 'BARV',
    id: 0,
    name: 'Barnaul, Russia',
    uiName: 'Barnaul',
    system: 'Voronezh-DM', // AKA Voronezh-M
    freqBand: 'VHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>53.139194,
    lon: <Degrees>83.68125,
    alt: <Kilometers>0.0,
    minAz: <Degrees>110, // All Information via russianforces.org
    maxAz: <Degrees>230,
    minEl: <Degrees>2,
    maxEl: <Degrees>70,
    minRng: <Kilometers>250,
    maxRng: <Kilometers>6000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'Russia',
    operator: Operators.RUSSF,
  }),
  YENV: new DetailedSensor({
    objName: 'YENV',
    id: 0,
    name: 'Yeniseysk, Russia',
    uiName: 'Yeniseysk',
    system: 'Voronezh-DM', // AKA Voronezh-M
    freqBand: 'VHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>58.506095,
    lon: <Degrees>92.046072,
    alt: <Kilometers>0.0,
    // boresight 240
    minAz: <Degrees>330, // All Information via russianforces.org
    maxAz: <Degrees>90,
    minEl: <Degrees>2,
    maxEl: <Degrees>70,
    minRng: <Kilometers>250,
    maxRng: <Kilometers>6000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'Russia',
    operator: Operators.RUSSF,
  }),
  ORSV: new DetailedSensor({
    objName: 'ORSV',
    id: 0,
    name: 'Orsk, Russia',
    uiName: 'Orsk',
    system: 'Voronezh-M',
    freqBand: 'UHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>51.273346,
    lon: <Degrees>58.95903,
    alt: <Kilometers>0.0,
    // boresight 180 - https://www.janes.com/images/default-source/news-images/bsp_13855-idr-16436.jpg?sfvrsn=aa8c7163_2
    minAz: <Degrees>120, // All Information via russianforces.org
    maxAz: <Degrees>240,
    minEl: <Degrees>2,
    maxEl: <Degrees>70,
    minRng: <Kilometers>250,
    maxRng: <Kilometers>6000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'Russia',
    operator: Operators.RUSSF,
  }),
  /*
   * Removed from combat alert in 2020
   * BAL: new DetailedSensor({
   *   objName: 'BAL',
   *   id: 0,
   *   name: 'Balkhash, Russia',
   *   uiName: 'BAL',
   *   system: 'Dnepr',
   *   freqBand: 'UHF',
   *   type: SpaceObjectType.PHASED_ARRAY_RADAR,
   *   lat: <Degrees>46.603076,
   *   lon: <Degrees>74.530985,
   *   alt: <Kilometers>0.0,
   *   minAz:<Degrees>91, // All Information via russianforces.org
   *   maxAz: <Degrees>151,
   *   minEl: <Degrees>5.5,
   *   maxEl: <Degrees>34.5,
   *   minRng: <Kilometers>385,
   *   maxRng: <Kilometers>4600,
   *   zoom: ZoomValue.LEO,
   *   changeObjectInterval: <Milliseconds>1000,
   *   country: 'Russia',
   *   operator: Operators.RUSSF,
   * }),
   */
  STO: new DetailedSensor({
    objName: 'STO',
    id: 0,
    name: 'Storozhevaya, Russia',
    uiName: 'Storozhevaya',
    system: 'Krona',
    freqBand: 'UHF/VHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR, // Actually two sites and the other is a telescope/LIDAR combo
    lat: <Degrees>43.718331,
    lon: <Degrees>41.226253,
    alt: <Kilometers>2.115,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>5,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    country: 'Russia',
    operator: Operators.RUSSF,
  }),
  NAK: new DetailedSensor({
    objName: 'NAK',
    id: 0,
    name: 'Nakhodka, Russia',
    uiName: 'Nakhodka',
    system: 'Krona-N', // crown of a tree (because of the antenna shape)
    freqBand: 'UHF/VHF',
    type: SpaceObjectType.PHASED_ARRAY_RADAR, // 1xUHF & 5xSHF radar
    lat: <Degrees>42.9357,
    lon: <Degrees>132.576769,
    alt: <Kilometers>0.21,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>2,
    maxEl: <Degrees>90,
    minRng: <Kilometers>0,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    country: 'Russia',
    operator: Operators.RUSSF,
  }),
  // eslint-disable-next-line multiline-comment-style
  // CHINESE RADARS
  // XUA: new DetailedSensor({ // Not operational anymore
  //   id: 0,
  //   name: 'Xuanhua, China',
  //   shortName: 'XUA',
  //   type: SpaceObjectType.PHASED_ARRAY_RADAR,
  //   lat: <Degrees>40.446944,
  //   lon: <Degrees>115.116389,
  //   alt: <Kilometers>1.6,
  //   minAz:<Degrees>300, // Information via global ssa sensors amos 2010.pdf (sinodefence.com/special/airdefense/project640.asp)
  //   maxAz: <Degrees>60, // Information via global ssa sensors amos 2010.pdf (sinodefence.com/special/airdefense/project640.asp)
  //   minEl: <Degrees>2, // Information via globalsecurity.org
  //   maxEl: <Degrees>80, // Information via globalsecurity.org
  //   minRng: <Kilometers>300,
  //   maxRng: <Kilometers>3000, // Information via global ssa sensors amos 2010.pdf (sinodefence.com/special/airdefense/project640.asp)
  //   zoom: ZoomValue.LEO,
  //   changeObjectInterval: <Milliseconds>1000,
  //   country: 'China',
  //
  //
  // }),
  XIN: new DetailedSensor({
    objName: 'XIN',
    id: 0,
    name: 'Korla Site Xingjiang, China', // Korla
    uiName: 'Korla',
    system: 'LPAR',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>41.64131987863126, // https://hfasia.blogspot.com/2017/02/blog-post_50.html
    lon: <Degrees>86.23695312232473,
    alt: <Kilometers>0.993, // https://www.freemaptools.com/elevation-finder.htm
    minAz: <Degrees>0, // https://www.globalsecurity.org/space/world/china/images/xinjiang-radar-image-2018-1-s.jpg
    maxAz: <Degrees>360, // It is on a rotatable platform
    minEl: <Degrees>3, // Assumed same characteristics as AN/FPS-115
    maxEl: <Degrees>85,
    minRng: <Kilometers>200, // Assumed same characteristics as AN/FPS-115
    maxRng: <Kilometers>5556, // Assumed same characteristics as AN/FPS-115
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'China',
    operator: Operators.PLA,
  }),
  HEI: new DetailedSensor({
    objName: 'HEI',
    id: 0,
    name: 'Huanan Heilongjiang, China',
    uiName: 'Huanan',
    system: 'LPAR',
    freqBand: 'X-Band',
    type: SpaceObjectType.PHASED_ARRAY_RADAR, // https://www.globalsecurity.org/wmd/world/china/lpar-x.htm
    lat: <Degrees>46.528164,
    lon: <Degrees>130.755235,
    alt: <Kilometers>0.241, // https://www.freemaptools.com/elevation-finder.htm
    minAz: <Degrees>135, // X-Band https://www.globalsecurity.org/wmd/world/china/lpar-x.htm
    maxAz: <Degrees>225, // Assumed +/- 45 degrees
    minEl: <Degrees>3,
    maxEl: <Degrees>65,
    minRng: <Kilometers>200,
    maxRng: <Kilometers>3000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'China',
    operator: Operators.PLA,
  }),
  ZHE: new DetailedSensor({
    objName: 'ZHE',
    id: 0,
    name: 'Lin\'an Zhejiang, China',
    uiName: 'Lin\'an',
    system: 'LPAR',
    freqBand: 'X-Band',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>30.28661248844618, // https://hfasia.blogspot.com/2017/02/blog-post_50.html
    lon: <Degrees>119.12859385017512,
    alt: <Kilometers>1.342, // https://www.freemaptools.com/elevation-finder.htm
    minAz: <Degrees>60,
    maxAz: <Degrees>300,
    minEl: <Degrees>2,
    maxEl: <Degrees>80,
    minRng: <Kilometers>300,
    maxRng: <Kilometers>3000,
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'China',
    operator: Operators.PLA,
  }),
  SHD: new DetailedSensor({
    objName: 'SHD',
    id: 0,
    name: 'Shandong, China', // P-Band (225 Mhz - 390 Mhz)
    uiName: 'Shandong',
    system: 'LPAR',
    freqBand: 'P-Band',
    type: SpaceObjectType.PHASED_ARRAY_RADAR, // https://www.globalsecurity.org/wmd/world/china/lpar-p.htm
    lat: <Degrees>36.023072,
    lon: <Degrees>118.093115,
    alt: <Kilometers>0.694, // https://www.freemaptools.com/elevation-finder.htm
    minAz: <Degrees>343.9, // Two Faces Boresight ~163.9 and ~103.9 degrees
    // https://www.omnicalculator.com/other/azimuth (163.9 degrees if aimed at Taipei)
    maxAz: <Degrees>223.9, // https://www.defensenews.com/global/asia-pacific/2022/04/18/new-chinese-radar-looks-towards-japan-satellite-image-shows/
    minEl: <Degrees>3,
    maxEl: <Degrees>85,
    minRng: <Kilometers>200,
    maxRng: <Kilometers>5500, // ~10,000 Transceivers
    zoom: ZoomValue.LEO,
    changeObjectInterval: <Milliseconds>1000,
    country: 'China',
    operator: Operators.PLA,
  }),
  PMO: new DetailedSensor({
    objName: 'PMO',
    id: 0,
    name: 'Purple Mountain Observatory, China',
    uiName: 'Nanjing',
    system: '60-cm Zeiss Telescope',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>32.064946,
    lon: <Degrees>118.829677,
    alt: <Kilometers>0.267,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>10,
    maxEl: <Degrees>90,
    minRng: <Kilometers>15000,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    country: 'China',
    operator: Operators.CAS,
  }),
  // OTHER SENSORS
  MLS: new DetailedSensor({
    objName: 'MLS',
    id: 0,
    name: 'Mount Lemmon Survey, Arizona',
    uiName: 'Tucson',
    system: '1.52 m Telescope',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>32.442,
    lon: <Degrees>-110.789,
    alt: <Kilometers>2.791,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>10,
    maxEl: <Degrees>90,
    minRng: <Kilometers>15000,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    country: 'United States',
    operator: Operators.COMMERCIAL,
  }),
  PO: new DetailedSensor({
    objName: 'PO',
    id: 0,
    name: 'Palomar Mountain Observatory, California',
    uiName: 'Palomar Observatory',
    system: 'Hale Telescope',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>33.3564,
    lon: <Degrees>-116.865,
    alt: <Kilometers>1.712,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>10,
    maxEl: <Degrees>90,
    minRng: <Kilometers>15000,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    country: 'United States',
    operator: Operators.CALTECH,
  }),
  LSO: new DetailedSensor({
    objName: 'LSO',
    id: 0,
    name: 'La Sagra Observatory, Spain',
    uiName: 'La Sagra',
    system: '4x Telescopes',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>37.9839,
    lon: <Degrees>-2.5644,
    alt: <Kilometers>0,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>10,
    maxEl: <Degrees>90,
    minRng: <Kilometers>15000,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    country: 'Spain',
    operator: Operators.ESA,
  }),

  // ISON Sensors
  MAY: new DetailedSensor({
    objName: 'MAY',
    id: 0,
    name: 'Remote Astronomical Society Observatory, New Mexico',
    uiName: 'Mayhill',
    system: 'MPC H06 Telescope',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>32.9039,
    lon: <Degrees>-105.5289,
    alt: <Kilometers>2.225,
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>10,
    maxEl: <Degrees>90,
    minRng: <Kilometers>15000,
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    country: 'United States',
    operator: Operators.COMMERCIAL,
  }),
  ROC: new DetailedSensor({
    objName: 'ROC',
    id: 0,
    name: 'Leshan , Republic of China (Taiwan)',
    uiName: 'Leshan',
    system: 'AN/FPS-115 (SRP)',
    type: SpaceObjectType.PHASED_ARRAY_RADAR,
    lat: <Degrees>24.499,
    lon: <Degrees>121.072,
    alt: <Kilometers>2.6, // https://taiwanenglishnews.com/tag/leshan-radar-station/ (2.6km)
    minAz: <Degrees>0, // Appears to be 3 faced
    // https://www.google.com/maps/place/24%C2%B029'45.6%22N+121%C2%B004'15.6%22E/@24.4985462,121.0709751,154m/data=!3m1!1e3!4m5!3m4!1s0x0:0xe0073e890dc05d63!8m2!3d24.496!4d121.071
    maxAz: <Degrees>360,
    minEl: <Degrees>-1, // "Tracks surface ships" https://www.globalsecurity.org/military/world/taiwan/air-defense-over.htm
    maxEl: <Degrees>85,
    minRng: <Kilometers>200,
    maxRng: <Kilometers>5556,
    changeObjectInterval: <Milliseconds>1000,

    zoom: ZoomValue.LEO,
    url: 'https://fas.org/man/eprint/leshan.pdf',
    country: 'Republic of China (Taiwan)',
    operator: Operators.ROC,
  }),

  // Korean OWL-Net
  OWLKorea: new DetailedSensor({
    objName: 'OWLKorea', // City is Daejeon
    id: 0,
    name: 'OWL-Net, Korea',
    uiName: 'OWL-Net Korea',
    system: 'OWL-Net',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>36.397635,
    lon: <Degrees>127.375679,
    alt: <Kilometers>0.139, // https://koreascience.kr/article/JAKO201535151810320.pdf
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>5, // Not sure what the minimum is
    maxEl: <Degrees>90,
    minRng: <Kilometers>100, // Multiple reports mentioning LEO tracking
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Republic of Korea',
    operator: Operators.OWLNET,
  }),
  OWLMongolia: new DetailedSensor({
    objName: 'OWLMongolia', // City is Songino
    id: 0,
    name: 'OWL-Net, Mongolia',
    uiName: 'OWL-Net Mongolia',
    system: 'OWL-Net',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>47.886111,
    lon: <Degrees>106.334722,
    alt: <Kilometers>1.674, // https://koreascience.kr/article/JAKO201618241377396.pdf
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>5, // Not sure what the minimum is
    maxEl: <Degrees>90,
    minRng: <Kilometers>100, // Multiple reports mentioning LEO tracking
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Republic of Korea',
    operator: Operators.OWLNET,
  }),
  OWLMorocco: new DetailedSensor({
    objName: 'OWLMorocco', // City is Oukaïmeden
    id: 0,
    name: 'OWL-Net, Morocco',
    uiName: 'OWL-Net Morocco',
    system: 'OWL-Net',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>31.206008082312465,
    lon: <Degrees>-7.866824237940079,
    alt: <Kilometers>2.725, // https://koreascience.kr/article/JAKO201618241377396.pdf
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>5, // Not sure what the minimum is
    maxEl: <Degrees>90,
    minRng: <Kilometers>100, // Multiple reports mentioning LEO tracking
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Republic of Korea',
    operator: Operators.OWLNET,
  }),
  OWLIsrael: new DetailedSensor({
    objName: 'OWLIsrael',
    id: 0,
    name: 'OWL-Net, Israel',
    uiName: 'OWL-Net Israel',
    system: 'OWL-Net',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>30.597389,
    lon: <Degrees>34.762194,
    alt: <Kilometers>0.8704, // Estimate
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>5, // Not sure what the minimum is
    maxEl: <Degrees>90,
    minRng: <Kilometers>100, // Multiple reports mentioning LEO tracking
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Republic of Korea',
    operator: Operators.OWLNET,
  }),
  OWLUSA: new DetailedSensor({
    objName: 'OWLUSA',
    id: 0,
    name: 'OWL-Net, USA',
    uiName: 'OWL-Net USA',
    system: 'OWL-Net',
    type: SpaceObjectType.OPTICAL,
    lat: <Degrees>32.44220852576508,
    lon: <Degrees>-110.7892718047087,
    alt: <Kilometers>2.7868, // Estimate
    minAz: <Degrees>0,
    maxAz: <Degrees>360,
    minEl: <Degrees>5, // Not sure what the minimum is
    maxEl: <Degrees>90,
    minRng: <Kilometers>100, // Multiple reports mentioning LEO tracking
    maxRng: <Kilometers>200000,
    zoom: ZoomValue.GEO,
    changeObjectInterval: <Milliseconds>20000,
    url: '',
    country: 'Republic of Korea',
    operator: Operators.OWLNET,
  }),
};
