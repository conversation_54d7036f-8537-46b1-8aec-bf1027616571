# Keep Track 5 - Apollo Augments

The term "Apollo" was selected as a nod to the Apollo program by NASA, representing pioneering advancements in space exploration. "Augments" suggests enhancements or additions to the existing features, aptly summarizing the nature of this update.

## Software Release Documentation

This software release focuses on code quality, testing, and numerous feature improvements. The central highlights include new meshes, a catalog loader, enhanced offline capabilities, and the transition to TypeScript for several modules.

## Major Features

### UI & Aesthetics

- Added new meshes.
- Added political maps and upgraded surveillance fence view.
- Added more visible error checking to the loading screen.
- Added link to show all sensors with FOV on a satellite.

### Functionality Enhancements

- Improved time sync between satCruncher, orbitCruncher, and main thread.
- Added new catalog loader.
- Added setting for overriding defaults to support making movies.
- Implemented Snyk and addressed multiple minor errors.
- Created an embeddable version of KeepTrack.
- Introduced initial gamepad support.
- Added lines for showing scanning by satellite or ground sensor.
- Introduced autopan, zoom speed settings, and zooming on missiles.
- Improved error detection and reporting for WebGL issues.

## Minor Features

- Migrated several modules to TypeScript including camera, sun, and moon.
- Improved offline catalog management with better handling of JSON parsing.
- Added setting for offline editing.
- Introduced new settings for overriding defaults.
- Enabled the moon position to be modified with an override.

### Bug Fixes

- Addressed a bug where WebGL would lag after highlighting certain stars.
- Resolved issues related to full FOV not working with fence update.
- Fixed a bug when selecting a missile.
- Corrected issue with ecf calculations affecting the sun and moon.
- Patched a vulnerability in get_data.php.
- Addressed a problem where 404 was always displayed.

### Code & Infrastructure Updates

- Extensive emphasis on testing, with many new Jest tests added and improved.
- Consolidated and refactored various modules and code segments.
- Upgraded multiple dependencies.
- Enhanced the CI/CD pipelines with better GitHub and GitLab integration.
- Migrated to node 17 for better performance and bug mitigation.
- Addressed numerous SonarQube and linting findings to improve code quality.
- Removed old and redundant scripts and tools.

### Documentation

- Updated the changelog regularly to reflect changes.
- Updated README.md.
