import { SatObject } from '@app/interfaces';
import { Degrees, Kilometers, SatelliteRecord, Sgp4, TleLine1, TleLine2 } from 'ootk';
import { keepTrackApi } from '../keepTrackApi';
import { StringPad } from '../lib/stringPad';
import { errorManagerInstance } from '../singletons/errorManager';
import { OrbitFinder } from '../singletons/orbit-finder';
import { TimeManager } from '../singletons/time-manager';
import { SatMath } from './sat-math';
import { SensorMath } from './sensor-math';

/**
 * Work in progress!
 */
export abstract class ManueverMath {
  public static createManeuverAnalyst = (satId: number, incVariation: number, meanmoVariation: number, rascVariation: number) => {
    const catalogManagerInstance = keepTrackApi.getCatalogManager();
    const timeManagerInstance = keepTrackApi.getTimeManager();
    const sensorManagerInstance = keepTrackApi.getSensorManager();
    const uiManagerInstance = keepTrackApi.getUiManager();

    if (sensorManagerInstance.currentSensors?.length === 0) throw new Error('No sensors selected');

    // TODO This needs rewrote from scratch to bypass the satcruncher
    var mainsat = catalogManagerInstance.getSat(satId);
    var origsat = mainsat;
    // Launch Points are the Satellites Current Location
    var TEARR = SensorMath.getTearr(mainsat, sensorManagerInstance.currentSensors, timeManagerInstance.simulationTimeObj);
    var launchLat: Degrees, launchLon: Degrees, alt: Kilometers;
    launchLat = TEARR.lat;
    launchLon = TEARR.lon;
    alt = TEARR.alt;
    const upOrDown = SatMath.getDirection(mainsat, timeManagerInstance.simulationTimeObj);
    if (upOrDown === 'Error') {
      uiManagerInstance.toast('无法计算卫星方向。请稍后重试。', 'caution');
    }
    var currentEpoch = TimeManager.currentEpoch(timeManagerInstance.simulationTimeObj);
    mainsat.TLE1 = (mainsat.TLE1.substr(0, 18) + currentEpoch[0] + currentEpoch[1] + mainsat.TLE1.substr(32)) as TleLine1;
    var TLEs;
    // Ignore argument of perigee for round orbits OPTIMIZE
    if (mainsat.apogee - mainsat.perigee < 300) {
      TLEs = new OrbitFinder(mainsat, launchLat, launchLon, upOrDown as 'N' | 'S', timeManagerInstance.simulationTimeObj).rotateOrbitToLatLon();
    } else {
      TLEs = new OrbitFinder(mainsat, launchLat, launchLon, upOrDown as 'N' | 'S', timeManagerInstance.simulationTimeObj, alt).rotateOrbitToLatLon();
    }
    var TLE1 = TLEs[0];
    var TLE2 = TLEs[1];
    satId = catalogManagerInstance.sccNum2Id(80000);
    var sat = catalogManagerInstance.getSat(satId);
    sat = origsat;
    let iTLE1 = '1 ' + 80000 + TLE1.substr(7);
    let iTLEs;
    // Ignore argument of perigee for round orbits OPTIMIZE
    if (sat.apogee - sat.perigee < 300) {
      iTLEs = new OrbitFinder(sat, launchLat, launchLon, upOrDown as 'N' | 'S', timeManagerInstance.simulationTimeObj, 0 as Kilometers, rascVariation).rotateOrbitToLatLon();
    } else {
      iTLEs = new OrbitFinder(sat, launchLat, launchLon, upOrDown as 'N' | 'S', timeManagerInstance.simulationTimeObj, alt, rascVariation).rotateOrbitToLatLon();
    }
    iTLE1 = iTLEs[0];
    let iTLE2 = iTLEs[1];
    // For the first 30
    var inc = TLE2.substr(8, 8);
    inc = (parseFloat(inc) + incVariation).toPrecision(7);
    inc = inc.split('.');
    inc[0] = inc[0].substr(-3, 3);
    if (inc[1]) {
      inc[1] = inc[1].substr(0, 4);
    } else {
      inc[1] = '0000';
    }
    inc = (inc[0] + '.' + inc[1]).toString();
    inc = StringPad.padEmpty(inc, 8);
    // For the second 30
    var meanmo: any = iTLE2.substr(52, 10);
    meanmo = (parseFloat(meanmo) * meanmoVariation).toPrecision(10);
    // meanmo = parseFloat(meanmo - (0.005 / 10) + (0.01 * ((meanmoIterat + 1) / 10))).toPrecision(10);
    meanmo = meanmo.split('.');
    meanmo[0] = meanmo[0].substr(-2, 2);
    if (meanmo[1]) {
      meanmo[1] = meanmo[1].substr(0, 8);
    } else {
      meanmo[1] = '00000000';
    }
    meanmo = (meanmo[0] + '.' + meanmo[1]).toString();
    iTLE2 = '2 ' + 80000 + ' ' + inc + ' ' + iTLE2.substr(17, 35) + meanmo + iTLE2.substr(63);
    sat = catalogManagerInstance.getSat(satId);
    sat.TLE1 = iTLE1 as TleLine1;
    sat.TLE2 = iTLE2 as TleLine2;
    sat.active = true;

    let satrec: SatelliteRecord;
    try {
      satrec = Sgp4.createSatrec(iTLE1, iTLE2);
    } catch (e) {
      errorManagerInstance.error(e, 'catalog-manager.ts', 'Error creating satellite record!');
      return false;
    }

    if (SatMath.altitudeCheck(satrec, timeManagerInstance.simulationTimeObj) > 1) {
      catalogManagerInstance.satCruncher.postMessage({
        type: 'satEdit',
        id: satId,
        TLE1: iTLE1,
        TLE2: iTLE2,
      });
      // TODO: This belongs in main or uiManager
      // orbitManager.changeOrbitBufferData(satId, iTLE1, iTLE2);
    } else {
      return false;
    }
    return true;
  };

  public static findChangeOrbitToDock = (sat: SatObject, sat2: SatObject, propLength: number) => {
    const catalogManagerInstance = keepTrackApi.getCatalogManager();
    let closestInc = 0;
    let closestRaan = 0;
    let closestMeanMo = 1;
    let minDistArray = {
      dist: 1000000,
    };
    for (let incTemp = -1; incTemp <= 1; incTemp++) {
      for (let raanTemp = -1; raanTemp <= 1; raanTemp++) {
        for (let meanMoTemp = 0.95; meanMoTemp <= 1.05; meanMoTemp += 0.05) {
          if (ManueverMath.createManeuverAnalyst(sat.id, incTemp, meanMoTemp, raanTemp)) {
            let minDistArrayTemp = SatMath.findClosestApproachTime(catalogManagerInstance.sccNum2Sat(80000), sat2, propLength);
            if (minDistArrayTemp.dist < minDistArray.dist) {
              minDistArray = minDistArrayTemp;
            }
          }
        }
      }
    }

    console.log(`${sat.inclination + closestInc}`);
    console.log(`${sat.raan + closestRaan}`);
    console.log(`${sat.meanMotion * closestMeanMo}`);
    ManueverMath.createManeuverAnalyst(sat.id, closestInc, closestMeanMo, closestRaan);
  };
}
