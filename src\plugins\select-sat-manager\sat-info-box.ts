/* eslint-disable max-lines */
import { country2flagIcon } from '@app/catalogs/countries';
import { GetSatType, KeepTrackApiEvents, ToastMsgType } from '@app/interfaces';
import { InputEventType, keepTrackApi } from '@app/keepTrackApi';
import { openColorbox } from '@app/lib/colorbox';
import { getEl, hideEl, showEl } from '@app/lib/get-el';
import { MissileObject } from '@app/singletons/catalog-manager/MissileObject';
import { lineManagerInstance } from '@app/singletons/draw-manager/line-manager';
import { LineColors } from '@app/singletons/draw-manager/line-manager/line';
import { errorManagerInstance } from '@app/singletons/errorManager';
import { SearchManager } from '@app/singletons/search-manager';
import { TimeManager } from '@app/singletons/time-manager';
import { CatalogSearch } from '@app/static/catalog-search';
import { CoordinateTransforms } from '@app/static/coordinate-transforms';
import { SatMath, SunStatus } from '@app/static/sat-math';
import { SensorMath, TearrData } from '@app/static/sensor-math';
import { StringExtractor } from '@app/static/string-extractor';
import bookmarkAddPng from '@public/img/icons/bookmark-add.png';
import bookmarkRemovePng from '@public/img/icons/bookmark-remove.png';
import Draggabilly from 'draggabilly';
import { BaseObject, CatalogSource, DEG2RAD, DetailedSatellite, PayloadStatus, RfSensor, SpaceObjectType, Sun, SunTime, cKmPerMs, eci2lla } from 'ootk';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { missileManager } from '../missile/missile-manager';
import { SensorManager } from '../sensor/sensorManager';
import { SoundNames } from '../sounds/SoundNames';
import { StereoMap } from '../stereo-map/stereo-map';
import { WatchlistPlugin } from '../watchlist/watchlist';
import './sat-info-box.css';
import { SelectSatManager } from './select-sat-manager';

/**
 * This class controls all the functionality of the satellite info box.
 * There are select events and update events that are registered to the keepTrackApi.
 */
export class SatInfoBox extends KeepTrackPlugin {
  readonly id = 'SatInfoBox';
  dependencies_: string[] = [SelectSatManager.name];
  private readonly selectSatManager_: SelectSatManager;
  private isVisible_ = false;

  constructor() {
    super();
    this.selectSatManager_ = keepTrackApi.getPlugin(SelectSatManager) as unknown as SelectSatManager; // this will be validated in KeepTrackPlugin constructor
  }

  private static readonly containerId_ = 'sat-infobox';

  private isorbitalDataLoaded_ = false;
  private issecondaryDataLoaded_ = false;
  private issensorInfoLoaded_ = false;
  private islaunchDataLoaded_ = false;
  private issatMissionDataLoaded_ = false;
  private isTopLinkEventListenersAdded_ = false;

  // Starting values of the collapsable sections
  private readonly isActionsSectionCollapsed_ = true;
  private readonly isIdentifiersSectionCollapsed_ = false;
  private readonly isOrbitalSectionCollapsed_ = false;
  private readonly isSecondaryDataSectionCollapsed_ = false;
  private readonly isSensorDataSectionCollapsed_ = false;
  private readonly isObjectDataSectionCollapsed_ = false;
  private readonly isMissionSectionCollapsed_ = false;

  currentTEARR = <TearrData>{
    az: 0,
    el: 0,
    rng: 0,
    objName: '',
    lat: 0,
    lon: 0,
    alt: 0,
    inView: false,
  };

  addHtml(): void {
    super.addHtml();

    // NOTE: This has to go first.
    keepTrackApi.on(KeepTrackApiEvents.selectSatData, this.orbitalData.bind(this));
    // -------------------------------------------------
    keepTrackApi.on(KeepTrackApiEvents.selectSatData, SatInfoBox.updateSensorInfo_.bind(this));
    keepTrackApi.on(KeepTrackApiEvents.selectSatData, SatInfoBox.updateLaunchData_.bind(this));
    keepTrackApi.on(InputEventType.KeyDown, this.onKeyDownLowerI_.bind(this));
    keepTrackApi.on(KeepTrackApiEvents.selectSatData, SatInfoBox.updateSatMissionData_.bind(this));
    keepTrackApi.on(KeepTrackApiEvents.selectSatData, SatInfoBox.updateObjectData_.bind(this));
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, this.uiManagerFinal_.bind(this));
  }

  private onKeyDownLowerI_(key: string): void {
    if (key !== 'i') {
      return;
    }

    if (this.isVisible_) {
      this.hide();
    } else {
      this.show();
    }
  }

  addJs(): void {
    super.addJs();

    keepTrackApi.on(
      KeepTrackApiEvents.updateSelectBox,
      // eslint-disable-next-line complexity, max-statements
      (obj: BaseObject) => {
        if (!keepTrackApi.isInitialized) {
          return;
        }

        if (!obj?.isSatellite() && !obj?.isMissile()) {
          return;
        }

        try {
          const timeManagerInstance = keepTrackApi.getTimeManager();
          const sensorManagerInstance = keepTrackApi.getSensorManager();

          if (obj.isSatellite()) {
            const sat = obj as DetailedSatellite;

            if (!sat.position?.x || !sat.position?.y || !sat.position?.z || isNaN(sat.position?.x) || isNaN(sat.position?.y) || isNaN(sat.position?.z)) {
              const newPosition = SatMath.getEci(sat, timeManagerInstance.simulationTimeObj).position as { x: number; y: number; z: number };

              if (!newPosition || (newPosition?.x === 0 && newPosition?.y === 0 && newPosition?.z === 0)) {
                keepTrackApi
                  .getUiManager()
                  .toast(
                    `${sat.sccNum} 卫星不在轨道上！<br>模拟时间是 ${timeManagerInstance.simulationTimeObj.toUTCString()}<br>确保TLE数据正确`,
                    ToastMsgType.error,
                    true,
                  );
                this.selectSatManager_.selectSat(-1);

                return;
              }
            }

            let isInView, rae;

            if (keepTrackApi.getSensorManager().isSensorSelected()) {
              const sensor = keepTrackApi.getSensorManager().currentSensors[0];

              rae = sensor.rae(sat, timeManagerInstance.simulationTimeObj);
              isInView = sensor.isRaeInFov(rae);
            } else {
              rae = {
                az: 0,
                el: 0,
                rng: 0,
              };
              isInView = false;
            }

            const lla = eci2lla(sat.position, keepTrackApi.getTimeManager().gmst);
            const currentTearr: TearrData = {
              time: timeManagerInstance.simulationTimeObj.toISOString(),
              az: rae.az,
              el: rae.el,
              rng: rae.rng,
              objName: sat.name,
              lat: lla.lat,
              lon: lla.lon,
              alt: lla.alt,
              inView: isInView,
            };

            this.currentTEARR = currentTearr;
          } else {
            // Is Missile
            this.currentTEARR = missileManager.getMissileTEARR(obj as MissileObject);
          }

          const gmst = keepTrackApi.getTimeManager().gmst;
          const lla = eci2lla(obj.position, gmst);

          const satLonElement = getEl('sat-longitude');
          const satLatElement = getEl('sat-latitude');

          if (satLonElement && satLatElement) {
            if (lla.lon >= 0) {
              satLonElement.innerHTML = `${lla.lon.toFixed(4)}°E`;
            } else {
              satLonElement.innerHTML = `${(lla.lon * -1).toFixed(4)}°W`;
            }
            if (lla.lat >= 0) {
              satLatElement.innerHTML = `${lla.lat.toFixed(4)}°N`;
            } else {
              satLatElement.innerHTML = `${(lla.lat * -1).toFixed(4)}°S`;
            }
          }

          const covMatrix = this.selectSatManager_.primarySatCovMatrix;
          let covRadial = covMatrix[0];
          let covCrossTrack = covMatrix[1];
          let covInTrack = covMatrix[2];

          const useKm =
            covRadial > 0.5 &&
            covCrossTrack > 0.5 &&
            covInTrack > 0.5;

          if (useKm) {
            getEl('sat-uncertainty-radial')!.innerHTML = `${(covMatrix[0]).toFixed(3)} km`;
            getEl('sat-uncertainty-crosstrack')!.innerHTML = `${(covMatrix[1]).toFixed(3)} km`;
            getEl('sat-uncertainty-intrack')!.innerHTML = `${(covMatrix[2]).toFixed(3)} km`;
          } else {
            covRadial *= 1000;
            covCrossTrack *= 1000;
            covInTrack *= 1000;
            getEl('sat-uncertainty-radial')!.innerHTML = `${covRadial.toFixed(3)} m`;
            getEl('sat-uncertainty-crosstrack')!.innerHTML = `${covCrossTrack.toFixed(3)} m`;
            getEl('sat-uncertainty-intrack')!.innerHTML = `${covInTrack.toFixed(3)} m`;
          }

          if (
            settingsManager.plugins?.StereoMap &&
            keepTrackApi.getPlugin(StereoMap)?.isMenuButtonActive &&
            timeManagerInstance.realTime > settingsManager.lastMapUpdateTime + 30000
          ) {
            keepTrackApi.getPlugin(StereoMap)?.updateMap();
            settingsManager.lastMapUpdateTime = timeManagerInstance.realTime;
          }

          const satAltitudeElement = getEl('sat-altitude');
          const satVelocityElement = getEl('sat-velocity');

          if (satAltitudeElement && satVelocityElement) {

            if (obj.isSatellite()) {
              const sat = obj as DetailedSatellite;
              const gmst = keepTrackApi.getTimeManager().gmst;

              satAltitudeElement.innerHTML = `${SatMath.getAlt(sat.position, gmst).toFixed(3)} km`;
              satVelocityElement.innerHTML = `${sat.totalVelocity.toFixed(3)} km/s`;
            } else {
              const misl = obj as MissileObject;

              satAltitudeElement.innerHTML = `${(this.currentTEARR?.alt ?? 0).toFixed(3)} km`;
              if (misl.totalVelocity) {
                satVelocityElement.innerHTML = `${misl.totalVelocity.toFixed(3)} km/s`;
              } else {
                satVelocityElement.innerHTML = '未知';
              }
            }
          }

          this.updateSatelliteTearrData_(obj, sensorManagerInstance, timeManagerInstance);

          if (this.selectSatManager_.secondarySat !== -1 && getEl('secondary-sat-info')?.style?.display === 'none') {
            showEl('secondary-sat-info');
            showEl('sec-angle-link');
          } else if (this.selectSatManager_.secondarySat === -1 && getEl('secondary-sat-info')?.style?.display !== 'none') {
            hideEl('secondary-sat-info');
            hideEl('sec-angle-link');
          }

          if (this.selectSatManager_.secondarySatObj && obj.isSatellite()) {
            const sat = obj as DetailedSatellite;
            const ric = CoordinateTransforms.sat2ric(this.selectSatManager_.secondarySatObj, sat);
            const dist = SensorMath.distanceString(sat, this.selectSatManager_.secondarySatObj).split(' ')[2];

            const satDistanceElement = getEl('sat-sec-dist');
            const satRadiusElement = getEl('sat-sec-rad');
            const satInTrackElement = getEl('sat-sec-intrack');
            const satCrossTrackElement = getEl('sat-sec-crosstrack');

            if (satDistanceElement && satRadiusElement && satInTrackElement && satCrossTrackElement) {
              satDistanceElement.innerHTML = `${dist} km`;
              satRadiusElement.innerHTML = `${ric.position[0].toFixed(2)}km`;
              satInTrackElement.innerHTML = `${ric.position[1].toFixed(2)}km`;
              satCrossTrackElement.innerHTML = `${ric.position[2].toFixed(2)}km`;
            } else {
              errorManagerInstance.debug('Error updating secondary satellite info!');
            }
          }

          const nextPassElement = getEl('sat-nextpass');

          if (sensorManagerInstance.isSensorSelected()) {
            const uiManagerInstance = keepTrackApi.getUiManager();

            /*
             * If we didn't just calculate next pass time for this satellite and sensor combination do it
             * TODO: Make new logic for this to allow it to be updated while selected
             */
            if (
              (this.selectSatManager_.selectedSat !== uiManagerInstance.lastNextPassCalcSatId ||
                sensorManagerInstance.currentSensors[0].objName !== uiManagerInstance.lastNextPassCalcSensorShortName) &&
              !obj.isMissile()
            ) {
              const sat = obj as DetailedSatellite;

              if (sat.perigee > sensorManagerInstance.currentSensors[0].maxRng) {
                if (nextPassElement) {
                  nextPassElement.innerHTML = '超出最大射程';
                }
              } else if (nextPassElement) {
                nextPassElement.innerHTML = SensorMath.nextpass(sat, sensorManagerInstance.currentSensors, 2, 5);
              }

              /*
               *  IDEA: Code isInSun()
               * sun.getXYZ();
               * lineManager.create('ref',[sun.sunvar.position.x,sun.sunvar.position.y,sun.sunvar.position.z]);
               */
            }
            uiManagerInstance.lastNextPassCalcSatId = this.selectSatManager_.selectedSat;
            uiManagerInstance.lastNextPassCalcSensorShortName = sensorManagerInstance.currentSensors[0]?.objName ?? '';
          } else if (nextPassElement) {
            nextPassElement.innerHTML = '不可用';
          }
        } catch (e) {
          errorManagerInstance.debug('Error updating satellite info!');
        }
      },
    );

    keepTrackApi.on(
      KeepTrackApiEvents.onWatchlistUpdated,
      (watchlistList: { id: number, inView: boolean }[]) => {
        let isOnList = false;

        watchlistList.forEach(({ id }) => {
          if (id === this.selectSatManager_.selectedSat) {
            isOnList = true;
          }
        });

        const addRemoveWatchlistDom = getEl('sat-add-watchlist');

        if (addRemoveWatchlistDom) {
          if (isOnList) {
            (<HTMLImageElement>getEl('sat-remove-watchlist')).style.display = 'block';
            (<HTMLImageElement>getEl('sat-add-watchlist')).style.display = 'none';
          } else {
            (<HTMLImageElement>getEl('sat-add-watchlist')).style.display = 'block';
            (<HTMLImageElement>getEl('sat-remove-watchlist')).style.display = 'none';
          }
        }
      },
    );

    keepTrackApi.on(KeepTrackApiEvents.selectSatData, this.selectSat_.bind(this));
  }

  private updateSatelliteTearrData_(obj: BaseObject, sensorManagerInstance: SensorManager, timeManagerInstance: TimeManager) {
    const elements = {
      az: getEl('sat-azimuth'),
      el: getEl('sat-elevation'),
      rng: getEl('sat-range'),
      vmag: getEl('sat-vmag'),
      beamwidth: getEl('sat-beamwidth'),
      maxTmx: getEl('sat-maxTmx'),
    };

    if (this.currentTEARR.inView) {
      this.updateSatTearrInFov_(elements, obj, sensorManagerInstance, timeManagerInstance);
    } else {
      this.updateSatTearrOutFov_(elements, sensorManagerInstance);
    }
  }

  private updateSatTearrOutFov_(elements: {
    az: HTMLElement | null; el: HTMLElement | null; rng: HTMLElement | null; vmag: HTMLElement | null; beamwidth: HTMLElement | null;
    maxTmx: HTMLElement | null;
  }, sensorManagerInstance: SensorManager) {
    if (elements.vmag) {
      elements.vmag.innerHTML = '超出视野范围';
    }
    if (elements.az) {
      elements.az.innerHTML = '超出视野范围';
      elements.az.title = `方位角: ${this.currentTEARR.az.toFixed(0)}°`;
    }

    if (elements.el) {
      elements.el.innerHTML = '超出视野范围';
      elements.el.title = `仰角: ${this.currentTEARR.el.toFixed(1)}°`;
    }

    if (elements.rng) {
      elements.rng.innerHTML = '超出视野范围';
      elements.rng.title = `范围: ${this.currentTEARR.rng.toFixed(2)} km`;
    }

    let beamwidthString = '未知';

    if (sensorManagerInstance.currentSensors[0] instanceof RfSensor) {
      beamwidthString = sensorManagerInstance.currentSensors[0]?.beamwidth ? `${sensorManagerInstance.currentSensors[0].beamwidth}°` : '未知';
    }
    if (elements.beamwidth) {
      elements.beamwidth.innerHTML = '超出视野范围';
    }
    if (elements.beamwidth) {
      elements.beamwidth.title = beamwidthString;
    }
    if (elements.maxTmx) {
      elements.maxTmx.innerHTML = '超出视野范围';
    }
  }

  private updateSatTearrInFov_(elements: {
    az: HTMLElement | null; el: HTMLElement | null; rng: HTMLElement | null; vmag: HTMLElement | null; beamwidth: HTMLElement | null;
    maxTmx: HTMLElement | null;
  }, obj: BaseObject, sensorManagerInstance: SensorManager, timeManagerInstance: TimeManager) {
    if (elements.az) {
      elements.az.innerHTML = `${this.currentTEARR.az.toFixed(0)}°`;
    } // Convert to Degrees
    if (elements.el) {
      elements.el.innerHTML = `${this.currentTEARR.el.toFixed(1)}°`;
    }
    if (elements.rng) {
      elements.rng.innerHTML = `${this.currentTEARR.rng.toFixed(2)} km`;
    }
    const sun = keepTrackApi.getScene().sun;

    if (elements.vmag) {
      if (obj.isMissile()) {
        elements.vmag.innerHTML = '不适用';
      } else {
        const sat = obj as DetailedSatellite;

        elements.vmag.innerHTML = SatMath.calculateVisMag(sat, sensorManagerInstance.currentSensors[0], timeManagerInstance.simulationTimeObj, sun).toFixed(2);
      }
    }
    let beamwidthString = '未知';

    if (sensorManagerInstance.currentSensors[0] instanceof RfSensor) {
      beamwidthString = sensorManagerInstance.currentSensors[0].beamwidth
        ? `${(this.currentTEARR.rng * Math.sin(DEG2RAD * sensorManagerInstance.currentSensors[0].beamwidth)).toFixed(2)} km`
        : '未知';
    }
    if (elements.beamwidth) {
      elements.beamwidth.innerHTML = beamwidthString;
    }
    if (elements.maxTmx) {
      // Time for RF to hit target and bounce back
      elements.maxTmx.innerHTML = `${((this.currentTEARR.rng / cKmPerMs) * 2).toFixed(2)} ms`;
    }
  }

  private uiManagerFinal_(): void {
    if (!this.isorbitalDataLoaded_) {
      SatInfoBox.createOrbitalData_();
      this.isorbitalDataLoaded_ = true;
    }
    if (!this.issecondaryDataLoaded_) {
      SatInfoBox.createSecondaryData_();
      this.issecondaryDataLoaded_ = true;
    }

    if (!this.issensorInfoLoaded_) {
      SatInfoBox.createSensorInfo();
      this.issensorInfoLoaded_ = true;
    }

    if (!this.islaunchDataLoaded_) {
      SatInfoBox.createLaunchData_();
      this.islaunchDataLoaded_ = true;
    }

    if (!this.issatMissionDataLoaded_) {
      SatInfoBox.createSatMissionData();
      this.issatMissionDataLoaded_ = true;
    }

    // Now that is is loaded, reset the sizing and location (不强制重置位置)
    SatInfoBox.resetMenuLocation(getEl(SatInfoBox.containerId_), false, false);

    this.addListenerToCollapseElement_(getEl('actions-section-collapse'), getEl('actions-section'), { value: this.isActionsSectionCollapsed_ });
    this.addListenerToCollapseElement_(getEl('identifiers-section-collapse'), getEl('sat-identifier-data'), { value: this.isIdentifiersSectionCollapsed_ });
    this.addListenerToCollapseElement_(getEl('orbit-data-section-collapse'), getEl('orbital-section'), { value: this.isOrbitalSectionCollapsed_ });
    this.addListenerToCollapseElement_(getEl('secondary-sat-info-collapse'), getEl('secondary-sat-info'), { value: this.isSecondaryDataSectionCollapsed_ });
    this.addListenerToCollapseElement_(getEl('sensor-data-section-collapse'), getEl('sensor-sat-info'), { value: this.isSensorDataSectionCollapsed_ });
    this.addListenerToCollapseElement_(getEl('object-data-section-collapse'), getEl('launch-section'), { value: this.isObjectDataSectionCollapsed_ });
    this.addListenerToCollapseElement_(getEl('mission-section-collapse'), getEl('sat-mission-data'), { value: this.isMissionSectionCollapsed_ });
  }

  private addListenerToCollapseElement_(collapseEl: HTMLElement | null, section: HTMLElement | null, isCollapsedRef: { value: boolean }): void {
    if (!collapseEl || !section) {
      return;
    }

    // 创建切换函数
    const toggleSection = () => {
      section.classList.toggle('collapsed');
      collapseEl.classList.toggle('collapse-closed');
      isCollapsedRef.value = !isCollapsedRef.value;

      if (collapseEl.classList.contains('collapse-closed')) {
        collapseEl.textContent = 'expand_more';
      } else {
        collapseEl.textContent = 'expand_less';
      }
    };

    // 为箭头图标添加点击事件
    collapseEl.addEventListener('click', (e) => {
      e.stopPropagation(); // 防止事件冒泡
      toggleSection();
    });

    // 为整个菜单标题添加点击事件
    const headerElement = collapseEl.parentElement;
    if (headerElement && headerElement.classList.contains('sat-info-section-header')) {
      headerElement.style.cursor = 'pointer';
      headerElement.addEventListener('click', toggleSection);
    }
  }

  hide(): void {
    hideEl(SatInfoBox.containerId_);
    this.isVisible_ = false;
  }

  show(): void {
    if (this.selectSatManager_.primarySatObj.id === -1) {
      return;
    }

    showEl(SatInfoBox.containerId_);
    this.isVisible_ = true;
  }

  orbitalData(sat: DetailedSatellite): void {
    // Only show orbital data if it is available
    if (sat === null || typeof sat === 'undefined') {
      return;
    }

    this.updateOrbitData_(sat);
  }

  private nearObjectsLinkClick_(distance: number = 100): void {
    keepTrackApi.getSoundManager().play(SoundNames.CLICK);
    const catalogManagerInstance = keepTrackApi.getCatalogManager();

    if (this.selectSatManager_.selectedSat === -1) {
      return;
    }
    const selectedSatelliteId = this.selectSatManager_.selectedSat;
    const sat = catalogManagerInstance.getObject(selectedSatelliteId, GetSatType.POSITION_ONLY);

    if (!sat) {
      errorManagerInstance.warn('未选择卫星');

      return;
    }

    const SccNums: string[] = [];
    let pos = sat.position;
    const posXmin = pos.x - distance;
    const posXmax = pos.x + distance;
    const posYmin = pos.y - distance;
    const posYmax = pos.y + distance;
    const posZmin = pos.z - distance;
    const posZmax = pos.z + distance;

    (<HTMLInputElement>getEl('search')).value = '';
    for (let i = 0; i < catalogManagerInstance.numSatellites; i++) {
      const satelliteAtIndex = catalogManagerInstance.getObject(i, GetSatType.POSITION_ONLY);

      if (!satelliteAtIndex) {
        errorManagerInstance.debug(`No satellite at index ${i}`);
        continue;
      }

      pos = satelliteAtIndex.position;
      if (pos.x < posXmax && pos.x > posXmin && pos.y < posYmax && pos.y > posYmin && pos.z < posZmax && pos.z > posZmin) {
        const sat = catalogManagerInstance.getSat(i, GetSatType.EXTRA_ONLY);

        if (sat) {
          SccNums.push(sat.sccNum);
        }
      }
    }

    for (let i = 0; i < SccNums.length; i++) {
      (<HTMLInputElement>getEl('search')).value += i < SccNums.length - 1 ? `${SccNums[i]},` : SccNums[i];
    }

    keepTrackApi.getUiManager().doSearch((<HTMLInputElement>getEl('search')).value.toString());
  }

  private nearOrbitsLink_() {
    keepTrackApi.getSoundManager().play(SoundNames.CLICK);
    const catalogManagerInstance = keepTrackApi.getCatalogManager();
    const selectedSatellite = catalogManagerInstance.getSat(this.selectSatManager_.selectedSat);

    if (!selectedSatellite) {
      errorManagerInstance.warn('未选择卫星');

      return;
    }

    const nearbyObjects = CatalogSearch.findObjsByOrbit(catalogManagerInstance.getSats(), selectedSatellite);
    const searchStr = SearchManager.doArraySearch(catalogManagerInstance, nearbyObjects);

    keepTrackApi.getUiManager().searchManager.doSearch(searchStr, false);
  }

  private allObjectsLink_(): void {
    keepTrackApi.getSoundManager().play(SoundNames.CLICK);
    const catalogManagerInstance = keepTrackApi.getCatalogManager();
    const selectedSatelliteData = catalogManagerInstance.getSat(this.selectSatManager_.selectedSat, GetSatType.EXTRA_ONLY);

    if (!selectedSatelliteData) {
      return;
    }
    const searchStr = selectedSatelliteData.intlDes.slice(0, 8);

    keepTrackApi.getUiManager().doSearch(searchStr);
    (<HTMLInputElement>getEl('search')).value = searchStr;
  }

  private drawLineToSun_() {
    keepTrackApi.getSoundManager().play(SoundNames.CLICK);
    lineManagerInstance.createSat2Sun(this.selectSatManager_.primarySatObj);
  }

  private drawRicLines_() {
    keepTrackApi.getSoundManager().play(SoundNames.CLICK);
    lineManagerInstance.createSatRicFrame(this.selectSatManager_.primarySatObj);
  }

  private drawLineToEarth_() {
    keepTrackApi.getSoundManager().play(SoundNames.CLICK);
    lineManagerInstance.createSatToRef(this.selectSatManager_.primarySatObj, [0, 0, 0], LineColors.PURPLE);
  }

  private drawLineToSat_() {
    keepTrackApi.getSoundManager().play(SoundNames.CLICK);
    if (this.selectSatManager_.secondarySat === -1) {
      keepTrackApi.getUiManager().toast('未选择从卫星', ToastMsgType.caution);

      return;
    }

    lineManagerInstance.createObjToObj(this.selectSatManager_.primarySatObj, this.selectSatManager_.secondarySatObj, LineColors.BLUE);
  }

  private updateOrbitData_(sat: DetailedSatellite): void {
    if (sat.isSatellite()) {
      getEl('sat-apogee')!.innerHTML = `${sat.apogee.toFixed(0)} km`;
      getEl('sat-perigee')!.innerHTML = `${sat.perigee.toFixed(0)} km`;
      getEl('sat-inclination')!.innerHTML = `${sat.inclination.toFixed(2)}°`;
      getEl('sat-eccentricity')!.innerHTML = sat.eccentricity.toFixed(3);
      getEl('sat-raan')!.innerHTML = `${sat.rightAscension.toFixed(2)}°`;
      getEl('sat-argPe')!.innerHTML = `${sat.argOfPerigee.toFixed(2)}°`;

      // 根据新的条件决定是否显示历史经度选项（倾角≤20度、非JSC名字、近地点>34000km、远地点<37000km）
      const historyLongitudeRow = getEl('sat-history-longitude-row');
      if (historyLongitudeRow) {
        // 检查所有条件
        const hasNoradId = sat.sccNum && sat.sccNum.trim() !== '';
        const isNotJSC = !sat.name || !sat.name.toUpperCase().startsWith('JSC');
        const isValidInclination = typeof sat.inclination === 'number' && !isNaN(sat.inclination) && sat.inclination <= 20;
        const isValidPerigee = typeof sat.perigee === 'number' && !isNaN(sat.perigee) && sat.perigee > 34000;
        const isValidApogee = typeof sat.apogee === 'number' && !isNaN(sat.apogee) && sat.apogee < 37000;

        if (hasNoradId && isNotJSC && isValidInclination && isValidPerigee && isValidApogee) {
          historyLongitudeRow.style.display = 'flex';
        } else {
          historyLongitudeRow.style.display = 'none';
        }
      }

      const periodDom = getEl('sat-period')!;

      periodDom.innerHTML = `${sat.period.toFixed(2)} min`;
      // 🔥 移除tooltip相关属性
      delete periodDom.dataset.position;
      delete periodDom.dataset.delay;
      delete periodDom.dataset.tooltip;

      const now: Date | number | string = new Date();
      const daysold = sat.ageOfElset(now);
      const age = daysold >= 1 ? daysold : daysold * 24;
      const units = daysold >= 1 ? '天' : '小时';
      const elsetAgeDom = getEl('sat-elset-age')!;

      elsetAgeDom.innerHTML = `${age.toFixed(2)} ${units}`;

      // 🔥 根据轨道纪元时间设置文字颜色
      const textColor = SatInfoBox.getElsetAgeColor_(daysold);
      elsetAgeDom.style.color = textColor;

      SatInfoBox.updateConfidenceDom_(sat);

      // 🔥 移除tooltip相关属性
      delete elsetAgeDom.dataset.position;
      delete elsetAgeDom.dataset.delay;
      delete elsetAgeDom.dataset.tooltip;
    }

    if (!this.isTopLinkEventListenersAdded_) {
      getEl('sat-info-close-btn')?.addEventListener('click', this.hide.bind(this));
      getEl('sat-add-watchlist')?.addEventListener('click', this.addRemoveWatchlist_.bind(this));
      getEl('sat-remove-watchlist')?.addEventListener('click', this.addRemoveWatchlist_.bind(this));
      getEl('all-objects-link')?.addEventListener('click', this.allObjectsLink_.bind(this));
      getEl('near-orbits-link')?.addEventListener('click', this.nearOrbitsLink_.bind(this));
      getEl('near-objects-link1')?.addEventListener('click', () => this.nearObjectsLinkClick_(100));
      getEl('near-objects-link2')?.addEventListener('click', () => this.nearObjectsLinkClick_(200));
      getEl('near-objects-link4')?.addEventListener('click', () => this.nearObjectsLinkClick_(400));
      getEl('sun-angle-link')?.addEventListener('click', this.drawLineToSun_.bind(this));
      getEl('ric-angle-link')?.addEventListener('click', this.drawRicLines_.bind(this));
      getEl('nadir-angle-link')?.addEventListener('click', this.drawLineToEarth_.bind(this));
      getEl('sec-angle-link')?.addEventListener('click', this.drawLineToSat_.bind(this));
      getEl('sat-history-track')?.addEventListener('click', this.openHistoryTrack_.bind(this));
      getEl('sat-history-longitude')?.addEventListener('click', this.openHistoryLongitude_.bind(this));
      this.isTopLinkEventListenersAdded_ = true;
    }
  }

  private addRemoveWatchlist_() {
    const watchlistPlugin = keepTrackApi.getPlugin(WatchlistPlugin);

    if (watchlistPlugin) {
      const id = this.selectSatManager_.selectedSat;

      keepTrackApi.getSoundManager().play(SoundNames.CLICK);
      if (watchlistPlugin.isOnWatchlist(id)) {
        watchlistPlugin.removeSat(id);
      } else {
        watchlistPlugin.addSat(id);
      }
    }
  }

  /**
   * 🔥 根据轨道纪元时间天数返回对应的颜色
   * @param daysOld - 轨道纪元时间距今天数
   * @returns CSS颜色字符串
   */
  private static getElsetAgeColor_(daysOld: number): string {
    // 根据GP Age颜色方案的逻辑设置颜色
    if (daysOld < 0.5) {
      return '#00ff00'; // 绿色 - 非常新
    }
    if (daysOld >= 0.5 && daysOld < 1.0) {
      return '#99fe00'; // 黄绿色 - 较新
    }
    if (daysOld >= 1.0 && daysOld < 1.5) {
      return '#ccff00'; // 浅黄色 - 一般
    }
    if (daysOld >= 1.5 && daysOld < 2.0) {
      return '#ffff00'; // 黄色 - 稍旧
    }
    if (daysOld >= 2.0 && daysOld < 2.5) {
      return '#ffcc00'; // 橙黄色 - 较旧
    }
    if (daysOld >= 2.5 && daysOld < 3.0) {
      return '#ff9900'; // 橙色 - 旧
    }
    // 超过3天
    return '#ff0000'; // 红色 - 很旧
  }

  private static updateConfidenceDom_(sat: DetailedSatellite) {
    let color = '';
    let text = '';

    const confidenceDom = getEl('sat-confidence');

    if (confidenceDom) {
      // We encode confidence score in the 65th character in the TLE line 1
      const confidenceScore = parseInt(sat.tle1.substring(64, 65)) || 0;

      if (settingsManager.dataSources.externalTLEsOnly) {
        text = '第三方';
        color = 'gray';
      } else if (confidenceScore >= 7) {
        text = `高 (${confidenceScore})`;
        color = 'green';
      } else if (confidenceScore >= 4) {
        text = `中 (${confidenceScore})`;
        color = 'orange';
      } else {
        text = `低 (${confidenceScore})`;
        color = 'red';
      }

      confidenceDom.innerHTML = text;
      confidenceDom.style.color = color;
      // 🔥 确保背景颜色与其他元素一致
      confidenceDom.style.backgroundColor = 'transparent';
      confidenceDom.style.background = 'transparent';
    }
  }

  private static updateObjectData_(obj: BaseObject): void {
    if (!obj || obj.isStatic() || obj.isSensor()) {
      return;
    }

    const isHasAltName: boolean = !!((obj as DetailedSatellite)?.altName && (obj as DetailedSatellite).altName !== '');
    const isHasAltId: boolean = !!((obj as DetailedSatellite)?.altId && (obj as DetailedSatellite).altId !== '');

    getEl('sat-info-title-name')!.innerHTML = obj.name;
    // 自动调整卫星名字字体大小
    SatInfoBox.adjustSatelliteNameFontSize_();

    if (obj.isSatellite() && (obj as DetailedSatellite).sccNum5 === '25544') {
      getEl('sat-infobox-fi')!.classList.value = 'fi fi-iss';
    } else {
      const flagClass = country2flagIcon((obj as DetailedSatellite).country);
      const fullClass = `fi ${flagClass}`;
      getEl('sat-infobox-fi')!.classList.value = fullClass;
    }

    if (isHasAltName) {
      showEl(getEl('sat-alt-name')!.parentElement!, 'flex');
      getEl('sat-alt-name')!.innerHTML = (obj as DetailedSatellite).altName;
    } else {
      hideEl(getEl('sat-alt-name')!.parentElement!);
    }

    if (isHasAltId) {
      showEl(getEl('sat-alt-id')!.parentElement!, 'flex');
      getEl('sat-alt-id')!.innerHTML = (obj as DetailedSatellite).altId;
    } else {
      hideEl(getEl('sat-alt-id')!.parentElement!);
    }

    const watchlistPlugin = keepTrackApi.getPlugin(WatchlistPlugin);

    if (watchlistPlugin) {
      if (watchlistPlugin.isOnWatchlist(obj.id)) {
        getEl('sat-remove-watchlist')!.style.display = 'block';
        getEl('sat-add-watchlist')!.style.display = 'none';
      } else {
        getEl('sat-add-watchlist')!.style.display = 'block';
        getEl('sat-remove-watchlist')!.style.display = 'none';
      }
    } else {
      getEl('sat-add-watchlist')!.style.display = 'none';
      getEl('sat-remove-watchlist')!.style.display = 'none';
    }

    SatInfoBox.updateSatType_(obj);
    SatInfoBox.updateSatStatus_(obj);

    /*
     * TODO:
     * getEl('edit-satinfo-link').innerHTML = "<a class='iframe' href='editor.htm?scc=" + sat.sccNum + "&popup=true'>Edit Satellite Info</a>";
     */

    if (obj.isMissile()) {
      getEl('sat-intl-des')!.innerHTML = '不适用';
      getEl('sat-objnum')!.innerHTML = '不适用';
      getEl('sat-source')!.innerHTML = '不适用';
    } else {
      const sat = obj as DetailedSatellite;

      getEl('sat-intl-des')!.innerHTML = sat.intlDes === 'none' ? '不适用' : sat.intlDes;
      if (sat.source && sat.source === CatalogSource.VIMPEL) {
        getEl('sat-objnum')!.innerHTML = '不适用';
        getEl('sat-intl-des')!.innerHTML = '不适用';
      } else {
        getEl('sat-objnum')!.innerHTML = sat.sccNum;
        // satObjNumDom.setAttribute('data-tooltip', `${FormatTle.convert6DigitToA5(sat.sccNum)}`);
      }

      getEl('sat-source')!.innerHTML = sat.source || CatalogSource.CELESTRAK;
      SatInfoBox.updateRcsData_(sat);
    }
  }

  private static updateLaunchData_(obj?: BaseObject) {
    if (!obj || (!obj.isSatellite() && !obj.isMissile())) {
      return;
    }
    const satMisl = obj as DetailedSatellite | MissileObject;

    SatInfoBox.updateCountryCorrelationTable_(satMisl);
    SatInfoBox.updateLaunchSiteCorrelationTable_(satMisl);

    SatInfoBox.updateLaunchVehicleCorrelationTable_(obj);

    if (satMisl.isMissile()) {
      return;
    }

    const sat = satMisl as DetailedSatellite;

    getEl('sat-configuration')!.innerHTML = sat.configuration !== '' ? sat.configuration : '未知';
  }

  private static updateLaunchVehicleCorrelationTable_(obj: BaseObject) {
    let satVehicleDom = getEl('sat-vehicle');

    if (!satVehicleDom) {
      errorManagerInstance.debug('sat-vehicle element not found');

      return;
    }

    const tempEl = satVehicleDom.cloneNode(true);

    if (!satVehicleDom.parentNode) {
      errorManagerInstance.debug('sat-vehicle element parent not found');

      return;
    }

    // Remove any existing event listeners
    satVehicleDom.parentNode.replaceChild(tempEl, satVehicleDom);

    // Update links
    satVehicleDom = getEl('sat-vehicle');

    if (!satVehicleDom) {
      errorManagerInstance.debug('sat-vehicle element not found');

      return;
    }

    if (obj.isMissile()) {
      const missile = obj as MissileObject;

      missile.launchVehicle = missile.desc.split('(')[1].split(')')[0]; // Remove the () from the booster type
      satVehicleDom.innerHTML = missile.launchVehicle;
    } else {
      const sat = obj as DetailedSatellite;

      satVehicleDom.innerHTML = sat.launchVehicle; // Set to JSON record
      if (sat.launchVehicle === 'U') {
        satVehicleDom.innerHTML = '未知';
      } // Replace with Unknown if necessary
      const satLvString = StringExtractor.extractLiftVehicle(sat.launchVehicle); // Replace with link if available

      satVehicleDom.innerHTML = satLvString;

      if (satLvString.includes('http')) {
        satVehicleDom.classList.add('pointable');
        satVehicleDom.addEventListener('click', (e) => {
          e.preventDefault();
          openColorbox((<HTMLAnchorElement>satVehicleDom.firstChild).href);
        });
      } else {
        satVehicleDom.classList.remove('pointable');
      }
    }
  }

  private static updateLaunchSiteCorrelationTable_(obj: BaseObject) {
    let siteArr: string[] = [];
    const site = {
      site: 'Unknown',
      launchPad: 'Unknown',
      wikiUrl: null as string | null,
    };
    let missileOrigin: string;

    if (obj.isMissile()) {
      const misl = obj as MissileObject;

      siteArr = misl.desc.split('(');
      missileOrigin = siteArr[0].slice(0, siteArr[0].length - 1);

      site.site = missileOrigin;
      site.launchPad = 'Unknown';
    } else {
      const sat = obj as DetailedSatellite;

      // Enhanced Catalog uses full names
      if (sat.launchSite?.length > 6) {
        site.site = sat.launchSite;
      } else {
        const launchData = StringExtractor.extractLaunchSite(sat.launchSite);

        site.site = launchData.site;
        site.wikiUrl = launchData.wikiUrl;
      }

      site.launchPad = sat.launchPad;
    }

    const launchSiteElement = getEl('sat-launchSite');
    const launchPadElement = getEl('sat-launchPad');

    if (!launchSiteElement || !launchPadElement) {
      errorManagerInstance.debug('sat-launchSite or sat-launchPad element not found');

      return;
    }

    launchPadElement.innerHTML = site.launchPad;

    if (site.wikiUrl) {
      launchSiteElement.innerHTML = `<a class="iframe" href="${site.wikiUrl}">${site.site}</a>`;
      launchSiteElement.classList.add('pointable');
      launchSiteElement.addEventListener('click', (e) => {
        e.preventDefault();
        openColorbox((<HTMLAnchorElement>launchSiteElement.firstChild).href);
      });
    } else {
      launchSiteElement.innerHTML = site.site;
      launchSiteElement.classList.remove('pointable');
    }

  }

  private static updateCountryCorrelationTable_(obj: DetailedSatellite | MissileObject) {
    const satCountryElement = getEl('sat-country');

    if (!satCountryElement) {
      errorManagerInstance.debug('sat-country element not found');

      return;
    }

    if (obj.country?.length > 4) {
      satCountryElement.innerHTML = obj.country;
    } else {
      const country = StringExtractor.extractCountry(obj.country);

      satCountryElement.innerHTML = country;
    }
  }

  private static createLaunchData_() {
    getEl('sat-infobox-content')?.insertAdjacentHTML(
      'beforeend',
      keepTrackApi.html`
          <div id="launch-section">
            <div class="sat-info-section-header">
              目标数据
              <span id="object-data-section-collapse" class="section-collapse material-icons" style="position: absolute; right: 0;">expand_less</span>
            </div>
            <div class="sat-info-row">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="目标类型">类型</div>
              <div class="sat-info-value" id="sat-type">载荷</div>
            </div>
            <div class="sat-info-row">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="Type of Object">状态</div>
              <div class="sat-info-value" id="sat-status">STATUS</div>
            </div>
            <div class="sat-info-row sat-only-info">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="卫星拥有国家">国家</div>
              <div class="sat-info-value" id="sat-country">COUNTRY</div>
            </div>
            <div class="sat-info-row" id="sat-site-row">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="卫星发射场地">发射场</div>
              <div class="sat-info-value">
                <div id="sat-launchSite">发射场</div>
                <div id="sat-launchPad">发射机位</div>
              </div>
              </div>
            <div class="sat-info-row">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="发射火箭">火箭</div>
              <div class="sat-info-value pointable" id="sat-vehicle">VEHICLE</div>
            </div>
            <div class="sat-info-row sat-only-info">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="火箭配置">
                配置
              </div>
              <div class="sat-info-value" id="sat-configuration">
                无数据
              </div>
            </div>
            <div class="sat-info-row sat-only-info">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="RCS反射面积">
                RCS值
              </div>
              <div class="sat-info-value" data-position="top" data-delay="50" id="sat-rcs">无数据</div>
            </div>
            <div class="sat-info-row sat-only-info">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="标准星等—数值越小，亮度越高">
                星等
              </div>
              <div class="sat-info-value" id="sat-stdmag">
                无数据
              </div>
            </div>
          </div>
          `,
    );
  }

  private static createSecondaryData_() {
    getEl('sat-infobox-content')?.insertAdjacentHTML(
      'beforeend',
      keepTrackApi.html`
          <div id="secondary-sat-info">
            <div class="sat-info-section-header">
               从卫星
              <span id="secondary-sat-info-collapse" class="section-collapse material-icons" style="position: absolute; right: 0;">expand_less</span>
            </div>
            <div class="sat-info-row">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="与次卫星的线性距离">
                线性距离
              </div>
              <div class="sat-info-value" id="sat-sec-dist">xxxx km</div>
            </div>
            <div class="sat-info-row">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="径向距离">
                径向距离
              </div>
              <div class="sat-info-value" id="sat-sec-rad">XX deg</div>
            </div>
            <div class="sat-info-row">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="与辅助卫星的轨道内距离">
                轨道距离
              </div>
              <div class="sat-info-value" id="sat-sec-intrack">XX deg</div>
            </div>
            <div class="sat-info-row">
              <div class="sat-info-key" data-position="top" data-delay="50"
                data-tooltip="与次卫星的跨轨距离">
                跨轨距离
              </div>
              <div class="sat-info-value" id="sat-sec-crosstrack">xxxx km</div>
            </div>
          </div>
          `,
    );
  }

  private static createOrbitalData_() {
    getEl('ui-wrapper')?.insertAdjacentHTML(
      'beforeend',
      keepTrackApi.html`
      <div id="sat-infobox" class="text-select satinfo-fixed start-hidden">
        <div id="sat-info-header">
          <div id="sat-info-title" class="center-text sat-info-section-header" style="position: relative;">
            <span id="sat-info-close-btn" class="material-icons" style="position: absolute; left: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #fff; font-size: 20px; z-index: 10;" title="关闭信息窗口">close</span>
            <img id="sat-add-watchlist" src="${bookmarkAddPng}" style="position: absolute; left: 35px; top: 50%; transform: translateY(-50%); cursor: pointer;"/>
            <img id="sat-remove-watchlist" src="${bookmarkRemovePng}" style="position: absolute; left: 35px; top: 50%; transform: translateY(-50%); cursor: pointer;"/>
            <span id="sat-info-title-name" style="position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%);">
              这是一个标题
            </span>
            <span id="sat-infobox-fi" class="fi" style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%);"></span>
          </div>
        </div>
        <div id="sat-infobox-content">
          <div id="actions-section" class="collapsed">
            <div class="sat-info-section-header">
              在轨操作
              <span id="actions-section-collapse" class="section-collapse collapse-closed material-icons" style="position: absolute; right: 0;">expand_more</span>
            </div>
            ${getEl('search') ? SatInfoBox.createSearchLinks_() : ''}
            <div id="draw-line-links">
            <div id="sun-angle-link" class="link sat-infobox-links menu-selectable" data-position="top" data-delay="50"
            data-tooltip="可视化与太阳的角度">添加太阳连线</div>
            <div id="ric-angle-link" class="link sat-infobox-links menu-selectable" data-position="top" data-delay="50"
            data-tooltip="可视化与RIC的角度">添加RIC连线</div>
            <div id="nadir-angle-link" class="link sat-infobox-links menu-selectable" data-position="top" data-delay="50"
            data-tooltip="可视化与地球的角度">添加垂地连线</div>
            <div id="sec-angle-link" class="link sat-infobox-links menu-selectable" data-position="top" data-delay="50"
            data-tooltip="可视化与辅助卫星的角度">添加到从卫星连线</div>
          </div>
          </div>
          <div id="sat-identifier-data">
          <div class="sat-info-section-header">
            卫星标识
            <span id="identifiers-section-collapse" class="section-collapse material-icons" style="position: absolute; right: 0;">expand_less</span>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
            data-tooltip="卫星国际统一编号">COSPAR编号</div>
            <div class="sat-info-value" id="sat-intl-des">xxxx-xxxA</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
            data-tooltip="美太空NORAD编号">NORAD编号</div>
            <div class="sat-info-value" id="sat-objnum" data-position="top" data-delay="50">99999</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key">备用名字</div>
            <div class="sat-info-value" id="sat-alt-name">未知</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key">备用编号</div>
            <div class="sat-info-value" id="sat-alt-id">99999</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key">来源</div>
            <div class="sat-info-value" id="sat-source">USSF</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key">可信度</div>
            <div class="sat-info-value" id="sat-confidence">高</div>
          </div>
        </div>
        <div id="orbital-section">
          <div class="sat-info-section-header">
            轨道参数
            <span id="orbit-data-section-collapse" class="section-collapse material-icons" style="position: absolute; right: 0;">expand_less</span>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="轨道中的最高的点">
              远地点
            </div>
            <div class="sat-info-value" id="sat-apogee">xxx km</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="轨道中的最低点">
              近地点
            </div>
            <div class="sat-info-value" id="sat-perigee">xxx km</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="从上升节点上的赤道测量的角度"">
              轨道倾角
            </div>
            <div class="sat-info-value" id="sat-inclination">xxx.xx</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="轨道的圆度（0是标准的圆）">
              偏心率
            </div>
            <div class="sat-info-value" id="sat-eccentricity">x.xx</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="在赤道之上的地方">
              升交点赤经
            </div>
            <div class="sat-info-value" id="sat-raan">x.xx</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="轨道的最低部分在哪里">
              近地点幅角
            </div>
            <div class="sat-info-value" id="sat-argPe">x.xx</div>
          </div>
          <div class="sat-info-row">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="当前地球纬度">
              星下点纬度
            </div>
            <div class="sat-info-value" id="sat-latitude">x.xx</div>
          </div>
          <div class="sat-info-row">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="当前地球经度">
              星下点经度
            </div>
            <div class="sat-info-value" id="sat-longitude">x.xx</div>
          </div>
          <div class="sat-info-row">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="当前海平面以上高度">
              轨道高度
            </div>
            <div class="sat-info-value" id="sat-altitude">xxx km</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="绕地球一圈所需时间">
              轨道周期
            </div>
            <div class="sat-info-value" id="sat-period">xxx 分钟</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="卫星的当前速度（越靠近地球越高）">
              轨道速度
            </div>
            <div class="sat-info-value" id="sat-velocity">xxx km/s</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="自官方轨道计算以来的时间（时间越长，精度越低）">
              轨道纪元时间
            </div>
            <div class="sat-info-value" id="sat-elset-age">xxx.xxxx</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="径向误差（米）">
              径向误差
            </div>
            <div class="sat-info-value" id="sat-uncertainty-radial">xxx.xxxx</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="轨道误差（米）">
              轨道误差
            </div>
            <div class="sat-info-value" id="sat-uncertainty-intrack">xxx.xxxx</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="交叉误差（米">
              交叉误差
            </div>
            <div class="sat-info-value" id="sat-uncertainty-crosstrack">xxx.xxxx</div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="查看该卫星的历史轨道数据">
              历史轨道
            </div>
            <div class="sat-info-value" id="sat-history-track" style="color: #ff5252; cursor: pointer;">查看历史轨道</div>
          </div>
          <div class="sat-info-row sat-only-info" id="sat-history-longitude-row" style="display: none;">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="查看该卫星的历史经度变化数据（仅限非JSC名字的GEO卫星：倾角≤20度、近地点>34000km、远地点<37000km）">
              历史经度
            </div>
            <div class="sat-info-value" id="sat-history-longitude" style="color: #ff5252; cursor: pointer;">查看历史经度</div>
          </div>
        </div>
        </div>
      </div>
    `,
    );

    // Create a Sat Info Box Initializing Script
    if (!settingsManager.isMobileModeEnabled) {
      const satInfoElement = getEl(SatInfoBox.containerId_);



      if (satInfoElement) {


        // 设置初始位置（仅在首次显示时）
        if (!satInfoElement.hasAttribute('data-position-set')) {
          satInfoElement.style.setProperty('position', 'absolute', 'important');
          satInfoElement.style.setProperty('right', '2px', 'important');
          satInfoElement.style.setProperty('top', '80px', 'important');
          satInfoElement.style.setProperty('left', 'auto', 'important');
          satInfoElement.style.setProperty('bottom', 'auto', 'important');
          satInfoElement.style.setProperty('transform', 'none', 'important');
          satInfoElement.setAttribute('data-position-set', 'true');


        }

        try {
          // 🔥 在初始化Draggabilly之前设置位置，并在初始化后再次设置
          const draggie = new Draggabilly(satInfoElement, {
            containment: false, // 移除容器限制
            handle: '#sat-info-title', // 🔥 只有标题栏可以拖动
          });



          // 🔥 初始化tooltip - 使用AutoInit自动初始化所有Materialize组件
          try {
            if (window.M && window.M.AutoInit) {
              window.M.AutoInit();
            }
          } catch (error) {
            console.warn('Materialize组件初始化失败:', error);
          }

          draggie.on('dragStart', () => {
            const satInfoBoxElement = getEl(SatInfoBox.containerId_)!;

            // 位置已经预设好了，只需要设置其他样式
            satInfoBoxElement.style.height = 'fit-content';
            satInfoBoxElement.style.maxHeight = '80%';

            document.documentElement.style.setProperty('--search-box-bottom', '0px');
            satInfoBoxElement.classList.remove('satinfo-fixed');

            getEl('search-results')!.style.maxHeight = '80%';
          });



          draggie.on('dragMove', () => {
            // 拖动过程中的处理逻辑（如果需要的话）
          });

          draggie.on('dragEnd', () => {
            // 拖动结束后的处理逻辑（如果需要的话）
          });

        } catch (error) {
          console.error('Draggabilly创建失败:', error);
        }
      }
    }

    // If right click kill and reinit
    const satInfobox = getEl(SatInfoBox.containerId_)!;

    satInfobox.addEventListener('mousedown', (e: MouseEvent) => {
      if (e.button === 2) {
        // 右键点击时强制重置位置
        SatInfoBox.resetMenuLocation(satInfobox, true, true);
        getEl('search-results')!.style.maxHeight = '';
      }
    });
  }

  private static createSearchLinks_(): string {
    return keepTrackApi.html`
    <div id="search-links">
      <div id="all-objects-link" class="link sat-infobox-links menu-selectable sat-only-info" data-position="top" data-delay="50"
      data-tooltip="查找相关对象">查找同批次发射目标</div>
      <div id="near-orbits-link" class="link sat-infobox-links menu-selectable sat-only-info" data-position="top" data-delay="50"
      data-tooltip="在轨道平面中查找物体">查找所有靠近该轨道的目标</div>
      <div id="near-objects-link1" class="link sat-infobox-links menu-selectable" data-position="top" data-delay="50"
      data-tooltip="查找附近的物体">查找100km内的目标</div>
      <div id="near-objects-link2" class="link sat-infobox-links menu-selectable" data-position="top" data-delay="50"
      data-tooltip="查找附近的物体">查找200km内的目标</div>
      <div id="near-objects-link4" class="link sat-infobox-links menu-selectable" data-position="top" data-delay="50"
      data-tooltip="查找附近的物体">查找400km内的目标</div>
    </div>`;
  }

















  static resetMenuLocation(satInfoboxDom: HTMLElement | null, isShow = true, forceReset = false) {
    if (!satInfoboxDom) {
      return;
    }

    satInfoboxDom.classList.remove('satinfo-fixed');
    satInfoboxDom.style.position = 'absolute';

    // 只在强制重置或首次显示时设置位置
    if (forceReset || !satInfoboxDom.hasAttribute('data-position-set')) {
      satInfoboxDom.style.top = '8%';
      satInfoboxDom.style.right = '2px';
      satInfoboxDom.style.left = 'auto';
      satInfoboxDom.style.transform = 'none';
      satInfoboxDom.setAttribute('data-position-set', 'true');


    }

    // 设置透明背景样式（与底部菜单相同）
    satInfoboxDom.style.background = 'transparent';
    satInfoboxDom.style.backgroundColor = 'transparent';
    satInfoboxDom.style.backgroundImage = 'none';

    if (isShow) {
      satInfoboxDom.style.display = 'block';
    }

    // 🔥 使用requestAnimationFrame批量计算，避免重复重排
    requestAnimationFrame(() => {
      const searchBoxHeight = satInfoboxDom?.getBoundingClientRect().height ?? 0;
      const bottomMenuTopVar = parseInt(document.documentElement.style.getPropertyValue('--bottom-menu-top')) || 0;

      document.documentElement.style.setProperty('--search-box-bottom', `${searchBoxHeight + bottomMenuTopVar}px`);
    });
  }

  private static updateSatType_(obj: BaseObject) {
    const satTypeElement = getEl('sat-type');

    if (!satTypeElement) {
      errorManagerInstance.debug('sat-type element not found');

      return;
    }

    switch (obj.type) {
      case SpaceObjectType.UNKNOWN:
        satTypeElement.innerHTML = '待确认';
        break;
      case SpaceObjectType.PAYLOAD:
        satTypeElement.innerHTML = '载荷';
        break;
      case SpaceObjectType.ROCKET_BODY:
        satTypeElement.innerHTML = '箭体';
        break;
      case SpaceObjectType.DEBRIS:
        satTypeElement.innerHTML = '碎片';
        break;
      case SpaceObjectType.SPECIAL:
        satTypeElement.innerHTML = '专用';
        break;
      default:
        if (obj.isMissile()) {
          satTypeElement.innerHTML = '弹道导弹';
        }
    }
  }

  private static updateSatStatus_(obj: BaseObject) {
    const satStatusElement = getEl('sat-status');
    const satStatusParentElement = satStatusElement?.parentElement;

    if (!satStatusElement) {
      errorManagerInstance.debug('sat-status element not found');

      return;
    }

    if (!satStatusParentElement) {
      errorManagerInstance.debug('sat-status parent element not found');

      return;
    }

    if (obj.type !== SpaceObjectType.PAYLOAD) {
      satStatusParentElement.style.display = 'none';

      return;
    }
    satStatusParentElement.style.display = 'flex';

    const sat = obj as DetailedSatellite;

    switch (sat.status) {
      case PayloadStatus.OPERATIONAL:
        satStatusElement.innerHTML = '运营';
        break;
      case PayloadStatus.NONOPERATIONAL:
        satStatusElement.innerHTML = '非运营';
        break;
      case PayloadStatus.PARTIALLY_OPERATIONAL:
        satStatusElement.innerHTML = '部分运营';
        break;
      case PayloadStatus.EXTENDED_MISSION:
        satStatusElement.innerHTML = '扩展任务';
        break;
      case PayloadStatus.BACKUP_STANDBY:
        satStatusElement.innerHTML = '备份备用';
        break;
      case PayloadStatus.SPARE:
        satStatusElement.innerHTML = '空闲的';
        break;
      case PayloadStatus.UNKNOWN:
      default:
        satStatusElement.innerHTML = '未知';
    }
  }

  private static updateRcsData_(sat: DetailedSatellite) {
    const satRcsEl = getEl('sat-rcs');

    if (!satRcsEl) {
      errorManagerInstance.debug('sat-rcs element not found');

      return;
    }

    if ((sat.rcs === null || typeof sat.rcs === 'undefined')) {
      const estRcs = SatMath.estimateRcsUsingHistoricalData(sat);

      if (estRcs !== null) {
        satRcsEl.innerHTML = `H-Est ${estRcs.toFixed(4)} m<sup>2</sup>`;
        satRcsEl.setAttribute('data-tooltip', `${SatMath.mag2db(estRcs).toFixed(2)} dBsm (Historical Estimate)`);
      } else if (sat.length && sat.diameter && sat.span && sat.shape) {
        const rcs = SatMath.estimateRcs(parseFloat(sat.length), parseFloat(sat.diameter), parseFloat(sat.span), sat.shape);

        satRcsEl.innerHTML = `Est ${rcs.toFixed(4)} m<sup>2</sup>`;
        satRcsEl.setAttribute('data-tooltip', `Est ${SatMath.mag2db(rcs).toFixed(2)} dBsm`);
      } else {
        satRcsEl.innerHTML = '未知';
        satRcsEl.setAttribute('data-tooltip', '未知');
      }
    } else if (!isNaN(sat.rcs)) {
      satRcsEl.innerHTML = `${sat.rcs} m<sup>2</sup>`;
    } else {
      satRcsEl.innerHTML = '未知';
      satRcsEl.setAttribute('data-tooltip', 'Unknown');
      // satRcsEl.setAttribute('data-tooltip', `${SatMath.mag2db(sat.rcs).toFixed(2)} dBsm`);
    }
  }

  // eslint-disable-next-line complexity
  private static updateSatMissionData_(obj?: BaseObject) {
    if (obj === null || typeof obj === 'undefined') {
      return;
    }

    if (obj.isSatellite()) {
      const sat = obj as DetailedSatellite;

      keepTrackApi.containerRoot.querySelectorAll('.sat-only-info')?.forEach((el) => {
        // 历史经度行需要特殊处理，由updateOrbitData_方法统一处理，这里不做处理
        if (el.id !== 'sat-history-longitude-row') {
          (<HTMLElement>el).style.display = 'flex';
        }
      });

      // 注意：历史经度行的显示逻辑已移至updateOrbitData_方法中统一处理，避免重复逻辑冲突
      let satUserDom = getEl('sat-user')!;
      const satUserString = StringExtractor.extractUserUrl(sat?.owner); // Replace with link if available

      satUserDom.innerHTML = satUserString;
      const tempEl = satUserDom.cloneNode(true);

      satUserDom.parentNode!.replaceChild(tempEl, satUserDom);
      satUserDom = tempEl as HTMLElement;

      if (satUserString.includes('http')) {
        satUserDom.classList.add('pointable');
        satUserDom.addEventListener('click', (e) => {
          e.preventDefault();
          const href = (<HTMLAnchorElement>satUserDom.firstChild).href;

          if (href.includes('http')) {
            openColorbox(href);
          }
        });
      } else {
        satUserDom.classList.remove('pointable');
      }


      const satMissionElement = getEl('sat-mission');
      const satPurposeElement = getEl('sat-purpose');
      const satContractorElement = getEl('sat-contractor');
      const satLaunchMassElement = getEl('sat-launchMass');
      const satDryMassElement = getEl('sat-dryMass');
      const satLifetimeElement = getEl('sat-lifetime');
      const satPowerElement = getEl('sat-power');
      const satStandardMagnitudeElement = getEl('sat-stdmag');
      const satBusElement = getEl('sat-bus');
      const satConfigurationElement = getEl('sat-configuration');
      const satPayloadElement = getEl('sat-payload');
      const satEquipmentElement = getEl('sat-equipment');
      const satMotorElement = getEl('sat-motor');
      const satLengthElement = getEl('sat-length');
      const satDiameterElement = getEl('sat-diameter');
      const satSpanElement = getEl('sat-span');
      const satShapeElement = getEl('sat-shape');

      if (!satMissionElement || !satPurposeElement || !satContractorElement || !satLaunchMassElement || !satDryMassElement || !satLifetimeElement || !satPowerElement ||
        !satStandardMagnitudeElement || !satBusElement || !satConfigurationElement || !satPayloadElement || !satEquipmentElement || !satMotorElement || !satLengthElement ||
        !satDiameterElement || !satSpanElement || !satShapeElement) {
        errorManagerInstance.warn('One or more updateSatMissionData_ elements not found');

        return;
      }

      satMissionElement.innerHTML = sat?.mission && sat?.mission !== '' ? sat?.mission : '未知';
      satPurposeElement.innerHTML = sat?.purpose && sat?.purpose !== '' ? sat?.purpose : '未知';
      satContractorElement.innerHTML = sat?.manufacturer && sat?.manufacturer !== '' ? sat?.manufacturer : '未知';
      // Update with other mass options
      satLaunchMassElement.innerHTML = sat?.launchMass && sat?.launchMass !== '' ? `${sat?.launchMass} kg` : '未知';
      satDryMassElement.innerHTML = sat?.dryMass && sat?.dryMass !== '' ? `${sat?.dryMass} kg` : '未知';
      satLifetimeElement.innerHTML = sat?.lifetime && sat?.lifetime !== '' ? `${sat?.lifetime} yrs` : '未知';
      satPowerElement.innerHTML = sat?.power && sat?.power !== '' ? `${sat?.power} w` : '未知';
      if (!sat?.vmag && sat?.vmag !== 0) {
        sat.vmag = SatInfoBox.calculateStdMag_(sat);
      }
      satStandardMagnitudeElement.innerHTML = sat?.vmag && sat?.vmag?.toFixed(2) !== '' ? sat?.vmag?.toFixed(2) : '未知';
      satBusElement.innerHTML = sat?.bus && sat?.bus !== '' ? sat?.bus : '未知';
      satConfigurationElement.innerHTML = sat?.configuration && sat?.configuration !== '' ? sat?.configuration : '未知';
      satPayloadElement.innerHTML = sat?.payload && sat?.payload !== '' ? sat?.payload : '未知';
      satEquipmentElement.innerHTML = sat?.equipment && sat?.equipment !== '' ? sat?.equipment : '未知';
      satMotorElement.innerHTML = sat?.motor && sat?.motor !== '' ? sat?.motor : '未知';
      satLengthElement.innerHTML = sat?.length && sat?.length !== '' ? `${sat?.length} m` : '未知';
      satDiameterElement.innerHTML = sat?.diameter && sat?.diameter !== '' ? `${sat?.diameter} m` : '未知';
      satSpanElement.innerHTML = sat?.span && sat?.span !== '' ? `${sat?.span} m` : '未知';
      satShapeElement.innerHTML = sat?.shape && sat?.shape !== '' ? sat?.shape : '未知';
    } else {
      // 隐藏所有卫星专用信息行，包括历史经度行
      keepTrackApi.containerRoot.querySelectorAll('.sat-only-info')?.forEach((el) => {
        (<HTMLElement>el).style.display = 'none';
      });
    }
  }

  private static calculateStdMag_(obj: DetailedSatellite): number | null {
    if (obj.vmag) {
      return obj.vmag;
    }

    const similarVmag: number[] = [];
    const catalogManager = keepTrackApi.getCatalogManager();
    const curSatType = obj.type;
    const curSatId = obj.id;
    const curSatCountry = obj.country;
    const curSatName = obj.name.toLowerCase();

    catalogManager.getSats().forEach((posSat) => {
      if (!posSat.vmag) {
        return;
      }
      if (curSatCountry !== posSat.country) {
        // Only look at same country
        return;
      }
      if (curSatType !== posSat.type) {
        // Only look at same type of curSat
        return;
      }
      if (curSatId === posSat.id) {
        // Don't look at the same curSat
        return;
      }

      similarVmag.push(posSat.vmag);

      // Only use the first word of the name
      const posName = posSat.name.toLowerCase();

      if (curSatName.length < 4 || posName.length < 4) {
        return;
      }

      // Determine how many characters match
      const matchingChars = curSatName.split('').filter((char, index) => char === posName[index]);

      if (matchingChars.length / curSatName.length > 0.85) {
        similarVmag.push(posSat.vmag);
        similarVmag.push(posSat.vmag);
        similarVmag.push(posSat.vmag);
      }
    });

    if (similarVmag.length > 0) {
      const avgVmag = similarVmag.reduce((a, b) => a + b, 0) / similarVmag.length;


      return avgVmag;
    }

    return null;
  }

  private static createSatMissionData() {
    getEl('sat-infobox-content')?.insertAdjacentHTML(
      'beforeend',
      keepTrackApi.html`
        <div id="sat-mission-data" class="start hidden">
          <div class="sat-info-section-header">
            任务数据
            <span id="mission-section-collapse" class="section-collapse material-icons" style="position: absolute; right: 0;">expand_less</span>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="卫星任务概述">
              研发使命
            </div>
            <div class="sat-info-value" id="sat-mission">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="卫星的用户">
              最终用户
            </div>
            <div class="sat-info-value" id="sat-user">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="卫星主要功能">
              最终目的
            </div>
            <div class="sat-info-value" id="sat-purpose">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="卫星承包商">
              承包商
            </div>
            <div class="sat-info-value" id="sat-contractor">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="入轨质量">
              入轨质量
            </div>
            <div class="sat-info-value" id="sat-launchMass">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50" data-tooltip="未加燃料的质量">
              物体净重
            </div>
            <div class="sat-info-value" id="sat-dryMass">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="卫星预计运行时间">
              预期寿命
            </div>
            <div class="sat-info-value" id="sat-lifetime">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="卫星总线">
              星载总线
            </div>
            <div class="sat-info-value" id="sat-bus">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="主要有效载荷">
              星上载荷
            </div>
            <div class="sat-info-value" id="sat-payload">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="卫星上的设备">
              星上设备
            </div>
            <div class="sat-info-value" id="sat-equipment">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="主电机">
              主电机
            </div>
            <div class="sat-info-value" id="sat-motor">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="长度（米）">
              卫星长度
            </div>
            <div class="sat-info-value" id="sat-length">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="直径（米）">
              卫星直径
            </div>
            <div class="sat-info-value" id="sat-diameter">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="宽度（米）">
              卫星宽度
            </div>
            <div class="sat-info-value" id="sat-span">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="形状描述">
              形状描述
            </div>
            <div class="sat-info-value" id="sat-shape">
              无数据
            </div>
          </div>
          <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="卫星功率">
              卫星功率
            </div>
            <div class="sat-info-value" id="sat-power">
              无数据
            </div>
          </div>
        </div>
        `,
    );
  }

  private static updateSensorInfo_(obj: BaseObject) {
    if (obj === null || typeof obj === 'undefined' || settingsManager.isDisableSensors) {
      return;
    }
    const sensorManagerInstance = keepTrackApi.getSensorManager();

    /*
     * If we are using the sensor manager plugin then we should hide the sensor to satellite
     * info when there is no sensor selected
     */
    if (!settingsManager.isDisableSensors) {
      if (sensorManagerInstance.isSensorSelected()) {
        showEl('sensor-sat-info');
      } else {
        hideEl('sensor-sat-info');
      }
    }

    if (!sensorManagerInstance.isSensorSelected()) {
      const satSunDom = getEl('sat-sun');

      if (satSunDom?.parentElement) {
        satSunDom.parentElement.style.display = 'none';
      }
    } else {
      SatInfoBox.calculateSunStatus_(obj);
    }
  }

  private static calculateSunStatus_(obj: BaseObject) {
    let satInSun: SunStatus;
    let sunTime: SunTime;
    const timeManagerInstance = keepTrackApi.getTimeManager();
    const sensorManagerInstance = keepTrackApi.getSensorManager();
    let now = new Date(timeManagerInstance.simulationTimeObj.getTime());

    try {
      sunTime = Sun.getTimes(now, sensorManagerInstance.currentSensors[0].lat, sensorManagerInstance.currentSensors[0].lon);
      satInSun = SatMath.calculateIsInSun(obj, keepTrackApi.getScene().sun.eci);
    } catch {
      sunTime = {
        sunriseStart: new Date(2050),
        sunsetEnd: new Date(1970),
      } as SunTime;
      satInSun = SunStatus.UNKNOWN;
    }

    // Reset the time to the current simulation time
    now = new Date(timeManagerInstance.simulationTimeObj.getTime());

    // If No Sensor, then Ignore Sun Exclusion
    const satSunDom = getEl('sat-sun');

    if (satSunDom?.parentElement) {
      // If No Sensor, then Ignore Sun Exclusion
      if (!sensorManagerInstance.isSensorSelected()) {
        satSunDom.parentElement.style.display = 'none';

        return;
      }
      satSunDom.parentElement.style.display = 'flex';

      // If Radar Selected, then Say the Sun Doesn't Matter
      if (sensorManagerInstance.currentSensors[0].type !== SpaceObjectType.OPTICAL && sensorManagerInstance.currentSensors[0].type !== SpaceObjectType.OBSERVER) {
        satSunDom.innerHTML = '无效果';
        satSunDom.style.color = 'green';

        return;
      }

      // If we are in the sun exclusion zone, then say so
      if (sunTime?.sunriseStart.getTime() - now.getTime() < 0 && (sunTime?.sunsetEnd.getTime() - now.getTime() > 0)) {
        // Unless you are in sun exclusion
        satSunDom.innerHTML = '太阳排除';
        satSunDom.style.color = 'red';

        return;
      }

      // If it is night then tell the user if the satellite is illuminated
      switch (satInSun) {
        case SunStatus.UMBRAL:
          satSunDom.innerHTML = '没有阳光';
          satSunDom.style.color = 'red';
          break;
        case SunStatus.PENUMBRAL:
          satSunDom.innerHTML = '阳光有限';
          satSunDom.style.color = 'orange';
          break;
        case SunStatus.SUN:
          satSunDom.innerHTML = '阳光直射';
          satSunDom.style.color = 'green';
          break;
        case SunStatus.UNKNOWN:
        default:
          satSunDom.innerHTML = '无法计算';
          satSunDom.style.color = 'red';
          break;
      }
    }
  }

  private static createSensorInfo() {
    getEl('sat-infobox-content')?.insertAdjacentHTML(
      'beforeend',
      keepTrackApi.html`
      <div id="sensor-sat-info">
        <div class="sat-info-section-header">
          传感器数据
          <span id="sensor-data-section-collapse" class="section-collapse material-icons" style="position: absolute; right: 0;">expand_less</span>
        </div>
        <div class="sat-info-row">
          <div class="sat-info-key" data-position="top" data-delay="50"
            data-tooltip="与传感器的距离">
            范围
          </div>
          <div class="sat-info-value" id="sat-range">xxxx km</div>
        </div>
        <div class="sat-info-row">
          <div class="sat-info-key" data-position="top" data-delay="50"
            data-tooltip="与传感器的角度（左/右）">
            方位角
          </div>
          <div class="sat-info-value" id="sat-azimuth">XX °</div>
        </div>
        <div class="sat-info-row">
          <div class="sat-info-key" data-position="top" data-delay="50"
            data-tooltip="与传感器的角度（上/下）">
            仰角
          </div>
          <div class="sat-info-value" id="sat-elevation">XX °</div>
        </div>
        <div class="sat-info-row">
          <div class="sat-info-key" data-position="top" data-delay="50"
            data-tooltip="目标范围内的线性宽度">
            波束宽度
          </div>
          <div class="sat-info-value" id="sat-beamwidth">xxxx km</div>
        </div>
        <div class="sat-info-row">
          <div class="sat-info-key" data-position="top" data-delay="50"
            data-tooltip="射频/光到达目标并返回的时间">
            通信来回时间
          </div>
          <div class="sat-info-value" id="sat-maxTmx">xxxx 毫秒</div>
        </div>
        <div class="sat-info-row sat-only-info">
          <div class="sat-info-key" data-position="top" data-delay="50"
            data-tooltip="阳光是否会影响传感器">
            太阳
          </div>
          <div class="sat-info-value" id="sat-sun">太阳范围</div>
        </div>
        <div class="sat-info-row sat-only-info">
            <div class="sat-info-key" data-position="top" data-delay="50"
              data-tooltip="视觉幅值（数字越小越亮）">
              视觉
            </div>
            <div class="sat-info-value" id="sat-vmag">xx.x</div>
          </div>
        <div id="sat-info-nextpass-row" class="sat-info-row sat-only-info">
          <div id="sat-info-nextpass" class="sat-info-key" data-position="top" data-delay="50"
            data-tooltip="下一次经过">
            下一次经过
          </div>
          <div id="sat-nextpass" class="sat-info-value">00:00:00z</div>
        </div>
      </div>
      `,
    );
  }

  /**
   * Selects a satellite, missile, or sensor object and updates the satellite info box accordingly.
   *
   * @param obj - The satellite, missile, or sensor object to be selected.
   */
  private selectSat_(obj?: BaseObject): void {
    if (obj) {
      if (obj.isSensor()) {
        return;
      }

      this.show();

      const satInfoBoxDom = getEl(SatInfoBox.containerId_);

      // 🔥 使用requestAnimationFrame批量计算，避免重复重排
      requestAnimationFrame(() => {
        // Get the height of the DOM
        const searchBoxHeight = keepTrackApi.getUiManager().searchManager.isResultsOpen ? (satInfoBoxDom?.getBoundingClientRect().height ?? 0) : 0;
        const bottomMenuTopVar = parseInt(document.documentElement.style.getPropertyValue('--bottom-menu-top')) || 0;
        const curVal = document.documentElement.style.getPropertyValue('--search-box-bottom');
        const newVal = `${searchBoxHeight + bottomMenuTopVar}px`;

        if (curVal !== newVal) {
          document.documentElement.style.setProperty('--search-box-bottom', newVal);
        }
      });

      if (obj.isSatellite()) {
        SatInfoBox.setSatInfoBoxSatellite_();
      } else {
        SatInfoBox.setSatInfoBoxMissile_();
      }
    }
  }

  private static setSatInfoBoxMissile_() {
    // TODO: There is an interdependency with SatCoreInfoBox and SelectSatManager.
    ['sat-apogee', 'sat-perigee', 'sat-inclination', 'sat-eccentricity', 'sat-raan', 'sat-argPe', 'sat-stdmag', 'sat-configuration', 'sat-elset-age', 'sat-period'].forEach(
      (id) => {
        const el = getEl(id, true);

        if (!el?.parentElement) {
          return;
        }
        hideEl(el.parentElement);
      },
    );

    const satMissionData = getEl('sat-mission-data', true);

    if (satMissionData) {
      satMissionData.style.display = 'none';
    }

    const satIdentifierData = getEl('sat-identifier-data', true);

    if (satIdentifierData) {
      satIdentifierData.style.display = 'none';
    }
  }

  private static setSatInfoBoxSatellite_() {
    // TODO: There is an interdependency with SatCoreInfoBox and SelectSatManager.
    ['sat-apogee', 'sat-perigee', 'sat-inclination', 'sat-eccentricity', 'sat-raan', 'sat-argPe', 'sat-stdmag', 'sat-configuration', 'sat-elset-age', 'sat-period'].forEach(
      (id) => {
        const el = getEl(id, true);

        if (!el?.parentElement) {
          return;
        }
        el.parentElement.style.display = 'flex';
      },
    );

    const satMissionData = getEl('sat-mission-data', true);

    if (settingsManager.isMissionDataEnabled) {
      satMissionData!.style.display = 'block';
    } else {
      satMissionData!.style.display = 'none';
    }

    const satIdentifierData = getEl('sat-identifier-data', true);

    if (satIdentifierData) {
      satIdentifierData.style.display = 'block';
    }
  }

  private openHistoryTrack_() {
    keepTrackApi.getSoundManager().play(SoundNames.CLICK);

    // 获取当前选中的卫星
    const selectedSat = this.selectSatManager_.getSelectedSat();
    if (!selectedSat?.isSatellite()) {
      keepTrackApi.getUiManager().toast('请先选择一个卫星', ToastMsgType.caution, false);
      return;
    }

    const sat = selectedSat as DetailedSatellite;

    // 获取历史轨道插件
    const historyTrackPlugin = keepTrackApi.getPluginByName('HistoryTrackPlugin');
    if (!historyTrackPlugin) {
      keepTrackApi.getUiManager().toast('历史轨道插件未加载', ToastMsgType.error, false);
      return;
    }

    // 打开历史轨道插件
    keepTrackApi.emit(KeepTrackApiEvents.bottomMenuClick, historyTrackPlugin.bottomIconElementName);

    // 延迟填充NORAD编号，确保界面已经加载
    setTimeout(() => {
      const noradInput = document.getElementById('history-norad-id') as HTMLInputElement;
      if (noradInput) {
        noradInput.value = sat.sccNum;
      }
    }, 100);
  }

  private openHistoryLongitude_() {
    keepTrackApi.getSoundManager().play(SoundNames.CLICK);

    // 获取当前选中的卫星
    const selectedSat = this.selectSatManager_.getSelectedSat();
    if (!selectedSat?.isSatellite()) {
      keepTrackApi.getUiManager().toast('请先选择一个卫星', ToastMsgType.caution, false);
      return;
    }

    const sat = selectedSat as DetailedSatellite;

    // 检查是否有NORAD编号
    if (!sat.sccNum || sat.sccNum.trim() === '') {
      keepTrackApi.getUiManager().toast('仅限非JSC名字的GEO卫星', ToastMsgType.caution, false);
      return;
    }

    // 检查是否为JSC卫星（名字以JSC开头）
    if (sat.name && sat.name.toUpperCase().startsWith('JSC')) {
      keepTrackApi.getUiManager().toast('不支持JSC名字卫星', ToastMsgType.caution, false);
      return;
    }

    // 检查轨道倾角条件
    if (sat.inclination > 20) {
      keepTrackApi.getUiManager().toast('仅支持倾角小于20度的GEO卫星', ToastMsgType.caution, false);
      return;
    }

    // 检查近地点和远地点条件
    if (sat.perigee <= 34000 || sat.apogee >= 37000) {
      keepTrackApi.getUiManager().toast('仅支持倾角小于20度的GEO卫星', ToastMsgType.caution, false);
      return;
    }

    // 获取历史经度插件
    const historyLongitudePlugin = keepTrackApi.getPluginByName('GeoLongitudeHistoryPlugin');
    if (!historyLongitudePlugin) {
      keepTrackApi.getUiManager().toast('历史经度插件未加载', ToastMsgType.error, false);
      return;
    }

    // 打开历史经度插件
    keepTrackApi.emit(KeepTrackApiEvents.bottomMenuClick, historyLongitudePlugin.bottomIconElementName);

    // 延迟填充NORAD编号，确保界面已经加载
    setTimeout(() => {
      const noradInput = document.getElementById('geo-longitude-norad-id') as HTMLInputElement;
      if (noradInput) {
        noradInput.value = sat.sccNum;
      }
    }, 100);
  }

  /**
   * 自动调整卫星名字字体大小，如果显示不开则缩小字体
   * 🔥 修复：使用统一缩放体系的CSS变量
   */
  private static adjustSatelliteNameFontSize_(): void {
    const nameElement = getEl('sat-info-title-name');
    if (!nameElement) {
      return;
    }

    // 🔥 使用统一缩放体系的CSS变量，不再直接设置像素值
    nameElement.style.fontSize = 'var(--font-lg)';
    nameElement.style.lineHeight = 'calc(var(--font-lg) * 1.2)';

    // 获取容器的可用宽度
    const container = getEl('sat-info-title');
    if (!container) {
      return;
    }

    const containerWidth = container.offsetWidth;
    const availableWidth = containerWidth - 80; // 为左右按钮留出空间

    // 如果文本宽度超出可用宽度，使用更小的字体变量
    if (nameElement.scrollWidth > availableWidth) {
      nameElement.style.fontSize = 'var(--font-md)';
      nameElement.style.lineHeight = 'calc(var(--font-md) * 1.2)';

      // 如果还是太大，使用基础字体
      if (nameElement.scrollWidth > availableWidth) {
        nameElement.style.fontSize = 'var(--font-base)';
        nameElement.style.lineHeight = 'calc(var(--font-base) * 1.2)';

        // 如果仍然太大，允许换行
        if (nameElement.scrollWidth > availableWidth) {
          nameElement.style.whiteSpace = 'normal';
          nameElement.style.lineHeight = '1.3';
        } else {
          nameElement.style.whiteSpace = 'nowrap';
        }
      } else {
        nameElement.style.whiteSpace = 'nowrap';
      }
    } else {
      nameElement.style.whiteSpace = 'nowrap';
    }
  }
}
