class LoginManager {
    constructor() {
        this.apiBaseUrl = '';
        this.failedAttempts = parseInt(localStorage.getItem('failedAttempts') || '0');
        this.lockoutUntil = localStorage.getItem('lockoutUntil');
        this.csrfToken = null;

        this.init();
    }

    async getApiBaseUrl() {
        // 首先尝试从localStorage获取（登录时保存的）
        const savedApiUrl = localStorage.getItem('apiBaseUrl');
        if (savedApiUrl) {
            console.log('从localStorage获取API地址:', savedApiUrl);
            return savedApiUrl;
        }

        // 使用统一配置加载器
        if (window.configLoader) {
            try {
                const apiUrl = await window.configLoader.getAuthUrl();
                console.log('从配置加载器获取API地址:', apiUrl);
                return apiUrl;
            } catch (error) {
                console.log('配置加载器获取失败:', error.message);
            }
        }

        try {
            // 尝试从配置文件获取API地址
            const response = await fetch('/config.json');
            if (response.ok) {
                const config = await response.json();
                if (config.apiServer && config.apiServer.url) {
                    const apiUrl = config.apiServer.url + '/api/auth';
                    console.log('从配置文件获取API地址:', apiUrl);
                    return apiUrl;
                }
            }
        } catch (error) {
            console.log('无法读取配置文件，使用默认地址');
        }

        // 回退到默认地址
        const currentHost = window.location.hostname;
        const protocol = window.location.protocol;
        const port = currentHost === 'localhost' ? '5001' : '5001';
        const apiUrl = `${protocol}//${currentHost}:${port}/api/auth`;
        console.log('使用默认API地址:', apiUrl);
        return apiUrl;
    }

    async init() {
        this.apiBaseUrl = await this.getApiBaseUrl();
        this.createStars();
        this.setupEventListeners();
        this.checkLockout();
        this.setupPasswordStrengthChecker();
    }

    createStars() {
        const starsContainer = document.getElementById('stars');
        const numStars = 100;
        
        for (let i = 0; i < numStars; i++) {
            const star = document.createElement('div');
            star.className = 'star';
            star.style.left = Math.random() * 100 + '%';
            star.style.top = Math.random() * 100 + '%';
            star.style.width = Math.random() * 3 + 1 + 'px';
            star.style.height = star.style.width;
            star.style.animationDelay = Math.random() * 2 + 's';
            starsContainer.appendChild(star);
        }
    }

    setupEventListeners() {
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        document.getElementById('registerForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // 密码确认验证
        document.getElementById('confirmPassword').addEventListener('input', (e) => {
            this.validatePasswordConfirm();
        });
    }

    setupPasswordStrengthChecker() {
        const passwordInput = document.getElementById('registerPassword');
        const strengthDiv = document.getElementById('passwordStrength');

        passwordInput.addEventListener('input', (e) => {
            const password = e.target.value;
            const strength = this.checkPasswordStrength(password);
            
            strengthDiv.textContent = strength.text;
            strengthDiv.className = `password-strength ${strength.class}`;
        });
    }

    checkPasswordStrength(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
        
        const criteria = [
            password.length >= minLength,
            hasUpperCase,
            hasLowerCase,
            hasNumbers,
            hasSpecialChar
        ];
        
        const score = criteria.filter(Boolean).length;
        
        if (score < 3) {
            return { text: '密码强度：弱', class: 'strength-weak' };
        } else if (score < 5) {
            return { text: '密码强度：中等', class: 'strength-medium' };
        } else {
            return { text: '密码强度：强', class: 'strength-strong' };
        }
    }

    validatePasswordConfirm() {
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const confirmInput = document.getElementById('confirmPassword');
        
        if (confirmPassword && password !== confirmPassword) {
            confirmInput.classList.add('error');
            return false;
        } else {
            confirmInput.classList.remove('error');
            return true;
        }
    }

    checkLockout() {
        if (this.lockoutUntil) {
            const lockoutTime = new Date(this.lockoutUntil);
            const now = new Date();
            
            if (lockoutTime > now) {
                this.showLockoutTimer(lockoutTime);
                return true;
            } else {
                // 锁定时间已过，清除锁定状态
                localStorage.removeItem('lockoutUntil');
                localStorage.removeItem('failedAttempts');
                this.lockoutUntil = null;
                this.failedAttempts = 0;
            }
        }
        return false;
    }

    showLockoutTimer(lockoutTime) {
        const timerDiv = document.getElementById('lockoutTimer');
        const loginBtn = document.getElementById('loginBtn');
        
        timerDiv.style.display = 'block';
        loginBtn.disabled = true;
        
        const updateTimer = () => {
            const now = new Date();
            const remaining = lockoutTime - now;
            
            if (remaining <= 0) {
                timerDiv.style.display = 'none';
                loginBtn.disabled = false;
                localStorage.removeItem('lockoutUntil');
                localStorage.removeItem('failedAttempts');
                this.lockoutUntil = null;
                this.failedAttempts = 0;
                return;
            }
            
            const seconds = Math.ceil(remaining / 1000);
            timerDiv.textContent = `登录被锁定，请等待 ${seconds} 秒后重试`;
            setTimeout(updateTimer, 1000);
        };
        
        updateTimer();
    }

    async handleLogin() {
        if (this.checkLockout()) {
            return;
        }

        const username = document.getElementById('loginUsername').value.trim();
        const password = document.getElementById('loginPassword').value;
        
        if (!username || !password) {
            this.showError('请填写用户名和密码');
            return;
        }

        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();
            
            if (response.ok) {
                // 登录成功
                localStorage.setItem('authToken', data.token);
                localStorage.setItem('user', JSON.stringify(data.user));
                localStorage.setItem('apiBaseUrl', this.apiBaseUrl); // 保存API地址
                localStorage.removeItem('failedAttempts');
                localStorage.removeItem('lockoutUntil');
                
                this.showSuccess('登录成功，正在跳转...');
                
                // 跳转到主页面
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
                
            } else {
                // 登录失败
                this.failedAttempts++;
                localStorage.setItem('failedAttempts', this.failedAttempts.toString());
                
                if (this.failedAttempts >= 5) {
                    // 锁定1分钟
                    const lockoutTime = new Date(Date.now() + 60 * 1000);
                    localStorage.setItem('lockoutUntil', lockoutTime.toISOString());
                    this.lockoutUntil = lockoutTime.toISOString();
                    this.showLockoutTimer(lockoutTime);
                }
                
                this.showError(data.error || '登录失败');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showError('网络错误，请检查连接');
        } finally {
            this.showLoading(false);
        }
    }

    async handleRegister() {
        const username = document.getElementById('registerUsername').value.trim();
        const email = document.getElementById('registerEmail').value.trim();
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        
        // 验证输入
        if (!username || !email || !password || !confirmPassword) {
            this.showError('请填写所有字段');
            return;
        }
        
        if (password !== confirmPassword) {
            this.showError('两次输入的密码不一致');
            return;
        }
        
        if (!this.validatePasswordConfirm()) {
            return;
        }
        
        // 检查密码强度
        const strength = this.checkPasswordStrength(password);
        if (strength.class === 'strength-weak') {
            this.showError('密码强度不足，请使用更强的密码');
            return;
        }

        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, email, password })
            });

            const data = await response.json();
            
            if (response.ok) {
                this.showSuccess('注册申请已提交，请等待管理员审批');
                document.getElementById('registerForm').reset();
                document.getElementById('passwordStrength').textContent = '';
            } else {
                this.showError(data.error || '注册失败');
            }
        } catch (error) {
            console.error('Register error:', error);
            this.showError('网络错误，请检查连接');
        } finally {
            this.showLoading(false);
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        const successDiv = document.getElementById('successMessage');
        
        successDiv.style.display = 'none';
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }

    showSuccess(message) {
        const errorDiv = document.getElementById('errorMessage');
        const successDiv = document.getElementById('successMessage');
        
        errorDiv.style.display = 'none';
        successDiv.textContent = message;
        successDiv.style.display = 'block';
        
        setTimeout(() => {
            successDiv.style.display = 'none';
        }, 5000);
    }

    showLoading(show) {
        const loadingDiv = document.getElementById('loading');
        const loginBtn = document.getElementById('loginBtn');
        const registerBtn = document.getElementById('registerBtn');
        
        if (show) {
            loadingDiv.style.display = 'block';
            loginBtn.disabled = true;
            registerBtn.disabled = true;
        } else {
            loadingDiv.style.display = 'none';
            loginBtn.disabled = false;
            registerBtn.disabled = false;
        }
    }
}

// 全局函数
function switchTab(tab) {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const tabButtons = document.querySelectorAll('.tab-button');
    
    // 清除错误和成功消息
    document.getElementById('errorMessage').style.display = 'none';
    document.getElementById('successMessage').style.display = 'none';
    
    if (tab === 'login') {
        loginForm.style.display = 'block';
        registerForm.style.display = 'none';
        tabButtons[0].classList.add('active');
        tabButtons[1].classList.remove('active');
    } else {
        loginForm.style.display = 'none';
        registerForm.style.display = 'block';
        tabButtons[0].classList.remove('active');
        tabButtons[1].classList.add('active');
    }
}

// 全局函数
function switchTab(tabName) {
    const tabs = document.querySelectorAll('.tab');
    const forms = document.querySelectorAll('.form-container');

    tabs.forEach(tab => tab.classList.remove('active'));
    forms.forEach(form => form.classList.remove('active'));

    document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');
    document.getElementById(`${tabName}Form`).classList.add('active');
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    new LoginManager();
});
