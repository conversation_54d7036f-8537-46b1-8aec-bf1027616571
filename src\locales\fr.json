{"time": {"days": {"0": "<PERSON><PERSON><PERSON>", "1": "<PERSON><PERSON>", "2": "<PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON><PERSON>", "4": "<PERSON><PERSON>", "5": "<PERSON><PERSON><PERSON><PERSON>", "6": "<PERSON><PERSON>"}, "days-short": {"0": "<PERSON><PERSON>", "1": "<PERSON>n", "2": "Mar", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON>", "5": "Ven", "6": "Sam"}, "months": {"1": "<PERSON><PERSON>", "2": "<PERSON><PERSON><PERSON><PERSON>", "3": "Mars", "4": "Avril", "5": "<PERSON>", "6": "Juin", "7": "<PERSON><PERSON><PERSON>", "8": "Août", "9": "Septembre", "10": "Octobre", "11": "Novembre", "12": "Décembre"}, "calendar": {"time": "<PERSON><PERSON>", "hour": "<PERSON><PERSON>", "minute": "Minute", "second": "Seconde", "now": "Maintenant", "done": "<PERSON><PERSON><PERSON><PERSON>", "pause": "Pause", "propagation": "Propagation"}}, "errorMsgs": {"catalogNotFullyInitialized": "Le catalogue n'est pas encore entièrement initialisé. Veuillez patienter quelques secondes et réessayer.", "sensorGroupsApiEmpty": "Aucun groupe de capteurs trouvé dans l'API, retour à la base de données de groupes de capteurs incluse.", "SelectSensorFirst": "Veuillez d'abord sélectionner un capteur !", "SelectSatelliteFirst": "Veuillez d'abord sélectionner un satellite !", "SelectSecondarySatellite": "Veuillez d'abord sélectionner un satellite secondaire !", "SatelliteNotDetailedSatellite": "Le satellite n'est pas un DetailedSatellite !", "SensorNotFound": "Capteur introuvable !", "Scene": {"disablingGodrays": "Votre ordinateur a des difficultés ! Désactivation des rayons divins.", "disablingAurora": "Votre ordinateur a des difficultés ! Désactivation des aurores.", "disablingAtmosphere": "Votre ordinateur a des difficultés ! Désactivation de l'atmosphère.", "disablingMoon": "Votre ordinateur a des difficultés ! Désactivation de la Lune.", "disablingMilkyWay": "Votre ordinateur a des difficultés ! Désactivation de la Voie lactée.", "disablingSun": "Votre ordinateur a des difficultés ! Désactivation du Soleil."}, "Breakup": {"SatelliteNotFound": "Satellite introuvable !", "CannotCreateBreakupForNonCircularOrbits": "Impossible de créer une fragmentation pour des orbites non circulaires. Correction en cours.", "CannotCalcDirectionOfSatellite": "Impossible de calculer la direction du satellite. Veuillez réessayer plus tard.", "ErrorCreatingBreakup": "Erreur lors de la création de la fragmentation !", "InvalidStartNum": "Numéro de départ du satellite invalide ! Valeur par défaut : 90000 !", "BreakupGeneratorFailed": "Échec du générateur de fragmentation !"}, "Collisions": {"noCollisionsData": "Aucune donnée de collision trouvée !", "errorProcessingCollisions": "Erreur lors du traitement des données SOCRATES !"}, "EditSat": {"errorReadingFile": "Erreur lors de la lecture du fichier !", "satelliteNotFound": "Satellite {{sccNum}} introuvable !"}, "CreateSat": {"errorCreatingSat": "Erreur lors de la création du satellite !"}, "Reports": {"popupBlocker": "Veuillez autoriser les pop-ups pour ce site afin de voir les rapports."}, "SensorManager": {"errorUpdatingUi": "Erreur lors de la mise à jour du style de l'interface utilisateur du capteur."}}, "hoverManager": {"launched": "<PERSON><PERSON><PERSON>", "launchedPlanned": "Lancé : Prévu", "launchedUnknown": "Lancé : Inconnu"}, "loadingScreenMsgs": {"math": "Tentative de mathématiques...", "science": "Localisation de la science...", "science2": "Science trouvée...", "dots": "Dessiner des points dans l'espace...", "satIntel": "Intégration des informations satellites...", "painting": "Peindre la Terre...", "coloring": "Coloriage dans les lignes...", "elsets": "Localisation des GPs...", "models": "Construction de modèles 3D...", "cunningPlan": "Élaboration d'un plan astucieux...", "copyrightNotice": "KeepTrack™ et spacedefense™ sont des marques déposées de Kruczek Labs LLC.<br>Cette instance est sous licence GNU AGPL v3.0. L'attribution, l'accès au code source et cet avis doivent rester visibles.<br>Aucune licence commerciale n'a été accordée et aucune compensation n'a été fournie au titulaire des droits.<br>L'utilisation non autorisée, le rebranding ou la suppression de l'attribution peuvent enfreindre les conditions de la marque et de la licence open source.<br>© 2025 Kruczek Labs LLC. Tous droits réservés. Voir LICENSE pour les conditions complètes.", "copyrightNoticeMobile": "KeepTrack™ et spacedefense™ sont des marques déposées de Kruczek Labs LLC.<br>Cette instance est sous licence GNU AGPL v3.0. L'attribution, l'accès au code source et cet avis doivent rester visibles. Aucune licence commerciale n'a été accordée. Aucune compensation n'a été fournie au titulaire des droits. L'utilisation non autorisée, le rebranding ou la suppression de l'attribution peuvent enfreindre les conditions de la marque et de la licence open source.<br>© 2025 Kruczek Labs LLC. Tous droits réservés. Voir LICENSE pour les conditions complètes."}, "splashScreens": {"1": "Les modèles de satellites paraissent plus grands qu'ils ne le sont réellement. Tout le reste est à l'échelle.", "2": "Appuyez sur la touche 'L' pour afficher/masquer les orbites des satellites.", "3": "Appuyez sur la touche 'P' pour ouvrir un graphique polaire du satellite actuel (<PERSON><PERSON> de<PERSON> d'abord choisir un capteur !).", "4": "Réinitialisez l'heure de la simulation en appuyant sur la touche 'T'.", "5": "Avancez l'heure de la simulation en appuyant sur la touche '>' ou '='.", "6": "<PERSON><PERSON>z l'heure de la simulation en appuyant sur la touche '<' ou ')'.", "7": "Appuyer sur la touche '/' permet d'alterner la vitesse de simulation entre 1x et 0x.", "8": "Appuyez sur la touche 'V' pour changer le mode d'affichage.", "9": "Appuyez sur Maj+F1 pour ouvrir le menu d'aide à tout moment.", "10": "Appuyez sur la touche 'R' pour activer la rotation automatique.", "11": "Appuyer sur Maj + C lorsqu'un satellite est sélectionné permet d'afficher/masquer son cône de visibilité.", "12": "La touche 'Home' oriente la caméra vers le capteur actuel.", "13": "La touche '`' (tilde) réinitialise la caméra à la vue par défaut.", "14": "La touche 'M' affiche la vue cartographique 2D du satellite actuel.", "15": "Le menu des paramètres situé dans la barre d'outils inférieure contient de nombreuses options pour personnaliser votre expérience.", "16": "Beaucoup de menus ont des paramètres supplémentaires accessibles en cliquant sur l'icône d'engrenage.", "17": "Ajoutez des satellites à la liste de surveillance pour recevoir des notifications lorsqu'ils passent au-dessus du capteur actuel.", "18": "Cliquez avec le bouton droit sur le globe pour ouvrir le menu contextuel avec plus d'options.", "19": "Appuyez sur les touches '+' ou '-' pour zoomer avant ou arrière.", "20": "Appuyez sur 'F11' pour activer/désactiver le mode plein écran.", "21": "Vous pouvez rechercher des satellites par nom ou ID NORAD dans la barre de recherche en haut à droite.", "22": "Un nouveau lancement nominal peut être créé en sélectionnant un satellite puis en cliquant sur le bouton 'Nouveau Lancement' dans le menu du bas.", "23": "Appuyez sur la touche 'N' pour activer/désactiver le mode nuit.", "24": "Appuyez sur la touche 'I' pour masquer/afficher les informations contextuelles sur un satellite.", "25": "Appuyez sur la touche 'B' pour masquer/afficher le menu.", "26": "Accélérez la simulation en appuyant sur Maj + ';'.", "27": "Ralentissez la simulation en appuyant sur la touche ','.", "28": "Définissez un objet comme Secondaire pour voir sa distance relative avec l'objet principal.", "29": "Basculez un objet entre Principal/Secondaire avec la touche '['."}, "plugins": {"SensorListPlugin": {"bottomIconLabel": "Capteurs", "title": "Menu Liste des Capteurs", "helpBody": "Le menu Capteurs vous permet de sélectionner un capteur à utiliser dans les calculs et les autres fonctions du menu. Les capteurs sont regroupés selon les réseaux qu'ils soutiennent principalement. Sur le côté gauche du menu se trouve le nom du capteur et à droite le pays/l'organisation propriétaire. <br><br> Sélectionner une option \"Tous...Capteurs\" permet de sélectionner tous les capteurs de ce groupe. Cela est utile pour visualiser la couverture des réseaux, mais ne fonctionne pas pour tous les calculs. Si vous souhaitez calculer les angles de visée pour un réseau, il est préférable d'utiliser l'outil multi-sites ou de calculer les angles pour chaque capteur individuellement. <br><br> Les capteurs de cette liste incluent des radars mécaniques et à réseau phasé, ainsi que des capteurs optiques : <ul style=\"margin-left: 40px;\"> <li> Les radars à réseau phasé sont généralement limités à l'orbite terrestre basse (LEO). </li> <li> Les radars mécaniques peuvent être utilisés pour LEO et l'orbite géostationnaire (GEO). </li> <li> Les capteurs optiques sont généralement utilisés pour GEO, mais peuvent aussi servir pour LEO. </li> <li> Les capteurs optiques sont limités aux observations nocturnes par ciel dégagé, tandis que les radars fonctionnent de jour comme de nuit. </li> </ul> <br> Les informations sur les capteurs sont basées sur des données publiques et peuvent être vérifiées dans le menu Info Capteur. Si vous avez des données publiques sur d'autres capteurs ou des corrections à apporter, contactez-moi à <a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>."}, "SensorInfoPlugin": {"bottomIconLabel": "Info Capteur", "title": "<PERSON><PERSON> Capteur", "helpBody": "Info Capteur fournit des informations sur le capteur actuellement sélectionné. Les informations sont basées sur des données publiques et peuvent ne pas toujours être 100% exactes. Si vous avez des données publiques sur d'autres capteurs ou des corrections à apporter, contactez-moi à <a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>. <br><br> Les informations fournies incluent : <ul style=\"margin-left: 40px;\"> <li> Nom du capteur </li> <li> Propri<PERSON><PERSON> du capteur </li> <li> Type de capteur </li> <li> Champ de vision du capteur </li> </ul> <br> De plus, des lignes peuvent être rapidement créées du capteur vers le soleil ou la lune depuis ce menu."}, "CustomSensorPlugin": {"bottomIconLabel": "Capteur <PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON>u Capteur person<PERSON>", "helpBody": "Cela vous permet de créer un capteur personnalisé à utiliser dans les calculs et autres fonctions du menu. Il peut s'agir d'un capteur totalement original ou d'une modification d'un capteur existant. <br><br> Après avoir défini la latitude, la longitude et l'altitude du capteur, vous pouvez définir son champ de vision. Sélectionner télescope créera un champ de vision à 360 degrés avec un masque d'élévation de 10 degrés et une portée illimitée. Désélectionner l'option télescope permet de définir manuellement le champ de vision. <br><br> Si vous souhaitez modifier un capteur existant, sélectionnez-le d'abord dans la liste et le capteur personnalisé sera mis à jour avec ses informations."}, "LookAnglesPlugin": {"bottomIconLabel": "<PERSON><PERSON> de visée", "title": "<PERSON><PERSON> visée", "helpBody": "Le menu Angles de visée permet de calculer la distance, l'azimut et l'élévation entre un capteur et un satellite. Un satellite et un capteur doivent d'abord être sélectionnés pour utiliser ce menu. <br><br> L'option n'afficher que les heures de lever et de coucher ne calcule que ces moments pour le satellite. Cela permet de savoir rapidement quand un satellite sera visible d'un capteur. <br><br> La plage de recherche peut être modifiée en changeant la longueur et l'intervalle."}, "MultiSiteLookAnglesPlugin": {"bottomIconLabel": "Angles multi-sites", "title": "Menu Angles multi-sites", "helpBody": "Le menu Angles multi-sites permet de calculer la distance, l'azimut et l'élévation entre un satellite et plusieurs capteurs. Un satellite doit d'abord être sélectionné pour utiliser ce menu. <br><br> <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, le menu calcule les angles de visée pour tous les capteurs du Space Surveillance Network. Pour calculer les angles pour d'autres capteurs, vous pouvez exporter un fichier csv en bas du menu. Le fichier csv contiendra les angles pour tous les capteurs. <br><br> Cliquer sur une ligne du tableau sélectionne le capteur et change l'heure de la simulation à celle de l'angle de visée."}, "SensorTimeline": {"bottomIconLabel": "Chronologie Capteur", "title": "Menu Chronologie Capteur", "helpBody": "Le menu Chronologie Capteur montre quand une liste de capteurs a une visibilité sur un satellite. La chronologie est colorée pour indiquer la qualité du passage. Rouge = mauvais passage, jaune = passage moyen, vert = bon passage. Cliquez sur un passage pour changer le capteur et l'heure. <br><br> La chronologie peut être modifiée en changeant l'heure de début et de fin de la simulation, ainsi que la longueur et l'intervalle."}, "ProximityOps": {"bottomIconLabel": "Rendez-vous et opérations de proximité", "title": "Rendez-vous et opérations de proximité (RPO)", "titleSecondary": "Liste RPO", "helpBody": "Trouver les approches entre satellites", "noradId": "ID NORAD", "maxDistThreshold": "Seuil de distance max (km)", "maxRelativeVelocity": "Vitesse relative max (km/s)", "searchDuration": "<PERSON><PERSON>e de recherche (heures)", "geoText": "Géostationnaire", "leoText": "<PERSON><PERSON><PERSON> basse (LEO)", "orbitType": "Type d'orbite", "geoAllVsAll": "Géostationnaire tous vs tous", "geoAllVsAllTooltip": "Chercher les RPO entre tous les satellites géostationnaires", "comparePayloadsOnly": "Comparer uniquement les charges utiles", "comparePayloadsOnlyTooltip": "Chercher uniquement les RPO entre charges utiles", "ignoreVimpelRso": "Ignorer Vimpel RSO", "ignoreVimpelRsoTooltip": "Ignorer les RSOs issus du catalogue Vimpel"}, "SatelliteTimeline": {"bottomIconLabel": "Chronologie Satellite", "title": "Menu Chronologie Satellite", "helpBody": "Le menu Chronologie Satellite montre quand un capteur unique a une visibilité sur une liste de satellites. La chronologie est colorée pour indiquer la qualité du passage. Rouge = mauvais passage, jaune = passage moyen, vert = bon passage. Cliquez sur un passage pour changer le satellite et l'heure. <br><br> La chronologie peut être modifiée en changeant l'heure de début et de fin de la simulation, ainsi que la longueur et l'intervalle."}, "WatchlistPlugin": {"bottomIconLabel": "Liste de surveillance", "title": "Menu Liste de surveillance", "helpBody": "Le menu Liste de surveillance vous permet de créer une liste de satellites prioritaires à suivre. Cela permet de retrouver rapidement les satellites qui vous intéressent le plus. La liste est enregistrée dans le stockage local de votre navigateur et sera disponible lors de votre prochaine visite. <br><br> Lorsque les satellites de la liste entrent dans le champ de vision du capteur sélectionné, une notification s'affiche, une ligne est tracée du capteur au satellite, et le numéro du satellite s'affiche sur le globe. <br><br> La superposition dépend du fait que la liste de surveillance soit remplie."}, "WatchlistOverlay": {"bottomIconLabel": "Superposition", "title": "Menu Superposition", "helpBody": "<p> La superposition de la liste de surveillance affiche l'heure du prochain passage pour chaque satellite de votre liste. La superposition est mise à jour toutes les 10 secondes. </p> <p> Le code couleur indique le temps jusqu'au prochain passage : </p> <ul> <li>J<PERSON>ne - En vue</li> <li>Bleu - Prochain passage dans les 30 minutes après l'heure actuelle ou 10 minutes avant</li> <li>Blanc - Tout autre passage futur</li> </ul> <p> Cliquer sur un satellite dans la superposition centre la carte sur ce satellite. </p>"}, "ReportsPlugin": {"bottomIconLabel": "Rapports", "title": "Menu Rapports", "helpBody": "Le menu Rapports est une collection d'outils pour vous aider à analyser et comprendre les données affichées."}, "PolarPlotPlugin": {"bottomIconLabel": "Trace polaire", "title": "Menu Trace polaire", "helpBody": "Le menu Trace polaire permet de générer un graphique polaire 2D de l’azimut et de l’élévation du satellite au fil du temps."}, "NextLaunchesPlugin": {"bottomIconLabel": "Prochains lancements", "title": "Menu Prochains lancements", "helpBody": "Le menu Prochains lancements récupère les données de <a href=\"https://thespacedevs.com/\" target=\"_blank\">The Space Devs</a> pour afficher les lancements à venir."}, "FindSatPlugin": {"bottomIconLabel": "Trouver un satellite", "title": "Menu Trouver un satellite", "helpBody": "Le menu Trouver un satellite permet de rechercher des satellites par paramètres orbitaux ou caractéristiques. <br><br> <PERSON><PERSON> la plupart des paramètres, saisissez la valeur cible à gauche et une marge d’erreur à droite. Par exemple, pour trouver tous les satellites avec une inclinaison de 51 à 52 degrés, saisissez 51,5 à gauche et 0,5 à droite. La recherche affichera alors tous les satellites dans cette plage d’inclinaison dans la barre de recherche."}, "ShortTermFences": {"bottomIconLabel": "Barrière court terme", "title": "<PERSON>u <PERSON> court terme (STF)", "helpBody": "Le menu Barrières court terme (STF) permet de visualiser les zones de recherche des capteurs. <br><br> Ceci n’est probablement utile que si vous possédez/exploitez un capteur avec cette fonctionnalité."}, "Collisions": {"bottomIconLabel": "Collisions", "title": "Menu Collisions", "helpBody": "Le menu Collisions affiche les satellites avec une forte probabilité de collision. <br><br> <PERSON><PERSON><PERSON> sur une ligne sélectionne les deux satellites impliqués et change l’heure à celle de la collision."}, "TrackingImpactPredict": {"bottomIconLabel": "Prédiction de rentrée", "title": "Menu Prédiction de suivi et d'impact", "helpBody": "Le menu Prédiction de suivi et d'impact (TIP) affiche les derniers messages de suivi et de prédiction d'impact pour les satellites. Le tableau affiche les colonnes suivantes :<br><br> <b>NORAD</b> : L’ID catalogue NORAD du satellite.<br><br> <b>Date de désintégration</b> : La date prévue de désintégration du satellite.<br><br> <b>Latitude</b> : La latitude du satellite au moment de la désintégration.<br><br> <b>Longitude</b> : La longitude du satellite au moment de la désintégration.<br><br> <b>Fenêtre (min)</b> : La fenêtre temporelle en minutes pour la prédiction.<br><br> <b>Prochain rapport (h)</b> : Le temps en heures avant le prochain rapport.<br><br> <b>Angle de rentrée (°)</b> : L’angle de rentrée du satellite en degrés.<br><br> <b>RCS (m²)</b> : La section efficace radar du satellite en mètres carrés.<br><br> <b>Âge GP (h)</b> : L’âge du dernier jeu d’éléments en heures.<br><br> <b>Masse à vide (kg)</b> : La masse à vide du satellite en kilogrammes.<br><br> <b>Volume (m³)</b> : Le volume estimé du satellite en mètres cubes.<br><br>"}, "Breakup": {"bottomIconLabel": "<PERSON><PERSON>er une fragmentation", "title": "Menu Fragmentation", "helpBody": "Le menu Fragmentation est un outil pour simuler la fragmentation d’un satellite. <br><br> En dupliquant et modifiant l’orbite d’un satellite, on peut modéliser sa fragmentation. Après avoir sélectionné un satellite et ouvert le menu, l’utilisateur peut choisir : <ul style=\"margin-left: 40px;\"> <li>Variation d’inclinaison</li> <li>Variation du RAAN</li> <li>Variation de période</li> <li>Nombre de fragments</li> </ul> Plus la variation est grande, plus la dispersion de la fragmentation simulée est importante. Les variations par défaut suffisent à simuler une fragmentation raisonnable."}, "DebrisScreening": {"bottomIconLabel": "Filtrage des débris", "title": "Menu Filtrage des débris", "helpBody": "Le menu Filtrage des débris permet de générer une liste d’objets de débris susceptibles d’être vus par un satellite. La liste est générée en calculant les paramètres orbitaux des débris et en les comparant à ceux du satellite. L’utilisateur peut choisir de générer la liste à l’aide du TLE ou du propagateur SGP4. Il est également possible de filtrer la liste par taille ou magnitude des débris."}, "TransponderChannelData": {"bottomIconLabel": "Infos transpondeur et canaux", "title": "Infos transpondeur et canaux satellites", "helpBody": "<p>Ce tableau contient des détails techniques sur les canaux satellites, y compris TV, radio et autres services de communication :</p><ul><li><strong>satellite :</strong> Nom du satellite diffusant le canal</li><li><strong>tvchannel :</strong> Nom du canal TV/radio ou service de communication</li><li><strong>beam :</strong> Direction du faisceau satellite (ex : hémisphère ouest)</li><li><strong>freq :</strong> Fréquence et polarité du transpondeur (ex : 3840 V)</li><li><strong>system :</strong> Système de diffusion (ex : DVB-S2 8PSK)</li><li><strong>SRFEC :</strong> Débit symbole et FEC (ex : 30000 3/4)</li><li><strong>video :</strong> Format de compression vidéo/données (ex : MPEG-4/HD)</li><li><strong>lang :</strong> Langues audio/communication disponibles (ex : <PERSON>, <PERSON>)</li><li><strong>encryption :</strong> Système de chiffrement utilisé (ex : PowerVu)</li></ul><p>Ces informations sont utiles pour les professionnels, techniciens, passionnés et toute personne installant ou dépannant des équipements de réception satellite.</p>"}, "EditSat": {"bottomIconLabel": "Modifier le satellite", "title": "Menu Modifier le satellite", "helpBody": "Le menu Modifier le satellite permet de modifier les données du satellite. <br><br> <ul> <li> SCC# du satellite - Numéro unique attribué à chaque satellite par l’US Space Force. </li> <li> <PERSON><PERSON> de l’époque - <PERSON><PERSON> de la dernière mise à jour orbitale du satellite. </li> <li> <PERSON>ur de l’époque - Jour de l’année de la dernière mise à jour orbitale. </li> <li> Inclinaison - Angle entre le plan orbital du satellite et le plan équatorial. </li> <li> Ascension droite - Angle entre le nœud ascendant et la position du satellite à la dernière mise à jour. </li> <li> Excentricité - Degré d’écart de l’orbite par rapport à un cercle parfait. </li> <li> Argument du périgée - Angle entre le nœud ascendant et le point le plus proche de la Terre. </li> <li> Anomalie moyenne - <PERSON>le entre la position du satellite à la dernière mise à jour et son point le plus proche de la Terre. </li> <li> Mouvement moyen - Taux de variation de l’anomalie moyenne du satellite. </li> </ul>"}, "NewLaunch": {"bottomIconLabel": "Nouveau lancement", "title": "Menu Nouveau lancement", "helpBody": "Le menu Nouveau lancement permet de générer des lancements orbitaux notionnels en modifiant des satellites existants avec des paramètres similaires. <br><br> Après avoir sélectionné un satellite, vous pouvez choisir un site de lancement et un azimut nord/sud. Le satellite sélectionné sera modifié pour l’aligner avec le site de lancement. L’horloge est alors remise à 00:00:00 pour représenter le temps relatif après le lancement. Cela peut aider à calculer la couverture capteur par rapport à l’heure de lancement. La relation de l’objet avec les autres objets orbitaux sera incorrecte."}, "MissilePlugin": {"bottomIconLabel": "Missile", "title": "Menu Missile", "helpBody": "Le menu Missile permet de générer des lancements de missiles notionnels entre pays. <br><br> Pour les missiles lancés depuis un sous-marin, le point de lancement est une latitude et longitude personnalisées. Pour les missiles terrestres, le point de lancement est une latitude et longitude fixes selon des sources ouvertes. <br><br> En plus des missiles personnalisés, quelques scénarios prédéfinis impliquant des centaines de missiles sont disponibles. <br><br> Tous les lancements de missiles sont notionnels et ne représentent pas des événements réels. Les trajectoires sont basées sur le même modèle balistique, mais avec différentes portées minimales et maximales."}, "StereoMap": {"bottomIconLabel": "Carte stéréographique", "title": "Menu Carte stéréographique", "helpBody": "Le menu Carte stéréographique permet de visualiser les traces au sol des satellites en projection stéréographique. <br/><br/> V<PERSON> pouvez cliquer sur un point de la trace pour changer l’heure de la simulation à ce moment précis. <br/><br/> Les points jaunes indiquent que le satellite est visible du capteur. Les points rouges indiquent qu’il ne l’est pas. Le point le plus proche du satellite correspond à l’heure actuelle."}, "SensorFov": {"bottomIconLabel": "Champ de vision capteur"}, "SensorSurvFence": {"bottomIconLabel": "Barr<PERSON> capteur"}, "SatelliteViewPlugin": {"bottomIconLabel": "Vue satellite"}, "SatelliteFov": {"bottomIconLabel": "Champ de vision satellite", "title": "Menu Champ de vision satellite", "helpBody": "Le plugin Champ de vision satellite permet de contrôler le champ de vision d’un satellite."}, "Planetarium": {"bottomIconLabel": "Vue planétarium"}, "NightToggle": {"bottomIconLabel": "Mode nuit"}, "SatConstellations": {"bottomIconLabel": "Constellations", "title": "<PERSON><PERSON>", "helpBody": "Le menu Constellations permet de visualiser des groupes de satellites. <br><br> Pour certaines constellations, des liaisons montantes/descendantes ou inter-satellites notionnelles seront affichées."}, "CountriesMenu": {"bottomIconLabel": "Pays", "title": "Menu Pays", "helpBody": "Le menu Pays permet de filtrer les satellites par pays d’origine."}, "ColorMenu": {"bottomIconLabel": "<PERSON><PERSON><PERSON><PERSON> de couleurs", "title": "<PERSON><PERSON> couleurs", "helpBody": "Le menu Couleurs permet de changer le thème de couleurs utilisé pour afficher les objets. <br><br> Les différents thèmes peuvent modifier les couleurs en fonction des orbites des objets, de leurs caractéristiques ou de leur relation avec le soleil et/ou la Terre."}, "Screenshot": {"bottomIconLabel": "<PERSON><PERSON><PERSON> une photo"}, "LaunchCalendar": {"bottomIconLabel": "Calendrier des lancements"}, "TimeMachine": {"bottomIconLabel": "Machine à remonter le temps"}, "SatellitePhotos": {"bottomIconLabel": "Photos satellites", "title": "Menu Photos satellites", "helpBody": "Le menu Photos satellites permet d'afficher des photos en direct de certains satellites. <br><br> Remarque : des changements dans l'API d'images peuvent entraîner la sélection du mauvais satellite dans KeepTrack."}, "ScreenRecorder": {"bottomIconLabel": "Enregistrer une vidéo"}, "Astronomy": {"bottomIconLabel": "Astronomie"}, "Calculator": {"bottomIconLabel": "Transformations de repère", "title": "Menu Transformations de repère", "helpBody": "Le menu Transformations de repère permet de convertir entre différents repères de référence. <br><br> Le menu permet de convertir entre les repères suivants : <ul style=\"margin-left: 40px;\"> <li> ECI - Terre centrée inertielle </li> <li> ECEF - Terre centrée fixe </li> <li> Géodésique </li> <li> Topocentrique </li> </ul>"}, "AnalysisMenu": {"bottomIconLabel": "Analyse", "title": "<PERSON><PERSON>", "helpBody": "Le menu Analyse propose plusieurs outils pour vous aider à analyser les données de la vue actuelle. Les outils sont : <ul style=\"margin-left: 40px;\"> <li>Exporter les TLEs officiels - Exporter les vrais ensembles de deux lignes d'éléments.</li> <li>Exporter les 3LES - Exporter les ensembles de trois lignes d'éléments.</li> <li>Exporter les TLEs KeepTrack - Exporter tous les ensembles de deux lignes d'éléments KeepTrack, y compris les analystes.</li> <li>Exporter les 3LES KeepTrack - Exporter tous les ensembles de trois lignes d'éléments KeepTrack, y compris les analystes.</li> <li>Trouver les objets proches - Trouver les objets proches les uns des autres.</li> <li>Trouver les rentrées - Trouver les objets susceptibles de rentrer dans l'atmosphère.</li> <li>Meilleurs passages - Trouver les meilleurs passages pour un satellite selon le capteur sélectionné.</li> </ul>"}, "SettingsMenuPlugin": {"bottomIconLabel": "Paramètres", "title": "<PERSON><PERSON>", "helpBody": "Le menu Paramètres vous permet de configurer l'application."}, "VideoDirectorPlugin": {"bottomIconLabel": "Réalisateur vidéo", "title": "Menu Réalisateur vidéo", "helpBody": "Le menu Réalisateur vidéo permet de manipuler la caméra et les objets de la scène pour créer une vidéo."}, "CreateSat": {"bottomIconLabel": "Créer un satellite", "title": "Créer un satellite", "helpBody": "Le menu Créer un satellite permet de créer un satellite à partir de ses éléments képlériens."}, "DopsPlugin": {"bottomIconLabel": "Voir les DOPs", "title": "Menu Dilution de Précision", "helpBody": "Le menu Dilution de Précision (DOP) permet de calculer les valeurs DOP pour un satellite et un capteur. <br><br> Les valeurs DOP sont : <ul style=\"margin-left: 40px;\"> <li> GDOP - Dilution géométrique de précision </li> <li> PDOP - Dilution de précision de position </li> <li> HDOP - Dilution de précision horizontale </li> <li> VDOP - Dilution de précision verticale </li> <li> TDOP - Dilution de précision temporelle </li> <li> NDOP - Dilution de précision selon le nombre de satellites </li> </ul>"}, "EciPlot": {"bottomIconLabel": "Trace ECI", "title": "Menu Trace ECI", "helpBody": "Le menu Trace ECI permet de tracer la position du satellite dans le repère Terre centrée inertielle (ECI)."}, "EcfPlot": {"bottomIconLabel": "Trace ECF", "title": "Menu Trace ECF", "helpBody": "Le menu Trace ECF permet de tracer la position du satellite dans le repère Terre centrée fixe (ECF)."}, "RicPlot": {"bottomIconLabel": "Trace RIC", "title": "Menu Trace RIC", "helpBody": "Le menu Trace RIC permet de tracer la position du satellite dans le repère Radial, In-Track, Cross-Track (RIC)."}, "Time2LonPlots": {"bottomIconLabel": "Trace en cascade", "title": "Menu Cascade", "helpBody": "Le menu Trace Cascade (Time to Longitude) permet de tracer la longitude du satellite en fonction du temps."}, "Lat2LonPlots": {"bottomIconLabel": "<PERSON> vs <PERSON><PERSON>", "title": "Menu Latitude vs Longitude", "helpBody": "Le menu Latitude vs Longitude permet de tracer la latitude en fonction de la longitude dans la ceinture GEO."}, "Inc2AltPlots": {"bottomIconLabel": "Trace Inc vs Alt", "title": "<PERSON><PERSON> vs Altitude", "helpBody": "Le menu Inclinaison vs Altitude permet de tracer l'inclinaison du satellite en fonction de son altitude."}, "Inc2LonPlots": {"bottomIconLabel": "Trace Inc vs Lon", "title": "<PERSON><PERSON> vs Longitude", "helpBody": "Le menu Inclinaison vs Longitude permet de tracer l'inclinaison en fonction de la longitude dans la ceinture GEO."}, "GraphicsMenuPlugin": {"bottomIconLabel": "Menu Graphismes", "title": "Menu Graphismes", "helpBody": "Le menu Graphismes permet de modifier les paramètres graphiques de l'application."}}}