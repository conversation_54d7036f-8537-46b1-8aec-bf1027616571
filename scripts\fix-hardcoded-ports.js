#!/usr/bin/env node

/**
 * 修复硬编码端口脚本
 * 查找并修复所有硬编码的端口，替换为配置加载器调用
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 修复硬编码端口工具\n');

// 需要检查的文件模式
const filePatterns = [
    { pattern: 'public/**/*.html', description: 'HTML文件' },
    { pattern: 'public/**/*.js', description: 'JavaScript文件' },
    { pattern: 'src/**/*.ts', description: 'TypeScript文件' },
    { pattern: 'src/**/*.js', description: 'JavaScript文件' }
];

// 硬编码端口的正则表达式
const portPatterns = [
    {
        regex: /localhost:3001/g,
        description: '旧端口3001',
        severity: 'error'
    },
    {
        regex: /localhost:5001/g,
        description: '新端口5001（硬编码）',
        severity: 'warning'
    },
    {
        regex: /:\s*['"]?3001['"]?/g,
        description: '端口3001配置',
        severity: 'error'
    },
    {
        regex: /:\s*['"]?5001['"]?/g,
        description: '端口5001配置（硬编码）',
        severity: 'warning'
    }
];

let issues = [];
let fixes = [];

// 递归获取文件列表
function getFiles(dir, extension) {
    const files = [];
    
    function traverse(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                // 跳过node_modules等目录
                if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
                    traverse(fullPath);
                }
            } else if (stat.isFile()) {
                if (extension.some(ext => fullPath.endsWith(ext))) {
                    files.push(fullPath);
                }
            }
        }
    }
    
    traverse(dir);
    return files;
}

// 检查文件中的硬编码端口
function checkFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        
        lines.forEach((line, lineNumber) => {
            portPatterns.forEach(pattern => {
                const matches = [...line.matchAll(pattern.regex)];
                
                matches.forEach(match => {
                    issues.push({
                        file: filePath,
                        line: lineNumber + 1,
                        column: match.index + 1,
                        match: match[0],
                        description: pattern.description,
                        severity: pattern.severity,
                        fullLine: line.trim()
                    });
                });
            });
        });
        
    } catch (error) {
        console.log(`❌ 读取文件失败 ${filePath}: ${error.message}`);
    }
}

// 生成修复建议
function generateFixSuggestions(issue) {
    const suggestions = [];
    
    if (issue.file.endsWith('.html')) {
        suggestions.push({
            type: 'html',
            description: '在HTML中引入配置加载器',
            code: '<script src="js/config-loader.js"></script>'
        });
        
        if (issue.match.includes('localhost:')) {
            suggestions.push({
                type: 'javascript',
                description: '使用配置加载器获取API地址',
                code: `
// 替换硬编码URL
// 旧代码: fetch('http://localhost:3001/api/...')
// 新代码:
const apiBaseUrl = await window.configLoader.getApiBaseUrl();
fetch(\`\${apiBaseUrl}/api/...\`)
`
            });
        }
    }
    
    if (issue.file.endsWith('.js')) {
        suggestions.push({
            type: 'javascript',
            description: '使用配置加载器替代硬编码',
            code: `
// 替换硬编码端口
// 旧代码: const port = '3001';
// 新代码:
const config = await window.configLoader.loadConfig();
const port = config.apiServer.port;
`
        });
    }
    
    return suggestions;
}

// 主检查函数
function checkHardcodedPorts() {
    console.log('🔍 扫描硬编码端口...\n');
    
    // 检查前端文件
    const frontendFiles = [
        ...getFiles('public', ['.html', '.js']),
        ...getFiles('src', ['.ts', '.js'])
    ];
    
    frontendFiles.forEach(file => {
        checkFile(file);
    });
    
    return issues;
}

// 生成报告
function generateReport() {
    console.log('📊 硬编码端口检查报告');
    console.log('='.repeat(50));
    
    if (issues.length === 0) {
        console.log('🎉 没有发现硬编码端口问题！');
        return;
    }
    
    // 按严重程度分组
    const errors = issues.filter(i => i.severity === 'error');
    const warnings = issues.filter(i => i.severity === 'warning');
    
    if (errors.length > 0) {
        console.log(`\n❌ 发现 ${errors.length} 个错误（必须修复）:`);
        errors.forEach((issue, index) => {
            console.log(`\n${index + 1}. ${issue.description}`);
            console.log(`   文件: ${issue.file}:${issue.line}:${issue.column}`);
            console.log(`   内容: ${issue.fullLine}`);
            console.log(`   匹配: ${issue.match}`);
            
            const suggestions = generateFixSuggestions(issue);
            if (suggestions.length > 0) {
                console.log('   修复建议:');
                suggestions.forEach(suggestion => {
                    console.log(`     - ${suggestion.description}`);
                    if (suggestion.code) {
                        console.log(`       ${suggestion.code.trim()}`);
                    }
                });
            }
        });
    }
    
    if (warnings.length > 0) {
        console.log(`\n⚠️  发现 ${warnings.length} 个警告（建议修复）:`);
        warnings.forEach((issue, index) => {
            console.log(`\n${index + 1}. ${issue.description}`);
            console.log(`   文件: ${issue.file}:${issue.line}:${issue.column}`);
            console.log(`   内容: ${issue.fullLine}`);
        });
    }
    
    console.log('\n💡 总体建议:');
    console.log('1. 确保所有HTML页面都引入了 js/config-loader.js');
    console.log('2. 使用 window.configLoader.getApiBaseUrl() 获取API地址');
    console.log('3. 使用 window.configLoader.getApiUrl(endpoint) 获取特定端点');
    console.log('4. 避免在代码中硬编码任何端口号');
    console.log('5. 所有端口配置应该在 config.json 中统一管理');
    
    console.log('\n🔧 快速修复命令:');
    console.log('   npm run validate:config  # 验证配置一致性');
    console.log('   npm run test:login       # 测试登录功能');
    console.log('   npm run start:api        # 重启API服务器');
}

// 主函数
function main() {
    console.log(`📅 检查时间: ${new Date().toISOString()}`);
    console.log(`📂 当前目录: ${process.cwd()}\n`);
    
    // 检查是否在项目根目录
    if (!fs.existsSync('package.json')) {
        console.log('❌ 错误: 请在项目根目录中运行此脚本');
        process.exit(1);
    }
    
    checkHardcodedPorts();
    generateReport();
    
    // 返回适当的退出码
    const hasErrors = issues.some(i => i.severity === 'error');
    process.exit(hasErrors ? 1 : 0);
}

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log('硬编码端口修复工具 - 查找并修复硬编码的端口配置');
    console.log('');
    console.log('使用方法:');
    console.log('  node scripts/fix-hardcoded-ports.js');
    console.log('');
    console.log('功能:');
    console.log('  - 扫描所有前端文件中的硬编码端口');
    console.log('  - 提供具体的修复建议');
    console.log('  - 生成详细的问题报告');
    console.log('');
    console.log('注意事项:');
    console.log('  - 请在项目根目录中运行');
    console.log('  - 建议在修复后运行测试验证');
    process.exit(0);
}

// 运行检查
main();
