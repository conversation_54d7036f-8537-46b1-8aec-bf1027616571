@echo off
echo 将JPG壁纸转换为WebP格式
echo.

cd /d "%~dp0..\public\img\wallpaper"

echo 当前目录: %CD%
echo.

REM 检查是否安装了cwebp工具
where cwebp >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误：未找到cwebp工具
    echo 请从以下地址下载WebP工具：
    echo https://developers.google.com/speed/webp/download
    echo 或者使用在线转换工具
    pause
    exit /b 1
)

echo 开始转换图片...
echo.

REM 转换所有JPG文件为WebP格式
for %%f in (*.jpg) do (
    echo 转换: %%f
    cwebp -q 85 "%%f" -o "%%~nf.webp"
    if exist "%%~nf.webp" (
        echo ✓ 成功创建: %%~nf.webp
    ) else (
        echo ✗ 转换失败: %%f
    )
    echo.
)

echo 转换完成！
echo.
echo 生成的WebP文件：
dir *.webp
echo.
echo 下一步：检查生成的WebP文件质量，如果满意可以删除原始JPG文件
pause
