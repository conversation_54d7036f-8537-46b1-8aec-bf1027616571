# 🚀 API服务器部署指南

## 问题诊断

如果访问 `http://your-server:5001/` 返回：
```json
{"statusCode":404,"timestamp":"2025-07-22T12:35:32.142Z","path":"/","message":"Cannot GET /"}
```

这表明：
1. **端口5001被其他服务占用** - 可能是NestJS或其他Node.js应用
2. **我们的API服务器没有正确启动**
3. **存在路由配置问题**

## 🔍 诊断步骤

### 1. 检查端口占用
```bash
# Linux/Mac
lsof -i :5001
netstat -tulpn | grep :5001

# Windows
netstat -ano | findstr :5001
```

### 2. 检查进程
```bash
# 查看Node.js进程
ps aux | grep node
# 或
pm2 list
```

### 3. 检查服务器日志
```bash
# 如果使用pm2
pm2 logs

# 如果直接运行
npx tsx src/api/server.ts
```

## 🛠️ 部署方案

### 方案1：直接部署
```bash
# 1. 安装依赖
npm install

# 2. 设置环境变量
export PORT=5001
export NODE_ENV=production
export HOST=0.0.0.0

# 3. 启动服务
npx tsx src/api/server.ts
```

### 方案2：使用PM2部署
```bash
# 1. 安装PM2
npm install -g pm2

# 2. 创建PM2配置文件
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'space-api',
    script: 'npx',
    args: 'tsx src/api/server.ts',
    cwd: '/path/to/your/project',
    env: {
      NODE_ENV: 'production',
      PORT: 5001,
      HOST: '0.0.0.0'
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

# 3. 启动服务
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 方案3：Docker部署
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 5001

CMD ["npx", "tsx", "src/api/server.ts"]
```

```bash
# 构建和运行
docker build -t space-api .
docker run -d -p 5001:5001 --name space-api space-api
```

## 🔧 配置文件

### 环境变量配置
创建 `.env` 文件：
```env
PORT=5001
HOST=0.0.0.0
NODE_ENV=production
ALLOWED_ORIGINS=http://your-frontend-domain.com,https://your-frontend-domain.com
```

### Nginx反向代理配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🚨 故障排除

### 问题1：端口被占用
```bash
# 找到占用进程
sudo lsof -i :5001
# 杀死进程
sudo kill -9 <PID>
```

### 问题2：权限问题
```bash
# 确保有执行权限
chmod +x src/api/server.ts
# 或使用sudo运行（不推荐）
```

### 问题3：依赖问题
```bash
# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

### 问题4：路径问题
确保在项目根目录运行：
```bash
cd /path/to/keeptrack.space_new
npx tsx src/api/server.ts
```

## ✅ 验证部署

部署成功后，访问以下URL应该返回正确响应：

1. **根路径**: `http://your-server:5001/`
   - 应返回API信息JSON

2. **健康检查**: `http://your-server:5001/api/health`
   - 应返回 `{"status": "ok"}`

3. **配置文件**: `http://your-server:5001/api/config.json`
   - 应返回配置信息

## 📝 日志监控

```bash
# PM2日志
pm2 logs space-api

# 直接运行日志
npx tsx src/api/server.ts 2>&1 | tee api.log
```

正确启动后应该看到：
```
🚀 API服务已启动:
  环境: production
  访问地址: http://localhost:5001
  网络地址: http://0.0.0.0:5001
  启动时间: 2025-07-22T12:35:32.142Z
```
