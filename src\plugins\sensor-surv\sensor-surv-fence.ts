/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * https://spacedefense
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { KeepTrackApiEvents, MenuMode } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import fencePng from '@public/img/icons/fence.png';
import { DetailedSensor } from 'ootk';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { SensorFov } from '../sensor-fov/sensor-fov';
import { SensorListPlugin } from '../sensor-list/sensor-list';

export class SensorSurvFence extends KeepTrackPlugin {
  readonly id = 'SensorSurvFence';
  dependencies_: string[] = [SensorListPlugin.name];
  bottomIconCallback = () => {
    if (!this.isMenuButtonActive) {
      this.disableSurvView();
    } else {
      this.enableSurvView_();
    }
  };

  menuMode: MenuMode[] = [MenuMode.ADVANCED, MenuMode.ALL];

  bottomIconImg = fencePng;
  isIconDisabledOnLoad = true;
  isIconDisabled = true;
  isRequireSensorSelected = true;

  addJs(): void {
    super.addJs();

    keepTrackApi.on(KeepTrackApiEvents.setSensor, this.enableIfSensorSelected.bind(this));
    keepTrackApi.on(KeepTrackApiEvents.sensorDotSelected, this.enableIfSensorSelected.bind(this));
  }

  enableIfSensorSelected(sensor?: DetailedSensor): void {
    if (sensor) {
      this.setBottomIconToEnabled();
    } else {
      this.setBottomIconToDisabled();
    }
  }

  disableSurvView() {
    this.setBottomIconToUnselected(false);
  }

  private enableSurvView_() {
    keepTrackApi.getPlugin(SensorFov)?.setBottomIconToUnselected();
    this.setBottomIconToSelected();
  }
}
