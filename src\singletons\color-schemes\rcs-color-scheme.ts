/* eslint-disable complexity */
import { ColorInformation, Pickable, rgbaArray } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { BaseObject, DetailedSatellite } from 'ootk';
import { ColorScheme, ColorSchemeColorMap } from './color-scheme';

export interface SourceColorSchemeColorMap extends ColorSchemeColorMap {
  sourceUssf: rgbaArray;
  sourceAldoria: rgbaArray;
  sourceCelestrak: rgbaArray;
  sourcePrismnet: rgbaArray;
  sourceVimpel: rgbaArray;
}

export class RcsColorScheme extends ColorScheme {
  colorTheme: Record<string, rgbaArray>;
  objectTypeFlags: Record<string, boolean>;
  readonly label = 'RCS面积';
  readonly id = 'RcsColorScheme';
  static readonly id = 'RcsColorScheme';

  static readonly uniqueObjectTypeFlags = {
    rcsXXSmall: true,
    rcsXSmall: true,
    rcsSmall: true,
    rcsMed: true,
    rcsLarge: true,
    rcsUnknown: true,
  };

  static readonly uniqueColorTheme = {
    rcsXXSmall: [0.0, 0.0, 0.0, 1.0] as rgbaArray,
    rcsXSmall: [0.0, 0.0, 0.0, 1.0] as rgbaArray,
    rcsSmall: [0.0, 0.0, 1.0, 1.0] as rgbaArray,
    rcsMed: [0.0, 0.0, 1.0, 1.0] as rgbaArray,
    rcsLarge: [0.0, 0.0, 1.0, 1.0] as rgbaArray,
    rcsUnknown: [0.0, 0.0, 1.0, 1.0] as rgbaArray,
  };

  constructor() {
    super(RcsColorScheme.uniqueColorTheme);
    this.objectTypeFlags = {
      ...this.objectTypeFlags, ...RcsColorScheme.uniqueObjectTypeFlags,
    };
  }

  update(obj: BaseObject): ColorInformation {
    if (!obj.isSatellite) {
      return { color: this.colorTheme.transparent, pickable: Pickable.No };
    }

    const sat = obj as DetailedSatellite;

    if (!sat.rcs) {
      if (this.objectTypeFlags.rcsUnknown) {
        return {
          color: this.colorTheme.rcsUnknown,
          pickable: Pickable.Yes,
        };
      }

      return {
        color: this.colorTheme.deselected,
        pickable: Pickable.No,
      };

    }
    if (sat.rcs < 0.01 && this.objectTypeFlags.rcsXXSmall) {
      return {
        color: this.colorTheme.rcsXXSmall,
        pickable: Pickable.Yes,
      };
    }
    if (sat.rcs >= 0.01 && sat.rcs <= 0.05 && this.objectTypeFlags.rcsXSmall) {
      return {
        color: this.colorTheme.rcsXSmall,
        pickable: Pickable.Yes,
      };
    }
    if (sat.rcs >= 0.05 && sat.rcs <= 0.1 && this.objectTypeFlags.rcsSmall) {
      return {
        color: this.colorTheme.rcsSmall,
        pickable: Pickable.Yes,
      };
    }
    if (sat.rcs >= 0.1 && sat.rcs <= 1 && this.objectTypeFlags.rcsMed) {
      return {
        color: this.colorTheme.rcsMed,
        pickable: Pickable.Yes,
      };
    }
    if (sat.rcs > 1 && this.objectTypeFlags.rcsLarge) {
      return {
        color: this.colorTheme.rcsLarge,
        pickable: Pickable.Yes,
      };
    }

    // Flag must have been turned off
    return {
      color: this.colorTheme.deselected,
      pickable: Pickable.No,
    };
  }

  updateGroup(obj: BaseObject): ColorInformation {
    // 检查是否有选中的组
    const selectedGroup = keepTrackApi.getGroupsManager().selectedGroup;
    if (!selectedGroup) {
      // 没有选中组时，使用正常的颜色方案
      return this.update(obj);
    }

    // 有选中组时，检查对象是否在组中
    if (!selectedGroup.hasObject(obj.id)) {
      // 不在组中的对象强制设为完全透明（隐藏）
      return {
        color: [0.0, 0.0, 0.0, 0.0] as rgbaArray, // 强制透明
        pickable: Pickable.No,
      };
    }

    // 在组中的对象使用正常的颜色方案
    return this.update(obj);
  }

  static readonly legendHtml = keepTrackApi.html`
  <ul id="legend-list-rcs">
  <li>
      <div class="Square-Box legend-rcsXXSmall-box"></div>
      小于0.01平方米
    </li>
    <li>
      <div class="Square-Box legend-rcsXSmall-box"></div>
      0.01至0.05平方米
    </li>
    <li>
      <div class="Square-Box legend-rcsSmall-box"></div>
      0.05至0.1平方米
    </li>
    <li>
      <div class="Square-Box legend-rcsMed-box"></div>
      0.1至1平方米
    </li>
    <li>
      <div class="Square-Box legend-rcsLarge-box"></div>
      大于1平方米
    </li>
    <li>
      <div class="Square-Box legend-rcsUnknown-box"></div>
      无公开数据
    </li>
  </ul>
  `.trim();
}
