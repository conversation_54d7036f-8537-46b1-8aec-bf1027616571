const { Client } = require('@elastic/elasticsearch');

// ES 配置
const esConfig = {
  url: "http://123.57.173.156:9200",
  username: "readonly_tle",
  password: "<PERSON><PERSON><PERSON>@readonly4tle"
};

async function checkESIndices() {
  try {
    console.log('正在连接 Elasticsearch...');
    console.log('ES 地址:', esConfig.url);
    
    const client = new Client({
      node: esConfig.url,
      auth: {
        username: esConfig.username,
        password: esConfig.password,
      },
      maxRetries: 3,
      requestTimeout: 10000,
    });

    // 1. 列出所有可访问的索引
    console.log('\n1. 列出所有可访问的索引...');
    try {
      const indices = await client.cat.indices({
        format: 'json',
        h: 'index,docs.count,store.size'
      });
      
      console.log('可访问的索引:');
      indices.forEach(index => {
        console.log(`  - ${index.index}: ${index['docs.count']} 条记录, ${index['store.size']}`);
      });
      
      // 2. 检查每个索引的样本数据
      console.log('\n2. 检查每个索引的样本数据...');
      for (const indexInfo of indices) {
        const indexName = indexInfo.index;
        if (indexName.startsWith('.')) continue; // 跳过系统索引
        
        console.log(`\n--- 索引: ${indexName} ---`);
        try {
          const searchResult = await client.search({
            index: indexName,
            size: 1,
            query: {
              match_all: {}
            }
          });
          
          if (searchResult.hits?.hits?.length > 0) {
            const firstRecord = searchResult.hits.hits[0]._source;
            console.log('字段列表:');
            Object.keys(firstRecord).forEach(field => {
              const value = firstRecord[field];
              const type = typeof value;
              const preview = type === 'string' ? value.substring(0, 50) : value;
              console.log(`  - ${field}: ${type} = ${preview}`);
            });
            
            // 检查是否有经度相关字段
            const longitudeFields = Object.keys(firstRecord).filter(field => 
              field.toLowerCase().includes('long') || 
              field.toLowerCase().includes('lon') ||
              field.toLowerCase().includes('subsat')
            );
            if (longitudeFields.length > 0) {
              console.log(`✓ 可能的经度字段: ${longitudeFields.join(', ')}`);
            }
            
            // 检查是否有类型字段
            const typeFields = Object.keys(firstRecord).filter(field => 
              field.toLowerCase().includes('type') || 
              field.toLowerCase().includes('class') ||
              field.toLowerCase().includes('orbit')
            );
            if (typeFields.length > 0) {
              console.log(`✓ 可能的类型字段: ${typeFields.join(', ')}`);
            }
          }
        } catch (error) {
          console.log(`  查询失败: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.log('列出索引失败:', error.message);
      
      // 如果无法列出索引，尝试一些常见的索引名
      console.log('\n尝试常见的索引名...');
      const commonIndices = [
        'orbital_tle',
        'satellite_data', 
        'satellite_history',
        'satellite_positions',
        'geo_satellites',
        'orbital_data',
        'tle_data',
        'satellite_tracking'
      ];
      
      for (const indexName of commonIndices) {
        try {
          const exists = await client.indices.exists({ index: indexName });
          if (exists) {
            console.log(`✓ 索引存在: ${indexName}`);
            
            // 查询样本数据
            const searchResult = await client.search({
              index: indexName,
              size: 1,
              query: { match_all: {} }
            });
            
            if (searchResult.hits?.hits?.length > 0) {
              const record = searchResult.hits.hits[0]._source;
              console.log(`  字段: ${Object.keys(record).join(', ')}`);
            }
          }
        } catch (error) {
          // 索引不存在或无权限
        }
      }
    }

  } catch (error) {
    console.error('检查ES索引失败:', error.message);
    console.error('错误详情:', error);
  }
}

checkESIndices();
