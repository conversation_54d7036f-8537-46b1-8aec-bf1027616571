import { keepTrackApi } from '@app/api/keepTrackApi';
import { RADIUS_OF_EARTH } from '@app/lib/constants';
import * as glm from 'gl-matrix';
/* eslint-disable no-useless-escape */
/* eslint-disable camelcase */

/* ***************************************************************************
 * Initialization Code
 * ***************************************************************************/
const NUM_LAT_SEGS = 16;
const NUM_LON_SEGS = 16;
const DRAW_RADIUS = 2500;
export const init = async () => {
  const { gl } = keepTrackApi.programs.drawManager;

  initProgram(gl);
  initBuffers(gl);
  initVao(gl);

  tools.isLoaded = true;
};
export const initProgram = (gl: WebGL2RenderingContext) => {
  const fragShader = gl.createShader(gl.FRAGMENT_SHADER);
  gl.shaderSource(fragShader, shaders.tools.frag);
  gl.compileShader(fragShader);

  const vertShader = gl.createShader(gl.VERTEX_SHADER);
  gl.shaderSource(vertShader, shaders.tools.vert);
  gl.compileShader(vertShader);

  tools.program = <any>gl.createProgram();
  gl.attachShader(tools.program, vertShader);
  gl.attachShader(tools.program, fragShader);
  gl.linkProgram(tools.program);

  // Assign Attributes
  tools.program.a_position = gl.getAttribLocation(tools.program, 'a_position');
  tools.program.u_pMatrix = gl.getUniformLocation(tools.program, 'u_pMatrix');
  tools.program.u_camMatrix = gl.getUniformLocation(tools.program, 'u_camMatrix');
  tools.program.u_mvMatrix = gl.getUniformLocation(tools.program, 'u_mvMatrix');
  tools.program.u_nMatrix = gl.getUniformLocation(tools.program, 'u_nMatrix');
  tools.program.u_drawPosition = gl.getUniformLocation(tools.program, 'u_drawPosition');
};
export const initBuffers = (gl: WebGL2RenderingContext) => {
  // generate a uvsphere bottom up, CCW order
  const vertPos = [];
  const vertNorm = [];
  for (let lat = 0; lat <= NUM_LAT_SEGS; lat++) {
    const latAngle = (Math.PI / NUM_LAT_SEGS) * lat - Math.PI / 2;
    const diskRadius = Math.cos(Math.abs(latAngle));
    const z = Math.sin(latAngle);
    for (let lon = 0; lon <= NUM_LON_SEGS; lon++) {
      // add an extra vertex for texture funness
      const lonAngle = ((Math.PI * 2) / NUM_LON_SEGS) * lon;
      const x = Math.cos(lonAngle) * diskRadius;
      const y = Math.sin(lonAngle) * diskRadius;
      vertPos.push(x * DRAW_RADIUS);
      vertPos.push(y * DRAW_RADIUS);
      vertPos.push(z * DRAW_RADIUS);
      vertNorm.push(x);
      vertNorm.push(y);
      vertNorm.push(z);
    }
  }

  // ok let's calculate vertex draw orders.... indiv triangles
  const vertIndex = [];
  for (let lat = 0; lat < NUM_LAT_SEGS; lat++) {
    // this is for each QUAD, not each vertex, so <
    for (let lon = 0; lon < NUM_LON_SEGS; lon++) {
      const blVert = lat * (NUM_LON_SEGS + 1) + lon; // there's NUM_LON_SEGS + 1 verts in each horizontal band
      const brVert = blVert + 1;
      const tlVert = (lat + 1) * (NUM_LON_SEGS + 1) + lon;
      const trVert = tlVert + 1;
      vertIndex.push(blVert);
      vertIndex.push(brVert);
      vertIndex.push(tlVert);

      vertIndex.push(tlVert);
      vertIndex.push(trVert);
      vertIndex.push(brVert);
    }
  }
  tools.buffers.vertCount = vertIndex.length;

  tools.buffers.vertPosBuf = gl.createBuffer();
  gl.bindBuffer(gl.ARRAY_BUFFER, tools.buffers.vertPosBuf);
  gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(vertPos), gl.STATIC_DRAW);

  tools.buffers.vertNormBuf = gl.createBuffer();
  gl.bindBuffer(gl.ARRAY_BUFFER, tools.buffers.vertNormBuf);
  gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(vertNorm), gl.STATIC_DRAW);

  tools.buffers.vertIndexBuf = gl.createBuffer();
  gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, tools.buffers.vertIndexBuf);
  gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, new Uint16Array(vertIndex), gl.STATIC_DRAW);
};
export const initVao = (gl: WebGL2RenderingContext) => {
  // Make New Vertex Array Objects
  tools.vao = gl.createVertexArray();
  gl.bindVertexArray(tools.vao);

  gl.bindBuffer(gl.ARRAY_BUFFER, tools.buffers.vertPosBuf);
  gl.enableVertexAttribArray(tools.program.a_position);
  gl.vertexAttribPointer(tools.program.a_position, 3, gl.FLOAT, false, 0, 0);

  gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, tools.buffers.vertIndexBuf);

  gl.bindVertexArray(null);
};

/* ***************************************************************************
 * Render Loop Code
 * ***************************************************************************/
export const update = () => {
  tools.mvMatrix = glm.mat4.create();
  glm.mat4.identity(tools.mvMatrix);
  glm.mat4.translate(tools.mvMatrix, tools.mvMatrix, glm.vec3.fromValues(tools.drawPosition[0], tools.drawPosition[1], tools.drawPosition[2]));
};
export const draw = function (pMatrix: glm.mat4, camMatrix: glm.mat4, tgtBuffer?: WebGLFramebuffer) {
  const { gl } = keepTrackApi.programs.drawManager;
  tools.pMatrix = pMatrix;
  tools.camMatrix = camMatrix;
  if (!tools.isLoaded) return;

  update();

  gl.useProgram(tools.program);
  gl.bindFramebuffer(gl.FRAMEBUFFER, tgtBuffer);

  gl.enable(gl.BLEND);
  gl.depthMask(false);

  // Set the uniforms
  gl.uniformMatrix3fv(tools.program.u_nMatrix, false, tools.nMatrix);
  gl.uniformMatrix4fv(tools.program.u_mvMatrix, false, tools.mvMatrix);
  gl.uniformMatrix4fv(tools.program.u_pMatrix, false, pMatrix);
  gl.uniformMatrix4fv(tools.program.u_camMatrix, false, camMatrix);
  gl.uniform1f(tools.program.u_drawPosition, Math.sqrt(tools.drawPosition[0] ** 2 + tools.drawPosition[1] ** 2 + tools.drawPosition[2] ** 2));

  gl.bindVertexArray(tools.vao);
  gl.drawElements(gl.TRIANGLES, tools.buffers.vertCount, gl.UNSIGNED_SHORT, 0);

  gl.depthMask(true);
  gl.disable(gl.BLEND);

  gl.bindVertexArray(null);
};

/* ***************************************************************************
 * Export Code
 * ***************************************************************************/
const shaders = {
  tools: {
    frag: `#version 300 es
      #ifdef GL_FRAGMENT_PRECISION_HIGH
        precision highp float;
      #else
        precision mediump float;
      #endif

      out vec4 fragColor;

      void main(void) {
          fragColor = vec4(0.0, 0.2, 0.8, 0.2);
      }
      `,
    vert: `#version 300 es
      uniform mat4 u_pMatrix;
      uniform mat4 u_camMatrix;
      uniform mat4 u_mvMatrix;
      uniform mat3 u_nMatrix;
      uniform float u_drawPosition;

      in vec3 a_position;

      void main(void) {
          vec4 position = u_mvMatrix * vec4(a_position, 1.0);
          gl_Position = u_pMatrix * u_camMatrix * position;
      }
      `,
  },
};
export const tools = {
  vao: <WebGLVertexArrayObject>null,
  buffers: {
    vertCount: 0,
    vertPosBuf: <WebGLBuffer>null,
    vertNormBuf: <WebGLBuffer>null,
    vertIndexBuf: <WebGLBuffer>null,
  },
  program: {
    a_position: <number>null,
    u_pMatrix: null as unknown as WebGLUniformLocation,
    u_camMatrix: null as unknown as WebGLUniformLocation,
    u_mvMatrix: null as unknown as WebGLUniformLocation,
    u_nMatrix: null as unknown as WebGLUniformLocation,
    u_drawPosition: null as unknown as WebGLUniformLocation,
  },
  camMatrix: glm.mat4.create(),
  mvMatrix: glm.mat4.create(),
  pMatrix: glm.mat4.create(),
  nMatrix: glm.mat3.create(),
  init: init,
  update: update,
  draw: draw,
  drawPosition: [DRAW_RADIUS + RADIUS_OF_EARTH, 0, 0],
  pos: [0, 0, 0],
  isLoaded: false,
};
