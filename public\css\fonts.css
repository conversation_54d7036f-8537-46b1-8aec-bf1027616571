@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src: url('fonts/droid/droid-sans-v6-latin-regular.eot'); /* IE9 Compat Modes */
  src:
    local('Open Sans'),
    local('DroidSans'),
    url('fonts/droid/droid-sans-v6-latin-regular.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('fonts/droid/droid-sans-v6-latin-regular.woff2') format('woff2'),
    /* Super Modern Browsers */ url('fonts/droid/droid-sans-v6-latin-regular.woff') format('woff'),
    /* Modern Browsers */ url('fonts/droid/droid-sans-v6-latin-regular.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('fonts/droid/droid-sans-v6-latin-regular.svg#DroidSans') format('svg'); /* Legacy iOS */
}

/*Icomoon Font*/

@font-face {
  font-family: 'icomoon';
  src: url('fonts/icomoon/icomoon.eot');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'nasalization';
  font-style: normal;
  font-weight: 400;
  src:
    url('fonts/nasalization-rg.woff2') format('woff2'),
    url('fonts/nasalization-rg.woff') format('woff'),
    url('fonts/nasalization-rg.ttf') format('truetype');
}

.ui-icon {
  font-family: 'icomoon', sans-serif;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-location2:before {
  content: '\e948';
}

.icon-undo:before {
  content: '\e965';
}

.icon-redo:before {
  content: '\e966';
}

.icon-undo2:before {
  content: '\e967';
}

.icon-redo2:before {
  content: '\e968';
}

.icon-forward:before {
  content: '\e969';
}

.icon-reply:before {
  content: '\e96a';
}

.icon-search:before {
  content: '\e986';
}

.icon-enlarge:before {
  content: '\e989';
}

.icon-shrink:before {
  content: '\e98a';
}

.icon-list-numbered:before {
  content: '\e9b9';
}

.icon-list:before {
  content: '\e9ba';
}

.icon-list2:before {
  content: '\e9bb';
}

.icon-menu:before {
  content: '\e9bd';
}

.icon-menu2:before {
  content: '\e9be';
}

.icon-menu3:before {
  content: '\e9bf';
}

.icon-menu4:before {
  content: '\e9c0';
}

.icon-download3:before {
  content: '\e9c7';
}

.icon-upload3:before {
  content: '\e9c8';
}

.icon-blocked:before {
  content: '\ea0e';
}

.icon-cross:before {
  content: '\ea0f';
}

.icon-checkmark:before {
  content: '\ea10';
}

/*Icomoon Font*/
