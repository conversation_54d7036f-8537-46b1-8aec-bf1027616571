import { KeepTrackApiEvents, MenuMode } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import videoSettingsPng from '@public/img/icons/video-settings.png';

import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { SoundNames } from '../sounds/SoundNames';

/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * http://www.spacesecure.cn
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

declare module '@app/interfaces' {
  interface UserSettings {
    isBlackEarth: boolean;
    isDrawMilkyWay: boolean;
  }
}

export class VideoDirectorPlugin extends KeepTrackPlugin {
  readonly id = 'VideoDirectorPlugin';
  dependencies_ = [];

  menuMode: MenuMode[] = [MenuMode.EXPERIMENTAL, MenuMode.ALL];

  isRotateL = true;
  isRotateR = false;
  isRotateU = false;
  isRotateD = false;
  bottomIconElementName: string = 'video-director-icon';
  bottomIconImg = videoSettingsPng;
  sideMenuElementName: string = 'video-director-menu';
  sideMenuElementHtml: string = keepTrackApi.html`
  <div id="video-director-menu" class="side-menu-parent start-hidden text-select">
    <div id="video-director-content" class="side-menu">
      <div class="row">
        <form id="video-director-form">
          <div id="video-director-general">
            <div class="row center"></div>
            </br>
            <div class="row center">
              <button id="video-director-submit" class="btn btn-ui waves-effect waves-light" type="submit" name="action">更新编排设置 &#9658;</button>
            </div>
            <h5 class="center-align">视频编排</h5>
            <div class="input-field col s12">
              <input value="0.000075" id="video-director-rotateSpeed" type="text" maxlength="9" />
              <label for="video-director-rotateSpeed" class="active">旋转速度</label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="旋转相机到左边">
                <input id="video-director-rotateL" type="checkbox" checked/>
                <span class="lever"></span>
                向左旋转相机
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="旋转相机到右边">
                <input id="video-director-rotateR" type="checkbox"/>
                <span class="lever"></span>
                向右旋转相机
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="向上旋转相机">
                <input id="video-director-rotateU" type="checkbox"/>
                <span class="lever"></span>
                向上旋转相机
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="向下旋转相机">
                <input id="video-director-rotateD" type="checkbox"/>
                <span class="lever"></span>
                向下旋转相机
              </label>
            </div>
            <div class="input-field col s12">
              <input value="0.05" id="video-director-panSpeed" type="text" maxlength="9" />
              <label for="video-director-panSpeed" class="active">平移速度</label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="向左平移相机">
                <input id="video-director-panL" type="checkbox"/>
                <span class="lever"></span>
                向左平移相机
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="向右平移相机">
                <input id="video-director-panR" type="checkbox"/>
                <span class="lever"></span>
                向右平移相机
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="向上平移相机">
                <input id="video-director-panU" type="checkbox"/>
                <span class="lever"></span>
                向上平移相机
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="向下平移相机">
                <input id="video-director-panD" type="checkbox"/>
                <span class="lever"></span>
                向下平移相机
              </label>
            </div>
            <div class="input-field col s12">
              <input value="0.0005" id="video-director-zoomSpeed" type="text" maxlength="9" />
              <label for="video-director-zoomSpeed" class="active">缩放速度</label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="放大相机">
                <input id="video-director-zoomIn" type="checkbox"/>
                <span class="lever"></span>
                放大相机
              </label>
            </div>
            <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="缩小相机">
                <input id="video-director-zoomOut" type="checkbox"/>
                <span class="lever"></span>
                缩小相机
              </label>
          </div>
          <div class="switch row">
              <label data-position="top" data-delay="50" data-tooltip="禁用选定的卫星点">
                <input id="video-director-selectedColor" type="checkbox"/>
                <span class="lever"></span>
                禁用选定的卫星点
              </label>
          </div>
          <div class="center-align row">
            <button id="video-director-rotate" class="btn btn-ui waves-effect waves-light" type="button" name="action">开始自动旋转 &#9658;</button>
          </div>
        </form>
      </div>
    </div>
  </div>`;

  isNotColorPickerInitialSetup = false;

  addHtml(): void {
    super.addHtml();
    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {
        getEl('video-director-form')!.addEventListener('change', VideoDirectorPlugin.onFormChange);
        getEl('video-director-form')!.addEventListener('submit', VideoDirectorPlugin.onSubmit);
        getEl('video-director-rotate')!.addEventListener('click', () => {
          keepTrackApi.getSoundManager()?.play(SoundNames.BUTTON_CLICK);
          keepTrackApi.getMainCamera().autoRotate(true);
        });
      },
    );
  }

  private static onFormChange(e: Event) {
    if (typeof e === 'undefined' || e === null) {
      throw new Error('e is undefined');
    }

    const elementId = (<HTMLElement>e.target)?.id;

    switch (elementId) {
      case 'video-director-rotateL':
      case 'video-director-rotateR':
      case 'video-director-rotateU':
      case 'video-director-rotateD':
      case 'video-director-panL':
      case 'video-director-panR':
      case 'video-director-panU':
      case 'video-director-panD':
      case 'video-director-zoomIn':
      case 'video-director-zoomOut':
        if ((<HTMLInputElement>getEl(elementId))?.checked) {
          // Play sound for enabling option
          keepTrackApi.getSoundManager()?.play(SoundNames.TOGGLE_ON);
        } else {
          // Play sound for disabling option
          keepTrackApi.getSoundManager()?.play(SoundNames.TOGGLE_OFF);
        }
        break;
      default:
        break;
    }

    if ((<HTMLInputElement>getEl('video-director-selectedColor')).checked && settingsManager.selectedColor[3] !== 0) {
      settingsManager.selectedColorFallback = settingsManager.selectedColor;
      settingsManager.selectedColor = [0, 0, 0, 0];
    } else {
      settingsManager.selectedColor = settingsManager.selectedColorFallback;
    }

    settingsManager.autoRotateSpeed = parseFloat((<HTMLInputElement>getEl('video-director-rotateSpeed')).value);
    settingsManager.autoPanSpeed = parseFloat((<HTMLInputElement>getEl('video-director-panSpeed')).value);
    settingsManager.autoZoomSpeed = parseFloat((<HTMLInputElement>getEl('video-director-zoomSpeed')).value);

    const isRotateL = (<HTMLInputElement>getEl('video-director-rotateL')).checked;
    const isRotateR = (<HTMLInputElement>getEl('video-director-rotateR')).checked;
    const isRotateU = (<HTMLInputElement>getEl('video-director-rotateU')).checked;
    const isRotateD = (<HTMLInputElement>getEl('video-director-rotateD')).checked;
    const isPanL = (<HTMLInputElement>getEl('video-director-panL')).checked;
    const isPanR = (<HTMLInputElement>getEl('video-director-panR')).checked;
    const isPanU = (<HTMLInputElement>getEl('video-director-panU')).checked;
    const isPanD = (<HTMLInputElement>getEl('video-director-panD')).checked;
    const isZoomIn = (<HTMLInputElement>getEl('video-director-zoomIn')).checked;
    const isZoomOut = (<HTMLInputElement>getEl('video-director-zoomOut')).checked;

    if (isRotateL && !settingsManager.isAutoRotateL) {
      (<HTMLInputElement>getEl('video-director-rotateR')).checked = false;
    }

    if (isRotateR && !settingsManager.isAutoRotateR) {
      (<HTMLInputElement>getEl('video-director-rotateL')).checked = false;
    }

    if (isRotateU && !settingsManager.isAutoRotateU) {
      (<HTMLInputElement>getEl('video-director-rotateD')).checked = false;
    }

    if (isRotateD && !settingsManager.isAutoRotateD) {
      (<HTMLInputElement>getEl('video-director-rotateU')).checked = false;
    }

    if (isPanL && !settingsManager.isAutoPanL) {
      (<HTMLInputElement>getEl('video-director-panR')).checked = false;
    }

    if (isPanR && !settingsManager.isAutoPanR) {
      (<HTMLInputElement>getEl('video-director-panL')).checked = false;
    }

    if (isPanU && !settingsManager.isAutoPanU) {
      (<HTMLInputElement>getEl('video-director-panD')).checked = false;
    }

    if (isPanD && !settingsManager.isAutoPanD) {
      (<HTMLInputElement>getEl('video-director-panU')).checked = false;
    }

    if (isZoomIn && !settingsManager.isAutoZoomIn) {
      (<HTMLInputElement>getEl('video-director-zoomOut')).checked = false;
    }

    if (isZoomOut && !settingsManager.isAutoZoomOut) {
      (<HTMLInputElement>getEl('video-director-zoomIn')).checked = false;
    }
  }

  private static onSubmit(e: SubmitEvent) {
    if (typeof e === 'undefined' || e === null) {
      throw new Error('e is undefined');
    }
    e.preventDefault();

    keepTrackApi.getSoundManager()?.play(SoundNames.BUTTON_CLICK);

    settingsManager.isAutoRotateR = (<HTMLInputElement>getEl('video-director-rotateR')).checked;
    settingsManager.isAutoRotateL = (<HTMLInputElement>getEl('video-director-rotateL')).checked;
    settingsManager.isAutoRotateU = (<HTMLInputElement>getEl('video-director-rotateU')).checked;
    settingsManager.isAutoRotateD = (<HTMLInputElement>getEl('video-director-rotateD')).checked;
    settingsManager.isAutoPanR = (<HTMLInputElement>getEl('video-director-panR')).checked;
    settingsManager.isAutoPanL = (<HTMLInputElement>getEl('video-director-panL')).checked;
    settingsManager.isAutoPanU = (<HTMLInputElement>getEl('video-director-panU')).checked;
    settingsManager.isAutoPanD = (<HTMLInputElement>getEl('video-director-panD')).checked;
    settingsManager.isAutoZoomIn = (<HTMLInputElement>getEl('video-director-zoomIn')).checked;
    settingsManager.isAutoZoomOut = (<HTMLInputElement>getEl('video-director-zoomOut')).checked;
  }
}

