# 管理界面测试指南

## 🚀 快速测试步骤

### 1. 启动系统
```bash
# 启动API服务器
npm run start:api

# 启动Web服务器（新终端）
npm start
```

### 2. 登录管理后台
1. 访问: http://localhost:8080
2. 会自动跳转到登录页面
3. 使用默认管理员账户:
   - 用户名: `admin`
   - 密码: `SpaceDefense2025!`
4. 登录成功后访问管理后台

### 3. 测试修改密码功能
1. 点击"账户设置"标签页
2. 填写修改密码表单:
   - 当前密码: `SpaceDefense2025!`
   - 新密码: `NewPassword123!`
   - 确认密码: `NewPassword123!`
3. 点击"修改密码"按钮
4. 应该显示"密码修改成功"消息

### 4. 测试注册审批功能
1. 点击"注册审批"标签页
2. 如果没有待审批申请，先创建一个:
   - 打开新标签页访问: http://localhost:8080/login.html
   - 点击"注册新账户"
   - 填写注册信息并提交
3. 回到管理后台，刷新注册审批页面
4. 应该看到新的注册申请
5. 点击"批准"或"拒绝"按钮测试功能

### 5. 测试登录日志功能
1. 点击"登录日志"标签页
2. 应该看到最近的登录记录
3. 包括刚才的登录记录
4. 检查今日登录统计是否正确

### 6. 测试用户管理功能
1. 点击"用户管理"标签页
2. 应该看到用户列表
3. 如果批准了注册申请，应该看到新用户
4. 测试搜索功能
5. 测试启用/禁用用户功能

## 🔍 功能验证清单

### ✅ 基础功能
- [ ] 管理后台页面正常加载
- [ ] 四个标签页都能正常切换
- [ ] 统计数据正确显示
- [ ] 用户信息正确显示

### ✅ 修改密码功能
- [ ] 账户设置标签页正常显示
- [ ] 当前账户信息正确显示
- [ ] 修改密码表单正常工作
- [ ] 密码强度验证生效
- [ ] 修改成功后显示成功消息
- [ ] 表单重置正常

### ✅ 注册审批功能
- [ ] 注册审批标签页正常显示
- [ ] 待审批列表正确加载
- [ ] 批准功能正常工作
- [ ] 拒绝功能正常工作
- [ ] 操作后统计数据更新
- [ ] 确认提示正常显示

### ✅ 登录日志功能
- [ ] 登录日志标签页正常显示
- [ ] 日志列表正确加载
- [ ] 时间格式正确显示
- [ ] 状态标识正确显示
- [ ] 今日登录统计正确

### ✅ 用户管理功能
- [ ] 用户列表正确加载
- [ ] 用户信息完整显示
- [ ] 搜索功能正常工作
- [ ] 启用/禁用功能正常
- [ ] 刷新功能正常

## 🐛 常见问题排查

### 问题1: 页面空白或加载失败
**检查步骤:**
1. 确认API服务器正在运行 (端口3001)
2. 确认Web服务器正在运行 (端口8080)
3. 检查浏览器控制台错误
4. 确认已登录且有管理员权限

### 问题2: 修改密码失败
**检查步骤:**
1. 确认当前密码输入正确
2. 检查新密码是否符合强度要求
3. 确认新密码和确认密码一致
4. 查看错误消息提示

### 问题3: 注册审批功能无响应
**检查步骤:**
1. 确认有待审批的注册申请
2. 检查网络请求是否成功
3. 查看浏览器控制台错误
4. 确认管理员权限

### 问题4: 登录日志不显示
**检查步骤:**
1. 确认有登录记录存在
2. 检查API请求是否成功
3. 确认数据文件权限
4. 查看服务器日志

## 📊 测试数据准备

### 创建测试用户注册
```javascript
// 在浏览器控制台运行
fetch('http://localhost:3001/api/auth/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPassword123!'
    })
})
.then(r => r.json())
.then(console.log);
```

### 创建测试登录记录
```javascript
// 多次登录创建日志记录
fetch('http://localhost:3001/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        username: 'admin',
        password: 'wrongpassword'
    })
})
.then(r => r.json())
.then(console.log);
```

## 🔧 调试工具

### 浏览器开发者工具
1. 按F12打开开发者工具
2. 切换到Console标签查看错误
3. 切换到Network标签查看API请求
4. 切换到Application标签查看本地存储

### API测试
```javascript
// 测试用户列表API
const token = localStorage.getItem('authToken');
fetch('http://localhost:3001/api/auth/users', {
    headers: { 'Authorization': `Bearer ${token}` }
})
.then(r => r.json())
.then(console.log);

// 测试注册审批API
fetch('http://localhost:3001/api/auth/pending-registrations', {
    headers: { 'Authorization': `Bearer ${token}` }
})
.then(r => r.json())
.then(console.log);

// 测试登录日志API
fetch('http://localhost:3001/api/auth/login-attempts', {
    headers: { 'Authorization': `Bearer ${token}` }
})
.then(r => r.json())
.then(console.log);
```

## 📱 移动端测试

### 响应式测试
1. 在浏览器中按F12
2. 点击设备模拟器图标
3. 选择不同设备尺寸测试
4. 检查布局是否正常

### 触摸操作测试
1. 测试标签页切换
2. 测试按钮点击
3. 测试表单输入
4. 测试表格滚动

## 📋 测试报告模板

```
测试日期: ___________
测试人员: ___________
浏览器版本: ___________

基础功能测试:
□ 页面加载 - 通过/失败
□ 标签切换 - 通过/失败
□ 数据显示 - 通过/失败

修改密码测试:
□ 表单显示 - 通过/失败
□ 密码验证 - 通过/失败
□ 修改成功 - 通过/失败

注册审批测试:
□ 列表显示 - 通过/失败
□ 批准功能 - 通过/失败
□ 拒绝功能 - 通过/失败

登录日志测试:
□ 日志显示 - 通过/失败
□ 统计正确 - 通过/失败

用户管理测试:
□ 列表显示 - 通过/失败
□ 搜索功能 - 通过/失败
□ 操作功能 - 通过/失败

问题记录:
1. ___________
2. ___________
3. ___________

总体评价: ___________
```

---

**测试完成后，管理界面应该具有完整的功能，包括修改密码、注册审批、登录日志等所有管理功能。**
