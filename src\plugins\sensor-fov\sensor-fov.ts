/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * https://spacedefense
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { KeepTrackApiEvents, MenuMode } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import fovPng from '@public/img/icons/fov.png';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { SensorListPlugin } from '../sensor-list/sensor-list';
import { SensorSurvFence } from '../sensor-surv/sensor-surv-fence';

export class SensorFov extends KeepTrackPlugin {
  readonly id = 'SensorFov';
  dependencies_: string[] = [SensorListPlugin.name];
  bottomIconCallback = () => {
    if (!this.isMenuButtonActive) {
      this.disableFovView();
    } else {
      this.enableFovView();
    }
  };

  menuMode: MenuMode[] = [MenuMode.ADVANCED, MenuMode.ALL];

  bottomIconImg = fovPng;
  isIconDisabledOnLoad = true;
  isIconDisabled = true;
  isRequireSensorSelected = true;

  addJs(): void {
    super.addJs();

    keepTrackApi.on(
      KeepTrackApiEvents.setSensor,
      (sensor): void => {
        if (sensor) {
          getEl(this.bottomIconElementName)?.classList.remove(KeepTrackPlugin.iconDisabledClassString);
          this.isIconDisabled = false;
        } else {
          getEl(this.bottomIconElementName)?.classList.add(KeepTrackPlugin.iconDisabledClassString);
          this.isIconDisabled = true;
          this.isMenuButtonActive = false;
          getEl(this.bottomIconElementName)?.classList.remove(KeepTrackPlugin.iconSelectedClassString);
        }
      },
    );

    keepTrackApi.on(
      KeepTrackApiEvents.sensorDotSelected,
      (sensor): void => {
        if (sensor) {
          getEl(this.bottomIconElementName)?.classList.remove(KeepTrackPlugin.iconDisabledClassString);
          this.isIconDisabled = false;
        } else {
          getEl(this.bottomIconElementName)?.classList.add(KeepTrackPlugin.iconDisabledClassString);
          this.isIconDisabled = true;
          this.isMenuButtonActive = false;
          getEl(this.bottomIconElementName)?.classList.remove(KeepTrackPlugin.iconSelectedClassString);
        }
      },
    );
  }

  disableFovView() {
    keepTrackApi.emit(KeepTrackApiEvents.changeSensorMarkers, this.id);
    this.setBottomIconToUnselected(false);
  }

  enableFovView() {
    keepTrackApi.getPlugin(SensorSurvFence)?.setBottomIconToUnselected();

    this.setBottomIconToSelected();
  }
}
