#!/usr/bin/env node

// 服务器构建脚本 - 用于诊断构建问题
const { spawn } = require('child_process');
const path = require('path');

console.log('=== 服务器构建诊断 ===');
console.log('Node.js 版本:', process.version);
console.log('平台:', process.platform);
console.log('架构:', process.arch);
console.log('当前目录:', process.cwd());
console.log('内存限制:', process.memoryUsage());

// 设置环境变量
process.env.NODE_OPTIONS = '--max-old-space-size=4096';

// 运行构建
const buildScript = path.join(__dirname, 'build', 'build-manager.ts');
const args = ['development'];

console.log('\n=== 开始构建 ===');
console.log('执行命令:', `npx tsx ${buildScript} ${args.join(' ')}`);

const child = spawn('npx', ['tsx', buildScript, ...args], {
  stdio: 'inherit',
  cwd: process.cwd(),
  env: process.env
});

child.on('error', (error) => {
  console.error('构建进程错误:', error);
  process.exit(1);
});

child.on('exit', (code) => {
  console.log(`\n=== 构建完成，退出码: ${code} ===`);
  process.exit(code);
});
