{
  "env": {
    "jest": true,
    "browser": true,
    "es2021": true
  },
  "globals": {
    "M": "readonly",
    "process": "readonly",
    "global": "writable",
    "__dirname": "readonly",
    "settingsManager": "writable"
  },
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "sourceType": "module",
    "ecmaVersion": 2021
  },
  "plugins": [
    "@typescript-eslint",
    "jest"
  ],
  "rules": {
    "accessor-pairs": "error",
    "array-bracket-newline": "error",
    "array-bracket-spacing": "error",
    "array-callback-return": "error",
    "array-element-newline": "off",
    "arrow-body-style": "error",
    "arrow-parens": "error",
    "arrow-spacing": "error",
    "block-scoped-var": "error",
    "block-spacing": "error",
    "brace-style": "error",
    "callback-return": "off",
    "camelcase": "error",
    "capitalized-comments": "off",
    "class-methods-use-this": "off",
    "comma-dangle": [
      "error",
      "always-multiline"
    ],
    "comma-spacing": "error",
    "comma-style": "error",
    "complexity": [
      "error",
      {
        "max": 40
      }
    ], // TODO: change to error
    "computed-property-spacing": "error",
    "consistent-return": "error",
    "consistent-this": "error",
    "curly": "error",
    "default-case": "warn", // TODO: change to error
    "default-case-last": "error",
    "default-param-last": "error",
    "dot-location": "off",
    "dot-notation": "error",
    "eol-last": "error",
    "eqeqeq": "warn", // TODO: change to error
    "func-call-spacing": "error",
    "func-name-matching": "error",
    "func-names": "warn", // TODO: change to error
    "func-style": "off",
    "function-call-argument-newline": "off",
    "function-paren-newline": "off",
    "generator-star-spacing": "error",
    "global-require": "error",
    "grouped-accessor-pairs": "error",
    "guard-for-in": "warn", // TODO: change to error
    "handle-callback-err": "error",
    "id-blacklist": "error",
    "id-denylist": "error",
    "id-length": "off",
    "id-match": "error",
    "implicit-arrow-linebreak": "off",
    "indent": [
      "error",
      2,
      {
        "SwitchCase": 1
      }
    ],
    "indent-legacy": "off",
    "init-declarations": "off",
    "jsx-quotes": "error",
    "key-spacing": "error",
    "keyword-spacing": "off",
    "line-comment-position": "off",
    "linebreak-style": [
      "error",
      "unix"
    ],
    "lines-around-comment": "off",
    "lines-around-directive": "error",
    "lines-between-class-members": "off",
    "max-classes-per-file": "error",
    "max-depth": "warn", // TODO: change to error
    "max-len": [
      "warn",
      {
        "code": 180
      }
    ], // TODO: change to error
    "max-lines": [
      "warn",
      {
        "max": 1000
      }
    ], // TODO: change to error
    "max-lines-per-function": [
      "error",
      {
        "max": 300
      }
    ],
    "max-nested-callbacks": "error",
    "max-params": [
      "warn",
      {
        "max": 8
      }
    ], // TODO: change to error
    "max-statements": [
      "warn",
      {
        "max": 85
      }
    ], // TODO: change to error
    "max-statements-per-line": "error",
    "multiline-comment-style": "warn", // TODO: change to error
    "multiline-ternary": "off",
    "new-cap": "error",
    "new-parens": "error",
    "newline-after-var": "error",
    "newline-before-return": "error",
    "newline-per-chained-call": "off",
    "no-alert": "error",
    "no-array-constructor": "error",
    "no-await-in-loop": "error",
    "no-bitwise": "off",
    "no-buffer-constructor": "error",
    "no-caller": "error",
    "no-catch-shadow": "error",
    "no-confusing-arrow": "error",
    "no-console": "off",
    "no-constructor-return": "error",
    "no-continue": "off",
    "no-div-regex": "error",
    "no-duplicate-imports": "error",
    "no-else-return": "error",
    "no-empty-function": "warn", // TODO: change to error
    "no-eq-null": "warn", // TODO: change to error
    "no-eval": "error",
    "no-extend-native": "error",
    "no-extra-bind": "error",
    "no-extra-label": "error",
    "no-extra-parens": "off",
    "no-floating-decimal": "error",
    "no-implicit-coercion": "off",
    "no-implicit-globals": "error",
    "no-implied-eval": "error",
    "no-inline-comments": "off",
    "no-invalid-this": "error",
    "no-iterator": "error",
    "no-label-var": "error",
    "no-labels": "error",
    "no-lone-blocks": "error",
    "no-lonely-if": "error",
    "no-loop-func": "error",
    "no-loss-of-precision": "off",
    "no-magic-numbers": "off",
    "no-mixed-operators": "off",
    "no-mixed-requires": "error",
    "no-multi-assign": "warn", // TODO: change to error
    "no-multi-spaces": "error",
    "no-multi-str": "error",
    "no-multiple-empty-lines": "error",
    "no-native-reassign": "error",
    "no-negated-condition": "off",
    "no-negated-in-lhs": "error",
    "no-nested-ternary": "error",
    "no-new": "error",
    "no-new-func": "error",
    "no-new-object": "error",
    "no-new-require": "error",
    "no-new-wrappers": "error",
    "no-nonoctal-decimal-escape": "error",
    "no-octal-escape": "error",
    "no-param-reassign": "off",
    "no-path-concat": "error",
    "no-plusplus": "off",
    "no-process-env": "error",
    "no-process-exit": "error",
    "no-promise-executor-return": "error",
    "no-proto": "error",
    "no-restricted-exports": "error",
    "no-restricted-globals": "error",
    "no-restricted-imports": "error",
    "no-restricted-modules": "error",
    "no-restricted-properties": "error",
    "no-restricted-syntax": "error",
    "no-return-assign": "error",
    "no-return-await": "error",
    "no-script-url": "error",
    "no-self-compare": "error",
    "no-sequences": "error",
    "no-shadow": "off",
    "no-spaced-func": "error",
    "no-sync": "error",
    "no-tabs": "error",
    "no-template-curly-in-string": "error",
    "no-ternary": "off",
    "no-throw-literal": "error",
    "no-trailing-spaces": [
      "error",
      {
        "ignoreComments": true
      }
    ],
    "no-undef-init": "error",
    "no-undefined": "error",
    "no-underscore-dangle": "off",
    "no-unmodified-loop-condition": "error",
    "no-unneeded-ternary": "error",
    "no-unreachable-loop": "error",
    "no-unsafe-optional-chaining": "error",
    "no-unused-expressions": "error",
    "no-use-before-define": "off", // TODO: change to error
    "no-useless-backreference": "error",
    "no-useless-call": "error",
    "no-useless-computed-key": "error",
    "no-useless-concat": "error",
    "no-useless-constructor": "off",
    "no-useless-rename": "error",
    "no-useless-return": "error",
    "no-var": "error",
    "no-void": "error",
    "no-warning-comments": "off",
    "no-whitespace-before-property": "error",
    "nonblock-statement-body-position": "error",
    "object-curly-newline": "error",
    "object-curly-spacing": "off",
    "object-property-newline": "off",
    "object-shorthand": "error",
    "one-var": "off",
    "one-var-declaration-per-line": "error",
    "operator-assignment": "error",
    "operator-linebreak": "error",
    "padded-blocks": "off",
    "padding-line-between-statements": "error",
    "prefer-arrow-callback": "error",
    "prefer-const": "error",
    "prefer-destructuring": "off",
    "prefer-exponentiation-operator": "error",
    "prefer-named-capture-group": "error",
    "prefer-numeric-literals": "error",
    "prefer-object-spread": "error",
    "prefer-promise-reject-errors": "error",
    "prefer-reflect": "off",
    "prefer-regex-literals": "error",
    "prefer-rest-params": "error",
    "prefer-spread": "error",
    "prefer-template": "error",
    "quote-props": "off",
    "quotes": [
      "error",
      "single"
    ],
    "radix": "off",
    "require-atomic-updates": "error",
    "require-await": "warn", // TODO: change to errors
    "require-jsdoc": "error",
    "require-unicode-regexp": "error",
    "rest-spread-spacing": "error",
    "semi": "error",
    "semi-spacing": "error",
    "semi-style": "error",
    "sort-imports": "off",
    "sort-keys": "off",
    "sort-vars": "error",
    "space-before-blocks": "error",
    "space-before-function-paren": "off",
    "space-in-parens": "error",
    "space-infix-ops": "error",
    "space-unary-ops": "error",
    "spaced-comment": "error",
    "strict": "error",
    "switch-colon-spacing": "error",
    "symbol-description": "error",
    "template-curly-spacing": "error",
    "template-tag-spacing": "error",
    "unicode-bom": "error",
    "valid-jsdoc": "off",
    "vars-on-top": "error",
    "wrap-iife": "error",
    "wrap-regex": "error",
    "yield-star-spacing": "error",
    "yoda": "error",
    "@typescript-eslint/no-explicit-any": "warn"
  },
  "overrides": [
    {
      "files": [
        "**/src/**/*.{js,ts}"
      ],
      "extends": "./.eslintrc.src.json"
    },
    {
      "files": [
        "**/test/**/*.{js,ts}"
      ],
      "extends": "./.eslintrc.test.json"
    }
  ]
}