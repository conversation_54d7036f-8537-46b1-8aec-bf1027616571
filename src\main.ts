/**
 *!
 * /////////////////////////////////////////////////////////////////////////////
 *
 * https://keeptrack.space
 *
 * @Copyright (C) 2025 Kruczek Labs LLC
 *
 * KeepTrack is free software: you can redistribute it and/or modify it under the
 * terms of the GNU Affero General Public License as published by the Free Software
 * Foundation, either version 3 of the License, or (at your option) any later version.
 *
 * KeepTrack is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY;
 * without even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 * See the GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License along with
 * KeepTrack. If not, see <http://www.gnu.org/licenses/>.
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { KeepTrack } from './keeptrack';





// Ensure settingsOverride exists with fallback
const settingsOverride = window.settingsOverride || {
  isPreventDefaultHtml: false,
  isShowSplashScreen: true,
};

try {
  // Load the main website class
  const keepTrack = new KeepTrack(settingsOverride);
  keepTrack.init();

  // Expose to window for debugging
  window.keepTrack = keepTrack;

  // Initialize the website
  KeepTrack.initCss().then(() => {
    keepTrack.run().catch((error) => {
      console.error('Error in keepTrack.run():', error);
    });
  }).catch((error) => {
    console.error('Error in KeepTrack.initCss():', error);
  });
} catch (error) {
  console.error('Error in main.ts:', error);
}