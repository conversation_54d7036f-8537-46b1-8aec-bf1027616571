#!/usr/bin/env node

/**
 * 配置验证脚本
 * 检查所有配置文件是否一致，端口是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 配置验证工具\n');

// 需要检查的配置文件
const configFiles = [
    {
        path: 'config.json',
        description: '主配置文件',
        required: true
    },
    {
        path: 'src/api/config.json',
        description: 'API配置文件',
        required: true
    },
    {
        path: 'api-config.json',
        description: '备用配置文件',
        required: false
    }
];

// 需要检查的JavaScript文件
const jsFiles = [
    {
        path: 'public/js/login.js',
        description: '登录脚本',
        checkPorts: true
    },
    {
        path: 'public/js/auth-check.js',
        description: '认证检查脚本',
        checkPorts: true
    },
    {
        path: 'public/js/admin-fixed.js',
        description: '管理后台脚本',
        checkPorts: true
    },
    {
        path: 'src/plugins/history-track/history-track.ts',
        description: '历史轨道插件',
        checkPorts: true
    }
];

let issues = [];
let warnings = [];

// 检查配置文件
function checkConfigFiles() {
    console.log('📋 检查配置文件...\n');
    
    const configs = {};
    
    configFiles.forEach(file => {
        if (fs.existsSync(file.path)) {
            try {
                const content = fs.readFileSync(file.path, 'utf8');
                const config = JSON.parse(content);
                configs[file.path] = config;
                
                console.log(`✅ ${file.description} (${file.path})`);
                
                if (config.apiServer) {
                    console.log(`   端口: ${config.apiServer.port}`);
                    console.log(`   URL: ${config.apiServer.url}`);
                    
                    // 检查端口一致性
                    if (config.apiServer.port !== 5001) {
                        issues.push(`${file.description} 端口不是5001: ${config.apiServer.port}`);
                    }
                    
                    // 检查URL一致性
                    if (config.apiServer.url && !config.apiServer.url.includes(':5001')) {
                        issues.push(`${file.description} URL不包含5001端口: ${config.apiServer.url}`);
                    }
                } else {
                    issues.push(`${file.description} 缺少 apiServer 配置`);
                }
                
                console.log('');
            } catch (error) {
                issues.push(`${file.description} JSON解析失败: ${error.message}`);
            }
        } else if (file.required) {
            issues.push(`缺少必需的配置文件: ${file.path}`);
        } else {
            warnings.push(`可选配置文件不存在: ${file.path}`);
        }
    });
    
    return configs;
}

// 检查JavaScript文件中的端口
function checkJavaScriptFiles() {
    console.log('📋 检查JavaScript文件中的端口配置...\n');
    
    jsFiles.forEach(file => {
        if (fs.existsSync(file.path)) {
            try {
                const content = fs.readFileSync(file.path, 'utf8');
                
                console.log(`🔍 ${file.description} (${file.path})`);
                
                // 检查是否包含3001端口
                const has3001 = content.includes('3001');
                const has5001 = content.includes('5001');
                
                if (has3001) {
                    issues.push(`${file.description} 仍包含旧端口3001`);
                    console.log('   ❌ 包含旧端口3001');
                } else {
                    console.log('   ✅ 不包含旧端口3001');
                }
                
                if (has5001) {
                    console.log('   ✅ 包含新端口5001');
                } else if (file.checkPorts) {
                    warnings.push(`${file.description} 不包含端口5001，可能使用动态配置`);
                    console.log('   ⚠️  不包含端口5001（可能使用动态配置）');
                }
                
                // 检查是否使用配置加载器
                const usesConfigLoader = content.includes('configLoader') || content.includes('getApiBaseUrl');
                if (usesConfigLoader) {
                    console.log('   ✅ 使用配置加载器');
                } else {
                    warnings.push(`${file.description} 未使用配置加载器`);
                    console.log('   ⚠️  未使用配置加载器');
                }
                
                console.log('');
            } catch (error) {
                issues.push(`读取 ${file.description} 失败: ${error.message}`);
            }
        } else {
            warnings.push(`JavaScript文件不存在: ${file.path}`);
        }
    });
}

// 检查环境变量文件
function checkEnvFiles() {
    console.log('📋 检查环境变量文件...\n');
    
    const envFiles = ['.env', '.env.local', '.env.example'];
    
    envFiles.forEach(envFile => {
        if (fs.existsSync(envFile)) {
            try {
                const content = fs.readFileSync(envFile, 'utf8');
                console.log(`✅ ${envFile}`);
                
                // 检查PORT配置
                const portMatch = content.match(/PORT=(\d+)/);
                if (portMatch) {
                    const port = parseInt(portMatch[1]);
                    console.log(`   端口: ${port}`);
                    
                    if (port !== 5001) {
                        issues.push(`${envFile} 端口不是5001: ${port}`);
                    }
                } else {
                    warnings.push(`${envFile} 未设置PORT变量`);
                }
                
                console.log('');
            } catch (error) {
                issues.push(`读取 ${envFile} 失败: ${error.message}`);
            }
        }
    });
}

// 生成报告
function generateReport() {
    console.log('📊 验证报告');
    console.log('='.repeat(50));
    
    if (issues.length === 0 && warnings.length === 0) {
        console.log('🎉 所有配置检查通过！');
        return true;
    }
    
    if (issues.length > 0) {
        console.log(`\n❌ 发现 ${issues.length} 个问题:`);
        issues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue}`);
        });
    }
    
    if (warnings.length > 0) {
        console.log(`\n⚠️  发现 ${warnings.length} 个警告:`);
        warnings.forEach((warning, index) => {
            console.log(`   ${index + 1}. ${warning}`);
        });
    }
    
    console.log('\n💡 建议操作:');
    if (issues.length > 0) {
        console.log('1. 运行端口迁移脚本: npm run migrate:port');
        console.log('2. 手动修复上述问题');
        console.log('3. 重新运行此验证脚本');
    }
    
    if (warnings.length > 0) {
        console.log('4. 检查警告项目是否需要处理');
    }
    
    console.log('5. 重启API服务器: npm run start:api');
    console.log('6. 清理浏览器缓存');
    
    return issues.length === 0;
}

// 主函数
function main() {
    console.log(`📅 验证时间: ${new Date().toISOString()}`);
    console.log(`📂 当前目录: ${process.cwd()}\n`);
    
    // 检查是否在项目根目录
    if (!fs.existsSync('package.json')) {
        console.log('❌ 错误: 请在项目根目录中运行此脚本');
        process.exit(1);
    }
    
    checkConfigFiles();
    checkJavaScriptFiles();
    checkEnvFiles();
    
    const success = generateReport();
    
    process.exit(success ? 0 : 1);
}

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log('配置验证工具 - 检查端口配置是否正确');
    console.log('');
    console.log('使用方法:');
    console.log('  node scripts/validate-config.js');
    console.log('');
    console.log('功能:');
    console.log('  - 检查配置文件中的端口设置');
    console.log('  - 检查JavaScript文件中的硬编码端口');
    console.log('  - 验证配置一致性');
    console.log('  - 生成详细的验证报告');
    process.exit(0);
}

// 运行验证
main();
