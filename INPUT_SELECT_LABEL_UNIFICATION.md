# 🎯 输入框和选择框标签字体大小统一

## 问题描述

根据用户截图反馈，侧边菜单中的输入框标题（如"卫星SCC#"）字体比选择框标题（如"目标类型"）要小，需要统一为相同大小。

## ✅ 解决方案

### 1. 统一字体大小标准
- **目标字体大小**: `var(--font-lg)` (1.25rem)
- **统一行高**: 1.4
- **统一颜色**: #b3e5fc (正常状态)，#2196F3 (激活状态)

### 2. 覆盖的元素

#### 输入框标签
```css
.side-menu .input-field input + label,
[id$="-menu"] .input-field input + label,
.side-menu-parent .input-field input + label
```

#### 选择框标签
```css
.side-menu .input-field select + label,
[id$="-menu"] .input-field select + label,
.side-menu-parent .input-field select + label,
.side-menu .input-field .select-wrapper + label,
[id$="-menu"] .input-field .select-wrapper + label,
.side-menu-parent .input-field .select-wrapper + label
```

#### 激活状态标签
```css
.side-menu .input-field label.active:not(.switch label):not(.lever),
.side-menu .input-field input:focus + label:not(.switch label):not(.lever),
.side-menu .input-field input:not(:placeholder-shown) + label:not(.switch label):not(.lever)
```

### 3. 核心CSS实现

在 `responsive-design.css` 中添加的统一样式：

```css
/* 🎯 侧边菜单标签统一样式 - 输入框标题、选择框标题完全一致 */
.side-menu label:not(.switch label):not(.lever),
.side-menu .input-field label:not(.switch label):not(.lever),
[id$="-menu"] label:not(.switch label):not(.lever),
[id$="-menu"] .input-field label:not(.switch label):not(.lever),
.side-menu-parent label:not(.switch label):not(.lever),
.side-menu-parent .input-field label:not(.switch label):not(.lever),
/* 特别针对输入框标签 */
.side-menu .input-field input + label,
[id$="-menu"] .input-field input + label,
.side-menu-parent .input-field input + label,
/* 特别针对选择框标签 */
.side-menu .input-field select + label,
[id$="-menu"] .input-field select + label,
.side-menu-parent .input-field select + label,
.side-menu .input-field .select-wrapper + label,
[id$="-menu"] .input-field .select-wrapper + label,
.side-menu-parent .input-field .select-wrapper + label {
  font-size: var(--font-lg) !important;
  line-height: 1.4 !important;
  color: #b3e5fc !important;
  font-weight: 400 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  transform: translateY(0.75rem) !important;
  transform-origin: 0% 100% !important;
  transition: transform 0.2s ease-out, color 0.2s ease-out !important;
}
```

### 4. 激活状态样式

```css
/* 🎯 激活状态的标签样式 - 保持相同字体大小 */
.side-menu .input-field label.active:not(.switch label):not(.lever),
.side-menu .input-field input:focus + label:not(.switch label):not(.lever),
.side-menu .input-field input:not(:placeholder-shown) + label:not(.switch label):not(.lever),
.side-menu .input-field select:focus + label:not(.switch label):not(.lever),
[id$="-menu"] .input-field label.active:not(.switch label):not(.lever),
[id$="-menu"] .input-field input:focus + label:not(.switch label):not(.lever),
[id$="-menu"] .input-field input:not(:placeholder-shown) + label:not(.switch label):not(.lever),
[id$="-menu"] .input-field select:focus + label:not(.switch label):not(.lever),
.side-menu-parent .input-field label.active:not(.switch label):not(.lever),
.side-menu-parent .input-field input:focus + label:not(.switch label):not(.lever),
.side-menu-parent .input-field input:not(:placeholder-shown) + label:not(.switch label):not(.lever),
.side-menu-parent .input-field select:focus + label:not(.switch label):not(.lever) {
  font-size: var(--font-lg) !important;
  line-height: 1.4 !important;
  color: #2196F3 !important;
  transform: translateY(-1.25rem) scale(0.85) !important;
  transform-origin: 0 0 !important;
}
```

## 🗑️ 移除的冲突样式

### 从 `ultimate-fix.css` 移除
- ❌ 基于缩放因子的标签字体大小
- ❌ 冲突的输入框样式
- ❌ 重复的开关标签样式

### 从 `menu-styles-optimized.css` 移除
- ❌ 基于缩放因子的标签样式
- ❌ 冲突的字体大小设置

### 从 `style.css` 移除
- ❌ 重复的标签样式定义
- ❌ 冲突的选择框标签样式
- ❌ 基于缩放因子的下拉框样式

## 📊 字体大小对比

### 修复前
- **输入框标题** ("卫星SCC#"): 较小字体 (约14px)
- **选择框标题** ("目标类型"): 较大字体 (约18px)
- **视觉不一致**

### 修复后
- **输入框标题** ("卫星SCC#"): var(--font-lg) (1.25rem)
- **选择框标题** ("目标类型"): var(--font-lg) (1.25rem)
- **完全一致的视觉效果**

## 🎯 响应式适应

根据屏幕大小自动调整：
- **移动设备** (< 768px): 14px base → 标签 17.5px
- **平板设备** (768px-1024px): 15px base → 标签 18.75px
- **桌面设备** (1025px-1440px): 16px base → 标签 20px
- **大屏幕** (1441px-1920px): 17px base → 标签 21.25px
- **超大屏幕** (> 1920px): 18px base → 标签 22.5px

## 🧪 测试验证

### 测试页面更新
更新了 `public/test-menu-labels.html`：
- ✅ 模拟"卫星SCC#"输入框
- ✅ 模拟"目标类型"选择框
- ✅ 实时显示字体大小对比
- ✅ 验证激活状态效果

### 测试步骤
1. 打开 `test-menu-labels.html`
2. 查看"卫星SCC#"和"目标类型"的字体大小
3. 确认两者显示相同的字体大小值
4. 测试输入框获得焦点时的效果
5. 在不同屏幕尺寸下验证响应式效果

## 🎨 视觉效果

### 正常状态
- 字体大小: var(--font-lg) (1.25rem)
- 颜色: #b3e5fc (浅蓝色)
- 位置: translateY(0.75rem)

### 激活状态
- 字体大小: var(--font-lg) (保持不变)
- 颜色: #2196F3 (蓝色)
- 位置: translateY(-1.25rem) scale(0.85)
- 缩放: 85% (视觉上更小但基础字体大小相同)

## 🚀 技术优势

1. **视觉一致性** - 输入框和选择框标签完全一致
2. **响应式设计** - 自动适应不同屏幕尺寸
3. **状态管理** - 正常和激活状态都保持一致
4. **高优先级** - 使用 !important 确保覆盖旧样式
5. **全面覆盖** - 包含所有可能的侧边菜单容器

## 📝 使用说明

### 开发者
- 新增侧边菜单时，输入框和选择框标签会自动使用统一样式
- 无需在其他CSS文件中重复定义标签样式
- 如需特殊样式，在 `responsive-design.css` 中添加例外规则

### 验证方法
- 使用测试页面验证新菜单的标签一致性
- 检查浏览器开发者工具中的计算样式
- 确保输入框和选择框标签显示相同的字体大小

现在"卫星SCC#"输入框标题和"目标类型"选择框标题使用完全相同的字体大小，提供了一致的视觉体验！
