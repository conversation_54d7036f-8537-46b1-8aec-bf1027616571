# Keep Track 7.2 - Nebula Navigator

"Nebula" represents vastness, mystery, and the space theme. "Navigator" captures the essence of the version which provides numerous tools and help menus to guide and assist users in their orbital analysis journey.

## Software Release Documentation

This software release emphasizes on refining user experience with optimized textures, a new loading screen, splash screens, and extensive help menus added across different modules. Major features such as the addition of different analytical plots and satellite search capabilities enhance functionality. Significant work went into the removal of jQuery from various parts of the application, focusing on performance optimization and bug fixes. The software also offers enhanced compatibility, now being available for use as a React component. Furthermore, security and dependency updates ensure that Keep Track remains robust and up-to-date.

## Major Features

### UI & Aesthetics

- Introduced new splash screens and a revamped loading screen.
- Enhanced Milky Way textures and introduced optimized image handling techniques.

### Functionality Enhancements

- Added a plethora of analytical plots like ECI, ECF, RIC, etc.
- Incorporated satellite search when a control site is clicked.
- Brought in scenario-creator scaffold.
- Added new satellite selection options and updated select boxes.
- Implemented faster searching capabilities and prefetching of splash screens.

### User Guidance

- Introduced extensive help menus across various modules, including analysis, sensors, settings, debug, satellite photos, and more.

## Minor Features

- Removed dependencies on jQuery, emphasizing better performance.
- Added support to use Keep Track as a React component.
- Introduced new and updated catalogs for satellite data.

### Bug Fixes

- Addressed numerous issues including satellite FOV issues, satellite editing, and texture anomalies.
- Enhanced error-catching with improvements like delayed error sounds.
- Resolved minor issues with menus like countries and constellations.

### Code & Infrastructure Updates

- Upgraded dependencies and addressed potential security vulnerabilities.
- Significant code refactoring, including the removal of jQuery and optimization of various modules.

### Documentation

- Continuous updating of changelogs and readmes, providing clearer instructions and feature explanations.
