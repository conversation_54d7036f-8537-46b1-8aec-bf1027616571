/**
 * 定时器配置管理
 * 统一管理系统中所有定时器的配置，优化资源使用
 * 创建时间: 2025-01-28
 */

export interface TimerConfig {
  // 样式强制应用
  styleCheck: {
    enabled: boolean;
    normalInterval: number;    // 正常检查间隔（毫秒）
    fastInterval: number;      // 快速检查间隔（毫秒）
    slowInterval: number;      // 慢速检查间隔（毫秒）
    maxRetries: number;        // 最大重试次数
  };

  // 认证检查
  authCheck: {
    enabled: boolean;
    activeInterval: number;    // 用户活跃时检查间隔
    inactiveInterval: number;  // 用户不活跃时检查间隔
    longInactiveInterval: number; // 长时间不活跃时检查间隔
    activityTimeout: number;   // 活跃状态超时时间
  };

  // 性能监控已移除

  // 用户交互检查
  userActivity: {
    enabled: boolean;
    checkInterval: number;     // 检查间隔
    inactivityThreshold: number; // 不活跃阈值
  };

  // 时间机器
  timeMachine: {
    enabled: boolean;
    delayBetweenSatellites: number; // 卫星间延迟
    delayAtPresentDay: number;      // 当前日期延迟
  };
}

// 默认配置
export const defaultTimerConfig: TimerConfig = {
  styleCheck: {
    enabled: true,
    normalInterval: 5000,      // 5秒
    fastInterval: 2000,        // 2秒
    slowInterval: 10000,       // 10秒
    maxRetries: 3
  },

  authCheck: {
    enabled: true,
    activeInterval: 5 * 60 * 1000,      // 5分钟
    inactiveInterval: 10 * 60 * 1000,   // 10分钟
    longInactiveInterval: 15 * 60 * 1000, // 15分钟
    activityTimeout: 10 * 60 * 1000     // 10分钟
  },

  // 性能监控配置已移除

  userActivity: {
    enabled: true,
    checkInterval: 30 * 1000,  // 30秒
    inactivityThreshold: 5 * 60 * 1000 // 5分钟
  },

  timeMachine: {
    enabled: true,
    delayBetweenSatellites: 10000,  // 10秒
    delayAtPresentDay: 30000        // 30秒
  }
};

// 性能配置预设
export const performancePresets = {
  // 高性能模式 - 更频繁的检查
  high: {
    ...defaultTimerConfig,
    styleCheck: {
      ...defaultTimerConfig.styleCheck,
      normalInterval: 3000,
      fastInterval: 1000,
      slowInterval: 8000
    },
    authCheck: {
      ...defaultTimerConfig.authCheck,
      activeInterval: 3 * 60 * 1000
    }
  },

  // 平衡模式 - 默认配置
  balanced: defaultTimerConfig,

  // 节能模式 - 降低检查频率
  eco: {
    ...defaultTimerConfig,
    styleCheck: {
      ...defaultTimerConfig.styleCheck,
      normalInterval: 10000,
      fastInterval: 5000,
      slowInterval: 20000
    },
    authCheck: {
      ...defaultTimerConfig.authCheck,
      activeInterval: 10 * 60 * 1000,
      inactiveInterval: 20 * 60 * 1000,
      longInactiveInterval: 30 * 60 * 1000
    },
    userActivity: {
      ...defaultTimerConfig.userActivity,
      checkInterval: 60 * 1000
    }
  },

  // 最小模式 - 最低资源使用
  minimal: {
    ...defaultTimerConfig,
    styleCheck: {
      ...defaultTimerConfig.styleCheck,
      enabled: false
    },
    authCheck: {
      ...defaultTimerConfig.authCheck,
      activeInterval: 15 * 60 * 1000,
      inactiveInterval: 30 * 60 * 1000,
      longInactiveInterval: 60 * 60 * 1000
    },
    userActivity: {
      ...defaultTimerConfig.userActivity,
      enabled: false
    }
    // 性能监控配置已移除
  }
};

// 配置管理器
export class TimerConfigManager {
  private static instance: TimerConfigManager;
  private currentConfig: TimerConfig;
  private configChangeCallbacks: Array<(config: TimerConfig) => void> = [];

  private constructor() {
    this.currentConfig = { ...defaultTimerConfig };
    this.loadFromStorage();
  }

  static getInstance(): TimerConfigManager {
    if (!TimerConfigManager.instance) {
      TimerConfigManager.instance = new TimerConfigManager();
    }
    return TimerConfigManager.instance;
  }

  /**
   * 获取当前配置
   */
  getConfig(): TimerConfig {
    return { ...this.currentConfig };
  }

  /**
   * 设置配置
   */
  setConfig(config: Partial<TimerConfig>): void {
    this.currentConfig = { ...this.currentConfig, ...config };
    this.saveToStorage();
    this.notifyConfigChange();
  }

  /**
   * 应用性能预设
   */
  applyPreset(preset: keyof typeof performancePresets): void {
    this.currentConfig = { ...performancePresets[preset] };
    this.saveToStorage();
    this.notifyConfigChange();
    console.log(`🔧 应用定时器预设: ${preset}`);
  }

  /**
   * 根据系统性能自动调整配置 - 已移除性能监控
   */
  autoAdjustForPerformance(_fps: number): void {
    // 性能监控功能已移除，保留接口以避免破坏现有代码
    console.log('性能监控功能已移除');
  }

  /**
   * 监听配置变化
   */
  onConfigChange(callback: (config: TimerConfig) => void): void {
    this.configChangeCallbacks.push(callback);
  }

  /**
   * 通知配置变化
   */
  private notifyConfigChange(): void {
    this.configChangeCallbacks.forEach(callback => {
      try {
        callback(this.currentConfig);
      } catch (error) {
        console.warn('定时器配置变化回调执行失败:', error);
      }
    });
  }

  /**
   * 从本地存储加载配置
   */
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem('keeptrack-timer-config');
      if (stored) {
        const config = JSON.parse(stored);
        this.currentConfig = { ...defaultTimerConfig, ...config };
      }
    } catch (error) {
      console.warn('加载定时器配置失败，使用默认配置:', error);
    }
  }

  /**
   * 保存配置到本地存储
   */
  private saveToStorage(): void {
    try {
      localStorage.setItem('keeptrack-timer-config', JSON.stringify(this.currentConfig));
    } catch (error) {
      console.warn('保存定时器配置失败:', error);
    }
  }

  /**
   * 重置为默认配置
   */
  reset(): void {
    this.currentConfig = { ...defaultTimerConfig };
    this.saveToStorage();
    this.notifyConfigChange();
  }
}

// 导出单例实例
export const timerConfigManager = TimerConfigManager.getInstance();
