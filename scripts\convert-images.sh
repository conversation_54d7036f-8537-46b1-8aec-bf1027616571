#!/bin/bash

echo "将JPG壁纸转换为WebP格式"
echo

# 切换到壁纸目录
cd "$(dirname "$0")/../public/img/wallpaper" || exit 1

echo "当前目录: $(pwd)"
echo

# 检查是否安装了cwebp工具
if ! command -v cwebp &> /dev/null; then
    echo "错误：未找到cwebp工具"
    echo "请安装WebP工具："
    echo "  Ubuntu/Debian: sudo apt-get install webp"
    echo "  macOS: brew install webp"
    echo "  或从以下地址下载: https://developers.google.com/speed/webp/download"
    exit 1
fi

echo "开始转换图片..."
echo

# 转换所有JPG文件为WebP格式
for file in *.jpg; do
    if [ -f "$file" ]; then
        echo "转换: $file"
        webp_file="${file%.jpg}.webp"
        
        # 使用cwebp转换，质量设置为85%
        if cwebp -q 85 "$file" -o "$webp_file"; then
            echo "✓ 成功创建: $webp_file"
            
            # 显示文件大小比较
            original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
            webp_size=$(stat -f%z "$webp_file" 2>/dev/null || stat -c%s "$webp_file" 2>/dev/null)
            
            if [ -n "$original_size" ] && [ -n "$webp_size" ]; then
                savings=$(echo "scale=1; ($original_size - $webp_size) * 100 / $original_size" | bc -l 2>/dev/null || echo "N/A")
                echo "  原始大小: $(echo "scale=1; $original_size / 1024" | bc -l 2>/dev/null || echo "N/A") KB"
                echo "  WebP大小: $(echo "scale=1; $webp_size / 1024" | bc -l 2>/dev/null || echo "N/A") KB"
                echo "  节省空间: ${savings}%"
            fi
        else
            echo "✗ 转换失败: $file"
        fi
        echo
    fi
done

echo "转换完成！"
echo
echo "生成的WebP文件："
ls -la *.webp 2>/dev/null || echo "没有找到WebP文件"
echo
echo "下一步：检查生成的WebP文件质量，如果满意可以删除原始JPG文件"
