import { MenuMode, KeepTrackApiEvents } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import calculatorPng from '@public/img/icons/calculator.png';

import { getEl } from '@app/lib/get-el';
import { errorManagerInstance } from '@app/singletons/errorManager';
import { Degrees, DetailedSensor, ecf2eci, eci2ecf, eci2rae, Kilometers, rae2eci, RaeVec3, Vector3D } from 'ootk';
import { ClickDragOptions, KeepTrackPlugin } from '../KeepTrackPlugin';

enum CalculatorMode {
  ITRF = 'ITRF',
  J2000 = 'J2000',
  RAE = 'RAE',
}

export class Calculator extends KeepTrackPlugin {
  readonly id = 'Calculator';
  protected dependencies_ = [];
  bottomIconImg = calculatorPng;
  currentMode: CalculatorMode = CalculatorMode.ITRF;
  sensorUsedInCalculation: DetailedSensor | null = null;

  menuMode: MenuMode[] = [MenuMode.ANALYSIS, MenuMode.ALL];

  sideMenuElementName = 'calculator-menu';
  private readonly itrfHtml = keepTrackApi.html`
  <div>
    <form id="calculator">
      <div class="center-align row">
          ITRF
        </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-itrf-x-input" type="text" />
          <label for="calc-itrf-x-input" class="active">ITRF X</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-itrf-y-input" type="text" />
          <label for="calc-itrf-y-input" class="active">ITRF Y</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-itrf-z-input" type="text" />
          <label for="calc-itrf-z-input" class="active">ITRF Z</label>
        </div>
      </div>
      <div class="center-align row">
        <button id="calculator-submit" class="btn btn-ui waves-effect waves-light" type="submit" name="action">转换 &#9658;</button>
      </div>
    </form>

    <br />
    <br />

    <div class="center-align row">
          J2000
        </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-j2000-x-input" type="text" />
          <label for="calc-j2000-x-input" class="active">J2000 X</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-j2000-y-input" type="text" />
          <label for="calc-j2000-y-input" class="active">J2000 Y</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-j2000-z-input" type="text" />
          <label for="calc-j2000-z-input" class="active">J2000 Z</label>
        </div>
      </div>

      <div class="center-align row">
          RAE
        </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="Sensor Name" id="calc-sensor-name" type="text" readonly />
          <label for="calc-sensor-name" class="active">传感器名字</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-rae-r-input" type="text" />
          <label for="calc-rae-r-input" class="active">范围</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-rae-a-input" type="text" />
          <label for="calc-rae-a-input" class="active">方位角</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-rae-e-input" type="text" />
          <label for="calc-rae-e-input" class="active">仰角</label>
        </div>
      </div>

      <div class="center-align row">
        <button id="calculator-draw-line" class="btn btn-ui waves-effect waves-light" type="button" name="action">绘制线</button>
      </div>
  </div>
  `;
  private readonly raeHtml = keepTrackApi.html`
  <div>
    <form id="calculator">
      <div class="center-align row">
          RAE
        </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="Sensor Name" id="calc-sensor-name" type="text" readonly />
          <label for="calc-sensor-name" class="active">传感器名字</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-rae-r-input" type="text" />
          <label for="calc-rae-r-input" class="active">范围</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-rae-a-input" type="text" />
          <label for="calc-rae-a-input" class="active">方位角</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-rae-e-input" type="text" />
          <label for="calc-rae-e-input" class="active">仰角</label>
        </div>
      </div>
      <div class="center-align row">
        <button id="calculator-submit" class="btn btn-ui waves-effect waves-light" type="submit" name="action">转换 &#9658;</button>
      </div>
    </form>

    <br />
    <br />

    <div class="center-align row">
      J2000
    </div>
    <div class="row">
      <div class="input-field col s12">
        <input value="3000" id="calc-j2000-x-input" type="text" />
        <label for="calc-j2000-x-input" class="active">J2000 X</label>
      </div>
    </div>
    <div class="row">
      <div class="input-field col s12">
        <input value="3000" id="calc-j2000-y-input" type="text" />
        <label for="calc-j2000-y-input" class="active">J2000 Y</label>
      </div>
    </div>
    <div class="row">
      <div class="input-field col s12">
        <input value="3000" id="calc-j2000-z-input" type="text" />
          <label for="calc-j2000-z-input" class="active">J2000 Z</label>
        </div>
      </div>

      <div class="center-align row">
          ITRF
        </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-itrf-x-input" type="text" />
          <label for="calc-itrf-x-input" class="active">ITRF X</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-itrf-y-input" type="text" />
          <label for="calc-itrf-y-input" class="active">ITRF Y</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-itrf-z-input" type="text" />
          <label for="calc-itrf-z-input" class="active">ITRF Z</label>
        </div>
      </div>

      <div class="center-align row">
        <button id="calculator-draw-line" class="btn btn-ui waves-effect waves-light" type="button" name="action">绘制线</button>
      </div>
  </div>
  `;
  private readonly j2000Html = keepTrackApi.html`
  <div>
    <form id="calculator">
      <div class="center-align row">
            J2000
          </div>
        <div class="row">
          <div class="input-field col s12">
            <input value="3000" id="calc-j2000-x-input" type="text" />
            <label for="calc-j2000-x-input" class="active">J2000 X</label>
          </div>
        </div>
        <div class="row">
          <div class="input-field col s12">
            <input value="3000" id="calc-j2000-y-input" type="text" />
            <label for="calc-j2000-y-input" class="active">J2000 Y</label>
          </div>
        </div>
        <div class="row">
          <div class="input-field col s12">
            <input value="3000" id="calc-j2000-z-input" type="text" />
            <label for="calc-j2000-z-input" class="active">J2000 Z</label>
          </div>
        </div>
        <div class="center-align row">
          <button id="calculator-submit" class="btn btn-ui waves-effect waves-light" type="submit" name="action">转换 &#9658;</button>
        </div>
      </form>

      <br />
      <br />

      <div class="center-align row">
          ITRF
        </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-itrf-x-input" type="text" />
          <label for="calc-itrf-x-input" class="active">ITRF X</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-itrf-y-input" type="text" />
          <label for="calc-itrf-y-input" class="active">ITRF Y</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-itrf-z-input" type="text" />
          <label for="calc-itrf-z-input" class="active">ITRF Z</label>
        </div>
      </div>

      <div class="center-align row">
          RAE
        </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="Sensor Name" id="calc-sensor-name" type="text" readonly />
          <label for="calc-sensor-name" class="active">传感器名字</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-rae-r-input" type="text" />
          <label for="calc-rae-r-input" class="active">范围</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-rae-a-input" type="text" />
          <label for="calc-rae-a-input" class="active">方位角</label>
        </div>
      </div>
      <div class="row">
        <div class="input-field col s12">
          <input value="3000" id="calc-rae-e-input" type="text" />
          <label for="calc-rae-e-input" class="active">仰角</label>
        </div>
      </div>

      <div class="center-align row">
        <button id="calculator-draw-line" class="btn btn-ui waves-effect waves-light" type="button" name="action">绘制线</button>
      </div>
  </div>
  `;

  sideMenuElementHtml = keepTrackApi.html`
    <div id="calculator-content-wrapper">
      ${this.itrfHtml}
    </div>`;
  sideMenuSecondaryHtml = keepTrackApi.html`
  <div>
    <div class="center-align row">
      <button id="calculator-itrf" class="btn btn-ui waves-effect waves-light" type="button" name="action">ITRF</button>
    </div>
    <div class="center-align row">
      <button id="calculator-j2000" class="btn btn-ui waves-effect waves-light" type="button" name="action">J2000</button>
    </div>
    <div class="center-align row">
      <button id="calculator-rae" class="btn btn-ui waves-effect waves-light" type="button" name="action">RAE</button>
    </div>
  </div>
`;


  dragOptions: ClickDragOptions = {
    isDraggable: true,
    minWidth: 350,
  };

  addHtml(): void {
    super.addHtml();
    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {
        // Nothing to do here
      },
    );
  }

  addJs(): void {
    super.addJs();
    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {
        const itrfEl = getEl('calculator-itrf');
        if (itrfEl) {
          itrfEl.addEventListener('click', () => {
            this.changeToITRF_();
          });
        }

        const j2000El = getEl('calculator-j2000');
        if (j2000El) {
          j2000El.addEventListener('click', () => {
            this.changeToJ2000_();
          });
        }

        const raeEl = getEl('calculator-rae');
        if (raeEl) {
          raeEl.addEventListener('click', () => {
            this.changeToRAE_();
          });
        }

        this.addRemovableListeners();
      },
    );
  }

  /**
   * Adds event listeners to the calculator elements that can be removed later.
   *
   * This method attaches click event listeners to the elements with IDs
   * 'calculator-draw-line' and 'calculator-submit'. When the 'calculator-draw-line'
   * element is clicked, the `drawLine_` method is called. When the 'calculator-submit'
   * element is clicked, the default form submission is prevented and the `handleSubmit_`
   * method is called.
   *
   * @private
   */
  private addRemovableListeners() {
    const drawLineEl = getEl('calculator-draw-line');
    if (drawLineEl) {
      drawLineEl.addEventListener('click', () => {
        this.drawLine_();
      });
    }

    const submitEl = getEl('calculator-submit');
    if (submitEl) {
      submitEl.addEventListener('click', (e) => {
        e.preventDefault();
        this.handleSubmit_();
      });
    }
  }

  private handleSubmit_(): void {
    switch (this.currentMode) {
      case CalculatorMode.ITRF:
        this.calculateITRF_();
        break;
      case CalculatorMode.J2000:
        this.calculateJ2000_();
        break;
      case CalculatorMode.RAE:
        this.calculateRAE_();
        break;
      default:
        errorManagerInstance.warn('Invalid calculator mode');
    }
  }

  private calculateITRF_(): void {
    const x = getEl('calc-itrf-x-input') as HTMLInputElement;
    const y = getEl('calc-itrf-y-input') as HTMLInputElement;
    const z = getEl('calc-itrf-z-input') as HTMLInputElement;

    if (isNaN(Number(x.value)) || isNaN(Number(y.value)) || isNaN(Number(z.value))) {
      errorManagerInstance.warn('ITRF 输入无效，必须为数字。');

      return;
    }

    const ecf = new Vector3D<Kilometers>(Number(x.value) as Kilometers, Number(y.value) as Kilometers, Number(z.value) as Kilometers);
    const date = keepTrackApi.getTimeManager().simulationTimeObj;
    const gmst = keepTrackApi.getTimeManager().gmst;
    const eci = ecf2eci(ecf, gmst);

    (getEl('calc-j2000-x-input') as HTMLInputElement).value = eci.x.toString();
    (getEl('calc-j2000-y-input') as HTMLInputElement).value = eci.y.toString();
    (getEl('calc-j2000-z-input') as HTMLInputElement).value = eci.z.toString();

    const currentSensor = keepTrackApi.getSensorManager().currentSensors[0];

    if (!currentSensor) {
      (getEl('calc-sensor-name') as HTMLInputElement).value = '未选择传感器';
      (getEl('calc-rae-r-input') as HTMLInputElement).value = '未选择传感器';
      (getEl('calc-rae-a-input') as HTMLInputElement).value = '未选择传感器';
      (getEl('calc-rae-e-input') as HTMLInputElement).value = '未选择传感器';
    } else {
      const rae = eci2rae(date, eci, currentSensor);

      this.sensorUsedInCalculation = new DetailedSensor(currentSensor);

      (getEl('calc-sensor-name') as HTMLInputElement).value = currentSensor.name;
      (getEl('calc-rae-r-input') as HTMLInputElement).value = rae.rng.toString();
      (getEl('calc-rae-a-input') as HTMLInputElement).value = rae.az.toString();
      (getEl('calc-rae-e-input') as HTMLInputElement).value = rae.el.toString();
    }
  }

  private calculateJ2000_(): void {
    const x = getEl('calc-j2000-x-input') as HTMLInputElement;
    const y = getEl('calc-j2000-y-input') as HTMLInputElement;
    const z = getEl('calc-j2000-z-input') as HTMLInputElement;

    if (isNaN(Number(x.value)) || isNaN(Number(y.value)) || isNaN(Number(z.value))) {
      errorManagerInstance.warn('J2000输入无效。必须为数字。');

      return;
    }

    const eci = new Vector3D<Kilometers>(Number(x.value) as Kilometers, Number(y.value) as Kilometers, Number(z.value) as Kilometers);
    const date = keepTrackApi.getTimeManager().simulationTimeObj;
    const gmst = keepTrackApi.getTimeManager().gmst;
    const ecf = eci2ecf(eci, gmst);

    (getEl('calc-itrf-x-input') as HTMLInputElement).value = ecf.x.toString();
    (getEl('calc-itrf-y-input') as HTMLInputElement).value = ecf.y.toString();
    (getEl('calc-itrf-z-input') as HTMLInputElement).value = ecf.z.toString();

    const currentSensor = keepTrackApi.getSensorManager().currentSensors[0];

    if (!currentSensor) {
      (getEl('calc-sensor-name') as HTMLInputElement).value = '未选择传感器';
      (getEl('calc-rae-r-input') as HTMLInputElement).value = '未选择传感器';
      (getEl('calc-rae-a-input') as HTMLInputElement).value = '未选择传感器';
      (getEl('calc-rae-e-input') as HTMLInputElement).value = '未选择传感器';
    } else {
      const rae = eci2rae(date, eci, currentSensor);

      this.sensorUsedInCalculation = new DetailedSensor(currentSensor);

      (getEl('calc-sensor-name') as HTMLInputElement).value = currentSensor.name;
      (getEl('calc-rae-r-input') as HTMLInputElement).value = rae.rng.toString();
      (getEl('calc-rae-a-input') as HTMLInputElement).value = rae.az.toString();
      (getEl('calc-rae-e-input') as HTMLInputElement).value = rae.el.toString();
    }
  }

  private calculateRAE_(): void {
    const r = getEl('calc-rae-r-input') as HTMLInputElement;
    const a = getEl('calc-rae-a-input') as HTMLInputElement;
    const e = getEl('calc-rae-e-input') as HTMLInputElement;

    if (isNaN(Number(r.value)) || isNaN(Number(a.value)) || isNaN(Number(e.value))) {
      errorManagerInstance.warn('RAE输入无效。必须为数字。');

      return;
    }

    const sensor = keepTrackApi.getSensorManager().currentSensors[0];

    if (!sensor) {
      errorManagerInstance.warn('未选择传感器');

      return;
    }

    const rae = {
      rng: Number(r.value) as Kilometers,
      az: Number(a.value) as Degrees,
      el: Number(e.value) as Degrees,
    } as RaeVec3<Kilometers>;
    const eci = rae2eci(rae, sensor.lla(), keepTrackApi.getTimeManager().gmst);

    (getEl('calc-j2000-x-input') as HTMLInputElement).value = eci.x.toString();
    (getEl('calc-j2000-y-input') as HTMLInputElement).value = eci.y.toString();
    (getEl('calc-j2000-z-input') as HTMLInputElement).value = eci.z.toString();

    const ecf = eci2ecf(eci, keepTrackApi.getTimeManager().gmst);

    (getEl('calc-itrf-x-input') as HTMLInputElement).value = ecf.x.toString();
    (getEl('calc-itrf-y-input') as HTMLInputElement).value = ecf.y.toString();
    (getEl('calc-itrf-z-input') as HTMLInputElement).value = ecf.z.toString();
  }

  private changeToITRF_(): void {
    this.currentMode = CalculatorMode.ITRF;

    getEl('calculator-content-wrapper')!.innerHTML = this.itrfHtml;
    this.addRemovableListeners();

    const x = getEl('calc-itrf-x-input') as HTMLInputElement;
    const y = getEl('calc-itrf-y-input') as HTMLInputElement;
    const z = getEl('calc-itrf-z-input') as HTMLInputElement;

    x.value = '3000';
    y.value = '3000';
    z.value = '3000';
  }

  private changeToJ2000_(): void {
    this.currentMode = CalculatorMode.J2000;

    getEl('calculator-content-wrapper')!.innerHTML = this.j2000Html;
    this.addRemovableListeners();

    const x = getEl('calc-itrf-x-input') as HTMLInputElement;
    const y = getEl('calc-itrf-y-input') as HTMLInputElement;
    const z = getEl('calc-itrf-z-input') as HTMLInputElement;

    x.value = '3000';
    y.value = '3000';
    z.value = '3000';
  }

  private changeToRAE_(): void {
    this.currentMode = CalculatorMode.RAE;

    getEl('calculator-content-wrapper')!.innerHTML = this.raeHtml;
    this.addRemovableListeners();

    const x = getEl('calc-itrf-x-input') as HTMLInputElement;
    const y = getEl('calc-itrf-y-input') as HTMLInputElement;
    const z = getEl('calc-itrf-z-input') as HTMLInputElement;

    x.value = '3000';
    y.value = '3000';
    z.value = '3000';
  }

  private drawLine_(): void {
    const r = getEl('calc-rae-r-input') as HTMLInputElement;
    const a = getEl('calc-rae-a-input') as HTMLInputElement;
    const e = getEl('calc-rae-e-input') as HTMLInputElement;

    keepTrackApi.getLineManager().createSensorToRae(this.sensorUsedInCalculation,
      { rng: Number(r.value) as Kilometers, az: Number(a.value) as Degrees, el: Number(e.value) as Degrees });
  }
}
