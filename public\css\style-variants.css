/*
 * 样式变体文件 - 统一管理embed和celestrak模式的差异
 * 替代: style.embed.css, style.celestrak.css
 * 创建时间: 2025-01-25
 */

/* ========================================
 * Embed模式特定样式
 * ======================================== */
.embed-mode {
  /* 隐藏顶部导航栏 */
  --nav-bar-height: 0px !important;
  --top-menu-height: 0px !important;
}

.embed-mode #nav-wrapper {
  display: none !important;
}

.embed-mode #sensor-selected-container {
  display: none !important;
  width: 0px !important;
}

.embed-mode #ui-datepicker-div {
  border: none !important;
  top: 0px !important;
  left: 0px !important;
  width: 100% !important;
  border-radius: 0px !important;
}

.embed-mode .ui-datepicker-calendar {
  margin-bottom: 0px !important;
}

/* ========================================
 * Celestrak模式特定样式
 * ======================================== */
.celestrak-mode {
  /* 使用深色背景 */
  --color-dark-background: #001a33 !important;
  --color-dark-border: #003366 !important;
}

.celestrak-mode #bottom-icons {
  background: var(--color-dark-background) !important;
}

.celestrak-mode #bottom-icons-filter {
  background: var(--color-dark-background) !important;
}

/* ========================================
 * 通用变体样式
 * ======================================== */

/* 确保变体模式下的底部菜单布局正确 */
.embed-mode #bottom-icons,
.celestrak-mode #bottom-icons {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, var(--bottom-icon-width)) !important;
  justify-content: center !important;
  width: calc(100% - var(--bottom-filter-width)) !important;
  float: right !important;
}

.embed-mode #bottom-icons-filter,
.celestrak-mode #bottom-icons-filter {
  display: flex !important;
  flex-direction: column !important;
  width: var(--bottom-filter-width) !important;
  float: left !important;
}

/* ========================================
 * 响应式适配
 * ======================================== */

/* 小屏幕适配 */
@media (max-width: 768px) {
  .embed-mode #bottom-icons,
  .celestrak-mode #bottom-icons {
    grid-template-columns: repeat(auto-fill, calc(var(--bottom-icon-width) * 0.8)) !important;
  }
}

/* 大屏幕优化 */
@media (min-width: 1920px) {
  .embed-mode #bottom-icons,
  .celestrak-mode #bottom-icons {
    grid-template-columns: repeat(auto-fill, calc(var(--bottom-icon-width) * 1.2)) !important;
  }
}

/* ========================================
 * 模式切换JavaScript接口
 * ======================================== */

/* 
 * 使用方法:
 * 
 * // 启用embed模式
 * document.body.classList.add('embed-mode');
 * 
 * // 启用celestrak模式  
 * document.body.classList.add('celestrak-mode');
 * 
 * // 恢复默认模式
 * document.body.classList.remove('embed-mode', 'celestrak-mode');
 */
