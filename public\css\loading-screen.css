:root {
  --colorTertiary: #4a4a4a !important;
  --color-dark-background: #1f1f1f !important;
  --color-dark-border: #3a3a3a !important;
  --color-dark-text-accent: #b3e5fc !important;
  --color-dark-text-muted: #6b6b6b !important;
  /* International Orange Engineering */
  --color-primary: #1a73e8 !important;
  /* 蓝色主色调 */
  /* Brighter version of International Orange Engineering for icons */
  /* 蓝色强调色 */
  /* Lighter shade of International Orange Engineering */
  --color-primary-light: #4285f4 !important;
  /* 浅蓝色 */
  /* Darker shade of International Orange Engineering */
  --color-primary-dark: #0d47a1 !important;
  /* 深蓝色 */
}

#keeptrack-root {
  overflow: hidden;
  font-family: 'Open Sans', sans-serif;
  color: white;
  background: var(--color-dark-background);
  background: -moz-linear-gradient(45deg, var(--color-dark-background), var(--color-dark-background));
  background: -webkit-linear-gradient(45deg, var(--color-dark-background), var(--color-dark-background));
  background: linear-gradient(45deg, var(--color-dark-background), var(--color-dark-background));
}

.ui-icon-minus {
  margin: auto !important;
}

.ui-icon-plus {
  margin: auto !important;
}

.ui-datepicker td a {
  text-align: center !important;
}

#canvas-holder {
  display: none;
  /* Display none till loading is complete main.js -227 */
}

#loading-screen {
  display: flex;
  padding: 0;
  z-index: 101;
  text-align: center;
  background: #000000;
  color: white;
  /* Apply filter only to background image, not text */
  /* Use a pseudo-element for background image and filter */
  position: relative;
}

#loading-screen::before {
  content: "";
  position: absolute;
  inset: 0;
  z-index: -1;
  background: inherit;
  filter: brightness(0.85) contrast(1.05);
  pointer-events: none;
}

.full-loader {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow: hidden;
  top: 0;
  bottom: 0;
  left: 0;
}

.mini-loader-container {
  background: rgba(0, 0, 0, 0) !important;
  position: absolute;
  width: 100%;
  height: 100%;
}

.mini-loader {
  border-radius: 35px;
  z-index: 101;
  border: 5px solid rgba(0, 0, 0, 0.3);
  width: 300px;
  padding-top: 25px;
  padding-bottom: 5px;
  overflow: hidden;
  background: var(--color-dark-background);
  background: -moz-linear-gradient(45deg, var(--colorTertiaryDarken4), var(--colorTertiary));
  background: -webkit-linear-gradient(45deg, var(--colorTertiaryDarken4), var(--colorTertiary));
  background: linear-gradient(45deg, var(--colorTertiaryDarken4), var(--colorTertiary));
  -moz-background-clip: padding;
  /* Firefox 3.6 */
  -webkit-background-clip: padding;
  /* Safari 4? Chrome 6? */
  background-clip: padding-box;
  /* Firefox 4, Safari 5, Opera 10, IE 9 */
}

#logo-inner-container {
  margin: auto;
  display: flex;
  justify-content: center;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: center;
  height: calc(150px / var(--system-scale-factor, 1));
  z-index: 1;
}

#logo-text {
  display: block;
  font-size: calc(9vh / var(--system-scale-factor, 1));
  max-width: 60%;
  margin: auto;
  /* Large black drop shadow */
  -webkit-filter: drop-shadow(0 0 6px rgba(0, 0, 0, 0.75)) drop-shadow(0 0 10px rgba(0, 0, 0, 0.75));
  filter: drop-shadow(0 0 6px rgba(0, 0, 0, 0.75)) drop-shadow(0 0 10px rgba(0, 0, 0, 0.75));
}

#logo-text-version {
  display: block;
  font-size: calc(9vh / var(--system-scale-factor, 1));
  padding-left: calc(50px / var(--system-scale-factor, 1));
  text-shadow:
    0px 0px calc(8px / var(--system-scale-factor, 1)) rgb(0 0 0),
    0px 0px calc(8px / var(--system-scale-factor, 1)) rgb(0 0 0);
  color: var(--colorPrimaryLighten2) !important;
}

#sponsor-text {
  display: block;
  font-size: calc(2em / var(--system-scale-factor, 1));
  text-shadow:
    0px 0px calc(8px / var(--system-scale-factor, 1)) rgb(0 0 0),
    0px 0px calc(8px / var(--system-scale-factor, 1)) rgb(0 0 0);
}

#sponsor-img {
  background: white;
  border-radius: calc(10px / var(--system-scale-factor, 1));
  padding: calc(5px / var(--system-scale-factor, 1));
  box-shadow:
    0px 0px calc(8px / var(--system-scale-factor, 1)) rgb(0 0 0),
    0px 0px calc(8px / var(--system-scale-factor, 1)) rgb(0 0 0);
}

@font-face {
  font-family: 'nasalization';
  font-style: normal;
  font-weight: 400;
  src:
    url('fonts/nasalization-rg.woff2') format('woff2'),
    url('fonts/nasalization-rg.woff') format('woff'),
    url('fonts/nasalization-rg.ttf') format('truetype');
}

.logo-font {
  font-family: 'nasalization', sans-serif;
  font-size: calc(8em / var(--system-scale-factor, 1));
  text-shadow: 0px 0px calc(10px / var(--system-scale-factor, 1)) rgba(0, 0, 0, 0.5);
}

@media (max-width: 800px) {
  #logo-text {
    font-size: calc(3em / var(--system-scale-factor, 1));
  }

  #logo-text-version {
    font-size: calc(3em / var(--system-scale-factor, 1));
  }
}

#loader-text {
  display: block;
  line-height: calc(50px / var(--system-scale-factor, 1));
  text-shadow:
    0px 0px calc(8px / var(--system-scale-factor, 1)) rgb(0 0 0),
    0px 0px calc(8px / var(--system-scale-factor, 1)) rgb(0 0 0);
}

#loading-hint {
  text-align: left;
  z-index: 1;
  position: absolute;
  width: 100%;
  font-size: calc(1.2em / var(--system-scale-factor, 1));
  background-color: rgba(0, 0, 0, 0.5);
  padding-left: calc(5% / var(--system-scale-factor, 1));
  padding-top: calc(5px / var(--system-scale-factor, 1));
  padding-bottom: calc(5px / var(--system-scale-factor, 1));
  bottom: calc(15% / var(--system-scale-factor, 1));
  text-shadow:
    0px 0px calc(8px / var(--system-scale-factor, 1)) rgb(0 0 0),
    0px 0px calc(8px / var(--system-scale-factor, 1)) rgb(0 0 0);
}

#mobile-start-button {
  display: none;
  margin-top: 10px;
}

.start-hidden {
  display: none;
}