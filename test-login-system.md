# 登录系统测试指南

## 系统概述

已成功为太空物体模拟平台添加了登录验证功能，确保用户必须登录后才能访问主页，同时不影响主页的正常工作。

## 主要修改

### 1. 启用主页认证检查
- 修改了 `public/index.html`，启用了认证检查脚本
- 移除了"不进行任何认证检查"的注释，添加了 `auth-check.js` 脚本引用

### 2. 优化认证检查逻辑
- 更新了 `public/js/auth-check.js` 中的路径检查逻辑
- 确保主页（根路径 "/"）也会进行认证验证
- 添加了更详细的日志输出

### 3. 创建测试页面
- 新增了 `public/test-auth.html` 用于测试认证功能

## 系统工作流程

1. **用户访问主页** (`/`)
   - 自动加载 `auth-check.js` 脚本
   - 检查 localStorage 中的 `authToken`
   - 如果没有 token，显示跳转提示并重定向到登录页

2. **用户登录** (`/login.html`)
   - 输入用户名和密码
   - 向认证服务器发送登录请求
   - 成功后保存 token 和用户信息到 localStorage
   - 根据用户角色跳转：
     - 管理员 → `/admin-simple.html`
     - 普通用户 → `/`（主页）

3. **Token 验证**
   - 每次访问主页时验证 token 有效性
   - 调用 `/api/auth/verify` 端点
   - 验证失败则清除本地数据并跳转到登录页

4. **主页正常运行**
   - 认证通过后，主页正常加载和运行
   - 不影响原有的功能和性能

## 测试步骤

### 1. 启动认证服务器
```bash
cd src/api
npm install
npm run dev
```

### 2. 启动前端服务器
```bash
# 在项目根目录
# 使用任何静态文件服务器，例如：
npx http-server public -p 8080
# 或者
python -m http.server 8080 --directory public
```

### 3. 测试认证功能

#### 测试未登录访问主页
1. 打开浏览器访问 `http://localhost:8080/`
2. 应该看到跳转提示页面
3. 2秒后自动跳转到登录页面

#### 测试登录功能
1. 在登录页面输入用户名和密码
2. 登录成功后应该跳转回主页
3. 主页应该正常加载和运行

#### 测试认证状态
1. 访问 `http://localhost:8080/test-auth.html`
2. 查看认证状态和用户信息
3. 测试各种操作按钮

#### 测试登出功能
1. 在测试页面点击"退出登录"
2. 应该清除认证信息并跳转到登录页
3. 再次访问主页应该被重定向到登录页

## 默认用户账户

系统已经配置了默认的管理员账户：
- **用户名**: `admin`
- **密码**: `SpaceDefense2025!`

⚠️ **重要提示**: 首次登录后系统会要求修改默认密码，这是为了安全考虑。

如果需要创建新用户，可以通过管理后台进行操作。

## 安全特性

1. **JWT Token 认证**
   - 使用 HMAC-SHA256 签名
   - 包含过期时间验证
   - 服务器端验证用户状态

2. **登录保护**
   - IP 地址限流（15分钟内最多5次尝试）
   - 账户锁定机制（5次失败后锁定1分钟）
   - 密码强度验证

3. **前端保护**
   - 自动 token 验证
   - 过期自动跳转
   - 本地存储清理

## 故障排除

### 如果主页无法加载
1. 检查浏览器控制台是否有 JavaScript 错误
2. 确认认证服务器是否正常运行
3. 检查网络请求是否成功

### 如果认证失败
1. 检查用户名和密码是否正确
2. 确认认证服务器的用户数据
3. 查看服务器日志

### 如果跳转不正常
1. 检查浏览器是否阻止了重定向
2. 确认 localStorage 中的数据
3. 查看认证检查脚本的日志输出

## 注意事项

1. **开发环境**：认证服务器默认运行在 `localhost:3001`
2. **生产环境**：需要配置正确的 API 服务器地址
3. **HTTPS**：生产环境建议使用 HTTPS 协议
4. **跨域**：确保前端和后端的跨域配置正确

## 下一步

系统现在已经具备完整的登录验证功能。建议：

1. 测试所有功能确保正常工作
2. 根据需要调整用户权限和角色
3. 配置生产环境的安全设置
4. 添加更多的用户管理功能（如果需要）
