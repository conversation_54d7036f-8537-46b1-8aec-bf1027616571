#sat-info-title {
  font-size: var(--font-xs) !important;
  color: #4A90E2 !important;
  min-height: 2.8rem;
  height: auto;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-around;
  align-items: center;
  padding-bottom: var(--spacing-xs);
}

#sat-add-watchlist,
#sat-remove-watchlist {
  width: var(--icon-base);
  height: var(--icon-base);
  cursor: pointer;
}

#sat-add-watchlist {
  filter: hue-rotate(275deg) brightness(1.2);
}

#sat-remove-watchlist {
  filter: invert(52%) sepia(130%) saturate(7323%) hue-rotate(353deg) brightness(100%) contrast(86%);
}

/* 卫星名字样式 - 加粗加大，红色字体 */
#sat-info-title-name {
  font-size: calc(18px * var(--system-scale-factor)) !important;
  font-weight: bold !important;
  color: #219ff3 !important;
  text-align: center;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: calc((100% - 80px) * var(--system-scale-factor)); /* 为左右按钮留出空间 */
  white-space: normal; /* 允许换行 */
  padding: calc(2px * var(--system-scale-factor)) 0; /* 增加上下内边距 */
}

/* 🔥 国旗图标样式 - 简化版本，避免与ultimate-fix.css冲突 */
#sat-infobox-fi {
  /* 🔥 基础显示属性 */
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
  /* 🔥 尺寸设置 */
  width: 1.5rem !important;
  height: 1.125rem !important;
  /* 🔥 定位设置 */
  position: absolute !important;
  right: var(--spacing-xs) !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 5 !important;
  /* 🔥 清除边框和轮廓 */
  border: none !important;
  outline: none !important;
  /* 🔥 让ultimate-fix.css处理背景相关属性 */
}

.sat-info-section-header {
  height: calc(25px / var(--system-scale-factor));
  font-size: calc(18px / var(--system-scale-factor));
  text-align: center !important;
  background: var(--color-dark-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: var(--colorWhite);
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-bottom: calc(5px / var(--system-scale-factor));
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

/* 🔥🔥🔥 章节标题悬停效果 - 超高优先级 🔥🔥🔥 */
html[lang="zh-CN"] body div#sat-infobox .sat-info-section-header:not(#sat-info-title):hover,
html[lang="en"] body div#sat-infobox .sat-info-section-header:not(#sat-info-title):hover,
html[lang] body div#sat-infobox .sat-info-section-header:not(#sat-info-title):hover,
html body div#sat-infobox .sat-info-section-header:not(#sat-info-title):hover,
body div#sat-infobox .sat-info-section-header:not(#sat-info-title):hover,
div#sat-infobox .sat-info-section-header:not(#sat-info-title):hover,
html[lang] body #sat-infobox .sat-info-section-header:not(#sat-info-title):hover,
html body #sat-infobox .sat-info-section-header:not(#sat-info-title):hover,
body #sat-infobox .sat-info-section-header:not(#sat-info-title):hover,
#sat-infobox .sat-info-section-header:not(#sat-info-title):hover,
.sat-info-section-header:hover {
  background: #1976d2 !important; /* 🔥 更明亮的蓝色，确保可见 */
  background-color: #1976d2 !important;
  background-image: none !important;
  cursor: pointer !important;
  /* 🔥 确保不受全局transform影响 */
  transform: none !important;
  /* 🔥 确保鼠标事件正常 */
  pointer-events: auto !important;
  /* 🔥 确保用户选择正常 */
  user-select: auto !important;
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
  /* 🔥 确保过渡效果 */
  transition: background-color 0.2s ease !important;
}

/* 🔥 整个sat-infobox应该可以拖动，但内部元素需要正常的鼠标事件 */
#sat-infobox {
  cursor: default !important; /* 改为默认光标，只有标题栏可拖动 */
  pointer-events: auto !important; /* 确保可以接收鼠标事件 */
  /* 🔥 确保拖动时不受CSS约束 */
  transition: none !important;
}

/* 🔥 只有标题栏可以拖动 */
#sat-info-title {
  cursor: move !important; /* 改为move光标，表示可拖动 */
  user-select: none !important; /* 防止文本选择干扰拖动 */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  pointer-events: auto !important; /* 确保可以接收鼠标事件 */
}

/* 🔥 确保内部元素可以正常接收鼠标事件 */
#sat-infobox .sat-info-row,
#sat-infobox .sat-info-section-header,
#sat-infobox .sat-infobox-links {
  user-select: auto !important; /* 允许文本选择 */
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
  pointer-events: auto !important; /* 确保可以接收鼠标事件 */
}

/* 🔥🔥🔥 确保Draggabilly可以正常工作 - 超高优先级 */
html body #sat-infobox.is-dragging,
#sat-infobox.is-dragging {
  position: absolute !important;
  z-index: 9999 !important;
  /* 🔥 移除位置强制设置，让Draggabilly控制位置 */
  transition: none !important;
}

/* 顶部卫星名字标题的拖动规则已在上面定义，这里移除重复 */

/* 🔥 顶部菜单：固定深蓝色背景，完全禁用悬停事件 */
#sat-info-title {
  background: rgba(0, 26, 51, 0.8) !important;
  background-color: rgba(0, 26, 51, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  pointer-events: auto !important; /* 保持可拖动 */
}

/* 🔥 恢复原始项目的样式设置 */
#sat-info-title {
  font-size: calc(22px / var(--system-scale-factor)) !important;
  height: calc(25px / var(--system-scale-factor));
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-around;
  align-items: center;
  padding-bottom: calc(5px / var(--system-scale-factor));
  /* 🔥 使用原始的深蓝色背景，覆盖透明样式 */
  background: var(--color-dark-border) !important;
  background-color: var(--color-dark-border) !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

#sat-info-title:hover {
  /* 🔥 禁用悬停效果，保持原始颜色 */
  background: var(--color-dark-border) !important;
  background-color: var(--color-dark-border) !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  cursor: move !important; /* 保持拖动功能 */
}

#sat-infobox a {
  color: white;
}

.sat-info-row {
  margin-bottom: 3px;
  padding: 1px 10px;
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  justify-content: space-between !important;
  align-items: baseline !important; /* 改为baseline对齐 */
  min-height: 24px !important; /* 确保有足够的高度 */
  line-height: 1.4 !important; /* 统一行高 */
  box-sizing: border-box !important;
}

.sat-info-row:hover {
  background: #0d47a1 !important; /* 🔥 与顶部菜单一致的深蓝色 */
  background-color: #0d47a1 !important;
  background-image: none !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.2s ease !important;
  /* 🔥 确保不受全局transform影响 */
  transform: none !important;
  /* 🔥 确保鼠标事件正常 */
  pointer-events: auto !important;
  cursor: default !important;
}

/* 🔥 为可点击的元素添加更明显的悬停效果 */
.sat-info-row[onclick]:hover,
.sat-info-row[data-tooltip]:hover {
  background: #1976d2 !important; /* 更亮的蓝色，区别于普通悬停 */
  background-color: #1976d2 !important;
  background-image: none !important;
  cursor: pointer !important;
  /* 🔥 确保不受全局transform影响 */
  transform: none !important;
  /* 🔥 确保鼠标事件正常 */
  pointer-events: auto !important;
}

/* 🔥 为按钮和链接添加悬停效果，但排除关闭按钮 */
#sat-infobox button:not(#sat-info-close-btn):hover,
#sat-infobox .clickable:hover {
  background: rgba(33, 150, 243, 0.4) !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.2s ease !important; /* 🔥 只过渡背景色，不影响transform */
}

/* 🔥🔥🔥 专门为sat-infobox-links添加悬停效果 🔥🔥🔥 */
.sat-infobox-links:hover,
#sat-infobox .sat-infobox-links:hover,
html body #sat-infobox .sat-infobox-links:hover {
  background: #1976d2 !important; /* 明亮的蓝色背景 */
  background-color: #1976d2 !important;
  background-image: none !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.2s ease !important;
  cursor: pointer !important;
  /* 🔥 确保不受全局transform影响 */
  transform: none !important;
  /* 🔥 确保鼠标事件正常 */
  pointer-events: auto !important;
}

/* 🔥🔥🔥 专门为menu-selectable类添加悬停效果 🔥🔥🔥 */
.menu-selectable:hover,
#sat-infobox .menu-selectable:hover,
html body #sat-infobox .menu-selectable:hover {
  background: #1976d2 !important; /* 明亮的蓝色背景 */
  background-color: #1976d2 !important;
  background-image: none !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.2s ease !important;
  cursor: pointer !important;
  /* 🔥 确保不受全局transform影响 */
  transform: none !important;
  /* 🔥 确保鼠标事件正常 */
  pointer-events: auto !important;
}

#sat-site {
  max-height: 40px;
}

#sat-site-row {
  max-height: 48px !important; /* 允许两行内容 */
  min-height: 24px !important;
}

.sat-info-key,
.sat-info-value {
  display: block; /* 改为block，配合flex布局 */
  float: none; /* 移除float，使用flex布局 */
}

.sat-info-key {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;  /* 🔧 使用缩放变量 */
  color: white;
  flex: 0 0 auto; /* 不伸缩，自动宽度 */
  margin-right: calc(10px / var(--system-scale-factor, 1)); /* 与值之间的间距 */
  max-width: 50%; /* 最大宽度限制 */
  word-wrap: break-word; /* 长文本换行 */
  line-height: calc(20px / var(--system-scale-factor, 1)) !important; /* 🔧 与值部分保持一致的行高 */
  vertical-align: baseline !important; /* 统一垂直对齐 */
}

.sat-infobox-links {
  font-size: small;
  padding: 2px 10px;
  color: var(--color-dark-text-accent);
}

.section-collapse {
  cursor: pointer;
  color: var(--color-dark-text-accent);
  width: 25px;
  height: 25px;
}

#sat-info-close-btn {
  transition: color 0.2s ease, background-color 0.2s ease !important; /* 🔥 只过渡颜色，不影响transform */
  border-radius: 50%;
  width: var(--icon-base) !important; /* 🔥 与添加按钮保持一致 */
  height: var(--icon-base) !important; /* 🔥 与添加按钮保持一致 */
  display: flex;
  align-items: center;
  justify-content: center;
  /* 🔥 修复定位：重新定位到正确的位置，与右侧按钮对齐 */
  position: absolute !important;
  left: var(--spacing-xs) !important;
  top: calc(50% - 0.15rem) !important; /* 向上调整 */
  transform: translateY(-50%) !important;
}

/* 🔥 关闭按钮悬停效果 - 完全锁定位置和尺寸，防止全局CSS干扰 */
#sat-info-close-btn:hover {
  color: #ff4444 !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  /* 🔥 确保悬停时位置和尺寸完全不变 */
  width: calc(25px / var(--system-scale-factor)) !important;
  height: calc(25px / var(--system-scale-factor)) !important;
  /* 🔥 保持原有的绝对定位，不受全局CSS影响 */
  position: absolute !important;
  left: calc(5px / var(--system-scale-factor)) !important;
  top: calc(50% - 2.5px / var(--system-scale-factor)) !important;
  transform: translateY(-50%) !important; /* 🔥 确保transform不变，不受全局translateY(-1px)影响 */
  /* 🔥 防止全局悬停效果干扰 */
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.section-collapse:hover {
  background-color: var(--color-dark-text-accent);
  color: white;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.section-collapse:active {
  background-color: var(--color-primary-dark);
  color: white;
  scale: 0.9;
}

#search-links {
  margin-top: 5px;
}

#actions-section,
#launch-section,
#sat-identifier-data,
#orbital-section,
#secondary-sat-info,
#sensor-sat-info,
#sat-mission-data {
  transition: max-height 0.5s ease-in-out;
  max-height: 1000px;
  /* Set this to a value larger than the content's height */
  overflow: hidden;
  margin: 0px; /* 🔥 移除margin，避免缝隙 */
  padding: 0px; /* 🔥 移除padding，避免缝隙 */
}

/* 子菜单容器悬停效果 */
#actions-section .sat-info-row:hover,
#launch-section .sat-info-row:hover,
#sat-identifier-data .sat-info-row:hover,
#orbital-section .sat-info-row:hover,
#secondary-sat-info .sat-info-row:hover,
#sensor-sat-info .sat-info-row:hover,
#sat-mission-data .sat-info-row:hover {
  background: #0d47a1 !important; /* 与顶部菜单一致的深蓝色 */
  background-color: #0d47a1 !important;
  background-image: none !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.2s ease !important;
  cursor: default !important;
}

#actions-section.collapsed,
#launch-section.collapsed,
#sat-identifier-data.collapsed,
#orbital-section.collapsed,
#secondary-sat-info.collapsed,
#sensor-sat-info.collapsed,
#sat-mission-data.collapsed {
  max-height: 25px;
  transition: max-height 0.25s ease-in-out;
  margin: 0px; /* 🔥 收纳时也移除margin，避免缝隙 */
  padding: 0px; /* 🔥 收纳时也移除padding，避免缝隙 */
}

/* 🔥 修复菜单标题部分的间距，避免缝隙 */
.sat-info-section-header {
  margin: 0px !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(10px / var(--system-scale-factor, 1)) !important;
  border: none !important; /* 🔥 移除边框，包括底部横线 */
  position: relative;
  color: white !important;
  font-weight: bold !important;
  min-height: calc(25px / var(--system-scale-factor, 1)) !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 🔥 章节标题：平时透明，悬停时深蓝色 */
.sat-info-section-header:not(#sat-info-title):not(:hover) {
  background: transparent !important;
  background-color: transparent !important;
}

.sat-info-section-header:not(#sat-info-title):hover {
  background: #0d47a1 !important; /* 深蓝色，跟顶部菜单一样 */
  background-color: #0d47a1 !important;
}

/* 🔧 超超超高优先级透明背景修复 - 无色透明模糊效果 */
html[lang="zh-CN"] body div#sat-infobox,
html[lang] body div#sat-infobox,
html body div#sat-infobox,
body div#sat-infobox,
div#sat-infobox,
#sat-infobox {
  display: none;
  position: absolute !important;
  /* 🔥 移除强制位置设置，允许用户拖动 */
  /* 🔥 移除全局user-select限制，让内部元素正常接收鼠标事件 */
  background: transparent !important;
  background-color: transparent !important;
  /* 🔥 不要强制移除背景图片，这会影响国旗显示 */
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  /* cursor: default !important; 移除光标控制，让Draggabilly处理 */
  height: fit-content;
  max-height: 80%;
  width: 60%; /* 90% * 2/3 = 60% */
  max-width: 333px; /* 500px * 2/3 ≈ 333px */
  border: none !important;
  border-width: 0 !important;
  box-shadow: none !important;
  overflow: auto; /* 恢复为auto，保持滚动功能 */
  z-index: 1001 !important; /* 提高z-index确保可拖动 */
  pointer-events: auto !important; /* 确保可以接收鼠标事件 */
}

/* 🔧 sat-info-title保持深蓝色不透明 */
html[lang] body #sat-info-title,
html body #sat-info-title,
body #sat-info-title,
#sat-info-title {
  background: rgba(0, 26, 51, 0.8) !important;
  background-color: rgba(0, 26, 51, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

/* 🔧 移除强制透明规则，让章节标题可以有悬停效果 */
/* 注释掉强制透明规则，这样章节标题就能正常显示悬停效果了 */

/* 🔧 只针对主容器背景透明，不影响其他元素 */
html[lang="zh-CN"] body div#sat-infobox:not(#sat-info-title),
html[lang] body div#sat-infobox:not(#sat-info-title),
html body div#sat-infobox:not(#sat-info-title),
body div#sat-infobox:not(#sat-info-title),
div#sat-infobox:not(#sat-info-title),
#sat-infobox:not(#sat-info-title) {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

.sat-info-value {
  flex: 1 1 auto; /* 占据剩余空间 */
  text-align: right; /* 右对齐 */
  min-height: calc(20px / var(--system-scale-factor, 1));
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap; /* 防止换行导致重叠 */
  font-size: calc(16px / var(--system-scale-factor, 1)) !important; /* 🔧 与标签字体一致，使用缩放变量 */
  color: white; /* 确保颜色一致 */
  line-height: calc(20px / var(--system-scale-factor, 1)) !important; /* 🔧 统一行高，使用缩放变量 */
  vertical-align: baseline !important; /* 统一垂直对齐 */
  padding: 0 calc(10px / var(--system-scale-factor, 1)) 0 calc(10px / var(--system-scale-factor, 1)) !important; /* 🔧 使用缩放变量 */
}

/* 确保上标和下标不影响行高 */
.sat-info-value sup,
.sat-info-value sub {
  font-size: 10px !important;
  line-height: 0 !important;
  vertical-align: baseline !important;
  position: relative;
}

.sat-info-value sup {
  top: -0.3em;
}

.sat-info-value sub {
  bottom: -0.2em;
}

/* 特殊处理发射场行的嵌套div结构 */
#sat-site-row .sat-info-value {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
  justify-content: center !important;
}

#sat-site-row .sat-info-value div {
  font-size: 14px !important;  /* 改为14px，与标签字体一致 */
  line-height: 1.2 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 固定头部样式 */
#sat-info-header {
  position: sticky;
  top: 0;
  z-index: 10;
  /* 🔥 设置深蓝色背景，不透明 */
  background: var(--color-dark-border) !important;
  background-color: var(--color-dark-border) !important;
}

/* 可滚动内容区域 - 让父容器处理滚动 */

/* 强制覆盖响应式CSS中的冲突样式 */
@media (min-width: 640px) {
  .sat-info-value {
    float: none !important;
    width: auto !important;
    padding: 0 0 0 10px !important; /* 🔥 移除右侧距离，只保留左侧距离 */
    text-align: right !important;
    flex: 1 1 auto !important;
  }
}

@media (min-width: 1024px) {
  .sat-info-value {
    float: none !important;
    width: auto !important;
    padding: 0 0 0 10px !important; /* 🔥 移除右侧距离，只保留左侧距离 */
    text-align: right !important;
    flex: 1 1 auto !important;
  }
}

/* ========================================
 * sat-info-box 标签字体强制设置
 * ======================================== */

/* 🔧 强制设置sat-info-box中所有标签使用缩放变量 */
#sat-infobox .sat-info-key,
html body #sat-infobox .sat-info-key,
html body div#sat-infobox .sat-info-key {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  color: white !important;
  font-weight: 400 !important;
}

/* NORAD编号值显示为蓝色 */
#sat-objnum,
html body #sat-objnum,
html body div#sat-objnum {
  color: #2196F3 !important;
  font-weight: bold !important;
}