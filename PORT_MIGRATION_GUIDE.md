# 🔄 API端口迁移指南

## 概述

本指南帮助您将太空物体模拟平台的API服务端口从 **3001** 迁移到 **5001**。

## 🚀 快速迁移

### 自动迁移（推荐）

```bash
# 运行自动迁移脚本
npm run migrate:port

# 或直接运行
node scripts/migrate-port.js
```

### 手动迁移

如果需要手动迁移，请按以下步骤操作：

## 📋 迁移清单

### 1. 服务器配置文件

#### ✅ 已自动更新的文件：
- [x] `src/api/server.ts` - 默认端口改为5001
- [x] `config.json` - API服务器配置
- [x] `src/api/config.json` - API目录配置
- [x] `.env.example` - 环境变量示例

#### 🔧 需要手动检查的文件：
- [ ] `.env` - 如果存在，更新 `PORT=5001`
- [ ] `.env.local` - 如果存在，更新端口配置
- [ ] `docker-compose.yml` - 如果使用Docker
- [ ] `ecosystem.config.js` - 如果使用PM2

### 2. 前端配置

#### ✅ 已自动更新：
- [x] `src/keeptrack.ts` - 认证API地址
- [x] `src/api/auth.service.ts` - API基础URL

#### 🌐 浏览器本地存储
需要手动清理浏览器中的旧配置：

1. 打开浏览器开发者工具 (F12)
2. 切换到 **Application** 标签
3. 在左侧找到 **Local Storage**
4. 查找并删除包含 `3001` 的配置项：
   - `apiBaseUrl`
   - `serverUrl`
   - `authUrl`

### 3. 脚本和工具

#### ✅ 已自动更新：
- [x] `scripts/diagnose-server.js` - 诊断脚本
- [x] `scripts/test-api.js` - API测试脚本
- [x] `scripts/start-api-server.sh` - Linux启动脚本
- [x] `scripts/start-api-server.bat` - Windows启动脚本
- [x] `deployment-guide.md` - 部署指南

## 🔧 迁移后操作

### 1. 重启服务

```bash
# 停止旧服务（如果在运行）
# Ctrl+C 或 kill 进程

# 启动新服务
npm run start:api

# 或使用安全启动脚本
npm run start:api:safe
```

### 2. 验证迁移

```bash
# 测试新端口
npm run test:api

# 或手动测试
curl http://localhost:5001/
curl http://localhost:5001/api/health
```

### 3. 更新防火墙（如果需要）

```bash
# Linux (ufw)
sudo ufw allow 5001
sudo ufw delete allow 3001

# Linux (iptables)
sudo iptables -A INPUT -p tcp --dport 5001 -j ACCEPT
sudo iptables -D INPUT -p tcp --dport 3001 -j ACCEPT

# Windows防火墙
# 通过控制面板添加5001端口规则
```

### 4. 更新反向代理配置

#### Nginx
```nginx
# 更新 nginx.conf 或站点配置
location /api/ {
    proxy_pass http://localhost:5001;
    # 其他配置保持不变
}
```

#### Apache
```apache
# 更新虚拟主机配置
ProxyPass /api/ http://localhost:5001/
ProxyPassReverse /api/ http://localhost:5001/
```

### 5. 更新监控和日志

如果使用监控工具，请更新：
- 健康检查URL: `http://localhost:5001/api/health`
- 监控端点配置
- 日志收集配置

## 🔍 故障排除

### 问题1: 端口被占用
```bash
# 检查端口占用
lsof -i :5001          # Linux/Mac
netstat -ano | findstr :5001  # Windows

# 如果被占用，可以：
# 1. 停止占用端口的服务
# 2. 或选择其他端口（修改环境变量）
export PORT=5002
```

### 问题2: 前端仍然访问3001
```javascript
// 在浏览器控制台清除本地存储
localStorage.removeItem('apiBaseUrl');
localStorage.removeItem('serverUrl');
localStorage.clear(); // 清除所有本地存储（谨慎使用）

// 刷新页面
location.reload();
```

### 问题3: 认证失败
```bash
# 检查API服务器是否正常运行
curl http://localhost:5001/api/health

# 检查认证端点
curl -X POST http://localhost:5001/api/auth/verify \
  -H "Content-Type: application/json" \
  -d '{"token":"test"}'
```

### 问题4: 配置文件未更新
```bash
# 手动检查配置文件
grep -r "3001" config.json src/api/config.json .env*

# 如果发现遗漏，手动编辑文件
```

## 📊 验证清单

迁移完成后，请验证以下项目：

- [ ] API服务器在5001端口启动成功
- [ ] 健康检查端点响应正常: `http://localhost:5001/api/health`
- [ ] 前端可以正常访问API
- [ ] 认证功能正常工作
- [ ] 浏览器本地存储已清理
- [ ] 防火墙规则已更新（如果需要）
- [ ] 反向代理配置已更新（如果使用）
- [ ] 监控配置已更新（如果使用）

## 🔄 回滚方案

如果迁移出现问题，可以快速回滚：

```bash
# 1. 恢复配置文件（如果有备份）
cp config.json.backup.* config.json
cp src/api/config.json.backup.* src/api/config.json

# 2. 设置环境变量回到3001
export PORT=3001

# 3. 重启服务
npm run start:api
```

## 📞 获取帮助

如果遇到问题：

1. 运行诊断脚本: `npm run diagnose:server`
2. 检查服务器日志
3. 查看浏览器控制台错误
4. 参考 `deployment-guide.md` 文档

## 🎉 迁移完成

恭喜！您已成功将API端口从3001迁移到5001。

新的访问地址：
- API根地址: `http://localhost:5001/`
- 健康检查: `http://localhost:5001/api/health`
- 认证API: `http://localhost:5001/api/auth/*`
