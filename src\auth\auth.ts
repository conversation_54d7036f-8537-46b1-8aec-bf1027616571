import { authService } from '../api/auth.service';

export class AuthManager {
  static async checkAuth() {
    const authToken = localStorage.getItem('authToken');
    if (!authToken) {
      return false;
    }
    
    try {
      return await authService.checkAuth(authToken);
    } catch (error) {
      console.error('认证检查失败:', error);
      return false;
    }
  }

  static async login(username: string, password: string) {
    const result = await authService.login(username, password);
    localStorage.setItem('authToken', result.token);
    return true;
  }

  static logout() {
    localStorage.removeItem('authToken');
  }
}