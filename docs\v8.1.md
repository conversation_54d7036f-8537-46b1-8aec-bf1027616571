# Keep Track 8.1 - Phoenix

"Galactic Groundwork" reflects the foundational improvements and expansions made in this update. It signifies the establishment of a more robust and feature-rich platform, setting the stage for future cosmic explorations within the software.

## Software Release Documentation

This release combines significant enhancements to the core functionality of Keep Track with numerous bug fixes and performance optimizations. It introduces new visualization tools, expands data handling capabilities, and improves user interaction across various features.

## Major Features

### Visualization and Interaction

- Implemented a new gray political map and corresponding color scheme.
- Added support for external TLE files, enhancing data flexibility.
- Introduced auto rotate, pan, and zoom settings for improved navigation.
- Implemented force presets for specific scenarios (e.g., FACSAT2).

### Data Handling and Analysis

- Developed an external catalog loader for improved data management.
- Added filtering options for objects that appear to have reentered.
- Expanded notional debris logic and coloring for better representation.
- Implemented maintenance of sensor filter and rerun on satellite change for multisite lookangles.

### User Interface Improvements

- Added option to show selected object as a large red dot for better visibility.
- Implemented neighbors color scheme for orbit density visualization.
- Added help menus to various features including plot menus, edit satellite menu, and constellation menu.
- Introduced orbit density color scheme with corresponding legend functions.

### Performance Enhancements

- Optimized color calculation speed and implemented caching of the satrec object.
- Improved loading times and reduced unnecessary DOM lookups.
- Implemented faster searching capabilities.

## Minor Features

- Added ability to search for stars and use the "panToStar" function.
- Implemented error reporting feature for more immediate feedback on issues.
- Added Google Cloud support for improved scalability.
- Introduced bulk add/remove functionality to watchlist.

### Bug Fixes

- Resolved issues with satellite FOV not displaying correctly.
- Fixed bugs related to search functionality and results display.
- Addressed problems with the watchlist plugin and line connections to satellites.
- Corrected various issues with menus including countries, constellations, and lookangles.
- Fixed time synchronization issues between different components.

### Code & Infrastructure Updates

- Migrated several modules to TypeScript for improved type safety.
- Removed dependencies on jQuery across various functionalities.
- Upgraded multiple dependencies to address security vulnerabilities.
- Implemented better error catching and defensive coding practices.

### Documentation

- Updated changelogs, readmes, and other documentation to reflect changes and new features.
- Improved developer contribution guidelines and setup instructions.

### Mobile and Compatibility

- Enhanced mobile experience with improved controls and UI adjustments.
- Implemented fixes for cross-browser compatibility, especially for Firefox and Internet Explorer.

This update lays a solid foundation for future developments while significantly improving the current user experience and system capabilities.
