/**
 * 清理浏览器缓存脚本
 * 用于清理旧的API端口配置
 */

(function() {
    'use strict';
    
    console.log('🧹 开始清理浏览器缓存...');
    
    // 需要清理的localStorage键
    const keysToRemove = [
        'apiBaseUrl',
        'serverUrl', 
        'authUrl',
        'apiUrl',
        'baseUrl'
    ];
    
    // 清理localStorage
    function clearLocalStorage() {
        let removedCount = 0;
        
        keysToRemove.forEach(key => {
            const value = localStorage.getItem(key);
            if (value) {
                console.log(`🗑️  移除 localStorage['${key}'] = '${value}'`);
                localStorage.removeItem(key);
                removedCount++;
            }
        });
        
        // 检查是否有包含3001的其他键
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            const value = localStorage.getItem(key);
            
            if (value && value.includes('3001')) {
                console.log(`🗑️  移除包含3001的配置: localStorage['${key}'] = '${value}'`);
                localStorage.removeItem(key);
                removedCount++;
                i--; // 因为移除了一个项目，索引需要调整
            }
        }
        
        return removedCount;
    }
    
    // 清理sessionStorage
    function clearSessionStorage() {
        let removedCount = 0;
        
        keysToRemove.forEach(key => {
            const value = sessionStorage.getItem(key);
            if (value) {
                console.log(`🗑️  移除 sessionStorage['${key}'] = '${value}'`);
                sessionStorage.removeItem(key);
                removedCount++;
            }
        });
        
        // 检查是否有包含3001的其他键
        for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            const value = sessionStorage.getItem(key);
            
            if (value && value.includes('3001')) {
                console.log(`🗑️  移除包含3001的配置: sessionStorage['${key}'] = '${value}'`);
                sessionStorage.removeItem(key);
                removedCount++;
                i--; // 因为移除了一个项目，索引需要调整
            }
        }
        
        return removedCount;
    }
    
    // 清理IndexedDB（如果有的话）
    function clearIndexedDB() {
        return new Promise((resolve) => {
            if (!window.indexedDB) {
                resolve(0);
                return;
            }
            
            // 这里可以添加特定的IndexedDB清理逻辑
            // 目前只是占位符
            resolve(0);
        });
    }
    
    // 主清理函数
    async function clearCache() {
        console.log('📊 清理前的存储状态:');
        console.log(`  localStorage项目数: ${localStorage.length}`);
        console.log(`  sessionStorage项目数: ${sessionStorage.length}`);
        
        const localStorageRemoved = clearLocalStorage();
        const sessionStorageRemoved = clearSessionStorage();
        const indexedDBRemoved = await clearIndexedDB();
        
        const totalRemoved = localStorageRemoved + sessionStorageRemoved + indexedDBRemoved;
        
        console.log('📊 清理后的存储状态:');
        console.log(`  localStorage项目数: ${localStorage.length}`);
        console.log(`  sessionStorage项目数: ${sessionStorage.length}`);
        console.log(`  总共移除项目: ${totalRemoved}`);
        
        if (totalRemoved > 0) {
            console.log('✅ 缓存清理完成！建议刷新页面。');
            
            // 显示用户友好的提示
            if (typeof window !== 'undefined' && window.confirm) {
                const shouldReload = confirm(
                    `已清理 ${totalRemoved} 个缓存项目。\n` +
                    '是否立即刷新页面以应用更改？'
                );
                
                if (shouldReload) {
                    window.location.reload();
                }
            }
        } else {
            console.log('ℹ️  没有找到需要清理的缓存项目。');
        }
        
        return totalRemoved;
    }
    
    // 导出清理函数供外部调用
    window.clearApiCache = clearCache;
    
    // 如果URL包含清理参数，自动执行清理
    if (window.location.search.includes('clear-cache=true')) {
        clearCache();
    }
    
    console.log('🧹 缓存清理脚本已加载。');
    console.log('💡 使用方法:');
    console.log('  1. 在控制台运行: clearApiCache()');
    console.log('  2. 或访问: ' + window.location.origin + window.location.pathname + '?clear-cache=true');
})();
