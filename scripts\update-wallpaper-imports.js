#!/usr/bin/env node

/**
 * 更新代码中的壁纸导入路径，从JPG改为WebP格式
 * 
 * 使用方法：
 * node scripts/update-wallpaper-imports.js
 */

const fs = require('fs');
const path = require('path');

const keeptrackFile = path.join(__dirname, '../src/keeptrack.ts');

// 要更新的导入映射
const importMappings = {
  "import blueMarbleJpg from '@public/img/wallpaper/blue-marble.jpg';": "import blueMarbleWebp from '@public/img/wallpaper/blue-marble.webp';",
  "import cubesatJpg from '@public/img/wallpaper/cubesat.jpg';": "import cubesatWebp from '@public/img/wallpaper/cubesat.webp';",
  "import earthJpg from '@public/img/wallpaper/Earth.jpg';": "import earthWebp from '@public/img/wallpaper/Earth.webp';",
  "import issJpg from '@public/img/wallpaper/iss.jpg';": "import issWebp from '@public/img/wallpaper/iss.webp';",
  "import moonJpg from '@public/img/wallpaper/moon.jpg';": "import moonWebp from '@public/img/wallpaper/moon.webp';",
  "import observatoryJpg from '@public/img/wallpaper/observatory.jpg';": "import observatoryWebp from '@public/img/wallpaper/observatory.webp';",
  "import rocketJpg from '@public/img/wallpaper/rocket.jpg';": "import rocketWebp from '@public/img/wallpaper/rocket.webp';",
  "import rocket2Jpg from '@public/img/wallpaper/rocket2.jpg';": "import rocket2Webp from '@public/img/wallpaper/rocket2.webp';",
  "import rocket3Jpg from '@public/img/wallpaper/rocket3.jpg';": "import rocket3Webp from '@public/img/wallpaper/rocket3.webp';",
  "import rocket4Jpg from '@public/img/wallpaper/rocket4.jpg';": "import rocket4Webp from '@public/img/wallpaper/rocket4.webp';",
  "import satJpg from '@public/img/wallpaper/sat.jpg';": "import satWebp from '@public/img/wallpaper/sat.webp';",
  "import sat2Jpg from '@public/img/wallpaper/sat2.jpg';": "import sat2Webp from '@public/img/wallpaper/sat2.webp';",
  "import telescopeJpg from '@public/img/wallpaper/telescope.jpg';": "import telescopeWebp from '@public/img/wallpaper/telescope.webp';",
  "import thuleJpg from '@public/img/wallpaper/thule.jpg';": "import thuleWebp from '@public/img/wallpaper/thule.webp';"
};

// 数组变量名的映射
const arrayMappings = {
  "blueMarbleJpg": "blueMarbleWebp",
  "cubesatJpg": "cubesatWebp", 
  "earthJpg": "earthWebp",
  "issJpg": "issWebp",
  "moonJpg": "moonWebp",
  "observatoryJpg": "observatoryWebp",
  "rocketJpg": "rocketWebp",
  "rocket2Jpg": "rocket2Webp",
  "rocket3Jpg": "rocket3Webp", 
  "rocket4Jpg": "rocket4Webp",
  "satJpg": "satWebp",
  "sat2Jpg": "sat2Webp",
  "telescopeJpg": "telescopeWebp",
  "thuleJpg": "thuleWebp"
};

function updateKeeptrackFile() {
  console.log('更新keeptrack.ts文件中的壁纸导入...');
  
  try {
    // 读取文件内容
    let content = fs.readFileSync(keeptrackFile, 'utf8');
    
    // 更新导入语句
    for (const [oldImport, newImport] of Object.entries(importMappings)) {
      if (content.includes(oldImport)) {
        content = content.replace(oldImport, newImport);
        console.log(`✅ 更新导入: ${oldImport.split("'")[1]} -> ${newImport.split("'")[1]}`);
      }
    }
    
    // 更新数组中的变量名
    const arrayRegex = /private static readonly splashScreenImgList_ =\s*\[([\s\S]*?)\];/;
    const arrayMatch = content.match(arrayRegex);
    
    if (arrayMatch) {
      let arrayContent = arrayMatch[1];
      
      for (const [oldVar, newVar] of Object.entries(arrayMappings)) {
        if (arrayContent.includes(oldVar)) {
          arrayContent = arrayContent.replace(new RegExp(oldVar, 'g'), newVar);
          console.log(`✅ 更新数组变量: ${oldVar} -> ${newVar}`);
        }
      }
      
      content = content.replace(arrayMatch[0], `private static readonly splashScreenImgList_ =\n    [${arrayContent}];`);
    }
    
    // 写回文件
    fs.writeFileSync(keeptrackFile, content, 'utf8');
    console.log('\n✅ keeptrack.ts文件更新完成！');
    
  } catch (error) {
    console.error('❌ 更新失败:', error.message);
  }
}

// 检查文件是否存在
if (!fs.existsSync(keeptrackFile)) {
  console.error(`❌ 文件不存在: ${keeptrackFile}`);
  process.exit(1);
}

// 执行更新
updateKeeptrackFile();

console.log('\n下一步：');
console.log('1. 检查更新后的代码');
console.log('2. 测试应用程序以确保WebP图片正常加载');
console.log('3. 如果一切正常，可以删除原始的JPG文件以节省空间');
