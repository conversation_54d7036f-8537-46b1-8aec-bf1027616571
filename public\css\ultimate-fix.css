/*
 * 🔥🔥🔥 终极修复CSS - 清理版本
 * 创建时间: 2025-01-29
 * 目标: 只保留必要的修复样式，移除缩放系统
 *
 * 注意: 主要样式已迁移到 responsive-design.css 统一管理
 * 这里只保留一些特殊的修复样式
 */

/* ========================================
 * 1. 卫星信息框特殊修复
 * ======================================== */

/* 卫星信息框宽度调整 - 稍微缩小 */
#sat-infobox {
  width: 55% !important;
  max-width: 320px !important;
}

/* 响应式宽度调整 - 稍微缩小 */
@media (min-width: 640px) {
  #sat-infobox {
    width: 42% !important;
    max-width: 280px !important;
  }
}

@media (min-width: 768px) {
  #sat-infobox {
    width: 30% !important;
    max-width: 320px !important;
  }
}

@media (min-width: 1024px) {
  #sat-infobox {
    width: 25% !important;
    max-width: 360px !important;
  }
}

/* ========================================
 * 2. 搜索结果框层级修复
 * ======================================== */

#search-results {
  z-index: 1001 !important;
}

#sat-infobox {
  z-index: 1000 !important;
}

/* ========================================
 * 3. 设置菜单弹出层修复
 * ======================================== */

#settings-menu {
  z-index: 1002 !important;
}

/* ========================================
 * 4. 国旗图标显示修复
 * ======================================== */

#sat-infobox-fi.fi {
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
  width: 24px !important;
  height: 18px !important;
  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  position: absolute !important;
  right: 5px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 5 !important;
  background-color: transparent !important;
}

/* ========================================
 * 5. 侧边菜单分隔线修复
 * ======================================== */

.side-menu .divider,
.side-menu li.divider,
[id$="-menu"] .divider,
[id$="-menu"] li.divider,
li.divider,
.divider {
  background-color: white !important;
  height: 2px !important;
  margin: 10px 0 !important;
  border: none !important;
}

/* ========================================
 * 6. 悬停效果修复
 * ======================================== */

#sat-infobox .sat-info-row:hover,
#sat-infobox .sat-infobox-links:hover,
#sat-infobox .menu-selectable:hover {
  border-radius: 4px !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* ========================================
 * 7. 特殊输入框修复
 * ======================================== */

/* 历史轨道日期输入框 */
#history-start-date,
#history-end-date,
#geo-longitude-start-date,
#geo-longitude-end-date {
  height: 32px !important;
  line-height: 32px !important;
  font-size: 16px !important;
  padding: 0 8px !important;
  margin: 0 16px 0 0 !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
}

/* ========================================
 * 8. 颜色图例修复
 * ======================================== */

.Square-Box {
  width: 30px !important;
  height: 30px !important;
  border-radius: 15px !important;
  margin-right: 20px !important;
  border-width: 2px !important;
}

/* ========================================
 * 9. 监视列表特殊修复
 * ======================================== */

#watchlist-menu .sat-sccnum,
#watchlist-menu .sat-name,
#watchlist-content .sat-sccnum,
#watchlist-content .sat-name {
  line-height: 18px !important;
  padding: 2px 0 !important;
  margin: 0 !important;
  display: block !important;
}

/* 新增卫星输入框和按钮的flex布局优化 */
#watchlist-menu .row:has(#watchlist-new),
#watchlist-content .row:has(#watchlist-new) {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 0 !important;
  gap: 10px !important;
}

#watchlist-menu .input-field:has(#watchlist-new),
#watchlist-content .input-field:has(#watchlist-new) {
  flex: 1 !important;
  margin-right: 0 !important;
  margin-bottom: 0 !important;
}

/* ========================================
 * 10. 强制输入框聚焦状态透明
 * ======================================== */

#geo-longitude-norad-id:focus,
#geo-longitude-start-date:focus,
#geo-longitude-end-date:focus,
#geo-longitude-range:focus,
#history-norad-id:focus,
#history-start-date:focus,
#history-end-date:focus,
#watchlist-new:focus {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* ========================================
 * 11. 最终位置修复
 * ======================================== */

#sat-infobox {
  position: absolute !important;
}

/* 样式已移至responsive-design.css统一管理的占位符 */
/* 侧边栏菜单标签样式已移至responsive-design.css统一管理 */
/* 选择框下拉内容样式已移至responsive-design.css统一管理 */
/* 输入框文字大小已移至responsive-design.css统一管理 */
/* 开关标签样式已移至responsive-design.css统一管理 */
/* 下拉框样式已移至responsive-design.css统一管理 */
