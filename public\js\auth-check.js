/**
 * 认证检查脚本 - 简化版本，移除所有调试输出
 * 在主页面加载前检查用户登录状态
 */

class AuthChecker {
    constructor() {
        this.apiBaseUrl = '';
        this.token = localStorage.getItem('authToken');
        this.init();
    }

    async init() {
        this.apiBaseUrl = await this.getApiBaseUrl();
    }

    async getApiBaseUrl() {
        // 首先尝试从localStorage获取（登录时保存的）
        const savedApiUrl = localStorage.getItem('apiBaseUrl');
        if (savedApiUrl) {
            return savedApiUrl;
        }

        // 使用统一配置加载器
        if (window.configLoader) {
            try {
                const apiUrl = await window.configLoader.getAuthUrl();
                return apiUrl;
            } catch (error) {
                // Silent fail
            }
        }

        // 尝试从配置文件获取
        try {
            const response = await fetch('/config.json');
            if (response.ok) {
                const config = await response.json();
                if (config.apiServer && config.apiServer.url) {
                    const apiUrl = config.apiServer.url + '/api/auth';
                    return apiUrl;
                }
            }
        } catch (error) {
            // Silent fail
        }

        // 尝试使用默认配置
        if (window.configLoader) {
            try {
                const defaultConfig = window.configLoader.getDefaultConfig();
                const apiUrl = defaultConfig.apiServer.url + '/api/auth';
                return apiUrl;
            } catch (error) {
                // Silent fail
            }
        }

        // 最终回退方案
        const currentHost = window.location.hostname;
        const protocol = window.location.protocol;
        const apiUrl = `${protocol}//${currentHost}:5001/api/auth`;
        return apiUrl;
    }

    async checkAuth() {
        // 如果没有token，直接跳转到登录页
        if (!this.token) {
            this.redirectToLogin();
            return false;
        }

        try {
            // 确保URL格式正确，避免重复路径
            let verifyUrl = `${this.apiBaseUrl}/verify`;
            if (verifyUrl.includes('/api/auth/api/auth')) {
                verifyUrl = verifyUrl.replace('/api/auth/api/auth', '/api/auth');
            }

            const response = await fetch(verifyUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();

                if (data.valid && data.user) {
                    // 保存用户信息
                    localStorage.setItem('user', JSON.stringify(data.user));

                    // 检查是否需要修改密码
                    if (data.user.needsPasswordChange) {
                        this.showPasswordChangeModal();
                        return false;
                    }

                    return true;
                }
            }

            // Token可能无效，但不立即清除，给用户更多机会
            return true; // 即使服务器认为token无效，也保持用户登录状态
        } catch (error) {
            // 网络错误时不清除token，可能是临时问题
            return true; // 网络错误时假设用户仍然有效
        }
    }

    redirectToLogin() {
        // 保存当前页面URL，登录后可以返回
        const currentUrl = window.location.href;
        if (!currentUrl.includes('/login.html')) {
            localStorage.setItem('redirectAfterLogin', currentUrl);
        }
        
        // 跳转到登录页面
        window.location.replace('/login.html');
    }

    showPasswordChangeModal() {
        // 创建密码修改模态框
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;

        modal.innerHTML = `
            <div style="background: white; padding: 30px; border-radius: 8px; max-width: 400px; width: 90%;">
                <h3 style="margin-top: 0; color: #333;">需要修改默认密码</h3>
                <p style="color: #666; margin-bottom: 20px;">为了账户安全，请修改您的默认密码。</p>
                <form id="passwordChangeForm">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; color: #333;">新密码:</label>
                        <input type="password" id="newPassword" required 
                               style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; color: #333;">确认新密码:</label>
                        <input type="password" id="confirmPassword" required 
                               style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="text-align: right;">
                        <button type="submit" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                            修改密码
                        </button>
                    </div>
                </form>
            </div>
        `;

        document.body.appendChild(modal);

        // 处理表单提交
        document.getElementById('passwordChangeForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword !== confirmPassword) {
                alert('两次输入的密码不一致');
                return;
            }

            if (newPassword.length < 6) {
                alert('密码长度至少6位');
                return;
            }

            try {
                const response = await fetch(`${this.apiBaseUrl}/change-password`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ newPassword })
                });

                if (response.ok) {
                    alert('密码修改成功');
                    document.body.removeChild(modal);
                    window.location.reload();
                } else {
                    const error = await response.json();
                    alert('密码修改失败: ' + (error.message || '未知错误'));
                }
            } catch (error) {
                alert('网络错误，请稍后重试');
            }
        });
    }
}

// 创建全局认证检查器实例
window.authChecker = new AuthChecker();

// 立即执行认证检查
(function() {
    const currentPath = window.location.pathname;
    const isMainPage = currentPath === '/' || currentPath === '/index.html' || currentPath.endsWith('/');
    
    // 需要认证的页面列表
    const authRequiredPages = [
        '/',
        '/index.html',
        '/admin.html'
    ];
    
    const needsAuth = authRequiredPages.some(page => 
        currentPath === page || currentPath.endsWith(page)
    );

    if (needsAuth || isMainPage) {
        // 检查是否有 token
        const token = localStorage.getItem('authToken');
        if (!token) {
            // 阻止页面继续加载
            document.write(`
                <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: #f5f5f5;">
                    <div style="text-align: center; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h2 style="color: #333; margin-bottom: 20px;">需要登录</h2>
                        <p style="color: #666; margin-bottom: 30px;">请先登录后再访问此页面</p>
                        <a href="/login.html" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                            前往登录
                        </a>
                    </div>
                </div>
            `);
            document.close();
            return;
        }
    }
})();

// DOM加载完成后的认证检查
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;
    const isMainPage = currentPath === '/' || currentPath === '/index.html' || currentPath.endsWith('/');

    if (isMainPage) {
        // 简化认证检查，只验证token存在
        const token = localStorage.getItem('authToken');
        if (!token) {
            window.location.replace('/login.html');
            return;
        }

        // 简化后台验证，不强制登出
        if (window.authChecker) {
            window.authChecker.checkAuth().catch(() => {
                // 网络错误时不强制登出，允许离线使用
            });
        }
    }
});

// 页面可见性变化时的认证检查
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        const currentPath = window.location.pathname;
        const isMainPage = currentPath === '/' || currentPath === '/index.html' || currentPath.endsWith('/');
        
        if (isMainPage) {
            const token = localStorage.getItem('authToken');
            if (!token) {
                window.location.replace('/login.html');
                return;
            }
        }
    }
});

// 定期认证检查
function performAuthCheck() {
    const currentPath = window.location.pathname;
    const isMainPage = currentPath === '/' || currentPath === '/index.html' || currentPath.endsWith('/');

    if (isMainPage) {
        const token = localStorage.getItem('authToken');
        if (!token) {
            window.location.replace('/login.html');
            return;
        }

        // 检查token是否过期
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Math.floor(Date.now() / 1000);
            const fiveMinutesFromNow = currentTime + (5 * 60);

            if (payload.exp && payload.exp < currentTime) {
                localStorage.removeItem('authToken');
                window.location.replace('/login.html');
                return;
            }
        } catch (error) {
            localStorage.removeItem('authToken');
            window.location.replace('/login.html');
            return;
        }
    }
}

// 🔧 优化的认证检查 - 根据用户活动调整频率
let authCheckInterval = 5 * 60 * 1000; // 默认5分钟
let lastUserActivity = Date.now();
let authCheckTimeoutId = null;

// 监听用户活动
function trackUserActivity() {
  lastUserActivity = Date.now();
}

// 添加用户活动监听器
['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(event => {
  document.addEventListener(event, trackUserActivity, { passive: true });
});

// 智能认证检查
function smartAuthCheck() {
  const timeSinceActivity = Date.now() - lastUserActivity;

  // 如果用户长时间不活跃，降低检查频率
  if (timeSinceActivity > 30 * 60 * 1000) { // 30分钟无活动
    authCheckInterval = 15 * 60 * 1000; // 15分钟检查一次
  } else if (timeSinceActivity > 10 * 60 * 1000) { // 10分钟无活动
    authCheckInterval = 10 * 60 * 1000; // 10分钟检查一次
  } else {
    authCheckInterval = 5 * 60 * 1000; // 正常5分钟检查一次
  }

  // 执行认证检查
  performAuthCheck();

  // 设置下次检查
  authCheckTimeoutId = setTimeout(smartAuthCheck, authCheckInterval);
}

// 启动智能认证检查
smartAuthCheck();
