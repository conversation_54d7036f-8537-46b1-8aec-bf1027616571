import { keepTrackApi } from '@app/keepTrackApi';

export const planetariumDiv = keepTrackApi.html`
<ul id="legend-list-planetarium">
  <li>
    <div class="Square-Box legend-payload-box"></div>
    载荷
  </li>
  <li>
    <div class="Square-Box legend-rocketBody-box"></div>
    火箭体
  </li>
  <li>
    <div class="Square-Box legend-debris-box"></div>
    碎片
  </li>
  <li>
    <div class="Square-Box legend-pink-box"></div>
    特殊卫星
  </li>
</ul>
`.trim();
export const astronomyDiv = keepTrackApi.html`
<ul id="legend-list-astronomy">
  <li>
    <div class="Square-Box legend-starHi-box"></div>
    视星等小于3.5
  </li>
  <li>
    <div class="Square-Box legend-starMed-box"></div>
    视星等介于3.5和4.7
  </li>
  <li>
    <div class="Square-Box legend-starLow-box"></div>
    视星等大于4.7
  </li>
</ul>
`.trim();
export const nearDiv = keepTrackApi.html`
<ul id="legend-list-near">
  <li>
    <div class="Square-Box legend-satLEO-box"></div>
    近地点小于2000 km
  </li>
  <!-- <li><div class="Square-Box legend-satOther-box"></div>Other Satellite</li> -->
  <li>
    <div class="Square-Box legend-inFOV-box"></div>
    可见卫星
  </li>
</ul>
`.trim();
export const deepDiv = keepTrackApi.html`
<ul id="legend-list-deep">
  <li>
    <div class="Square-Box legend-satGEO-box"></div>
    远地点大于35000 km
  </li>
  <!-- <li><div class="Square-Box legend-satOther-box"></div>Other Satellite</li> -->
  <li>
    <div class="Square-Box legend-inFOV-box"></div>
    可见卫星
  </li>
</ul>
`.trim();

