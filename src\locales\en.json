{"time": {"days": {"0": "Sunday", "1": "Monday", "2": "Tuesday", "3": "Wednesday", "4": "Thursday", "5": "Friday", "6": "Saturday"}, "days-short": {"0": "Sun", "1": "Mon", "2": "<PERSON><PERSON>", "3": "Wed", "4": "<PERSON>hu", "5": "<PERSON><PERSON>", "6": "Sat"}, "months": {"1": "January", "2": "February", "3": "March", "4": "April", "5": "May", "6": "June", "7": "July", "8": "August", "9": "September", "10": "October", "11": "November", "12": "December"}, "calendar": {"time": "Time", "hour": "Hour", "minute": "Minute", "second": "Second", "now": "Now", "done": "Done", "pause": "Pause", "propagation": "Propagation"}}, "errorMsgs": {"catalogNotFullyInitialized": "Catalog has not fully initialized yet. Please wait a few seconds and try again.", "sensorGroupsApiEmpty": "No sensor groups found in the API, reverting to the included sensor groups database.", "SelectSensorFirst": "Select a sensor first!", "SelectSatelliteFirst": "Select a satellite first!", "SelectSecondarySatellite": "Select a secondary satellite first!", "SatelliteNotDetailedSatellite": "Satellite is not DetailedSatellite!", "SensorNotFound": "Sensor not found!", "Scene": {"disablingGodrays": "Your computer is struggling! Disabling Godrays.", "disablingAurora": "Your computer is struggling! Disabling Aurora.", "disablingAtmosphere": "Your computer is struggling! Disabling Atmosphere.", "disablingMoon": "Your computer is struggling! Disabling <PERSON>.", "disablingMilkyWay": "Your computer is struggling! Disabling Milky Way.", "disablingSun": "Your computer is struggling! Disabling Sun."}, "Breakup": {"SatelliteNotFound": "Satellite not found!", "CannotCreateBreakupForNonCircularOrbits": "Cannot create a breakup for non-circular orbits. Working on a fix.", "CannotCalcDirectionOfSatellite": "Cannot calculate direction of satellite. Try again later.", "ErrorCreatingBreakup": "Error creating breakup!", "InvalidStartNum": "Invalid satellite starting number! Defaulting to 90000!", "BreakupGeneratorFailed": "Breakup Generator Failed!"}, "Collisions": {"noCollisionsData": "No collision data found!", "errorProcessingCollisions": "Error processing SOCRATES data!"}, "EditSat": {"errorReadingFile": "Error while reading file!", "satelliteNotFound": "Satellite {{sccNum}} not found!"}, "CreateSat": {"errorCreatingSat": "Error creating satellite!"}, "Reports": {"popupBlocker": "Please allow popups for this site to view reports."}, "SensorManager": {"errorUpdatingUi": "Error updating sensor UI styling."}}, "hoverManager": {"launched": "Launched", "launchedPlanned": "Launched: Planned", "launchedUnknown": "Launched: Unknown"}, "loadingScreenMsgs": {"math": "Attempting to Math...", "science": "Locating Science...", "science2": "Found Science...", "dots": "Drawing Dots in Space...", "satIntel": "Integrating Satellite Intel...", "painting": "Painting the Earth...", "coloring": "Coloring Inside the Lines..", "elsets": "Locating GPs...", "models": "Building 3D Models...", "cunningPlan": "Crafting a Cunning Plan...", "copyrightNotice": "KeepTrack™ and spacedefense™ are trademarks of Kruczek Labs LLC.<br>This instance is licensed under the GNU AGPL v3.0. Attribution, source access, and this notice must remain visible.<br>No commercial license has been granted, and no compensation has been provided to the rights holder.<br>Unauthorized use, rebranding, or removal of attribution may violate trademark and open source license terms.<br>© 2025 Kruczek Labs LLC. All rights reserved. See LICENSE for full terms.", "copyrightNoticeMobile": "KeepTrack™ and spacedefense™ are trademarks of Kruczek Labs LLC.<br>This instance is licensed under the GNU AGPL v3.0. Attribution, source access, and this notice must remain visible. No commercial license has been granted. No compensation has been provided to the rights holder. Unauthorized use, rebranding, or removal of attribution may violate trademark and open source license terms.<br>© 2025 Kruczek Labs LLC. All rights reserved. See LICENSE for full terms."}, "splashScreens": {"1": "Satellite models appear bigger than they really are. Everything else is to scale.", "2": "Press the 'L' key to toggle satellite orbits on/off.", "3": "Press the 'P' key to open a polar plot of the current satellite (You need to pick a sensor first!).", "4": "Reset the simulation time by pressing the 'T' key.", "5": "Advance the simulation time by pressing the '>' or '=' key.", "6": "Move back the simulation time by pressing the '<' or ')' key.", "7": "Pressing the '/' key will toggle the simulation speed between 1x and 0x.", "8": "Press the 'V' key to change the view mode.", "9": "Press Shift+F1 to open the help menu at any time.", "10": "Press the 'R' key to enable auto-rotation.", "11": "Pressing Shift + C when a satellite is selected will toggle its visibility cone on/off.", "12": "The 'Home' key will rotate the camera to the current sensor.", "13": "The '`' (tilda) key will reset the camera to the default view.", "14": "The 'M' key will show the 2D map view of the current satellite.", "15": "The settings menu located in the bottom toolbar contains many options to customize your experience.", "16": "Many of the menus have additional settings that can be accessed by clicking the gear icon.", "17": "Add satellites to the watchlist to get notifications when they are overhead of the current sensor.", "18": "Right click on the globe to open the context menu with more options.", "19": "Press '+' or '-' keys to zoom in and out.", "20": "Press 'F11' to toggle on/off the fullscreen mode.", "21": "You can search for satellites by name or NORAD ID in the search bar at the top right.", "22": "A new launch nominal can be created by selecting a satellite and clicking the 'New Launch' button in the bottom menu.", "23": "Press the 'N' key to toggle night.", "24": "Press the 'I' key to hide/display contextual information about a satellite.", "25": "Press the 'B' key to hide/display the menu.", "26": "Speed up the simulation by pressing the Shift + ';' key.", "27": "Slow down the simulation by pressing the ',' key.", "28": "Set an object as Secondary to see its relative distance with primary object.", "29": "Switch an object between Primary/Secondary with the '[' key."}, "plugins": {"SensorListPlugin": {"bottomIconLabel": "Sensors", "title": "Sensor List <PERSON>u", "helpBody": "The Sensors menu allows you to select a sensor for use in calculations and other menu's functions. Sensors are in groups based on the networks they primarily support. On the left side of the menu is the name of the sensor and on the right side is the country/organization that owns it. <br><br> Selecting an \"All...Sensors\" option will select all sensors in that group. This is useful for visualizing the networks coverage, but currently does not work for all calculations. If you are trying to calculate look angles for a network it is best to use the multi-site look angles tool or to use look angles for each of the individual sensors in the network. <br><br> Sensors on this list include Mechanical and Phased Array Radars, in addition to Optical sensors: <ul style=\"margin-left: 40px;\"> <li> Phased Array Radars typically are limited to Low Earth Orbit (LEO). </li> <li> Mechanical Radars can be used for both LEO and Geostationary Orbit (GEO). </li> <li> Optical sensors are typically used for GEO, but can also be used for LEO. </li> <li> Optical sensors are limited to night time observations in clear skies, whereas radars can be used for both day and night. </li> </ul> <br> Sensor information is based on publicly available data and can be verified in the Sensor Info menu. If you have public data on additional sensors or corrections to existing sensor information please contact me at <a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>."}, "SensorInfoPlugin": {"bottomIconLabel": "Sensor Info", "title": "Sensor Info Menu", "helpBody": "Sensor Info provides information about the currently selected sensor. The information is based on publicly available data and may not always be 100% accurate. If you have public data on additional sensors or corrections to existing sensor information please contact me at <a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>. <br><br> The information provided includes: <ul style=\"margin-left: 40px;\"> <li> Sensor Name </li> <li> Sensor Owner </li> <li> Sensor Type </li> <li> Sensor Field of View </li> </ul> <br> Additionally, lines can be quickly created from the sensor to the sun or moon from this menu."}, "CustomSensorPlugin": {"bottomIconLabel": "Custom Sensor", "title": "Custom Sensor Menu", "helpBody": "This allows you to create a custom sensor for use in calculations and other menu's functions. This can be a completely original sensor or a modification of an existing sensor. <br><br> After setting the latitude, longitude, and altitude of the sensor, you can set the sensor's field of view. Selecting telescope will create a 360 degree field of view with an elevation mask of 10 degrees and unlimited range. Deselecting the telescope option will allow you to set the field of view manually. <br><br> If you are trying to edit an existing sensor, you can select it from the sensor list first and the custom sensor will be updated with the selected sensor's information."}, "LookAnglesPlugin": {"bottomIconLabel": "Look Angles", "title": "<PERSON> Angles <PERSON>", "helpBody": "The Look Angles menu allows you to calculate the range, azimuth, and elevation angles between a sensor and a satellite. A satellite and sensor must first be selected before the menu can be used. <br><br> The toggle only rise and set times will only calculate the rise and set times of the satellite. This is useful for quickly determining when a satellite will be visible to a sensor. <br><br> The search range can be modified by changing the length and interval options."}, "MultiSiteLookAnglesPlugin": {"bottomIconLabel": "Multi-Site Looks", "title": "Multi-Site Look Angles Menu", "helpBody": "The Multi-Site Look Angles menu allows you to calculate the range, azimuth, and elevation angles between a satellite and multiple sensors. A satellite must first be selected before the menu can be used. <br><br> By default the menu will calculate the look angles for all sensors in the Space Surveillance Netowrk. If you would like to calculate the look angles for additional sensors, you can export a csv file at the bottom of the menu. The csv file will contain look angles for all sensors. <br><br> Clicking on a row in the table will select the sensor and change the simulation time to the time of the look angle."}, "SensorTimeline": {"bottomIconLabel": "Sensor Timeline", "title": "Sensor Timeline Menu", "helpBody": "The Sensor Timeline menu shows when a list of sensors have visibility of one satellite. The timeline is color-coded to show the quality of the pass. Red is a bad pass, yellow is an average pass, and green is a good pass. Click on a pass to change the sensor and time to that pass. <br><br> The timeline can be modified by changing the start and end time of the simulation. Additionally, the timeline can be modified by changing the length and interval options."}, "ProximityOps": {"bottomIconLabel": "Rendezvous and Proximity Operations", "title": "Rendezvous and Proximity Operations (RPO)", "titleSecondary": "Rendezvous and Proximity Operations List", "helpBody": "Find coming approachs between satellites", "noradId": "NORAD ID", "maxDistThreshold": "Max Distance Threshold (km)", "maxRelativeVelocity": "Max Relative Velocity (km/s)", "searchDuration": "Search Duration (hours)", "geoText": "Geostationary", "leoText": "Low Earth Orbit", "orbitType": "Orbit Type", "geoAllVsAll": "Geostationary All vs All", "geoAllVsAllTooltip": "Search for RPOs between all geostationary satellites", "comparePayloadsOnly": "Compare Payloads Only", "comparePayloadsOnlyTooltip": "Only search for RPOs between payloads", "ignoreVimpelRso": "Ignore Vimpel RSO", "ignoreVimpelRsoTooltip": "Ignore RSOs sourced from Vimpel Catalog"}, "SatelliteTimeline": {"bottomIconLabel": "Satellite Timeline", "title": "Satellite Timeline Menu", "helpBody": "The Satellite Timeline menu shows when a single sensor has visibility of a list of satellites. The timeline is color-coded to show the quality of the pass. Red is a bad pass, yellow is an average pass, and green is a good pass. Click on a pass to change the satellite and time to that pass. <br><br> The timeline can be modified by changing the start and end time of the simulation. Additionally, the timeline can be modified by changing the length and interval options."}, "WatchlistPlugin": {"bottomIconLabel": "Watchlist", "title": "Watchlist <PERSON>u", "helpBody": "The Watchlist menu allows you to create a list of priority satellites to track. This allows you to quickly retrieve the satellites you are most interested in. The list is saved in your browser's local storage and will be available the next time you visit the site. <br><br> When satellites on the watchlist enter the selected sensor's field of view a notification will be displayed, a line will be drawn from the sensor to the satellite, and the satellite's number will be displayed on the globe. <br><br> The overlay feature relies on the watchlist being populated."}, "WatchlistOverlay": {"bottomIconLabel": "Overlay", "title": "Overlay Menu", "helpBody": "<p> The Watchlist Overlay shows the next pass time for each satellite in your watchlist. The overlay is updated every 10 seconds. </p> <p> The overlay is color coded to show the time to the next pass. The colors are as follows: </p> <ul> <li>Yellow - In View</li> <li>Blue - Time to Next Pass is up to 30 minutes after the current time or 10 minutes before the current time</li> <li>White - Any future pass not fitting the above requirements</li> </ul> <p> Clicking on a satellite in the overlay will center the map on that satellite. </p>"}, "ReportsPlugin": {"bottomIconLabel": "Reports", "title": "Reports Menu", "helpBody": "The Reports Menu is a collection of tools to help you analyze and understand the data you are viewing."}, "PolarPlotPlugin": {"bottomIconLabel": "Polar Plot", "title": "Polar Plot Menu", "helpBody": "The Polar Plot Menu is used to generate a 2D polar plot of the satellite's azimuth and elevation over time."}, "NextLaunchesPlugin": {"bottomIconLabel": "Next Launches", "title": "Next Launches Menu", "helpBody": "The Next Launches Menu pulls data from <a href=\"https://thespacedevs.com/\" target=\"_blank\">The Space Devs</a> to display upcoming launches."}, "FindSatPlugin": {"bottomIconLabel": "Find Satellite", "title": "Find Satellite Menu", "helpBody": "The Find Satellite Menu is used for finding satellites by orbital parameters or satellite characteristics. <br><br> For most parameters, you type in the target value on the left and then a margin of error on the right. For example, if you wanted to find all satellites in a 51-52 degree inclination, you can type 51.5 in the left box and 0.5 in the right box. The search will then find all satellites within those inclinations and display them in the search bar."}, "ShortTermFences": {"bottomIconLabel": "Short Term Fence", "title": "Short Term Fences (STF) Menu", "helpBody": "The Short Term Fences (STF) Menu is used for visualizing sensor search boxes. <br><br> This is unlikely to be very helpful unless you own/operate a sensor with a search box functionality."}, "Collisions": {"bottomIconLabel": "Collisions", "title": "Collisions Menu", "helpBody": "The Collisions Menu shows satellites with a high probability of collision. <br><br> Clicking on a row will select the two satellites involved in the collision and change the time to the time of the collision."}, "TrackingImpactPredict": {"bottomIconLabel": "Reentry Prediction", "title": "Tracking and Impact Prediction Menu", "helpBody": "The Tracking and Impact Prediction (TIP) menu displays the latest tracking and impact prediction messages for satellites. The table shows the following columns:<br><br> <b>NORAD</b>: The NORAD catalog ID of the satellite.<br><br> <b>Decay Date</b>: The date of the predicted decay of the satellite.<br><br> <b>Latitude</b>: The latitude of the satellite at decay time.<br><br> <b>Longitude</b>: The longitude of the satellite at decay time.<br><br> <b>Window (min)</b>: The time window in minutes for the prediction.<br><br> <b>Next Report (hrs)</b>: The time in hours until the next report.<br><br> <b>Reentry Angle (deg)</b>: The reentry angle of the satellite in degrees.<br><br> <b>RCS (m²)</b>: The Radar Cross Section of the satellite in square meters.<br><br> <b>GP Age (hrs)</b>: The age of the latest element set in hours.<br><br> <b>Dry Mass (kg)</b>: The dry mass of the satellite in kilograms.<br><br> <b>Volume (m³)</b>: The estimated volume of the satellite in cubic meters.<br><br>"}, "Breakup": {"bottomIconLabel": "Create Breakup", "title": "Breakup <PERSON>", "helpBody": "The Breakup Menu is a tool for simulating the breakup of a satellite. <br><br> By modifying duplicating and modifying a satellite's orbit we can model the breakup of a satellite. After selecting a satellite and opening the menu, the user can select: <ul style=\"margin-left: 40px;\"> <li>Inclination Variation</li> <li>RAAN Variation</li> <li>Period Variation</li> <li>Number of Breakup Pieces</li> </ul> The larger the variation the bigger the spread in the simulated breakup. The default variations are sufficient to simulate a breakup with a reasonable spread."}, "DebrisScreening": {"bottomIconLabel": "Debris Screening", "title": "Debris Screening Menu", "helpBody": "The Debris Screening menu is used to generate a list of debris objects that could potentially be seen by a satellite. The list is generated by calculating the orbital parameters of the debris objects and comparing them to the orbital parameters of the satellite. The user can choose to generate the list using either the TLE or the SGP4 propagator. The user can also choose to filter the list by the debris object's size and the debris object's magnitude. The user can also choose to filter the list by the debris object's size and the debris object's magnitude. The user can also choose to generate the list using either the TLE or the SGP4 propagator. The user can also choose to filter the list by the debris object's size and the debris object's magnitude."}, "TransponderChannelData": {"bottomIconLabel": "Transponder and Channel Info", "title": "Satellite Transponder and Channel Info", "helpBody": "<p>This table contains technical details for satellite channels, including TV, radio, and other communication services:</p><ul><li><strong>satellite:</strong> Name of the satellite broadcasting the channel</li><li><strong>tvchannel:</strong> Name of the TV/radio channel or communication service</li><li><strong>beam:</strong> Satellite beam direction (e.g., West hemi)</li><li><strong>freq:</strong> Transponder frequency and polarity (e.g., 3840 V)</li><li><strong>system:</strong> Broadcasting system (e.g., DVB-S2 8PSK)</li><li><strong>SRFEC:</strong> Symbol rate and FEC ratio (e.g., 30000 3/4)</li><li><strong>video:</strong> Video/data compression format (e.g., MPEG-4/HD)</li><li><strong>lang:</strong> Available audio/communication languages (e.g., Eng, Chi)</li><li><strong>encryption:</strong> Encryption system used (e.g., PowerVu)</li></ul><p>This information is useful for satellite communication professionals, technicians, enthusiasts, and anyone setting up or troubleshooting satellite reception equipment.</p>"}, "EditSat": {"bottomIconLabel": "Edit Satellite", "title": "Edit Satellite Menu", "helpBody": "The Edit Satellite Menu is used to edit the satellite data. <br><br> <ul> <li> Satellite SCC# - A unique number assigned to each satellite by the US Space Force. </li> <li> Epoch Year - The year of the satellite's last orbital update. </li> <li> Epoch Day - The day of the year of the satellite's last orbital update. </li> <li> Inclination - The angle between the satellite's orbital plane and the equatorial plane. </li> <li> Right Ascension - The angle between the ascending node and the satellite's position at the time of the last orbital update. </li> <li> Eccentricity - The amount by which the satellite's orbit deviates from a perfect circle. </li> <li> Argument of Perigee - The angle between the ascending node and the satellite's closest point to the earth. </li> <li> Mean Anomaly - The angle between the satellite's position at the time of the last orbital update and the satellite's closest point to the earth. </li> <li> Mean Motion - The rate at which the satellite's mean anomaly changes. </li> </ul>"}, "NewLaunch": {"bottomIconLabel": "New Launch", "title": "New Launch Menu", "helpBody": "The New Launch Menu is used for generating notional orbital launches by modifying existing satellites with similar parameters. <br><br> After selecting a satellite, you can select a launch location and a north/south azimuth. The selected satellite will be modified to align it with the launch site. The clock is then changed to 00:00:00 to represent relative time after the launch. This can be helpful in calculating sensor coverage relative to launch time. The objects relationship with other orbital objects will be incorrect."}, "MissilePlugin": {"bottomIconLabel": "Missile", "title": "Missile Menu", "helpBody": "The Missile Menu is used for generating notional missile launches between countries. <br><br> When you using submarine launched missiles, the launch point is a custom latitude and longitude. When you are using land based missiles, the launch point is a fix latitude and longitude based on open source reporting. <br><br> In addition to custom missiles, a few predefined scenarios involving hundreds of missiles are available. <br><br> All missile launches are notional and are not intended to represent real world events. The launch trajectories are all based on the same ballistic model, but use different minimum and maximum ranges."}, "StereoMap": {"bottomIconLabel": "Stereo Map", "title": "Stereographic Map Menu", "helpBody": "The Stereographic Map Menu is used for visualizing satellite ground traces in a stereographic projection. <br/><br/> You can click on a spot along the ground trace to change the time of the simulation to when the satellite reaches that spot. <br/><br/> The yellow dots indicate when the satellite is in view of the sensor. The red dots indicate when the satellite is not in view of the sensor. The dot closest to the satellite is the current time."}, "SensorFov": {"bottomIconLabel": "Sensor FOV"}, "SensorSurvFence": {"bottomIconLabel": "<PERSON><PERSON>"}, "SatelliteViewPlugin": {"bottomIconLabel": "Satellite View"}, "SatelliteFov": {"bottomIconLabel": "Satellite FOV", "title": "Satellite Field of View Menu", "helpBody": "The Satellite Field of View plugin allows you to control the field of view of a satellite."}, "Planetarium": {"bottomIconLabel": "Planetarium View"}, "NightToggle": {"bottomIconLabel": "Night Toggle"}, "SatConstellations": {"bottomIconLabel": "Constellations", "title": "Constellations Menu", "helpBody": "The Constellations menu allows you to view groups of satellites. <br><br> For some constellations, notional uplink/downlinks and/or crosslinks will be drawn between satellites in the constellation."}, "CountriesMenu": {"bottomIconLabel": "Countries", "title": "Countries Menu", "helpBody": "The Countries Menu allows you to filter the satellites by country of origin."}, "ColorMenu": {"bottomIconLabel": "Color Schemes", "title": "Color Scheme Menu", "helpBody": "The Colors Menu is a place to change the color theme used to render the objects. <br><br> The various themes can change the colors based on the objects' orbits, objects' characteristics, or the objects' relation to sun and/or earth."}, "Screenshot": {"bottomIconLabel": "Take Photo"}, "LaunchCalendar": {"bottomIconLabel": "Launch Calendar"}, "TimeMachine": {"bottomIconLabel": "Time Machine"}, "SatellitePhotos": {"bottomIconLabel": "Satellite Photos", "title": "Satellite Photos Menu", "helpBody": "The Satellite Photos Menu is used for displaying live photos from select satellites. <br><br> Note - changes in the image API may cause the wrong satellite to be selected in KeepTrack."}, "ScreenRecorder": {"bottomIconLabel": "Record Video"}, "Astronomy": {"bottomIconLabel": "Astronomy"}, "Calculator": {"bottomIconLabel": "Reference Frame Transforms", "title": "Reference Frame Transforms Menu", "helpBody": "The Reference Frame Transforms Menu is used to convert between different reference frames. <br><br> The menu allows you to convert between the following reference frames: <ul style=\"margin-left: 40px;\"> <li> ECI - Earth Centered Inertial </li> <li> ECEF - Earth Centered Earth Fixed </li> <li> Geodetic </li> <li> Topocentric </li> </ul>"}, "AnalysisMenu": {"bottomIconLabel": "Analysis", "title": "Analysis Menu", "helpBody": "The Analysis Menu provides a number of tools to help you analyze the data in the current view. The tools are: <ul style=\"margin-left: 40px;\"> <li>Export Official TLEs - Export real two line element sets.</li> <li>Export 3LES - Export three line element sets.</li> <li>Export KeepTrack TLEs - Export All KeepTrack two line element sets including analysts.</li> <li>Export KeepTrack 3LES - Export All KeepTrack three line element sets including analysts.</li> <li>Find Close Objects - Find objects that are close to each other.</li> <li>Find Reentries - Find objects that are likely to reenter the atmosphere.</li> <li>Best Passes - Find the best passes for a satellite based on the currently selected sensor.</li> </ul>"}, "SettingsMenuPlugin": {"bottomIconLabel": "Settings", "title": "<PERSON><PERSON><PERSON>", "helpBody": "The Settings menu allows you to configure the application."}, "VideoDirectorPlugin": {"bottomIconLabel": "Video Director", "title": "Video Director <PERSON><PERSON>", "helpBody": "The Video Director <PERSON><PERSON> is used to manipulate the camera and objects in the scene to create a video."}, "CreateSat": {"bottomIconLabel": "Create Satellite", "title": "Create Satellite", "helpBody": "The Create Satellite menu is used to create a satellite from its keplerian elements"}, "DopsPlugin": {"bottomIconLabel": "View DOPs", "title": "Dilution of Precision Menu", "helpBody": "The Dilution of Precision (DOP) menu is used to calculate the DOP values for a satellite and sensor. <br><br> The DOP values are: <ul style=\"margin-left: 40px;\"> <li> GDOP - Geometric Dilution of Precision </li> <li> PDOP - Position Dilution of Precision </li> <li> HDOP - Horizontal Dilution of Precision </li> <li> VDOP - Vertical Dilution of Precision </li> <li> TDOP - Time Dilution of Precision </li> <li> NDOP - Number of Satellites Dilution of Precision </li> </ul>"}, "EciPlot": {"bottomIconLabel": "ECI Plot", "title": "ECI Plot Menu", "helpBody": "The ECI Plot Menu is used to plot the satellite's position in the Earth Centered Inertial (ECI) reference frame."}, "EcfPlot": {"bottomIconLabel": "ECF Plot", "title": "ECF Plot Menu", "helpBody": "The ECF Plot Menu is used to plot the satellite's position in the Earth Centered Earth Fixed (ECF) reference frame."}, "RicPlot": {"bottomIconLabel": "RIC Plot", "title": "RIC Plot Menu", "helpBody": "The RIC Plot Menu is used to plot the satellite's position in the Radial, In-Track, and Cross-Track (RIC) reference frame."}, "Time2LonPlots": {"bottomIconLabel": "Waterfall Plot", "title": "Waterfall Menu", "helpBody": "The Time to Longitude (Waterfall) Plot Menu is used to plot the satellite's longitude over time."}, "Lat2LonPlots": {"bottomIconLabel": "<PERSON><PERSON> vs Lon Plot", "title": "Latitude to Longitude Plot Menu", "helpBody": "The Latitude vs Longitude Plot Menu is used for plotting the latitude vs longitude in the GEO belt."}, "Inc2AltPlots": {"bottomIconLabel": "Inc vs Alt Plot", "title": "Inclination to Altitude Plot Menu", "helpBody": "The Inclination to Altitude Plot Menu is used to plot the satellite's inclination against its altitude."}, "Inc2LonPlots": {"bottomIconLabel": "Inc Vs Lon Plot", "title": "Inclination to Longitude Plot Menu", "helpBody": "The Inc Vs Lon Plot Menu is used for plotting the inclination vs longitude in the GEO belt."}, "GraphicsMenuPlugin": {"bottomIconLabel": "Graphics Menu", "title": "Graphics Menu", "helpBody": "The Graphics Menu is used to change the graphics settings of the application."}}}