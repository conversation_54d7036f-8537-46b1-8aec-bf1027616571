# 服务器部署指南

## 项目路径

**服务器部署路径**: `/home/<USER>/keeptrack.space/`

## 快速启动

### 1. 基础启动（推荐用于开发/测试）

```bash
# 切换到项目目录
cd /home/<USER>/keeptrack.space/

# 安装依赖
npm install

# 启动后端API服务
npm run start:api
```

**服务地址**: http://localhost:5001

### 2. 生产环境启动（推荐用于服务器）

```bash
# 使用部署脚本
chmod +x server-deploy.sh
./server-deploy.sh
```

或手动启动：

```bash
# 切换到项目目录
cd /home/<USER>/keeptrack.space/

# 安装PM2（推荐用于生产环境）
npm install -g pm2

# 使用PM2启动后端服务
pm2 start "npm run start:api" --name "keeptrack-api" --time

# 查看服务状态
pm2 status

# 查看日志
pm2 logs keeptrack-api

# 重启服务
pm2 restart keeptrack-api

# 停止服务
pm2 stop keeptrack-api
```

## 环境要求

- **Node.js**: 16.x 或更高版本
- **npm**: 8.x 或更高版本
- **内存**: 至少 512MB
- **端口**: 5001（可修改）
- **项目路径**: `/home/<USER>/keeptrack.space/`

## 配置文件

### 必需文件
- `es-config.enc` - AES-256-GCM加密的ES配置文件
- `config.json` - 原始ES配置（用于生成加密文件）

### 可选环境变量
```bash
# 设置加密密钥（增强安全性）
export ES_ENCRYPTION_KEY='your-secret-key-32-chars-long!!'
```

## API端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | API服务信息 |
| `/api/health` | GET | 健康检查 |
| `/api/es-history` | GET | ES历史轨道数据代理 |

### ES历史数据API使用示例

```bash
# 查询单个卫星
curl "http://localhost:5001/api/es-history?norad_id=25544&start=2025-01-01&end=2025-07-01"

# 查询多个卫星
curl "http://localhost:5001/api/es-history?norad_id=25544&norad_id=43013&start=2025-01-01&end=2025-07-01"
```

## 故障排除

### 1. 项目路径问题
```bash
# 检查项目路径
ls -la /home/<USER>/keeptrack.space/

# 如果路径不存在，创建目录
sudo mkdir -p /home/<USER>/keeptrack.space/
sudo chown anonymous:anonymous /home/<USER>/keeptrack.space/
```

### 2. 端口被占用
```bash
# 查看端口占用
lsof -i :3001

# 杀死进程
kill -9 <PID>
```

### 3. ES配置文件错误
```bash
# 切换到项目目录
cd /home/<USER>/keeptrack.space/

# 重新生成配置文件
node create-secure-config.js
```

### 4. 权限问题
```bash
# 确保脚本可执行
chmod +x server-deploy.sh

# 确保文件权限正确
chmod 600 es-config.enc

# 确保目录权限
sudo chown -R anonymous:anonymous /home/<USER>/keeptrack.space/
```

### 5. PM2相关问题
```bash
# 清理PM2进程
pm2 delete keeptrack-api

# 重新启动
pm2 start "npm run start:api" --name "keeptrack-api"
```

## 安全建议

1. **防火墙配置**: 只开放必要端口（3001）
2. **HTTPS**: 生产环境建议使用HTTPS
3. **加密密钥**: 设置环境变量 `ES_ENCRYPTION_KEY`
4. **文件权限**: 确保 `es-config.enc` 只有服务用户可读
5. **日志监控**: 定期检查服务日志
6. **目录权限**: 确保项目目录权限正确

## 监控和维护

### 查看服务状态
```bash
# PM2状态
pm2 status

# 系统资源
pm2 monit

# 日志
pm2 logs keeptrack-api --lines 100
```

### 自动重启
```bash
# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

## 更新部署

```bash
# 1. 停止服务
pm2 stop keeptrack-api

# 2. 切换到项目目录
cd /home/<USER>/keeptrack.space/

# 3. 更新代码
git pull

# 4. 安装依赖
npm install

# 5. 重启服务
pm2 restart keeptrack-api
```

## 部署检查清单

- [ ] 项目路径存在: `/home/<USER>/keeptrack.space/`
- [ ] Node.js版本 >= 16.x
- [ ] npm版本 >= 8.x
- [ ] 端口3001可用
- [ ] ES配置文件存在: `es-config.enc`
- [ ] 项目依赖已安装
- [ ] 文件权限正确
- [ ] PM2已安装（可选） 