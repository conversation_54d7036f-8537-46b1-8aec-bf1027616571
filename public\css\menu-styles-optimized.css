/*
 * 优化的菜单样式系统
 * 替代 ultimate-menu-fix.css，提供精简高效的菜单样式
 * 创建时间: 2025-01-16
 * 目标: 统一所有菜单样式，减少代码重复
 */

/* ========================================
 * 1. CSS变量定义 - 🔥 正确的缩放变量设置
 * ======================================== */
:root {
  /* 🔥 缩放因子 - 提供默认值，JavaScript会动态更新 */
  --system-scale-factor: 1;

  /* 🔥 统一乘法缩放系统：1=不缩放，0.3=30%大小 */
  --base-font-size: calc(20px * var(--system-scale-factor, 1));
  --large-font-size: calc(24px * var(--system-scale-factor, 1));
  --xlarge-font-size: calc(28px * var(--system-scale-factor, 1));
  --icon-size: calc(32px * var(--system-scale-factor, 1));

  /* 菜单相关变量 - 统一乘法缩放 */
  --menu-label-font-size: var(--base-font-size);
  --menu-input-height: calc(44px * var(--system-scale-factor, 1));
  --menu-spacing: calc(12px * var(--system-scale-factor, 1));
  --menu-label-color: #b3e5fc;
  --menu-input-color: white;
  --menu-border-color: rgba(255, 255, 255, 0.8); /* 🔥 进一步增强白色底线可见度 */

  /* 底部菜单变量 - 统一乘法缩放 */
  --bottom-icon-width: calc(115px * var(--system-scale-factor, 1));
  --bottom-filter-width: calc(185px * var(--system-scale-factor, 1));
  --bottom-menu-height: calc(120px * var(--system-scale-factor, 1));
}

/* ========================================
 * 2. 通用菜单容器样式
 * ======================================== */
[id$="-menu"]:not(#sat-infobox),
.side-menu-parent:not(#sat-infobox) {
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

/* ========================================
 * 3. 输入字段统一样式
 * ======================================== */

/* 输入字段容器 - 增加顶部间距避免重叠 */
[id$="-menu"]:not(#sat-infobox) .input-field,
.side-menu-parent:not(#sat-infobox) .input-field {
  position: relative !important;
  margin-bottom: calc(20px / var(--system-scale-factor)) !important;
  padding-top: calc(25px / var(--system-scale-factor)) !important;
  min-height: calc(60px / var(--system-scale-factor)) !important;
}

/* 标签样式已移至responsive-design.css统一管理 */
html body [id$="-menu"]:not(#sat-infobox) .input-field label:not(.switch label):not(.lever),
html body .side-menu-parent:not(#sat-infobox) .input-field label:not(.switch label):not(.lever),
html body .side-menu .input-field label:not(.switch label):not(.lever),
html body #settings-menu .input-field label:not(.switch label):not(.lever) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  font-weight: 400 !important;
  color: var(--menu-label-color) !important;
  pointer-events: none !important;
  transition: transform 0.2s ease-out, color 0.2s ease-out !important;
  transform-origin: 0% 100% !important;
  z-index: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
  background: transparent !important;
  transform: translateY(calc(12px / var(--system-scale-factor))) !important;
  cursor: text !important;
  line-height: 1.2 !important;
}

/* 输入框统一样式 */
[id$="-menu"]:not(#sat-infobox) .input-field input[type="text"],
[id$="-menu"]:not(#sat-infobox) .input-field input[type="number"],
[id$="-menu"]:not(#sat-infobox) .input-field input[type="email"],
[id$="-menu"]:not(#sat-infobox) .input-field input[type="password"],
.side-menu-parent:not(#sat-infobox) .input-field input[type="text"],
.side-menu-parent:not(#sat-infobox) .input-field input[type="number"],
.side-menu-parent:not(#sat-infobox) .input-field input[type="email"],
.side-menu-parent:not(#sat-infobox) .input-field input[type="password"] {
  height: var(--menu-input-height) !important;
  margin-top: calc(3px / var(--system-scale-factor)) !important;
  padding: calc(4px / var(--system-scale-factor)) 0 !important;
  font-size: var(--menu-label-font-size) !important;
  line-height: 1.4 !important;
  border: none !important;
  border-bottom: 1px solid var(--menu-border-color) !important;
  background: transparent !important;
  color: var(--menu-input-color) !important;
  box-shadow: none !important;
}

/* 🔥🔥🔥 侧边菜单所有输入框统一白色底线 - 包括不在input-field容器内的 🔥🔥🔥 */
/* 🔥 使用超高优先级选择器覆盖transparency-unified.css的border: none规则 */
html body div .side-menu input[type="text"],
html body div .side-menu input[type="number"],
html body div .side-menu input[type="email"],
html body div .side-menu input[type="password"],
html body div .side-menu input[type="date"],
html body div .side-menu input[type="time"],
html body div .side-menu input[type="datetime-local"],
html body div .side-menu input[type="search"],
html body div .side-menu input[type="url"],
html body div .side-menu input[type="tel"],
html body div [id$="-menu"] input[type="text"],
html body div [id$="-menu"] input[type="number"],
html body div [id$="-menu"] input[type="email"],
html body div [id$="-menu"] input[type="password"],
html body div [id$="-menu"] input[type="date"],
html body div [id$="-menu"] input[type="time"],
html body div [id$="-menu"] input[type="datetime-local"],
html body div [id$="-menu"] input[type="search"],
html body div [id$="-menu"] input[type="url"],
html body div [id$="-menu"] input[type="tel"],
html body div .side-menu-parent input[type="text"],
html body div .side-menu-parent input[type="number"],
html body div .side-menu-parent input[type="email"],
html body div .side-menu-parent input[type="password"],
html body div .side-menu-parent input[type="date"],
html body div .side-menu-parent input[type="time"],
html body div .side-menu-parent input[type="datetime-local"],
html body div .side-menu-parent input[type="search"],
html body div .side-menu-parent input[type="url"],
html body div .side-menu-parent input[type="tel"],
html body .side-menu input[type="text"],
html body .side-menu input[type="number"],
html body .side-menu input[type="email"],
html body .side-menu input[type="password"],
html body .side-menu input[type="date"],
html body .side-menu input[type="time"],
html body .side-menu input[type="datetime-local"],
html body .side-menu input[type="search"],
html body .side-menu input[type="url"],
html body .side-menu input[type="tel"],
html body [id$="-menu"] input[type="text"],
html body [id$="-menu"] input[type="number"],
html body [id$="-menu"] input[type="email"],
html body [id$="-menu"] input[type="password"],
html body [id$="-menu"] input[type="date"],
html body [id$="-menu"] input[type="time"],
html body [id$="-menu"] input[type="datetime-local"],
html body [id$="-menu"] input[type="search"],
html body [id$="-menu"] input[type="url"],
html body [id$="-menu"] input[type="tel"],
html body .side-menu-parent input[type="text"],
html body .side-menu-parent input[type="number"],
html body .side-menu-parent input[type="email"],
html body .side-menu-parent input[type="password"],
html body .side-menu-parent input[type="date"],
html body .side-menu-parent input[type="time"],
html body .side-menu-parent input[type="datetime-local"],
html body .side-menu-parent input[type="search"],
html body .side-menu-parent input[type="url"],
html body .side-menu-parent input[type="tel"],
.side-menu input[type="text"],
.side-menu input[type="number"],
.side-menu input[type="email"],
.side-menu input[type="password"],
.side-menu input[type="date"],
.side-menu input[type="time"],
.side-menu input[type="datetime-local"],
.side-menu input[type="search"],
.side-menu input[type="url"],
.side-menu input[type="tel"],
[id$="-menu"] input[type="text"],
[id$="-menu"] input[type="number"],
[id$="-menu"] input[type="email"],
[id$="-menu"] input[type="password"],
[id$="-menu"] input[type="date"],
[id$="-menu"] input[type="time"],
[id$="-menu"] input[type="datetime-local"],
[id$="-menu"] input[type="search"],
[id$="-menu"] input[type="url"],
[id$="-menu"] input[type="tel"],
.side-menu-parent input[type="text"],
.side-menu-parent input[type="number"],
.side-menu-parent input[type="email"],
.side-menu-parent input[type="password"],
.side-menu-parent input[type="date"],
.side-menu-parent input[type="time"],
.side-menu-parent input[type="datetime-local"],
.side-menu-parent input[type="search"],
.side-menu-parent input[type="url"],
.side-menu-parent input[type="tel"] {
  border: none !important;
  border-bottom: 1px solid var(--menu-border-color) !important; /* 🔥 统一白色底线 */
  background: transparent !important;
  color: white !important;
  outline: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}



/* 🔥 输入框聚焦时底线更亮 - 使用超高优先级选择器 */
html body div .side-menu input[type="text"]:focus,
html body div .side-menu input[type="number"]:focus,
html body div .side-menu input[type="email"]:focus,
html body div .side-menu input[type="password"]:focus,
html body div .side-menu input[type="date"]:focus,
html body div .side-menu input[type="time"]:focus,
html body div .side-menu input[type="datetime-local"]:focus,
html body div .side-menu input[type="search"]:focus,
html body div .side-menu input[type="url"]:focus,
html body div .side-menu input[type="tel"]:focus,
html body div [id$="-menu"] input[type="text"]:focus,
html body div [id$="-menu"] input[type="number"]:focus,
html body div [id$="-menu"] input[type="email"]:focus,
html body div [id$="-menu"] input[type="password"]:focus,
html body div [id$="-menu"] input[type="date"]:focus,
html body div [id$="-menu"] input[type="time"]:focus,
html body div [id$="-menu"] input[type="datetime-local"]:focus,
html body div [id$="-menu"] input[type="search"]:focus,
html body div [id$="-menu"] input[type="url"]:focus,
html body div [id$="-menu"] input[type="tel"]:focus,
html body div .side-menu-parent input[type="text"]:focus,
html body div .side-menu-parent input[type="number"]:focus,
html body div .side-menu-parent input[type="email"]:focus,
html body div .side-menu-parent input[type="password"]:focus,
html body div .side-menu-parent input[type="date"]:focus,
html body div .side-menu-parent input[type="time"]:focus,
html body div .side-menu-parent input[type="datetime-local"]:focus,
html body div .side-menu-parent input[type="search"]:focus,
html body div .side-menu-parent input[type="url"]:focus,
html body div .side-menu-parent input[type="tel"]:focus,
html body .side-menu input[type="text"]:focus,
html body .side-menu input[type="number"]:focus,
html body .side-menu input[type="email"]:focus,
html body .side-menu input[type="password"]:focus,
html body .side-menu input[type="date"]:focus,
html body .side-menu input[type="time"]:focus,
html body .side-menu input[type="datetime-local"]:focus,
html body .side-menu input[type="search"]:focus,
html body .side-menu input[type="url"]:focus,
html body .side-menu input[type="tel"]:focus,
html body [id$="-menu"] input[type="text"]:focus,
html body [id$="-menu"] input[type="number"]:focus,
html body [id$="-menu"] input[type="email"]:focus,
html body [id$="-menu"] input[type="password"]:focus,
html body [id$="-menu"] input[type="date"]:focus,
html body [id$="-menu"] input[type="time"]:focus,
html body [id$="-menu"] input[type="datetime-local"]:focus,
html body [id$="-menu"] input[type="search"]:focus,
html body [id$="-menu"] input[type="url"]:focus,
html body [id$="-menu"] input[type="tel"]:focus,
html body .side-menu-parent input[type="text"]:focus,
html body .side-menu-parent input[type="number"]:focus,
html body .side-menu-parent input[type="email"]:focus,
html body .side-menu-parent input[type="password"]:focus,
html body .side-menu-parent input[type="date"]:focus,
html body .side-menu-parent input[type="time"]:focus,
html body .side-menu-parent input[type="datetime-local"]:focus,
html body .side-menu-parent input[type="search"]:focus,
html body .side-menu-parent input[type="url"]:focus,
html body .side-menu-parent input[type="tel"]:focus,
.side-menu input[type="text"]:focus,
.side-menu input[type="number"]:focus,
.side-menu input[type="email"]:focus,
.side-menu input[type="password"]:focus,
.side-menu input[type="date"]:focus,
.side-menu input[type="time"]:focus,
.side-menu input[type="datetime-local"]:focus,
.side-menu input[type="search"]:focus,
.side-menu input[type="url"]:focus,
.side-menu input[type="tel"]:focus,
[id$="-menu"] input[type="text"]:focus,
[id$="-menu"] input[type="number"]:focus,
[id$="-menu"] input[type="email"]:focus,
[id$="-menu"] input[type="password"]:focus,
[id$="-menu"] input[type="date"]:focus,
[id$="-menu"] input[type="time"]:focus,
[id$="-menu"] input[type="datetime-local"]:focus,
[id$="-menu"] input[type="search"]:focus,
[id$="-menu"] input[type="url"]:focus,
[id$="-menu"] input[type="tel"]:focus,
.side-menu-parent input[type="text"]:focus,
.side-menu-parent input[type="number"]:focus,
.side-menu-parent input[type="email"]:focus,
.side-menu-parent input[type="password"]:focus,
.side-menu-parent input[type="date"]:focus,
.side-menu-parent input[type="time"]:focus,
.side-menu-parent input[type="datetime-local"]:focus,
.side-menu-parent input[type="search"]:focus,
.side-menu-parent input[type="url"]:focus,
.side-menu-parent input[type="tel"]:focus {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.9) !important; /* 🔥 聚焦时更亮的白色底线 */
  background: transparent !important;
  color: white !important;
  outline: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* 选择框样式已移至responsive-design.css统一管理 */

/* 选择框标签特殊处理 - 修复重叠问题 */
[id$="-menu"]:not(#sat-infobox) .input-field .select-wrapper + label,
[id$="-menu"]:not(#sat-infobox) .input-field .select-wrapper ~ label,
.side-menu-parent:not(#sat-infobox) .input-field .select-wrapper + label,
.side-menu-parent:not(#sat-infobox) .input-field .select-wrapper ~ label {
  transform: translateY(calc(-20px / var(--system-scale-factor))) scale(0.8) !important;
  transform-origin: 0 0 !important;
  color: var(--menu-label-color) !important;
  top: calc(-5px / var(--system-scale-factor)) !important;
  z-index: 2 !important;
}

/* ========================================
 * 4. 激活状态样式 - 标签移到输入框上方
 * ======================================== */
[id$="-menu"]:not(#sat-infobox) .input-field label.active,
[id$="-menu"]:not(#sat-infobox) .input-field input:focus + label,
[id$="-menu"]:not(#sat-infobox) .input-field input:not(:placeholder-shown) + label,
[id$="-menu"]:not(#sat-infobox) .input-field input[type]:not(:placeholder-shown) + label,
.side-menu-parent:not(#sat-infobox) .input-field label.active,
.side-menu-parent:not(#sat-infobox) .input-field input:focus + label,
.side-menu-parent:not(#sat-infobox) .input-field input:not(:placeholder-shown) + label,
.side-menu-parent:not(#sat-infobox) .input-field input[type]:not(:placeholder-shown) + label {
  transform: translateY(calc(-14px / var(--system-scale-factor))) scale(0.8) !important;
  transform-origin: 0 0 !important;
  color: var(--menu-label-color) !important;
}

/* ========================================
 * 5. 开关样式
 * ======================================== */
.switch {
  margin: calc(15px / var(--system-scale-factor)) 0 !important;
  padding: calc(5px / var(--system-scale-factor)) 0 !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
}

.switch label {
  font-size: var(--menu-label-font-size) !important;
  color: var(--menu-label-color) !important;
  display: flex !important;
  align-items: center !important;
  cursor: pointer !important;
  line-height: 1.4 !important;
  position: relative !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
  padding: 0 !important;
  margin: 0 !important;
  pointer-events: auto !important;
}

/* 🔥🔥🔥 开关按钮未激活状态 - 超强覆盖Materialize默认样式 🔥🔥🔥 */
html body div .side-menu .switch label .lever,
html body div [id$="-menu"] .switch label .lever,
html body div .switch label .lever,
html body .side-menu .switch label .lever,
html body [id$="-menu"] .switch label .lever,
html body .switch label .lever,
.side-menu .switch label .lever,
[id$="-menu"] .switch label .lever,
.switch label .lever {
  margin: 0 calc(15px / var(--system-scale-factor)) !important;
  vertical-align: middle !important;
  flex-shrink: 0 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
  /* 🔥 未激活状态使用灰色背景 */
  background-color: rgba(255, 255, 255, 0.3) !important;
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: calc(15px / var(--system-division-factor, 1)) !important;
  border: none !important;
  /* 🔥 使用除法因子的缩放尺寸 */
  width: calc(40px / var(--system-division-factor, 1)) !important;
  height: calc(15px / var(--system-division-factor, 1)) !important;
}

/* 🔥🔥🔥 开关按钮激活状态 - 超强覆盖Materialize默认样式 🔥🔥🔥 */
html body div .side-menu .switch label input[type=checkbox]:checked + .lever,
html body div [id$="-menu"] .switch label input[type=checkbox]:checked + .lever,
html body div .switch label input[type=checkbox]:checked + .lever,
html body .side-menu .switch label input[type=checkbox]:checked + .lever,
html body [id$="-menu"] .switch label input[type=checkbox]:checked + .lever,
html body .switch label input[type=checkbox]:checked + .lever,
.side-menu .switch label input[type=checkbox]:checked + .lever,
[id$="-menu"] .switch label input[type=checkbox]:checked + .lever,
.switch label input[type=checkbox]:checked + .lever {
  background-color: #2196f3 !important; /* 蓝色背景 */
  background: #2196f3 !important;
}

/* 🔥 开关按钮滑块 - 覆盖Materialize默认样式 */
.side-menu .switch label .lever:after,
[id$="-menu"] .switch label .lever:after,
.switch label .lever:after {
  background-color: white !important;
  border: none !important;
  border-radius: 50% !important;
  width: calc(21px / var(--system-scale-factor)) !important;
  height: calc(21px / var(--system-scale-factor)) !important;
  top: calc(-3px / var(--system-scale-factor)) !important;
  left: calc(-5px / var(--system-scale-factor)) !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
}

/* 🔥🔥🔥 强制移除开关按钮的:before伪元素外框 - 超高优先级 🔥🔥🔥 */
html body div .side-menu .switch label .lever:before,
html body div [id$="-menu"] .switch label .lever:before,
html body div .switch label .lever:before,
html body .side-menu .switch label .lever:before,
html body [id$="-menu"] .switch label .lever:before,
html body .switch label .lever:before,
.side-menu .switch label .lever:before,
[id$="-menu"] .switch label .lever:before,
.switch label .lever:before {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
}

/* 🔥🔥🔥 强制覆盖AstroUX和其他可能的边框样式 - 终极优先级 🔥🔥🔥 */
html body div#settings-menu .switch,
html body div#settings-menu .switch *,
html body div#settings-menu .switch *:before,
html body div#settings-menu .switch *:after,
html body #settings-menu .switch,
html body #settings-menu .switch *,
html body #settings-menu .switch *:before,
html body #settings-menu .switch *:after,
#settings-menu .switch,
#settings-menu .switch *,
#settings-menu .switch *:before,
#settings-menu .switch *:after {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 🔥🔥🔥 特别针对设置菜单的开关滑块外框移除 🔥🔥🔥 */
html body div#settings-menu .switch label .lever:before,
html body #settings-menu .switch label .lever:before,
#settings-menu .switch label .lever:before {
  content: none !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  width: 0 !important;
  height: 0 !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
}

/* 🔥 开关按钮激活状态滑块位置 */
.side-menu .switch label input[type=checkbox]:checked + .lever:after,
[id$="-menu"] .switch label input[type=checkbox]:checked + .lever:after,
.switch label input[type=checkbox]:checked + .lever:after {
  left: calc(24px / var(--system-scale-factor)) !important;
}

/* 🔥🔥🔥 新的按钮分类系统 🔥🔥🔥 */

/* 🔥🔥🔥 1. 透明图标按钮 - 用于菜单顶部的设置、下载、关闭、列表等图标按钮 - 超高优先级 🔥🔥🔥 */
html[lang] body .icon-btn,
html[lang] body [id$="-settings-btn"],
html[lang] body [id$="-close-btn"],
html[lang] body [id$="-download-btn"],
html[lang] body [id$="-list-btn"],
html[lang] body .side-menu .icon-btn,
html[lang] body .side-menu [id$="-settings-btn"],
html[lang] body .side-menu [id$="-close-btn"],
html[lang] body .side-menu [id$="-download-btn"],
html[lang] body .side-menu [id$="-list-btn"],
html[lang] body [id$="-menu"] .icon-btn,
html[lang] body [id$="-menu"] [id$="-settings-btn"],
html[lang] body [id$="-menu"] [id$="-close-btn"],
html[lang] body [id$="-menu"] [id$="-download-btn"],
html[lang] body [id$="-menu"] [id$="-list-btn"],
html body .icon-btn,
html body [id$="-settings-btn"],
html body [id$="-close-btn"],
html body [id$="-download-btn"],
html body [id$="-list-btn"],
html body .side-menu .icon-btn,
html body .side-menu [id$="-settings-btn"],
html body .side-menu [id$="-close-btn"],
html body .side-menu [id$="-download-btn"],
html body .side-menu [id$="-list-btn"],
html body [id$="-menu"] .icon-btn,
html body [id$="-menu"] [id$="-settings-btn"],
html body [id$="-menu"] [id$="-close-btn"],
html body [id$="-menu"] [id$="-download-btn"],
html body [id$="-menu"] [id$="-list-btn"],
.icon-btn,
[id$="-settings-btn"],
[id$="-close-btn"],
[id$="-download-btn"],
[id$="-list-btn"] {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  color: white !important;
  cursor: pointer !important;
  outline: none !important;
  box-shadow: none !important;
  transition: all 0.2s ease !important;
}

/* 🔥 图标按钮悬停状态 - 超高优先级 */
html body .icon-btn:hover,
html body [id*="settings-btn"]:hover,
html body [id*="close-btn"]:hover,
html body [id*="download-btn"]:hover,
html body [id*="list-btn"]:hover,
html body .side-menu .icon-btn:hover,
html body .side-menu [id*="settings-btn"]:hover,
html body .side-menu [id*="close-btn"]:hover,
html body .side-menu [id*="download-btn"]:hover,
html body .side-menu [id*="list-btn"]:hover,
html body [id$="-menu"] .icon-btn:hover,
html body [id$="-menu"] [id*="settings-btn"]:hover,
html body [id$="-menu"] [id*="close-btn"]:hover,
html body [id$="-menu"] [id*="download-btn"]:hover,
html body [id$="-menu"] [id*="list-btn"]:hover,
.icon-btn:hover,
[id*="settings-btn"]:hover,
[id*="close-btn"]:hover,
[id*="download-btn"]:hover,
[id*="list-btn"]:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  transform: translateY(-1px) !important;
}

/* 2. 蓝色功能按钮 - 用于各种重大功能的按钮，包括大按钮 */
.blue-btn,
.btn-primary,
/* 🔥🔥🔥 所有侧边栏菜单的重要功能按钮 - 蓝色背景 🔥🔥🔥 */
/* 自定义传感器菜单 */
#cs-submit,           /* 添加自定义传感器 */
#cs-replace,          /* 更换传感器 */
#cs-clear,            /* 清除自定义传感器 */
#cs-geolocation,      /* 使用地理定位 */
#cs-save,             /* 保存传感器列表 */
#cs-load,             /* 加载传感器列表 */
#cs-export,           /* 导出为文件 */
#cs-import,           /* 从文件导入 */
#reset-sensor-button, /* 重置传感器 */
/* 设置菜单 */
#settings-submit,     /* 更新设置 */
#settings-reset,      /* 重置设置 */
/* 过滤菜单 */
#filter-reset,        /* 恢复默认设置 */
/* 历史轨道菜单 */
#history-get-data,    /* 历史轨道获取数据按钮 */
/* 历史经度菜单 */
#geo-longitude-get-data, /* 历史经度获取数据按钮 */
/* 计算器菜单 */
#calculator-itrf,     /* ITRF计算 */
#calculator-j2000,    /* J2000计算 */
/* 新建卫星菜单 */
#createSat-submit,    /* 新建卫星 */
#createSat-save,      /* 保存TLE */
/* 编辑卫星菜单 */
#editSat-submit,      /* 更新卫星 */
#editSat-newTLE,      /* 更新Epoch为现在 */
#editSat-save,        /* 保存TLE */
#editSat-open,        /* 读取TLE */
/* 卫星关注列表菜单 */
#watchlist-save,      /* 保存列表 */
#watchlist-open,      /* 读取列表 */
#watchlist-clear,     /* 清除列表 */
#calculator-rae,      /* RAE计算 */
.side-menu .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(#history-track-menu .param-btn):not(#history-track-menu .satellite-btn),
.side-menu .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(#history-track-menu .param-btn):not(#history-track-menu .satellite-btn),
.side-menu .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(#history-track-menu .param-btn):not(#history-track-menu .satellite-btn),
.side-menu button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(#history-track-menu .param-btn):not(#history-track-menu .satellite-btn),
[id$="-menu"] .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(#history-track-menu .param-btn):not(#history-track-menu .satellite-btn),
[id$="-menu"] .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(#history-track-menu .param-btn):not(#history-track-menu .satellite-btn),
[id$="-menu"] .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(#history-track-menu .param-btn):not(#history-track-menu .satellite-btn),
[id$="-menu"] button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(#history-track-menu .param-btn):not(#history-track-menu .satellite-btn) {
  background: #2196f3 !important;
  background-color: #2196f3 !important;
  background-image: none !important;
  border: none !important;
  color: white !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.blue-btn:hover,
.btn-primary:hover,
/* 🔥🔥🔥 所有侧边栏菜单重要功能按钮悬停状态 - 深蓝色背景 🔥🔥🔥 */
/* 自定义传感器菜单悬停 */
#cs-submit:hover,           /* 添加自定义传感器 */
#cs-replace:hover,          /* 更换传感器 */
#cs-clear:hover,            /* 清除自定义传感器 */
#cs-geolocation:hover,      /* 使用地理定位 */
#cs-save:hover,             /* 保存传感器列表 */
#cs-load:hover,             /* 加载传感器列表 */
#cs-export:hover,           /* 导出为文件 */
#cs-import:hover,           /* 从文件导入 */
#reset-sensor-button:hover, /* 重置传感器 */
/* 设置菜单悬停 */
#settings-submit:hover,     /* 更新设置 */
#settings-reset:hover,      /* 重置设置 */
/* 过滤菜单悬停 */
#filter-reset:hover,        /* 恢复默认设置 */
/* 历史轨道菜单悬停 */
#history-get-data:hover,    /* 历史轨道获取数据按钮 */
/* 历史经度菜单悬停 */
#geo-longitude-get-data:hover, /* 历史经度获取数据按钮 */
/* 计算器菜单悬停 */
#calculator-itrf:hover,     /* ITRF计算 */
#calculator-j2000:hover,    /* J2000计算 */
/* 新建卫星菜单悬停 */
#createSat-submit:hover,    /* 新建卫星 */
#createSat-save:hover,      /* 保存TLE */
/* 编辑卫星菜单悬停 */
#editSat-submit:hover,      /* 更新卫星 */
#editSat-newTLE:hover,      /* 更新Epoch为现在 */
#editSat-save:hover,        /* 保存TLE */
#editSat-open:hover,        /* 读取TLE */
/* 卫星关注列表菜单悬停 */
#watchlist-save:hover,      /* 保存列表 */
#watchlist-open:hover,      /* 读取列表 */
#watchlist-clear:hover,     /* 清除列表 */
#calculator-rae:hover,      /* RAE计算 */
.side-menu .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
.side-menu .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
.side-menu .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
.side-menu button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
[id$="-menu"] .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
[id$="-menu"] .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
[id$="-menu"] .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
[id$="-menu"] button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover {
  background: #1976d2 !important;
  background-color: #1976d2 !important;
  transform: translateY(-1px) !important;
}

/* 🔥🔥🔥 完全移除参数按钮的CSS干扰，让内联样式完全生效 🔥🔥🔥 */
/* 参数按钮不应该有任何CSS覆盖，完全依赖内联样式 */

/* 4. 颜色图例按钮保护 - 防止被全局样式覆盖 */
[id*="settings-color-"],
button[id*="settings-color-"],
.color-legend-btn {
  background: inherit !important; /* 🔥 保持原有颜色 */
  background-color: inherit !important;
  border: inherit !important;
  color: inherit !important;
  border-radius: inherit !important;
  cursor: pointer !important;
  outline: none !important;
}

/* 🔥🔥🔥 历史轨道页面参数按钮 - 完全使用内联样式 🔥🔥🔥 */
/* 参数按钮的所有样式都在HTML内联样式中定义，包括颜色、边框、背景等 */

/* 🔥🔥🔥 移除参数按钮的CSS覆盖，完全使用内联样式 🔥🔥🔥 */
/* 参数按钮的所有样式都在HTML内联样式中定义，不需要CSS覆盖 */

/* 参数按钮悬停效果也使用内联样式或JavaScript控制 */

/* 🔥🔥🔥 历史轨道页面卫星按钮样式 🔥🔥🔥 */
/* 卫星按钮基础样式 - 左上角位置 */
#satellite-buttons-container .satellite-btn {
  border-radius: 6px !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  cursor: pointer !important;
  min-width: 80px !important;
  transition: all 0.2s ease !important;
  user-select: none !important;
  outline: none !important;
  border: 2px solid rgba(255, 255, 255, 0.5) !important; /* 🔥 白色边框 */
  font-weight: 500 !important;
  text-align: center !important;
  white-space: nowrap !important;
}

/* 卫星按钮未选中状态 - 暗色背景 */
#satellite-buttons-container .satellite-btn[data-selected="false"] {
  background: rgba(0, 0, 0, 0.6) !important; /* 🔥 暗色背景 */
  color: rgba(255, 255, 255, 0.8) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

/* 卫星按钮选中状态 - 白色背景 */
#satellite-buttons-container .satellite-btn[data-selected="true"] {
  background: rgba(255, 255, 255, 0.9) !important; /* 🔥 白色背景 */
  color: #333333 !important; /* 🔥 深色文字 */
  border-color: rgba(255, 255, 255, 1) !important; /* 🔥 纯白边框 */
}

/* 卫星按钮悬停效果 */
#satellite-buttons-container .satellite-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

/* 🔥🔥🔥 侧边栏菜单顶部按钮（设置、关闭、下载等）强制去掉边框 - 超高优先级 🔥🔥🔥 */
html body div.side-menu-parent [id*="settings-btn"],
html body div.side-menu-parent [id*="close-btn"],
html body div.side-menu-parent [id*="download-btn"],
html body div.side-menu [id*="settings-btn"],
html body div.side-menu [id*="close-btn"],
html body div.side-menu [id*="download-btn"],
html body [id$="-menu"] [id*="settings-btn"],
html body [id$="-menu"] [id*="close-btn"],
html body [id$="-menu"] [id*="download-btn"],
html body .icon-btn,
.side-menu-parent [id*="settings-btn"],
.side-menu-parent [id*="close-btn"],
.side-menu-parent [id*="download-btn"],
.side-menu [id*="settings-btn"],
.side-menu [id*="close-btn"],
.side-menu [id*="download-btn"],
[id$="-menu"] [id*="settings-btn"],
[id$="-menu"] [id*="close-btn"],
[id$="-menu"] [id*="download-btn"],
.icon-btn,
[id*="settings-btn"],
[id*="close-btn"],
[id*="download-btn"] {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  color: white !important;
  cursor: pointer !important;
}

/* 🔥🔥🔥 超高优先级 - 强制所有侧边栏功能按钮为蓝色背景，覆盖透明样式，排除图标按钮 🔥🔥🔥 */
html body .side-menu .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not([class*="settings"]):not([class*="list"]):not([class*="gear"]):not([id*="settings"]):not([id*="list"]):not([id*="gear"]):not(:has(.material-icons)),
html body .side-menu .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not([class*="settings"]):not([class*="list"]):not([class*="gear"]):not([id*="settings"]):not([id*="list"]):not([id*="gear"]):not(:has(.material-icons)),
html body .side-menu .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not([class*="settings"]):not([class*="list"]):not([class*="gear"]):not([id*="settings"]):not([id*="list"]):not([id*="gear"]):not(:has(.material-icons)),
html body .side-menu button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not([class*="settings"]):not([class*="list"]):not([class*="gear"]):not([id*="settings"]):not([id*="list"]):not([id*="gear"]):not(:has(.material-icons)),
html body [id$="-menu"] .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not([class*="settings"]):not([class*="list"]):not([class*="gear"]):not([id*="settings"]):not([id*="list"]):not([id*="gear"]):not(:has(.material-icons)),
html body [id$="-menu"] .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not([class*="settings"]):not([class*="list"]):not([class*="gear"]):not([id*="settings"]):not([id*="list"]):not([id*="gear"]):not(:has(.material-icons)),
html body [id$="-menu"] .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not([class*="settings"]):not([class*="list"]):not([class*="gear"]):not([id*="settings"]):not([id*="list"]):not([id*="gear"]):not(:has(.material-icons)),
html body [id$="-menu"] button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not([class*="settings"]):not([class*="list"]):not([class*="gear"]):not([id*="settings"]):not([id*="list"]):not([id*="gear"]):not(:has(.material-icons)),
html body #cs-submit,
html body #cs-replace,
html body #cs-clear,
html body #cs-geolocation,
html body #cs-save,
html body #cs-load,
html body #cs-export,
html body #cs-import,
html body #reset-sensor-button,
html body #createSat-submit,
html body #createSat-save,
html body #editSat-submit,
html body #editSat-newTLE,
html body #editSat-save,
html body #editSat-open,
html body #watchlist-save,
html body #watchlist-open,
html body #watchlist-clear {
  background: #2196f3 !important;
  background-color: #2196f3 !important;
  background-image: none !important;
  border: none !important; /* 🔥 移除边框，统一样式 */
  color: white !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  font-weight: 500 !important;
  text-shadow: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* 🔥🔥🔥 超高优先级 - 强制所有侧边栏功能按钮悬停状态 🔥🔥🔥 */
html body .side-menu .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
html body .side-menu .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
html body .side-menu .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
html body .side-menu button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
html body [id$="-menu"] .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
html body [id$="-menu"] .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
html body [id$="-menu"] .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
html body [id$="-menu"] button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):hover,
html body #cs-submit:hover,
html body #cs-replace:hover,
html body #cs-clear:hover,
html body #cs-geolocation:hover,
html body #cs-save:hover,
html body #cs-load:hover,
html body #cs-export:hover,
html body #cs-import:hover,
html body #reset-sensor-button:hover {
  background: #1976d2 !important;
  background-color: #1976d2 !important;
  border-color: #1565c0 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* 🔥🔥🔥 最高优先级 - 强制覆盖所有透明背景样式 🔥🔥🔥 */
html[lang] body .side-menu .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not(:has(.material-icons)),
html[lang] body .side-menu .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not(:has(.material-icons)),
html[lang] body .side-menu .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not(:has(.material-icons)),
html[lang] body .side-menu button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not(:has(.material-icons)),
html[lang] body [id$="-menu"] .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not(:has(.material-icons)),
html[lang] body [id$="-menu"] .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not(:has(.material-icons)),
html[lang] body [id$="-menu"] .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not(:has(.material-icons)),
html[lang] body [id$="-menu"] button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not([id$="-secondary-btn"]):not(:has(.material-icons)) {
  background: #2196f3 !important;
  background-color: #2196f3 !important;
  background-image: none !important;
  border: none !important;
  color: white !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

/* 🔥🔥🔥🔥🔥 终极优先级修复 - 必须放在最后 🔥🔥🔥🔥🔥 */

/* 🔥 终极输入框白色底线修复 - 覆盖所有透明度规则 */
html body div[id$="-menu"] input[type="text"],
html body div[id$="-menu"] input[type="number"],
html body div[id$="-menu"] input[type="email"],
html body div[id$="-menu"] input[type="password"],
html body div[id$="-menu"] input[type="date"],
html body div[id$="-menu"] input[type="time"],
html body div[id$="-menu"] input[type="datetime-local"],
html body div[id$="-menu"] input[type="search"],
html body div[id$="-menu"] input[type="url"],
html body div[id$="-menu"] input[type="tel"],
html body div.side-menu input[type="text"],
html body div.side-menu input[type="number"],
html body div.side-menu input[type="email"],
html body div.side-menu input[type="password"],
html body div.side-menu input[type="date"],
html body div.side-menu input[type="time"],
html body div.side-menu input[type="datetime-local"],
html body div.side-menu input[type="search"],
html body div.side-menu input[type="url"],
html body div.side-menu input[type="tel"],
html body div.side-menu-parent input[type="text"],
html body div.side-menu-parent input[type="number"],
html body div.side-menu-parent input[type="email"],
html body div.side-menu-parent input[type="password"],
html body div.side-menu-parent input[type="date"],
html body div.side-menu-parent input[type="time"],
html body div.side-menu-parent input[type="datetime-local"],
html body div.side-menu-parent input[type="search"],
html body div.side-menu-parent input[type="url"],
html body div.side-menu-parent input[type="tel"] {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.6) !important;
  background: transparent !important;
  color: white !important;
  outline: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* 删除重复的强制规则 - 已由上面的规则覆盖 */

/* 🔥 终极按钮悬停状态修复 */
html body div[id$="-menu"] #cs-submit:hover,
html body div[id$="-menu"] #cs-replace:hover,
html body div[id$="-menu"] #cs-clear:hover,
html body div[id$="-menu"] #cs-geolocation:hover,
html body div[id$="-menu"] #cs-save:hover,
html body div[id$="-menu"] #cs-load:hover,
html body div[id$="-menu"] #cs-export:hover,
html body div[id$="-menu"] #cs-import:hover,
html body div[id$="-menu"] #reset-sensor-button:hover,
html body div.side-menu #cs-submit:hover,
html body div.side-menu #cs-replace:hover,
html body div.side-menu #cs-clear:hover,
html body div.side-menu #cs-geolocation:hover,
html body div.side-menu #cs-save:hover,
html body div.side-menu #cs-load:hover,
html body div.side-menu #cs-export:hover,
html body div.side-menu #cs-import:hover,
html body div.side-menu #reset-sensor-button:hover {
  background: #1976d2 !important;
  background-color: #1976d2 !important;
  border-color: #1565c0 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* 🔥🔥🔥🔥🔥🔥🔥 超级终极修复 - 必须放在文件最后 🔥🔥🔥🔥🔥🔥🔥 */
/* 这个规则使用最高可能的CSS特异性来覆盖所有其他规则 */

/* 🔥 超级强制输入框白色底线 - 覆盖transparency-unified.css */
html body div[class*="side-menu"] input[type="text"],
html body div[class*="side-menu"] input[type="number"],
html body div[class*="side-menu"] input[type="email"],
html body div[class*="side-menu"] input[type="password"],
html body div[class*="side-menu"] input[type="date"],
html body div[class*="side-menu"] input[type="time"],
html body div[class*="side-menu"] input[type="datetime-local"],
html body div[class*="side-menu"] input[type="search"],
html body div[class*="side-menu"] input[type="url"],
html body div[class*="side-menu"] input[type="tel"],
html body div[id*="-menu"] input[type="text"],
html body div[id*="-menu"] input[type="number"],
html body div[id*="-menu"] input[type="email"],
html body div[id*="-menu"] input[type="password"],
html body div[id*="-menu"] input[type="date"],
html body div[id*="-menu"] input[type="time"],
html body div[id*="-menu"] input[type="datetime-local"],
html body div[id*="-menu"] input[type="search"],
html body div[id*="-menu"] input[type="url"],
html body div[id*="-menu"] input[type="tel"],
html body div[class*="menu-container"] input[type="text"],
html body div[class*="menu-container"] input[type="number"],
html body div[class*="menu-container"] input[type="email"],
html body div[class*="menu-container"] input[type="password"],
html body div[class*="menu-container"] input[type="date"],
html body div[class*="menu-container"] input[type="time"],
html body div[class*="menu-container"] input[type="datetime-local"],
html body div[class*="menu-container"] input[type="search"],
html body div[class*="menu-container"] input[type="url"],
html body div[class*="menu-container"] input[type="tel"] {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.8) !important;
  background: transparent !important;
  color: white !important;
  outline: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* 删除重复的超级强制规则 - 已由上面的规则覆盖 */

/* 🔥 超级强制按钮悬停状态 */
html body div[id="custom-sensor-menu"] button[id="cs-submit"]:hover,
html body div[id="custom-sensor-menu"] button[id="cs-replace"]:hover,
html body div[id="custom-sensor-menu"] button[id="cs-clear"]:hover,
html body div[id="custom-sensor-menu"] button[id="cs-geolocation"]:hover,
html body div[id="custom-sensor-menu"] button[id="cs-save"]:hover,
html body div[id="custom-sensor-menu"] button[id="cs-load"]:hover,
html body div[id="custom-sensor-menu"] button[id="cs-export"]:hover,
html body div[id="custom-sensor-menu"] button[id="cs-import"]:hover,
html body div[id="sensor-list-menu"] button[id="reset-sensor-button"]:hover,
html body button[id="cs-submit"]:hover,
html body button[id="cs-replace"]:hover,
html body button[id="cs-clear"]:hover,
html body button[id="cs-geolocation"]:hover,
html body button[id="cs-save"]:hover,
html body button[id="cs-load"]:hover,
html body button[id="cs-export"]:hover,
html body button[id="cs-import"]:hover,
html body button[id="reset-sensor-button"]:hover {
  background: #1976d2 !important;
  background-color: #1976d2 !important;
  border-color: #1565c0 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* 🔥🔥🔥 卫星视场菜单特殊处理 - 移除外框 🔥🔥🔥 */
#satellite-fov-menu .input-field,
#satellite-fov-menu .input-field input,
#satellite-fov-menu .input-field input[type="text"],
#satellite-fov-menu .input-field input[type="number"],
#satellite-fov-menu form .input-field,
#satellite-fov-menu form .input-field input,
#satellite-fov-menu form .input-field input[type="text"],
#satellite-fov-menu form .input-field input[type="number"] {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.8) !important;
  border-radius: 0 !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  outline: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 🔥🔥🔥 卫星视场菜单标签样式 🔥🔥🔥 */
#satellite-fov-menu .input-field label,
#satellite-fov-menu form .input-field label {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 🔥🔥🔥 最终暴力修复 - 使用内联样式级别的优先级 🔥🔥🔥 */
/* 如果上面的规则还不够，这个规则会强制覆盖一切 */
[style*="border: none"] input[type="text"],
[style*="border: none"] input[type="number"],
[style*="border: none"] input[type="email"],
[style*="border: none"] input[type="password"],
[style*="border: none"] input[type="date"],
[style*="border: none"] input[type="time"],
[style*="border: none"] input[type="datetime-local"],
[style*="border: none"] input[type="search"],
[style*="border: none"] input[type="url"],
[style*="border: none"] input[type="tel"] {
  border-bottom: 1px solid rgba(255, 255, 255, 0.8) !important;
}

/* 删除针对内联样式的强制修复 - 不再需要 */

/* JavaScript强制修复标记已删除 - 改用纯CSS解决 */

/* 🔥🔥🔥 强制所有图标按钮透明 - 最高优先级 🔥🔥🔥 */
html body .icon-btn,
html body [id$="-settings-btn"],
html body [id$="-close-btn"],
html body [id$="-download-btn"],
html body [id$="-list-btn"],
html body [id$="-secondary-btn"],
html body .side-menu .icon-btn,
html body .side-menu [id$="-settings-btn"],
html body .side-menu [id$="-close-btn"],
html body .side-menu [id$="-download-btn"],
html body .side-menu [id$="-list-btn"],
html body .side-menu [id$="-secondary-btn"],
html body [id$="-menu"] .icon-btn,
html body [id$="-menu"] [id$="-settings-btn"],
html body [id$="-menu"] [id$="-close-btn"],
html body [id$="-menu"] [id$="-download-btn"],
html body [id$="-menu"] [id$="-list-btn"],
html body [id$="-menu"] [id$="-secondary-btn"],
html body button:has(.material-icons),
html body .side-menu button:has(.material-icons),
html body [id$="-menu"] button:has(.material-icons) {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* ========================================
 * 🔥🔥🔥 最终简洁的按钮样式规则 🔥🔥🔥
 * 覆盖Materialize默认样式，正确区分图标按钮和功能按钮
 * ======================================== */

/* 1. 图标按钮 - 透明背景 */
.icon-btn,
[id$="-settings-btn"],
[id$="-close-btn"],
[id$="-download-btn"],
[id$="-list-btn"],
.side-menu .btn:has(.material-icons),
[id$="-menu"] .btn:has(.material-icons),
.side-menu button:has(.material-icons),
[id$="-menu"] button:has(.material-icons) {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  color: white !important;
  cursor: pointer !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 2. 功能按钮 - 蓝色背景，覆盖Materialize的#26a69a，文字居中 */
.side-menu .btn:not(.icon-btn):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)),
[id$="-menu"] .btn:not(.icon-btn):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)),
.side-menu button:not(.icon-btn):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)),
[id$="-menu"] button:not(.icon-btn):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)) {
  background: #2196f3 !important;
  background-color: #2196f3 !important;
  background-image: none !important;
  border: 1px solid #1976d2 !important;
  color: white !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;

  /* 🔥🔥🔥 强制文字居中对齐 🔥🔥🔥 */
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: normal !important;
  vertical-align: middle !important;
}

/* 按钮文字居中规则已移至responsive-design.css统一管理 */

/* 🔥🔥🔥 特别处理包含图标的按钮 - 确保图标和文字都居中 🔥🔥🔥 */
.side-menu .btn:has(.material-icons),
.side-menu button:has(.material-icons),
[id$="-menu"] .btn:has(.material-icons),
[id$="-menu"] button:has(.material-icons),
.side-menu a.btn:has(.material-icons),
[id$="-menu"] a.btn:has(.material-icons) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  flex-direction: row !important;
}

/* 🔥🔥🔥 Material Icons在按钮中的对齐 🔥🔥🔥 */
.side-menu .material-icons,
[id$="-menu"] .material-icons {
  vertical-align: middle !important;
  line-height: 1 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 🔥🔥🔥 修复center-align容器 - 最高优先级 🔥🔥🔥 */
html body .side-menu .center-align,
html body .side-menu .center,
html body [id$="-menu"] .center-align,
html body [id$="-menu"] .center {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
}

/* 🔥🔥🔥 强制所有侧边栏按钮文字居中 - 最高优先级 🔥🔥🔥 */
html body .side-menu .btn,
html body .side-menu .btn-large,
html body .side-menu .btn-small,
html body .side-menu button,
html body [id$="-menu"] .btn,
html body [id$="-menu"] .btn-large,
html body [id$="-menu"] .btn-small,
html body [id$="-menu"] button,
html body .side-menu a.btn,
html body .side-menu a.btn-large,
html body .side-menu a.btn-small,
html body [id$="-menu"] a.btn,
html body [id$="-menu"] a.btn-large,
html body [id$="-menu"] a.btn-small {
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: normal !important;
  vertical-align: middle !important;
}

/* 🔥🔥🔥 超级强制按钮文字居中 - 覆盖所有可能的样式 🔥🔥🔥 */
html body .side-menu .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu button:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] button:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu a.btn:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu a.btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu a.btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] a.btn:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] a.btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] a.btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn) {
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  line-height: 1.2 !important;
  vertical-align: middle !important;
  padding-left: calc(16px / var(--system-scale-factor, 1)) !important;
  padding-right: calc(16px / var(--system-scale-factor, 1)) !important;
  padding-top: calc(12px / var(--system-scale-factor, 1)) !important;
  padding-bottom: calc(12px / var(--system-scale-factor, 1)) !important;
  box-sizing: border-box !important;
}

/* 🔥🔥🔥 强制按钮内文本内容居中 - 排除图标，避免破坏flex布局 🔥🔥🔥 */
html body .side-menu .btn:not(:has(.material-icons)) *:not(.material-icons),
html body .side-menu .btn-large:not(:has(.material-icons)) *:not(.material-icons),
html body .side-menu .btn-small:not(:has(.material-icons)) *:not(.material-icons),
html body .side-menu button:not(:has(.material-icons)) *:not(.material-icons),
html body [id$="-menu"] .btn:not(:has(.material-icons)) *:not(.material-icons),
html body [id$="-menu"] .btn-large:not(:has(.material-icons)) *:not(.material-icons),
html body [id$="-menu"] .btn-small:not(:has(.material-icons)) *:not(.material-icons),
html body [id$="-menu"] button:not(:has(.material-icons)) *:not(.material-icons),
html body .side-menu a.btn:not(:has(.material-icons)) *:not(.material-icons),
html body .side-menu a.btn-large:not(:has(.material-icons)) *:not(.material-icons),
html body .side-menu a.btn-small:not(:has(.material-icons)) *:not(.material-icons),
html body [id$="-menu"] a.btn:not(:has(.material-icons)) *:not(.material-icons),
html body [id$="-menu"] a.btn-large:not(:has(.material-icons)) *:not(.material-icons),
html body [id$="-menu"] a.btn-small:not(:has(.material-icons)) *:not(.material-icons) {
  text-align: center !important;
  vertical-align: middle !important;
}

/* 🔥🔥🔥 修复Materialize框架的按钮文字偏移问题 🔥🔥🔥 */
html body .side-menu .btn:before,
html body .side-menu .btn:after,
html body .side-menu .btn-large:before,
html body .side-menu .btn-large:after,
html body .side-menu .btn-small:before,
html body .side-menu .btn-small:after,
html body .side-menu button:before,
html body .side-menu button:after,
html body [id$="-menu"] .btn:before,
html body [id$="-menu"] .btn:after,
html body [id$="-menu"] .btn-large:before,
html body [id$="-menu"] .btn-large:after,
html body [id$="-menu"] .btn-small:before,
html body [id$="-menu"] .btn-small:after,
html body [id$="-menu"] button:before,
html body [id$="-menu"] button:after {
  display: none !important;
  content: none !important;
}

/* 🔥🔥🔥 修复center-align容器 - 让按钮在容器中居中 🔥🔥🔥 */
.side-menu .center-align,
.side-menu .center,
[id$="-menu"] .center-align,
[id$="-menu"] .center {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
}

/* 🔥🔥🔥 终极按钮文字居中修复 - 覆盖所有框架样式 🔥🔥🔥 */
.side-menu .btn,
.side-menu .btn-large,
.side-menu .btn-small,
.side-menu button,
[id$="-menu"] .btn,
[id$="-menu"] .btn-large,
[id$="-menu"] .btn-small,
[id$="-menu"] button,
.side-menu a.btn,
.side-menu a.btn-large,
.side-menu a.btn-small,
[id$="-menu"] a.btn,
[id$="-menu"] a.btn-large,
[id$="-menu"] a.btn-small {
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  line-height: 1.2 !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 🔥🔥🔥 强制移除可能影响居中的样式 🔥🔥🔥 */
.side-menu .btn,
.side-menu .btn-large,
.side-menu .btn-small,
.side-menu button,
[id$="-menu"] .btn,
[id$="-menu"] .btn-large,
[id$="-menu"] .btn-small,
[id$="-menu"] button {
  padding-left: 16px !important;
  padding-right: 16px !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  text-indent: 0 !important;
  letter-spacing: normal !important;
}

/* 🔥🔥🔥 超级强制侧边栏按钮文字居中 - 最高优先级覆盖所有框架样式 🔥🔥🔥 */
html body .side-menu .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu button:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] button:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu a.btn:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu a.btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body .side-menu a.btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] a.btn:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] a.btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn),
html body [id$="-menu"] a.btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn) {
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  line-height: 1.2 !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding-left: 16px !important;
  padding-right: 16px !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  text-indent: 0 !important;
  letter-spacing: normal !important;
  box-sizing: border-box !important;
}

/* 🔥🔥🔥 强制移除可能影响居中的Materialize框架样式 🔥🔥🔥 */
html body .side-menu .btn:before,
html body .side-menu .btn:after,
html body .side-menu .btn-large:before,
html body .side-menu .btn-large:after,
html body .side-menu .btn-small:before,
html body .side-menu .btn-small:after,
html body .side-menu button:before,
html body .side-menu button:after,
html body [id$="-menu"] .btn:before,
html body [id$="-menu"] .btn:after,
html body [id$="-menu"] .btn-large:before,
html body [id$="-menu"] .btn-large:after,
html body [id$="-menu"] .btn-small:before,
html body [id$="-menu"] .btn-small:after,
html body [id$="-menu"] button:before,
html body [id$="-menu"] button:after {
  display: none !important;
  content: none !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 🔥🔥🔥 传感器时间线开关按钮透明背景 🔥🔥🔥 */
#sensor-timeline-menu .switch,
#sensor-timeline-content .switch {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: calc(10px / var(--system-scale-factor, 1)) 0 !important;
}

#sensor-timeline-menu .switch label,
#sensor-timeline-content .switch label {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  color: white !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 修复可能的文字偏移问题 🔥🔥🔥 */
.side-menu .btn *,
.side-menu .btn-large *,
.side-menu .btn-small *,
.side-menu button *,
[id$="-menu"] .btn *,
[id$="-menu"] .btn-large *,
[id$="-menu"] .btn-small *,
[id$="-menu"] button *,
.side-menu a.btn *,
.side-menu a.btn-large *,
.side-menu a.btn-small *,
[id$="-menu"] a.btn *,
[id$="-menu"] a.btn-large *,
[id$="-menu"] a.btn-small * {
  text-align: center !important;
  vertical-align: middle !important;
}

/* 🔥🔥🔥 确保按钮内容不会因为其他样式而偏移 🔥🔥🔥 */
.side-menu .btn:before,
.side-menu .btn:after,
.side-menu .btn-large:before,
.side-menu .btn-large:after,
.side-menu .btn-small:before,
.side-menu .btn-small:after,
.side-menu button:before,
.side-menu button:after,
[id$="-menu"] .btn:before,
[id$="-menu"] .btn:after,
[id$="-menu"] .btn-large:before,
[id$="-menu"] .btn-large:after,
[id$="-menu"] .btn-small:before,
[id$="-menu"] .btn-small:after,
[id$="-menu"] button:before,
[id$="-menu"] button:after {
  display: none !important;
}

/* 🔥🔥🔥 通用侧边栏按钮文字居中规则 🔥🔥🔥 */
.side-menu .btn,
.side-menu .btn-large,
.side-menu .btn-small,
.side-menu button,
[id$="-menu"] .btn,
[id$="-menu"] .btn-large,
[id$="-menu"] .btn-small,
[id$="-menu"] button {
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: normal !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 🔥🔥🔥 特别处理包含图标的按钮 🔥🔥🔥 */
.side-menu .btn:has(.material-icons),
.side-menu button:has(.material-icons),
[id$="-menu"] .btn:has(.material-icons),
[id$="-menu"] button:has(.material-icons) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

/* 🔥🔥🔥 Material Icons在按钮中的对齐 🔥🔥🔥 */
.side-menu .material-icons,
[id$="-menu"] .material-icons {
  vertical-align: middle !important;
  line-height: 1 !important;
}

/* 🔥🔥🔥🔥🔥 终极图标按钮边框移除 - 最高优先级 🔥🔥🔥🔥🔥 */
html body div[class*="side-menu"] button[id*="settings-btn"],
html body div[class*="side-menu"] button[id*="close-btn"],
html body div[class*="side-menu"] button[id*="download-btn"],
html body div[id*="-menu"] button[id*="settings-btn"],
html body div[id*="-menu"] button[id*="close-btn"],
html body div[id*="-menu"] button[id*="download-btn"],
html body button[class*="icon-btn"],
html body button[id*="settings-btn"],
html body button[id*="close-btn"],
html body button[id*="download-btn"],
html body .icon-btn,
html body [id*="settings-btn"],
html body [id*="close-btn"],
html body [id*="download-btn"] {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  color: white !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

/* 🔥🔥🔥 按钮悬停状态 - 移除蓝色背景，排除历史轨道参数按钮 🔥🔥🔥 */
html body .side-menu .btn:not(.param-btn):not(.satellite-btn):hover,
html body .side-menu .btn-small:not(.param-btn):not(.satellite-btn):hover,
html body .side-menu .btn-large:not(.param-btn):not(.satellite-btn):hover,
html body .side-menu button:not(.param-btn):not(.satellite-btn):hover,
html body [id$="-menu"] .btn:not(.param-btn):not(.satellite-btn):hover,
html body [id$="-menu"] .btn-small:not(.param-btn):not(.satellite-btn):hover,
html body [id$="-menu"] .btn-large:not(.param-btn):not(.satellite-btn):hover,
html body [id$="-menu"] button:not(.param-btn):not(.satellite-btn):hover,
html body .side-menu .btn-ui:not(.param-btn):not(.satellite-btn):hover,
html body [id$="-menu"] .btn-ui:not(.param-btn):not(.satellite-btn):hover,
.side-menu .btn:not(.param-btn):not(.satellite-btn):hover,
.side-menu .btn-small:not(.param-btn):not(.satellite-btn):hover,
.side-menu .btn-large:not(.param-btn):not(.satellite-btn):hover,
.side-menu button:not(.param-btn):not(.satellite-btn):hover,
[id$="-menu"] .btn:not(.param-btn):not(.satellite-btn):hover,
[id$="-menu"] .btn-small:not(.param-btn):not(.satellite-btn):hover,
[id$="-menu"] .btn-large:not(.param-btn):not(.satellite-btn):hover,
[id$="-menu"] button:not(.param-btn):not(.satellite-btn):hover,
.side-menu .btn-ui:not(.param-btn):not(.satellite-btn):hover,
[id$="-menu"] .btn-ui:not(.param-btn):not(.satellite-btn):hover {
  background-color: rgba(255, 255, 255, 0.1) !important; /* 🔥 悬停时半透明白色背景 */
  background: rgba(255, 255, 255, 0.1) !important;
  background-image: none !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  transform: translateY(-1px) !important;
}

/* ========================================
 * 6. 下拉框优化
 * ======================================== */
/* 下拉框样式已移至responsive-design.css统一管理 */

/* 终极下拉框样式已移至responsive-design.css统一管理 */

/* 🔥🔥🔥 超强化下拉框背景 - 防止被任何透明规则覆盖 🔥🔥🔥 */
.dropdown-content,
select + .dropdown-content,
.select-wrapper + .dropdown-content,
.input-field .dropdown-content {
  background: #000000 !important;
  background-color: #000000 !important;
  background-image: none !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  opacity: 1 !important;
}

/* 按钮缩放样式已移至responsive-design.css统一管理 */

/* 🔥 修复顶部菜单图标缩放 */
.top-menu-icons img,
#sound-icon,
#legend-icon img,
#tutorial-icon img,
#fullscreen-icon img {
  width: calc(30px / var(--system-scale-factor, 1)) !important;
  height: calc(30px / var(--system-scale-factor, 1)) !important;
  margin: calc(3px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复搜索框缩放 - 完全透明背景 */
#search {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  height: calc(36px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  border: none !important; /* 强制移除所有边框 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important; /* 只保留底部细线 */
  border-radius: 0 !important; /* 移除圆角 */
  background: transparent !important; /* 🔥 确保完全透明背景 */
  background-color: transparent !important; /* 🔥 强制透明背景色 */
  background-image: none !important; /* 🔥 移除背景图片 */
  outline: none !important; /* 移除轮廓 */
  box-shadow: none !important; /* 移除阴影 */
  -webkit-box-shadow: none !important; /* 移除webkit阴影 */
}

/* 🔥 修复搜索框placeholder文字缩放 */
#search::placeholder {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 🔥 修复搜索框输入文字缩放 - 聚焦状态也保持透明 */
#search:focus,
#search:active {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  border: none !important; /* 强制移除所有边框 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.9) !important; /* 🔥 聚焦时更亮的白色底线 */
  background: transparent !important; /* 🔥 聚焦时也保持透明背景 */
  background-color: transparent !important; /* 🔥 强制透明背景色 */
  background-image: none !important; /* 🔥 移除背景图片 */
  outline: none !important; /* 移除轮廓 */
  box-shadow: none !important; /* 移除阴影 */
  -webkit-box-shadow: none !important; /* 移除webkit阴影 */
}

/* 🔥 修复搜索清除图标缩放 */
#clear-search {
  width: calc(18px / var(--system-scale-factor, 1)) !important;
  height: calc(18px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  right: calc(8px / var(--system-scale-factor, 1)) !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 🔥 强制修复时间管理器缩放 - 使用缩放变量和强选择器 */
html body #datetime,
html body #jday,
html body #datetime-text,
html body #datetime-title,
html body #non-realtime-indicator,
html body .datetime-container,
html body .time-machine,
html body .ui-timepicker-wrapper,
#datetime,
#jday,
#datetime-text,
#datetime-title,
#non-realtime-indicator,
.datetime-container,
.time-machine,
.ui-timepicker-wrapper {
  font-size: var(--large-font-size) !important;
  line-height: calc(28px / var(--system-scale-factor, 1)) !important;
  color: white !important;
}

/* 🔥 非实时指示器专门样式 */
html body #non-realtime-indicator,
#non-realtime-indicator {
  background-color: #ffeb3b !important;
  color: #333 !important;
  padding: calc(2px / var(--system-scale-factor, 1)) calc(8px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(3px / var(--system-scale-factor, 1)) !important;
  font-size: calc(12px / var(--system-scale-factor, 1)) !important;
  font-weight: bold !important;
  cursor: pointer !important;
  margin-left: calc(5px / var(--system-scale-factor, 1)) !important;
  font-family: 'Microsoft YaHei', '微软雅黑', sans-serif !important;
}

/* 🔥 修复顶部菜单日期时间一致性 */
html body #jday,
#jday {
  font-size: var(--large-font-size) !important;
  line-height: calc(28px / var(--system-scale-factor, 1)) !important;
  color: white !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  display: block !important;
}

/* 🔥 修复竖线分隔符缩放 */
html body #datetime-text,
#datetime-text {
  border-left: calc(2px / var(--system-scale-factor, 1)) solid var(--color-dark-text-accent) !important;
  padding-left: calc(12px / var(--system-scale-factor, 1)) !important;
  margin-left: calc(8px / var(--system-scale-factor, 1)) !important;
  font-size: calc(20px / var(--system-scale-factor, 1)) !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 🔥 为传感器选择区域添加一致的竖线分隔符 */
html body #sensor-selected-container,
#sensor-selected-container {
  border-left: calc(2px / var(--system-scale-factor, 1)) solid var(--color-dark-text-accent) !important;
  padding-left: calc(12px / var(--system-scale-factor, 1)) !important;
  margin-left: calc(8px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 统一传感器选择区域字体大小 */
html body #sensor-selected,
#sensor-selected {
  font-size: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 统一日期字体族和大小 */
html body #jday,
#jday {
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  font-size: calc(20px / var(--system-scale-factor, 1)) !important;
  width: calc(200px / var(--system-scale-factor, 1)) !important;
  min-width: calc(200px / var(--system-scale-factor, 1)) !important;
  max-width: calc(200px / var(--system-scale-factor, 1)) !important;
}

#datetime input,
.datetime-container input,
.time-machine input,
.ui-timepicker-wrapper input {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  height: calc(32px / var(--system-scale-factor)) !important;
  padding: calc(8px / var(--system-scale-factor)) !important;
}

#datetime button,
.datetime-container button,
.time-machine button,
.ui-timepicker-wrapper button {
  font-size: calc(14px / var(--system-division-factor, 1)) !important;
  height: calc(32px / var(--system-division-factor, 1)) !important;
  padding: calc(8px / var(--system-division-factor, 1)) calc(12px / var(--system-division-factor, 1)) !important;
  line-height: calc(16px / var(--system-division-factor, 1)) !important;
}

/* 🔥 强制修复sat-info-box所有元素缩放 - 使用更大字体和强选择器 */
html body #sat-infobox,
html body .sat-info-box,
#sat-infobox,
.sat-info-box {
  font-size: var(--base-font-size) !important;
  line-height: calc(24px / var(--system-division-factor, 1)) !important;
}

html body #sat-infobox .sat-info-title,
html body .sat-info-box .sat-info-title,
#sat-infobox .sat-info-title,
.sat-info-box .sat-info-title {
  font-size: var(--large-font-size) !important;
  line-height: calc(28px / var(--system-scale-factor, 1)) !important;
  font-weight: bold !important;
}

html body #sat-infobox .sat-info-content,
html body .sat-info-box .sat-info-content,
html body #sat-infobox .sat-info-key,
html body #sat-infobox .sat-info-value,
#sat-infobox .sat-info-content,
.sat-info-box .sat-info-content,
#sat-infobox .sat-info-key,
#sat-infobox .sat-info-value {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

#sat-infobox button,
.sat-info-box button {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  height: calc(32px / var(--system-scale-factor)) !important;
  padding: calc(8px / var(--system-scale-factor)) calc(12px / var(--system-scale-factor)) !important;
  line-height: calc(16px / var(--system-scale-factor)) !important;
}

/* 🔥🔥🔥 终极修复：所有图标和特殊元素缩放 🔥🔥🔥 */

/* 🔥 1. 强制修复所有material-icons图标缩放 - 使用强选择器 */
html body .material-icons,
html body .side-menu .material-icons,
html body #settings-menu .material-icons,
html body #sat-infobox .material-icons,
html body .sat-info-box .material-icons,
.material-icons,
.side-menu .material-icons,
#settings-menu .material-icons,
#sat-infobox .material-icons,
.sat-info-box .material-icons {
  font-size: var(--icon-size) !important;
  line-height: var(--icon-size) !important;
  width: var(--icon-size) !important;
  height: var(--icon-size) !important;
}

/* 🔥 时间管理器简化缩放 - 只缩放字体 */

/* 1. 顶部时间显示区域 */
#datetime-text,
#datetime-input,
#datetime-input-form,
#datetime-input-tb,
#non-realtime-indicator {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 2. 时间管理器容器 - 只缩放字体，不缩放容器 */
#ui-datepicker-div,
.ui-timepicker-div {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

/* 3. 时间管理器子元素缩放 */
.ui-timepicker-div .ui-slider,
.ui-timepicker-div table,
.ui-timepicker-div td,
.ui-timepicker-div tr {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

/* 4. 按钮缩放和对齐修复 */
#ui-datepicker-div button,
.ui-datepicker button,
.ui-timepicker-div button {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(6px / var(--system-scale-factor, 1)) calc(8px / var(--system-scale-factor, 1)) !important;
  min-height: calc(28px / var(--system-scale-factor, 1)) !important;
  line-height: calc(16px / var(--system-scale-factor, 1)) !important;
  vertical-align: middle !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 calc(2px / var(--system-scale-factor, 1)) !important;
  border: none !important;
  outline: none !important;
}

/* 4. 表格单元格缩放 */
#ui-datepicker-div td,
#ui-datepicker-div th,
.ui-datepicker td,
.ui-datepicker th {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(6px / var(--system-scale-factor, 1)) !important;
  text-align: center !important;
}

/* 5. 日历表格布局 - 保持原有布局 */
.ui-datepicker-calendar {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: calc(5px / var(--system-scale-factor, 1)) 0 !important;
  border-spacing: 0 !important;
}

.ui-datepicker-calendar td {
  width: 14.28% !important;
  height: calc(40px / var(--system-scale-factor, 1)) !important;
  vertical-align: middle !important;
  cursor: pointer !important;
  pointer-events: auto !important;
  border: calc(1px / var(--system-scale-factor, 1)) solid transparent !important;
}

.ui-datepicker-calendar th {
  height: calc(30px / var(--system-scale-factor, 1)) !important;
  padding: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 确保日历链接和文本可以正确点击 */
.ui-datepicker-calendar td a,
.ui-datepicker-calendar td .ui-datepicker-cal-day,
.ui-datepicker-calendar td .ui-datepicker-jday {
  cursor: pointer !important;
  pointer-events: auto !important;
  display: block !important;
  text-decoration: none !important;
  user-select: none !important;
}

/* 6. 时间选择器简化布局 */
.ui_tpicker_hour,
.ui_tpicker_minute,
.ui_tpicker_second,
.ui_tpicker_proprate {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  padding: calc(5px / var(--system-scale-factor, 1)) calc(10px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(8px / var(--system-scale-factor, 1)) !important;
}

.ui-slider-access {
  margin-left: calc(10px / var(--system-scale-factor, 1)) !important;
  margin-right: 0px !important;
  display: flex !important;
  flex-direction: row !important;
  gap: calc(4px / var(--system-scale-factor, 1)) !important;
  align-items: center !important; /* 🔥 确保+-按钮垂直居中 */
  height: calc(40px / var(--system-scale-factor, 1)) !important; /* 🔥 与容器高度一致 */
}

.ui-slider-access button {
  font-size: calc(24px / var(--system-scale-factor, 1)) !important; /* 从16px增加到24px */
  width: calc(32px / var(--system-scale-factor, 1)) !important; /* 从20px增加到32px */
  height: calc(32px / var(--system-scale-factor, 1)) !important; /* 从20px增加到32px */
  padding: 0 !important;
  margin: 0 calc(4px / var(--system-scale-factor, 1)) !important; /* 增加间距 */
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  text-align: center !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  /* 🔥🔥🔥 修复：设置为蓝色背景，不是透明 🔥🔥🔥 */
  background: #2196f3 !important;
  background-color: #2196f3 !important;
  background-image: none !important;
  color: white !important;
  border: 1px solid #2196f3 !important; /* 添加边框 */
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important; /* 添加圆角 */
  box-shadow: none !important;
  outline: none !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important; /* 添加过渡效果 */
}

/* 🔥🔥🔥 滑动按钮悬停状态 🔥🔥🔥 */
.ui-slider-access button:hover {
  background: #1976d2 !important; /* 悬停时更深的蓝色 */
  background-color: #1976d2 !important;
  border-color: #1976d2 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  transform: translateY(-1px) !important; /* 轻微上移效果 */
}

/* 🔥 确保+-按钮显示文本符号 */
.ui-slider-access button {
  font-family: monospace !important;
  text-indent: 0 !important;
  overflow: visible !important;
}

/* 🔥 隐藏图标，显示文本 */
.ui-slider-access button .ui-button-icon {
  display: none !important;
}

.ui-slider-access button .ui-button-text,
.ui-slider-access button span {
  display: inline-block !important;
  font-size: calc(32px / var(--system-scale-factor, 1)) !important; /* 🔥 从24px增加到32px，更大 */
  line-height: calc(32px / var(--system-scale-factor, 1)) !important; /* 保持与按钮高度一致 */
  color: white !important;
  text-align: center !important;
  width: 100% !important;
  height: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  font-weight: 900 !important; /* 🔥 从bold改为900，超粗字体 */
  font-family: "Arial Black", "Microsoft YaHei", monospace !important; /* 🔥 使用更粗的字体 */
  vertical-align: middle !important; /* 🔥 垂直居中对齐 */
  visibility: visible !important;
}

/* 时间管理器标签缩放 */
.ui_tpicker_time_label,
.ui_tpicker_hour_label,
.ui_tpicker_minute_label,
.ui_tpicker_second_label,
.ui_tpicker_proprate_label,
.ui-timepicker-div dt {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
  font-weight: bold !important;
  color: white !important;
}

/* 修复时间输入框缩放 */
.ui_tpicker_time_input {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  height: calc(24px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  padding: calc(4px / var(--system-scale-factor, 1)) calc(8px / var(--system-scale-factor, 1)) !important;
}

/* 修复滑动条缩放和布局 */
.ui_tpicker_hour_slider,
.ui_tpicker_minute_slider,
.ui_tpicker_second_slider,
.ui_tpicker_proprate_slider {
  width: calc(150px / var(--system-scale-factor, 1)) !important; /* 增加宽度 */
  height: calc(8px / var(--system-scale-factor, 1)) !important;
  display: inline-block !important;
  margin: 0 calc(8px / var(--system-scale-factor, 1)) !important;
  flex: 1 !important;
  min-width: calc(100px / var(--system-scale-factor, 1)) !important; /* 增加最小宽度 */
  background: #2196f3 !important; /* 确保蓝色背景 */
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 修复滑动条滑块 - 强化优先级 */
.ui-timepicker-div .ui-slider-handle,
.ui-datepicker .ui-slider-handle,
#ui-datepicker-div .ui-slider-handle,
.ui-slider-handle {
  width: calc(20px / var(--system-scale-factor, 1)) !important;
  height: calc(20px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(10px / var(--system-scale-factor, 1)) !important;
  margin-left: calc(-10px / var(--system-scale-factor, 1)) !important;
  margin-top: calc(-10px / var(--system-scale-factor, 1)) !important;
  border: calc(2px / var(--system-scale-factor, 1)) solid #fff !important;
  background: #ff9800 !important; /* 橙色点 */
  background-color: #ff9800 !important;
  top: 50% !important;
  cursor: pointer !important;
}

/* 🔥🔥🔥 修复滑动条轨道 - 超强化优先级 🔥🔥🔥 */
html body .ui-timepicker-div .ui-slider,
html body .ui-datepicker .ui-slider,
html body #ui-datepicker-div .ui-slider,
html body .ui-slider,
html .ui-timepicker-div .ui-slider,
html .ui-datepicker .ui-slider,
html #ui-datepicker-div .ui-slider,
html .ui-slider,
.ui-timepicker-div .ui-slider,
.ui-datepicker .ui-slider,
#ui-datepicker-div .ui-slider,
.ui-slider {
  background: #2196f3 !important; /* 🔥 强制蓝色横线 */
  background-color: #2196f3 !important;
  background-image: none !important; /* 🔥 移除任何背景图片 */
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  border: none !important;
  height: calc(8px / var(--system-scale-factor, 1)) !important;
  position: relative !important; /* 🔥 确保定位正确 */
  top: 50% !important; /* 🔥 垂直居中 */
  transform: translateY(-50%) !important; /* 🔥 精确居中 */
}

/* === 9. 最终完善和清理 === */

/* 确保所有时间管理器元素都有正确的字体 */
#ui-datepicker-div,
.ui-timepicker-div,
#ui-datepicker-div *,
.ui-timepicker-div * {
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 时间管理器按钮蓝色背景 - 只针对时间管理器 */
#ui-datepicker-div .ui-datepicker-current,
#ui-datepicker-div .ui-datepicker-pause,
#ui-datepicker-div .ui-datepicker-close,
#ui-datepicker-div .ui-datepicker-buttonpane button {
  background: #2196f3 !important; /* 蓝色背景 */
  background-color: #2196f3 !important;
  color: white !important;
  border: 1px solid #1976d2 !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(16px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

#ui-datepicker-div .ui-datepicker-current:hover,
#ui-datepicker-div .ui-datepicker-pause:hover,
#ui-datepicker-div .ui-datepicker-close:hover,
#ui-datepicker-div .ui-datepicker-buttonpane button:hover {
  background: #1976d2 !important; /* 深蓝色悬浮 */
  background-color: #1976d2 !important;
}

/* 确保时间管理器在不同屏幕尺寸下的响应式 */
@media (max-width: 768px) {
  #ui-datepicker-div {
    max-width: calc(320px / var(--system-scale-factor, 1)) !important;
    min-width: calc(280px / var(--system-scale-factor, 1)) !important;
  }

  .ui-timepicker-div {
    max-width: calc(300px / var(--system-scale-factor, 1)) !important;
    min-width: calc(260px / var(--system-scale-factor, 1)) !important;
  }
}

/* 时间管理器动画效果 */
#ui-datepicker-div,
.ui-timepicker-div {
  transition: all 0.2s ease !important;
}

.ui-datepicker-calendar td a {
  transition: background-color 0.15s ease !important;
}

/* 确保时间管理器在高DPI屏幕上的清晰度 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  #ui-datepicker-div,
  .ui-timepicker-div {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }
}

/* 修复日历导航按钮 */
.ui-datepicker-prev,
.ui-datepicker-next {
  width: calc(32px / var(--system-scale-factor, 1)) !important;
  height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  text-align: center !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
}

/* 🔧 专门修复时间管理器+-按钮对齐 */
.ui-timepicker-div .ui_tpicker_hour_slider,
.ui-timepicker-div .ui_tpicker_minute_slider,
.ui-timepicker-div .ui_tpicker_second_slider,
.ui-timepicker-div .ui-slider {
  margin: calc(8px / var(--system-scale-factor, 1)) 0 !important;
}

/* 🔥🔥🔥 时间调整按钮容器对齐 - 确保水平对齐 🔥🔥🔥 */
.ui-timepicker-div dt,
.ui-timepicker-div dd {
  display: flex !important;
  align-items: center !important; /* 🔥 垂直居中对齐 */
  justify-content: flex-start !important;
  margin: calc(2px / var(--system-scale-factor, 1)) 0 !important; /* 🔥 减少上下间距 */
  gap: calc(12px / var(--system-scale-factor, 1)) !important; /* 🔥 增加左右间距 */
  height: calc(40px / var(--system-scale-factor, 1)) !important; /* 🔥 固定高度确保对齐 */
  line-height: calc(40px / var(--system-scale-factor, 1)) !important; /* 🔥 行高与高度一致 */
}

/* 时间管理器标签样式 */
.ui-timepicker-div dt {
  min-width: calc(60px / var(--system-scale-factor, 1)) !important;
  flex-shrink: 0 !important; /* 防止标签被压缩 */
  text-align: left !important;
}

/* 🔥🔥🔥 时间管理器滑动条容器 - 确保水平对齐 🔥🔥🔥 */
.ui-timepicker-div dd {
  flex: 1 !important;
  display: flex !important;
  align-items: center !important; /* 🔥 垂直居中 */
  gap: calc(8px / var(--system-scale-factor, 1)) !important;
  height: calc(40px / var(--system-scale-factor, 1)) !important; /* 🔥 与dt相同高度 */
  line-height: calc(40px / var(--system-scale-factor, 1)) !important; /* 🔥 行高一致 */
}

/* 时间输入框和按钮组对齐 */
.ui-timepicker-div .ui_tpicker_time,
.ui-timepicker-div .ui_tpicker_hour,
.ui-timepicker-div .ui_tpicker_minute,
.ui-timepicker-div .ui_tpicker_second {
  display: flex !important;
  align-items: center !important;
  gap: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 修复日历标题 */
.ui-datepicker-title {
  font-size: calc(18px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-weight: bold !important;
}

/* 修复日历头部布局 */
.ui-datepicker-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: calc(8px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复设置菜单标题文字缩放 */
html body #settings-menu h4,
html body #settings-menu h5,
html body #settings-menu .menu-title,
html body #settings-menu h5.center-align,
#settings-menu h4,
#settings-menu h5,
#settings-menu .menu-title,
#settings-menu h5.center-align {
  font-size: calc(28px / var(--system-scale-factor, 1)) !important;
  line-height: calc(34px / var(--system-scale-factor, 1)) !important;
  margin: calc(24px / var(--system-scale-factor, 1)) 0 calc(18px / var(--system-scale-factor, 1)) 0 !important;
  color: white !important;
  font-weight: bold !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  text-align: center !important;
}

/* 🔥 修复非实时窗口缩放和颜色 */
html body #non-realtime-indicator,
#non-realtime-indicator {
  background-color: #ffeb3b !important;
  color: #333 !important;
  padding: calc(4px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(6px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  font-weight: bold !important;
  cursor: pointer !important;
  margin-left: calc(8px / var(--system-scale-factor, 1)) !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.3s ease !important;
  pointer-events: auto !important; /* 🔥 确保可以接收点击事件 */
  user-select: none !important; /* 🔥 防止文本选择 */
  z-index: 1000 !important; /* 🔥 确保在最上层 */
}

#non-realtime-indicator:hover {
  background-color: #fdd835 !important;
  color: #000 !important;
}

/* 🔥 修复顶部菜单传感器显示框高度问题 */
html body #sensor-selected-container,
#sensor-selected-container {
  height: calc(28px / var(--system-scale-factor, 1)) !important;
  line-height: calc(28px / var(--system-scale-factor, 1)) !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 calc(12px / var(--system-scale-factor, 1)) !important;
  margin: 0 calc(8px / var(--system-scale-factor, 1)) !important;
  box-sizing: border-box !important;
}

html body #sensor-selected,
#sensor-selected {
  font-size: calc(20px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 🔥 3. 强制修复sat-info-box顶部菜单和图标缩放 - 增大字体 */
#sat-info-title,
#sat-info-header {
  font-size: var(--base-font-size) !important;
  min-height: calc(50px / var(--system-scale-factor)) !important;
}

#sat-info-title-name {
  font-size: var(--large-font-size) !important;
  line-height: calc(28px / var(--system-scale-factor)) !important;
}

/* 🔥 关闭按钮尺寸 - 与sat-info-box.css保持一致，避免冲突 */
#sat-info-close-btn {
  width: calc(25px / var(--system-scale-factor)) !important;
  height: calc(25px / var(--system-scale-factor)) !important;
  font-size: calc(20px / var(--system-scale-factor)) !important;
  /* 🔥 防止全局悬停效果影响关闭按钮 */
  transform: translateY(-50%) !important; /* 保持原有定位，不受全局translateY(-1px)影响 */
}

#sat-add-watchlist,
#sat-remove-watchlist {
  width: calc(25px / var(--system-scale-factor)) !important;
  height: calc(25px / var(--system-scale-factor)) !important;
}

#sat-infobox-fi {
  width: calc(24px / var(--system-scale-factor)) !important;
  height: calc(18px / var(--system-scale-factor)) !important;
}

.sat-info-section-header {
  height: calc(25px / var(--system-division-factor, 1)) !important;
  font-size: calc(18px / var(--system-division-factor, 1)) !important;
  margin-bottom: calc(5px / var(--system-division-factor, 1)) !important;
}

.section-collapse {
  width: calc(25px / var(--system-division-factor, 1)) !important;
  height: calc(25px / var(--system-division-factor, 1)) !important;
  font-size: calc(20px / var(--system-division-factor, 1)) !important;
}

/* 4. 强制修复侧菜单设置图标和子菜单缩放 */
.side-menu-parent .material-icons,
#settings-menu .material-icons,
.side-menu .material-icons {
  font-size: calc(24px / var(--system-division-factor, 1)) !important;
  width: calc(24px / var(--system-division-factor, 1)) !important;
  height: calc(24px / var(--system-division-factor, 1)) !important;
}

/* 设置按钮特殊处理 */
.side-menu button .material-icons,
#settings-menu button .material-icons {
  font-size: calc(20px / var(--system-division-factor, 1)) !important;
  width: calc(20px / var(--system-division-factor, 1)) !important;
  height: calc(20px / var(--system-division-factor, 1)) !important;
}

/* 🔥🔥🔥 设置按钮现在也使用蓝色背景 - 已在上方统一设置 🔥🔥🔥 */

/* 重复规则已删除 - 使用统一按钮样式 */

/* 🔥🔥🔥 5. 修改sat-info-box宽度为原来的2/3 - 覆盖所有响应式CSS */
#sat-infobox {
  width: 100% !important;
  max-width: calc(300px / var(--system-division-factor, 1)) !important; /* 450px * 2/3 = 300px */
}

/* 🔥 强制覆盖responsive-sm.css中的宽度设置 */
@media (min-width: 640px) {
  #sat-infobox {
    width: calc(47% / var(--system-division-factor, 1)) !important; /* 70% * 2/3 ≈ 47% */
    max-width: calc(280px / var(--system-division-factor, 1)) !important; /* 420px * 2/3 = 280px */
  }
}

/* 🔥 强制覆盖responsive-md.css中的宽度设置 */
@media (min-width: 768px) {
  #sat-infobox {
    width: calc(33% / var(--system-division-factor, 1)) !important; /* 50% * 2/3 ≈ 33% */
    max-width: calc(333px / var(--system-division-factor, 1)) !important; /* 500px * 2/3 ≈ 333px */
  }
}

/* 🔥 大屏幕进一步优化宽度 */
@media (min-width: 1024px) {
  #sat-infobox {
    width: calc(27% / var(--system-scale-factor)) !important; /* 40% * 2/3 ≈ 27% */
    max-width: calc(367px / var(--system-scale-factor)) !important; /* 550px * 2/3 ≈ 367px */
  }
}

/* sat-info-box背景已通过修复根本原因解决，无需暴力覆盖 */



/* 🔥 时间线菜单使用全屏显示 */
#sensor-timeline-menu,
#satellite-timeline-menu {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  min-width: 100vw !important;
  min-height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  z-index: 9999 !important;
  overflow: auto !important;
  background: rgba(0, 0, 0, 0.9) !important;
}

/* 🔥 确保时间线菜单默认隐藏 */
#sensor-timeline-menu.start-hidden,
#satellite-timeline-menu.start-hidden {
  display: none !important;
  visibility: hidden !important;
}

/* 🔥 确保时间线菜单显示时正常显示 - 移除display规则，让全屏样式生效 */
#sensor-timeline-menu:not(.start-hidden),
#satellite-timeline-menu:not(.start-hidden) {
  visibility: visible !important;
}

/* 🔥 确保时间线菜单内容容器也是全屏 */
#sensor-timeline-menu-content,
#satellite-timeline-menu-content,
#sensor-timeline-menu .side-menu,
#satellite-timeline-menu .side-menu {
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: auto !important;
}

/* 🔥 强制修复ECI、ECF、RIC绘图窗口宽度 */
#eci-plots-menu,
#ecf-plots-menu,
#ric-plots-menu {
  width: calc(1300px / var(--system-scale-factor, 1)) !important;
  min-width: calc(1300px / var(--system-scale-factor, 1)) !important;
  max-width: none !important;
}

.plot-analysis-menu-normal {
  width: calc(1300px / var(--system-scale-factor, 1)) !important;
  min-width: calc(1300px / var(--system-scale-factor, 1)) !important;
  max-width: none !important;
}

/* 🔧 修复传感器时间线设置开关按钮背景色 */
#sensor-timeline-menu-secondary {
  z-index: 99999 !important; /* 🔥 确保设置弹出窗口在最前面 */
}

#sensor-timeline-menu-secondary .switch label {
  background: transparent !important;
  background-color: transparent !important;
}

#sensor-timeline-menu-secondary .switch label .lever {
  background-color: rgba(255, 255, 255, 0.3) !important;
}

#sensor-timeline-menu-secondary .switch label .lever:after {
  background-color: #fff !important;
}

#sensor-timeline-menu-secondary .switch label input[type=checkbox]:checked + .lever {
  background-color: rgba(33, 150, 243, 0.5) !important;
}

#sensor-timeline-menu-secondary .switch label input[type=checkbox]:checked + .lever:after {
  background-color: #2196F3 !important;
}

/* 🔥🔥🔥 修复传感器时间线鼠标悬停框被图表遮挡的问题 🔥🔥🔥 */
#sensor-timeline-menu .tooltip,
#sensor-timeline-menu [data-tooltip],
#sensor-timeline-menu .hover-info,
#sensor-timeline-menu .hover-box,
#sensor-timeline-canvas + .tooltip,
#sensor-timeline-canvas + [data-tooltip],
#sensor-timeline-canvas + .hover-info,
#sensor-timeline-canvas + .hover-box {
  z-index: 100000 !important; /* 🔥 确保悬停框在图表之上 */
  position: relative !important;
}

/* 修复传感器时间线canvas的z-index */
#sensor-timeline-canvas,
#sensor-timeline-canvas-static {
  z-index: 1 !important; /* 🔥 降低canvas的z-index */
}

/* 🔥🔥🔥 专门修复RPO页面的列表按钮和其他图标按钮 🔥🔥🔥 */
#proximityOps-menu-list-btn,
[id*="proximityOps"][id*="list"],
[id*="proximity"][id*="list"],
.material-icons:contains("view_list"),
button[title*="列表"],
button[title*="list"],
[class*="secondary-menu-icon"] {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  color: white !important;
  cursor: pointer !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 6. 强制修复所有子菜单缩放 */
.sat-infobox-links {
  font-size: calc(12px / var(--system-scale-factor)) !important;
  padding: calc(2px / var(--system-scale-factor)) calc(10px / var(--system-scale-factor)) !important;
}

/* 🔥🔥🔥 超高优先级sat-info-row样式 - 支持悬停效果和消除缝隙 🔥🔥🔥 */
html[lang] body #sat-infobox .sat-info-row,
html body #sat-infobox .sat-info-row,
body #sat-infobox .sat-info-row,
#sat-infobox .sat-info-row,
.sat-info-row {
  margin: 0px !important; /* 🔥 强制移除所有margin，避免缝隙 */
  margin-top: 0px !important;
  margin-bottom: 0px !important;
  margin-left: 0px !important;
  margin-right: 0px !important;
  padding: calc(2px / var(--system-scale-factor)) calc(10px / var(--system-scale-factor)) !important;
  min-height: calc(24px / var(--system-scale-factor)) !important;
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  justify-content: space-between !important;
  align-items: baseline !important;
  line-height: 1.4 !important;
  box-sizing: border-box !important;
  border-spacing: 0px !important;
  border-collapse: collapse !important;
  /* 🔥🔥🔥 关键修复：覆盖父容器的user-select设置，确保悬停事件正常 🔥🔥🔥 */
  user-select: auto !important; /* 🔥 允许文本选择，确保鼠标事件正常 */
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
  pointer-events: auto !important; /* 🔥 强制确保可以接收鼠标事件 */
  cursor: default !important; /* 🔥🔥🔥 关键修复：覆盖父容器的move光标 🔥🔥🔥 */
}

/* 🔥🔥🔥 超高优先级悬停效果 - 确保蓝色高亮显示 🔥🔥🔥 */
html[lang] body #sat-infobox .sat-info-row:hover,
html body #sat-infobox .sat-info-row:hover,
body #sat-infobox .sat-info-row:hover,
#sat-infobox .sat-info-row:hover,
.sat-info-row:hover {
  background: rgba(33, 150, 243, 0.3) !important; /* 🔥 蓝色高亮背景 */
  background-color: rgba(33, 150, 243, 0.3) !important;
  background-image: none !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.2s ease !important;
}

/* 🔥🔥🔥 可点击元素的更明显悬停效果 🔥🔥🔥 */
html[lang] body #sat-infobox .sat-info-row[onclick]:hover,
html[lang] body #sat-infobox .sat-info-row[data-tooltip]:hover,
html body #sat-infobox .sat-info-row[onclick]:hover,
html body #sat-infobox .sat-info-row[data-tooltip]:hover,
body #sat-infobox .sat-info-row[onclick]:hover,
body #sat-infobox .sat-info-row[data-tooltip]:hover,
#sat-infobox .sat-info-row[onclick]:hover,
#sat-infobox .sat-info-row[data-tooltip]:hover,
.sat-info-row[onclick]:hover,
.sat-info-row[data-tooltip]:hover {
  background: rgba(33, 150, 243, 0.5) !important; /* 更明显的蓝色高亮 */
  background-color: rgba(33, 150, 243, 0.5) !important;
  background-image: none !important;
  cursor: pointer !important;
}

/* 🔥🔥🔥 从transparent-menus.css复制的关键悬停效果 - 确保加载 🔥🔥🔥 */
/* 这是缺失的关键样式，必须确保被加载 */
html body #sat-infobox .sat-info-row:hover,
body #sat-infobox .sat-info-row:hover,
#sat-infobox .sat-info-row:hover {
  background: rgba(33, 150, 243, 0.3) !important;
  background-color: rgba(33, 150, 243, 0.3) !important;
  background-image: none !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.2s ease !important;
  /* 🔥 确保不受全局transform影响 */
  transform: none !important;
  /* 🔥 确保鼠标事件正常 */
  pointer-events: auto !important;
  cursor: default !important;
}

/* 🔥🔥🔥 专门排除sat-info-box不受全局按钮悬停效果影响 🔥🔥🔥 */
html body #sat-infobox .sat-info-row:hover,
html body #sat-infobox .sat-info-section-header:hover,
html body #sat-infobox .sat-infobox-links:hover,
body #sat-infobox .sat-info-row:hover,
body #sat-infobox .sat-info-section-header:hover,
body #sat-infobox .sat-infobox-links:hover,
#sat-infobox .sat-info-row:hover,
#sat-infobox .sat-info-section-header:hover,
#sat-infobox .sat-infobox-links:hover {
  /* 🔥 完全禁用全局transform效果 */
  transform: none !important;
  /* 🔥 确保正确的悬停背景 */
  background: rgba(33, 150, 243, 0.3) !important;
  background-color: rgba(33, 150, 243, 0.3) !important;
  /* 🔥 确保鼠标事件正常 */
  pointer-events: auto !important;
}

/* 🔥🔥🔥 7. 强制修复鼠标悬停信息框(tooltip)缩放 */
[data-tooltip]:before {
  font-size: var(--base-font-size) !important;
  padding: calc(8px / var(--system-scale-factor)) !important;
  border-width: calc(3px / var(--system-scale-factor)) !important;
  width: calc(150px / var(--system-scale-factor)) !important;
  margin-left: calc(-75px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
  /* 🔥 修复tooltip重叠问题 */
  z-index: 9999 !important;
  margin-top: calc(-10px / var(--system-scale-factor)) !important; /* 向上偏移避免重叠 */
}

/* 🔥 特别修复sat-info-box中的tooltip */
#sat-infobox [data-tooltip]:before {
  z-index: 10000 !important; /* 更高的z-index */
  margin-top: calc(-25px / var(--system-scale-factor)) !important; /* 🔥 更大的向上偏移，确保完全显示 */
  background: rgba(0, 0, 0, 0.9) !important; /* 更深的背景色确保可见性 */
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  /* 🔥 确保tooltip完全显示 */
  min-height: calc(30px / var(--system-scale-factor)) !important;
  white-space: nowrap !important; /* 防止文字换行 */
  overflow: visible !important;
  display: block !important;
  visibility: visible !important;
}

/* 🔥 修复悬停框位置 */
[data-tooltip][data-position='bottom']:before {
  bottom: calc(-120% / var(--system-scale-factor)) !important;
}

/* 🔥 修复UI tooltip缩放 */
.ui-tooltip {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) !important;
  max-width: calc(300px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复Material tooltip缩放 */
.material-tooltip {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 修复鼠标悬停信息框 - 透明模糊背景 🔥🔥🔥 */
#sat-hoverbox {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  padding: calc(12px / var(--system-scale-factor, 1)) !important; /* 增加内边距 */
  border-radius: calc(8px / var(--system-scale-factor, 1)) !important; /* 增加圆角 */
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
  /* 🔥 透明模糊背景效果 */
  background: rgba(0, 0, 0, 0.7) !important; /* 半透明黑色背景 */
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(15px) !important; /* 背景模糊效果 */
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important; /* 半透明白色边框 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important; /* 阴影效果 */
}

/* 🔥🔥🔥 正确的层级控制：让悬停信息框显示在菜单下面，而不是隐藏 🔥🔥🔥 */

/* 全屏菜单的z-index层级 */
#history-track-menu,
#geo-longitude-history-menu,
#waterfall-menu,
#lat-lon-menu {
  z-index: 2000 !important; /* 全屏菜单在最上层 */
}

/* 悬停信息框的基础层级 */
#sat-hoverbox {
  z-index: 1000 !important; /* 悬停信息框的基础层级 */
}

/* 当全屏菜单打开时，降低悬停信息框的层级，让它显示在菜单下面 */
#history-track-menu:not(.start-hidden) ~ * #sat-hoverbox,
#geo-longitude-history-menu:not(.start-hidden) ~ * #sat-hoverbox,
#waterfall-menu:not(.start-hidden) ~ * #sat-hoverbox,
#lat-lon-menu:not(.start-hidden) ~ * #sat-hoverbox,
body:has(#history-track-menu:not(.start-hidden)) #sat-hoverbox,
body:has(#geo-longitude-history-menu:not(.start-hidden)) #sat-hoverbox,
body:has(#waterfall-menu:not(.start-hidden)) #sat-hoverbox,
body:has(#lat-lon-menu:not(.start-hidden)) #sat-hoverbox {
  z-index: 500 !important; /* 降低层级，但仍然可见 */
}

/* 🔥🔥🔥 超强化悬停框背景 - 覆盖所有其他CSS文件 🔥🔥🔥 */
html body #sat-hoverbox,
body #sat-hoverbox,
#sat-hoverbox {
  background: rgba(0, 0, 0, 0.7) !important; /* 🔥 强制半透明黑色背景 */
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(15px) !important; /* 🔥 强制背景模糊 */
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important; /* 🔥 强制半透明边框 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important; /* 🔥 强制阴影 */
}

#sat-hoverbox span {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
  color: white !important; /* 🔥 确保文字为白色 */
}

/* 🔥 修复悬停框中的国旗缩放 */
#hoverbox-fi,
.fi {
  width: calc(24px / var(--system-scale-factor, 1)) !important;
  height: calc(18px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复系统提示信息(Toast)缩放 */
.toast {
  font-size: calc(18px / var(--system-scale-factor, 1)) !important;
  padding: calc(12px / var(--system-scale-factor, 1)) calc(16px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(10px / var(--system-scale-factor, 1)) !important;
  min-width: calc(300px / var(--system-scale-factor, 1)) !important;
  max-width: calc(400px / var(--system-scale-factor, 1)) !important;
  text-align: center !important;
  line-height: calc(24px / var(--system-scale-factor, 1)) !important;
}

#toast-container {
  top: calc(75px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 修复层级菜单缩放 */
#legend-hover-menu {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: calc(10px / var(--system-scale-factor, 1)) !important;
  top: calc(var(--top-menu-height) / var(--system-scale-factor, 1)) !important;
}

#legend-hover-menu li {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(24px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(5px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 颜色图例方块缩放 - 超高优先级 */
.Square-Box {
  width: calc(30px / var(--system-scale-factor, 1)) !important;
  height: calc(30px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(15px / var(--system-scale-factor, 1)) !important;
  margin-right: calc(20px / var(--system-scale-factor, 1)) !important;
  border-width: calc(2px / var(--system-scale-factor, 1)) !important;
  border-style: solid !important;
  box-shadow: 0 0px calc(6px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.2),
              0 0px calc(8px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.19) !important;
  cursor: pointer !important;
}

/* 🔥 设置菜单颜色按钮缩放 - 超高优先级，更小更紧凑布局 */
html body #settings-color-payload,
html body #settings-color-rocketBody,
html body #settings-color-debris,
html body #settings-color-inview,
html body #settings-color-missile,
html body #settings-color-missileInview,
html body #settings-color-special,
#settings-color-payload,
#settings-color-rocketBody,
#settings-color-debris,
#settings-color-inview,
#settings-color-missile,
#settings-color-missileInview,
#settings-color-special {
  width: calc(24px / var(--system-scale-factor, 1)) !important; /* 🔥🔥🔥 从28px减少到24px，更小 */
  height: calc(14px / var(--system-scale-factor, 1)) !important; /* 🔥🔥🔥 从20px减少到14px，更扁平 */
  border-radius: calc(12px / var(--system-scale-factor, 1)) calc(7px / var(--system-scale-factor, 1)) !important; /* 🔥🔥🔥 调整椭圆形圆角 */
  border-width: calc(1px / var(--system-scale-factor, 1)) !important;
  border-style: solid !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 0px calc(4px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.2),
              0 0px calc(6px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.19) !important; /* 🔥🔥🔥 减小阴影 */
  cursor: pointer !important;
  margin: calc(1px / var(--system-scale-factor, 1)) auto !important; /* 🔥🔥🔥 从2px减少到1px，更密集 */
  display: block !important;
}

/* 🔥🔥🔥 超级强制颜色按钮尺寸 - 最高优先级 🔥🔥🔥 */
html body div#settings-menu div#settings-colors input[type="color"],
html body #settings-menu #settings-colors input[type="color"],
#settings-menu #settings-colors input[type="color"],
.side-menu #settings-colors input[type="color"] {
  width: 20px !important;
  height: 12px !important;
  min-width: 20px !important;
  min-height: 12px !important;
  max-width: 20px !important;
  max-height: 12px !important;
  border-radius: 6px !important;
  margin: 1px auto !important;
  padding: 0 !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* 🔥 设置菜单颜色区域行间距优化 - 超密集布局 */
#settings-colors .row {
  margin-bottom: calc(2px / var(--system-scale-factor, 1)) !important; /* 🔥🔥🔥 从4px减少到2px */
}

#settings-colors .input-field {
  margin-top: 0 !important;
  margin-bottom: calc(1px / var(--system-scale-factor, 1)) !important; /* 🔥🔥🔥 从2px减少到1px */
}

#settings-colors p {
  margin: calc(1px / var(--system-scale-factor, 1)) 0 calc(2px / var(--system-scale-factor, 1)) 0 !important; /* 🔥🔥🔥 进一步减少文字间距 */
  font-size: calc(13px / var(--system-scale-factor, 1)) !important; /* 🔥🔥🔥 从14px减少到13px */
  line-height: calc(14px / var(--system-scale-factor, 1)) !important; /* 🔥🔥🔥 从16px减少到14px */
}

/* 🔥 颜色图例容器超密集布局 */
#settings-colors .col {
  padding: calc(1px / var(--system-scale-factor, 1)) !important; /* 🔥🔥🔥 从2px减少到1px */
}

/* 🔥 颜色图例标签文字超紧凑 */
#settings-colors label {
  margin-bottom: calc(1px / var(--system-scale-factor, 1)) !important; /* 🔥🔥🔥 从2px减少到1px */
  font-size: calc(12px / var(--system-scale-factor, 1)) !important; /* 🔥🔥🔥 从13px减少到12px */
  line-height: calc(13px / var(--system-scale-factor, 1)) !important; /* 🔥🔥🔥 从15px减少到13px */
}

/* 🔥🔥🔥 修复搜索结果窗口 - 根据搜索结果自动调整大小 🔥🔥🔥 */
#search-results {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
  padding-top: calc(10px / var(--system-scale-factor, 1)) !important;
  /* 🔥🔥🔥 根据搜索结果自动调整大小，最大高度为屏幕高度 */
  max-height: calc(100vh - var(--top-menu-height, 60px) - var(--bottom-menu-height, 120px)) !important;
  min-height: auto !important; /* 🔥🔥🔥 自动最小高度，根据内容调整 */
  height: auto !important; /* 🔥🔥🔥 自动高度，根据搜索结果数量调整 */
  overflow-y: auto !important; /* 🔥 当内容超过最大高度时允许垂直滚动 */
  overflow-x: hidden !important;
}

.search-result {
  padding: calc(6px / var(--system-scale-factor, 1)) calc(10px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
}

.truncate-search {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 搜索结果框响应式高度覆盖 - 自动调整大小 🔥🔥🔥 */
@media (max-height: 768px) {
  #search-results {
    max-height: calc(100vh - var(--top-menu-height, 60px) - var(--bottom-menu-height, 120px)) !important;
    min-height: auto !important; /* 🔥🔥🔥 自动最小高度 */
  }
}

@media (max-height: 600px) {
  #search-results {
    max-height: calc(100vh - var(--top-menu-height, 60px) - var(--bottom-menu-height, 120px)) !important;
    min-height: auto !important; /* 🔥🔥🔥 自动最小高度 */
  }
}

@media (max-height: 480px) {
  #search-results {
    max-height: calc(100vh - var(--top-menu-height, 60px) - var(--bottom-menu-height, 120px)) !important;
    min-height: auto !important; /* 🔥🔥🔥 自动最小高度 */
  }
}

/* 🔥 大屏幕搜索结果框优化 */
@media (min-width: 768px) {
  #search-results {
    max-height: calc(100vh - var(--top-menu-height, 60px) - var(--bottom-menu-height, 120px)) !important;
  }
}

/* 🔥🔥🔥 极坐标图窗口宽度增加一倍 - 覆盖侧边菜单默认宽度限制 🔥🔥🔥 */
html body #polar-plot-menu.side-menu-parent,
body #polar-plot-menu.side-menu-parent,
#polar-plot-menu.side-menu-parent {
  width: calc(900px / var(--system-scale-factor, 1)) !important; /* 从默认300px增加到900px */
  min-width: calc(900px / var(--system-scale-factor, 1)) !important; /* 从默认280px增加到900px */
  max-width: calc(2000px / var(--system-scale-factor, 1)) !important; /* 从默认400px增加到2000px */
}

/* 🔥 极坐标图内容区域优化 */
#polar-plot-content {
  width: 100% !important;
  max-width: none !important;
  padding: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 极坐标图canvas优化 */
#polar-plot {
  max-width: calc(800px / var(--system-scale-factor, 1)) !important; /* 设置canvas最大宽度 */
  width: auto !important;
  height: auto !important;
}

/* 🔥 统一传感器菜单列表字体 */
#sensor-list-content ul li {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

#sensor-list-content ul li span {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 统一badge（国家名称）字体 */
#sensor-list-content .badge,
#sensor-list-content .dark-blue-badge {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  font-weight: normal !important;
}

[data-tooltip][data-position='top']:before {
  bottom: calc(140% / var(--system-scale-factor)) !important;
}

/* 🔥🔥🔥 8. 强制修复登录界面相关元素缩放 */
.container,
.form-group,
.btn,
button,
input[type="text"],
input[type="password"],
input[type="email"] {
  font-size: var(--base-font-size) !important;
  line-height: calc(24px / var(--system-scale-factor)) !important;
}

.container h1,
.container h2 {
  font-size: var(--xlarge-font-size) !important;
  line-height: calc(32px / var(--system-scale-factor)) !important;
}

/* 🔥 修复登录按钮和输入框 */
.btn,
button {
  padding: calc(12px / var(--system-scale-factor)) calc(20px / var(--system-scale-factor)) !important;
  min-height: calc(44px / var(--system-scale-factor)) !important;
}

input[type="text"],
input[type="password"],
input[type="email"] {
  padding: calc(12px / var(--system-scale-factor)) !important;
  min-height: calc(44px / var(--system-scale-factor)) !important;
}

/* ========================================
 * 7. 特殊菜单处理
 * ======================================== */

/* 历史轨道菜单 - 水平布局 */
#history-track-menu .input-field label,
#geo-longitude-history-menu .input-field label {
  position: static !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
  display: inline-block !important;
  vertical-align: baseline !important;
  font-size: var(--menu-label-font-size) !important;
  line-height: calc(32px / var(--system-scale-factor)) !important;
  height: calc(32px / var(--system-scale-factor)) !important;
  color: #ffffff !important;
  margin: 0 calc(6px / var(--system-scale-factor)) 0 0 !important;
  padding: 0 !important;
  white-space: nowrap !important;
}

/* 历史轨道页面输入框 - 增加下划线宽度1px */
#history-track-menu input,
#history-norad-id,
#history-start-date,
#history-end-date {
  height: calc(32px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(8px / var(--system-scale-factor, 1)) !important;
  margin: 0 calc(16px / var(--system-scale-factor, 1)) 0 0 !important;
  display: inline-block !important;
  vertical-align: baseline !important;
  border: none !important;
  border-bottom: calc(1px / var(--system-scale-factor, 1)) solid rgba(255, 255, 255, 0.5) !important; /* 🔥 增加下划线，宽度1px，白色半透明 */
  background: transparent !important;
  color: white !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  outline: none !important;
  transition: border-bottom-color 0.2s ease !important;
}

/* 历史轨道页面输入框聚焦效果 */
#history-track-menu input:focus,
#history-norad-id:focus,
#history-start-date:focus,
#history-end-date:focus {
  border-bottom-color: rgba(255, 255, 255, 0.8) !important; /* 🔥 聚焦时下划线更亮 */
}

/* 历史轨道页面获取数据按钮 - 应用缩放 */
#history-get-data {
  height: calc(32px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(16px / var(--system-scale-factor, 1)) !important;
  margin-left: calc(8px / var(--system-scale-factor, 1)) !important;
  border: calc(1px / var(--system-scale-factor, 1)) solid #1976d2 !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  background: #2196f3 !important; /* 🔥 蓝色背景 */
  background-color: #2196f3 !important;
  color: white !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  box-sizing: border-box !important;
  vertical-align: baseline !important;
  display: inline-block !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

/* 🔥 历史轨道页面获取数据按钮悬停效果 - 蓝色主题 */
#history-get-data:hover {
  background: #1976d2 !important;
  background-color: #1976d2 !important;
  border-color: #1565c0 !important;
  transform: translateY(calc(-1px / var(--system-scale-factor, 1))) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* 🔥🔥🔥 历史经度页面输入框 - 与历史轨道页面使用完全相同的简单规则 🔥🔥🔥 */
#geo-longitude-history-menu input,
#geo-longitude-norad-id,
#geo-longitude-start-date,
#geo-longitude-end-date,
#geo-longitude-range {
  height: calc(32px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(8px / var(--system-scale-factor, 1)) !important;
  margin: 0 calc(16px / var(--system-scale-factor, 1)) 0 0 !important;
  display: inline-block !important;
  vertical-align: baseline !important;
  border: none !important;
  border-bottom: calc(1px / var(--system-scale-factor, 1)) solid rgba(255, 255, 255, 0.5) !important;
  background: transparent !important;
  color: white !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  outline: none !important;
  text-align: center !important; /* 🔥 输入框文字居中 */
}

/* 历史经度页面输入框聚焦效果 */
#geo-longitude-history-menu input:focus,
#geo-longitude-norad-id:focus,
#geo-longitude-start-date:focus,
#geo-longitude-end-date:focus,
#geo-longitude-range:focus {
  border-bottom-color: rgba(255, 255, 255, 0.8) !important; /* 🔥 聚焦时下划线更亮 */
}

/* 🔥 删除专门的自动填充规则，使用全局规则（style.css第49-67行）*/

/* 历史经度页面下载按钮悬停效果 - 与获取数据按钮一致的深蓝色 */
#geo-longitude-download-btn:hover {
  background: #1976d2 !important; /* 🔥 与获取数据按钮相同的深蓝色悬停背景 */
  background-color: #1976d2 !important;
  border: 2px solid #1565c0 !important; /* 🔥 更深的蓝色边框 */
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* 🔥 删除重复的强制规则，避免冲突 */

/* 🔥🔥🔥 移除多余的focus状态规则 - 让历史经度输入框像历史轨道一样保持透明 🔥🔥🔥 */

/* 🔥 删除复杂的自动填充规则，使用全局CSS规则 */

/* 🔥 删除特殊date处理规则，使用统一的简单规则 */

/* 🔥 date输入框的内部元素也要透明 */
html body input[type="date"]#geo-longitude-start-date::-webkit-datetime-edit,
html body input[type="date"]#geo-longitude-end-date::-webkit-datetime-edit,
html body input[type="date"]#history-start-date::-webkit-datetime-edit,
html body input[type="date"]#history-end-date::-webkit-datetime-edit,
input[type="date"]#geo-longitude-start-date::-webkit-datetime-edit,
input[type="date"]#geo-longitude-end-date::-webkit-datetime-edit,
input[type="date"]#history-start-date::-webkit-datetime-edit,
input[type="date"]#history-end-date::-webkit-datetime-edit {
  background: transparent !important;
  background-color: transparent !important;
  color: white !important;
}

/* DOP菜单页面输入框 - 增加白色下划线 */
#dops-menu input,
#dops-lat,
#dops-lon,
#dops-alt,
#dops-el {
  border: none !important;
  border-bottom: calc(1px / var(--system-scale-factor, 1)) solid rgba(255, 255, 255, 0.5) !important; /* 🔥 增加白色下划线 */
  background: transparent !important;
  color: white !important;
  outline: none !important;
  transition: border-bottom-color 0.2s ease !important;
}

/* DOP菜单页面输入框聚焦效果 */
#dops-menu input:focus,
#dops-lat:focus,
#dops-lon:focus,
#dops-alt:focus,
#dops-el:focus {
  border-bottom-color: rgba(255, 255, 255, 0.8) !important; /* 🔥 聚焦时下划线更亮 */
}

/* 修复历史页面标签缩放 */
#history-track-menu label,
#geo-longitude-history-menu label {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  color: #b3e5fc !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  margin-bottom: calc(5px / var(--system-scale-factor, 1)) !important;
  display: block !important;
}

/* 🔥🔥🔥 修复历史轨道和经度页面图标按钮缩放和位置 🔥🔥🔥 */
#history-track-close-btn,
#geo-longitude-history-close-btn {
  width: calc(50px / var(--system-scale-factor, 1)) !important;
  height: calc(50px / var(--system-scale-factor, 1)) !important;
  font-size: calc(32px / var(--system-scale-factor, 1)) !important;
  top: calc(20px / var(--system-scale-factor, 1)) !important;
  right: calc(20px / var(--system-scale-factor, 1)) !important;
  position: fixed !important;
  z-index: 99999 !important;
}

/* 🔥🔥🔥 历史轨道页面参数按钮容器 🔥🔥🔥 */
#param-buttons-container {
  /* 移除强制显示，让JavaScript控制显示时机 */
  flex-wrap: wrap !important;
  justify-content: flex-end !important;
  align-items: center !important;
  gap: calc(6px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 强制隐藏参数按钮容器，直到JavaScript显示 🔥🔥🔥 */
#param-buttons-container:not(.js-show) {
  display: none !important;
  visibility: hidden !important;
}

/* 🔥🔥🔥 历史轨道页面卫星按钮容器 🔥🔥🔥 */
#satellite-buttons-container {
  /* 移除强制显示，让JavaScript控制显示时机 */
  justify-content: flex-start !important;
  align-items: center !important;
  gap: calc(6px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 修复历史页面高度和层级 🔥🔥🔥 */
#history-track-menu,
#geo-longitude-history-menu {
  height: auto !important;
  max-height: none !important;
  overflow-y: visible !important;
  z-index: 1500 !important; /* 🔥 确保历史菜单在前面 */
}

/* 🔥 确保历史菜单内部元素层级正确 */
#history-track-menu .side-menu,
#geo-longitude-history-menu .side-menu {
  z-index: 1501 !important;
  position: relative !important;
}

/* 🔥🔥🔥 历史轨道图表容器 - 确保在前面显示 🔥🔥🔥 */
#history-chart-container {
  height: 800px !important;
  width: 100% !important;
  margin: 10px 0 !important;
  position: relative !important;
  z-index: 1502 !important; /* 🔥 确保图表在历史菜单内部最前面 */
  background: rgba(0, 0, 0, 0.8) !important; /* 🔥 添加背景确保可见 */
}

/* 🔥 历史经度图表容器 */
#geo-longitude-chart-container {
  height: 800px !important;
  width: 100% !important;
  margin: 10px 0 !important;
  position: relative !important;
}

/* 🔥🔥🔥 历史轨道图表容器和画布 - 确保可见 🔥🔥🔥 */
#history-results {
  position: relative !important;
  z-index: 1 !important;
  margin-bottom: calc(100px / var(--system-scale-factor, 1)) !important; /* 🔥 确保底部有足够空间 */
}

#history-chart-container {
  position: relative !important;
  z-index: 2 !important;
  overflow: visible !important;
  background: rgba(0, 0, 0, 0.9) !important;
  border: calc(2px / var(--system-scale-factor, 1)) solid rgba(255, 255, 255, 0.7) !important;
  border-radius: calc(8px / var(--system-scale-factor, 1)) !important;
}

#history-chart {
  width: 100% !important;
  height: calc(100% - 40px) !important;
  display: block !important;
  visibility: visible !important;
  position: relative !important;
  z-index: 3 !important;
}

/* 🔥 历史经度图表画布 */
#geo-longitude-chart {
  width: 100% !important;
  height: calc(100% - 80px) !important;
  display: block !important;
  visibility: visible !important;
}

/* 🔥 修复历史结果容器 - 确保在前面显示，但不强制显示 */
#history-results {
  /* 移除强制显示，让JavaScript控制显示时机 */
  width: 100% !important;
  height: auto !important;
  overflow: visible !important;
  position: relative !important;
  z-index: 999 !important; /* 🔥 结果容器也要在前面 */
}

/* 🔥 修复历史经度结果容器 */
#geo-longitude-results {
  display: block !important;
  width: 100% !important;
  height: auto !important;
  overflow: visible !important;
}

/* 🔥 历史经度页面ECharts滑动控件 */
#geo-longitude-echarts-container {
  height: calc(80px / var(--system-scale-factor, 1)) !important;
  width: 100% !important;
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
}

#history-get-data,
#geo-longitude-get-data {
  height: calc(36px / var(--system-scale-factor, 1)) !important;
  line-height: calc(36px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(16px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  margin: calc(10px / var(--system-scale-factor, 1)) 0 !important;
}

#geo-longitude-download-btn {
  height: calc(36px / var(--system-scale-factor, 1)) !important;
  line-height: calc(36px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(16px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  margin: calc(10px / var(--system-scale-factor, 1)) 0 !important;
  background: #2196f3 !important; /* 🔥 与获取数据按钮相同的蓝色背景 */
  background-color: #2196f3 !important;
  background-image: none !important;
  border: 2px solid #1976d2 !important; /* 🔥 添加深蓝色边框 */
  color: white !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  border: 2px solid white !important;
  border-radius: 6px !important;
  background: rgba(0, 0, 0, 0.7) !important;
  color: white !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

/* 🔥🔥🔥 历史轨道页面参数按钮 - 完全使用内联样式，不需要CSS覆盖 🔥🔥🔥 */
/* 参数按钮的所有样式都在HTML内联样式中定义，包括颜色、边框、背景等 */

/* 🔥🔥🔥 卫星选择按钮 - 强制白色边框和文字 🔥🔥🔥 */
html body #satellite-buttons-container .satellite-btn,
html body #history-track-menu .satellite-btn,
html body .satellite-btn,
button.satellite-btn {
  background: rgba(0, 0, 0, 0.7) !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  background-image: none !important;
  padding: 6px 12px !important;
  font-size: 12px !important;
  min-width: 120px !important;
  border-radius: 4px !important;
  margin: 2px !important;
  height: 30px !important;
  line-height: 18px !important;
  display: inline-block !important;
  vertical-align: top !important;
  border: 2px solid white !important;
  cursor: pointer !important;
  transition: all 0.1s ease !important;
  user-select: none !important;
  outline: none !important;
  text-align: center !important;
  white-space: nowrap !important;
  color: white !important;
  box-shadow: none !important;
  text-shadow: none !important;
  font-weight: normal !important;
}

/* 🔥🔥🔥 卫星按钮选中状态 - 显示卫星数据，白色背景，黑色文字 🔥🔥🔥 */
#satellite-buttons-container .satellite-btn[data-selected="true"],
button.satellite-btn[data-selected="true"] {
  background: rgba(255, 255, 255, 0.95) !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  background-image: none !important;
  color: #333333 !important;
  border: 2px solid white !important;
  box-shadow: none !important;
  font-weight: 600 !important;
}

/* 🔥🔥🔥 卫星按钮未选中状态 - 隐藏卫星数据，暗色背景，白色文字 🔥🔥🔥 */
#satellite-buttons-container .satellite-btn[data-selected="false"],
button.satellite-btn[data-selected="false"] {
  background: rgba(0, 0, 0, 0.7) !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  background-image: none !important;
  color: white !important;
  border: 2px solid white !important;
  box-shadow: none !important;
  font-weight: normal !important;
}

/* 修复历史轨道页面标题缩放 */
#history-track-menu h5,
#geo-longitude-history-menu h5 {
  font-size: calc(24px / var(--system-scale-factor, 1)) !important;
  line-height: calc(30px / var(--system-scale-factor, 1)) !important;
  margin: calc(20px / var(--system-scale-factor, 1)) 0 calc(15px / var(--system-scale-factor, 1)) 0 !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 移除冲突的分隔线样式，由ultimate-fix.css统一处理 */
#history-track-menu .divider,
#geo-longitude-history-menu .divider {
  /* margin: calc(15px / var(--system-scale-factor, 1)) 0 !important; */
  /* height: calc(1px / var(--system-scale-factor, 1)) !important; */
}

/* 设置菜单间距优化已移至responsive-design.css统一管理 */

/* 🔥 修复侧边菜单选项间距缩放 */
.side-menu .row,
.side-menu .input-field,
.side-menu .switch,
.side-menu .col {
  margin-bottom: calc(20px / var(--system-scale-factor, 1)) !important;
  padding: calc(8px / var(--system-scale-factor, 1)) 0 !important;
}

.side-menu ul li {
  padding: calc(8px / var(--system-scale-factor, 1)) !important;
  margin: calc(4px / var(--system-scale-factor, 1)) 0 !important;
}

/* 移除冲突的侧边菜单分隔线样式，由ultimate-fix.css统一处理 */
.side-menu .divider {
  /* margin: calc(15px / var(--system-scale-factor, 1)) 0 !important; */
  /* height: calc(2px / var(--system-scale-factor, 1)) !important; */
}

/* 侧边菜单按钮样式已移至responsive-design.css统一管理 */

/* 🔥 修复历史经度页面内联样式的图标高度缩放 */
#geo-longitude-close-btn {
  width: calc(40px / var(--system-scale-factor, 1)) !important;
  height: calc(40px / var(--system-scale-factor, 1)) !important;
  font-size: calc(28px / var(--system-scale-factor, 1)) !important;
  top: calc(20px / var(--system-scale-factor, 1)) !important;
  right: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 修复历史经度页面表单元素缩放 */
#geo-longitude-form label {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  margin-right: calc(12px / var(--system-scale-factor, 1)) !important;
}

#geo-longitude-form input {
  height: calc(32px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(8px / var(--system-scale-factor, 1)) !important;
  margin-right: calc(16px / var(--system-scale-factor, 1)) !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: calc(1px / var(--system-scale-factor, 1)) solid rgba(255, 255, 255, 0.5) !important;
  color: white !important;
  outline: none !important;
  box-shadow: 0 0 0 30px transparent inset !important;
  -webkit-box-shadow: 0 0 0 30px transparent inset !important;
}

#geo-longitude-form div {
  margin-right: calc(24px / var(--system-scale-factor, 1)) !important;
}

#geo-longitude-get-data {
  height: calc(32px / var(--system-scale-factor, 1)) !important;
  line-height: calc(32px / var(--system-scale-factor, 1)) !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: 0 calc(16px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 🔥 历史经度页面图表容器已在上方统一设置自动缩放 */

#geo-longitude-stats-title {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  margin: calc(12px / var(--system-scale-factor, 1)) 0 calc(8px / var(--system-scale-factor, 1)) 0 !important;
}

/* 🔥 ECharts容器高度已在上方统一设置 */

/* 🔥 修复全屏图表菜单的缩放 - 使用反向缩放逻辑 */
#inc2lon-plots-menu,
#inc2alt-plots-menu,
#lat2lon-plots-menu,
#time2lon-plots-menu {
  /* 全屏容器不需要transform缩放，而是内部元素使用反向缩放 */
  width: 100vw !important;
  height: 100vh !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 9999 !important;
  background: rgba(0, 0, 0, 0.95) !important;
}

/* 修复全屏图表菜单的关闭按钮缩放和位置 */
#inc2lon-close-btn,
#inc2alt-close-btn,
#lat2lon-close-btn,
#time2lon-close-btn {
  width: calc(40px / var(--system-scale-factor, 1)) !important;
  height: calc(40px / var(--system-scale-factor, 1)) !important;
  font-size: calc(28px / var(--system-scale-factor, 1)) !important;
  top: calc(60px / var(--system-scale-factor, 1)) !important; /* 从20px改为60px，向下移动 */
  right: calc(20px / var(--system-scale-factor, 1)) !important;
  position: fixed !important;
  z-index: 10000 !important;
}

/* 修复全屏图表菜单的标题缩放 */
#inc2lon-plots-menu h5,
#inc2alt-plots-menu h5,
#lat2lon-plots-menu h5,
#time2lon-plots-menu h5 {
  font-size: calc(24px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(20px / var(--system-scale-factor, 1)) !important;
  color: white !important;
  text-align: center !important;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

/* 修复全屏图表菜单的内容区域缩放 */
#inc2lon-plots-menu #plot-analysis-content,
#inc2alt-plots-menu #plot-analysis-content,
#lat2lon-plots-menu #plot-analysis-content,
#time2lon-plots-menu #plot-analysis-content {
  width: 100vw !important;
  height: 100vh !important;
  padding: calc(20px / var(--system-scale-factor, 1)) !important;
  box-sizing: border-box !important;
}

/* 修复全屏图表容器缩放 */
#plot-analysis-chart-inc2lon,
#plot-analysis-chart-inc2alt,
#plot-analysis-chart-lat2lon,
#plot-analysis-chart-time2lon {
  width: 100% !important;
  height: calc(100vh - 100px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 系统性缩放修复 - 检查遗漏的控件 🔥🔥🔥 */

/* 修复Toast消息缩放 */
.toast,
#toast-container .toast {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: calc(15px / var(--system-scale-factor, 1)) !important;
  border-radius: 0 !important;
  min-height: calc(48px / var(--system-scale-factor, 1)) !important;
}

/* 修复搜索框缩放 */
#search,
.search-input,
#search-holder input {
  font-size: calc(16px / var(--system-division-factor, 1)) !important;
  height: calc(40px / var(--system-division-factor, 1)) !important;
  padding: calc(8px / var(--system-division-factor, 1)) calc(12px / var(--system-division-factor, 1)) !important;
  border: none !important; /* 强制移除所有边框 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important; /* 🔥 增强白色底线 */
  border-radius: 0 !important; /* 移除圆角 */
  background: transparent !important; /* 确保透明背景 */
  outline: none !important; /* 移除轮廓 */
  box-shadow: none !important; /* 移除阴影 */
}

/* 修复搜索结果缩放 */
.search-result,
.search-results li {
  font-size: calc(14px / var(--system-division-factor, 1)) !important;
  padding: calc(8px / var(--system-division-factor, 1)) calc(12px / var(--system-division-factor, 1)) !important;
  line-height: calc(20px / var(--system-division-factor, 1)) !important;
}

/* 修复右键菜单缩放 */
.context-menu,
.right-btn-menu {
  font-size: calc(14px / var(--system-division-factor, 1)) !important;
  border-radius: calc(4px / var(--system-division-factor, 1)) !important;
}

.context-menu li,
.right-btn-menu li {
  padding: calc(8px / var(--system-division-factor, 1)) calc(12px / var(--system-division-factor, 1)) !important;
  font-size: calc(14px / var(--system-division-factor, 1)) !important;
  line-height: calc(20px / var(--system-division-factor, 1)) !important;
}

/* 修复模态框缩放 */
.modal,
.modal-content {
  border-radius: calc(8px / var(--system-scale-factor, 1)) !important;
}

.modal-content {
  padding: calc(20px / var(--system-scale-factor, 1)) !important;
}

.modal h4,
.modal h5 {
  font-size: calc(24px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(16px / var(--system-scale-factor, 1)) !important;
}

.modal p,
.modal div {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(24px / var(--system-scale-factor, 1)) !important;
}



/* 修复顶部菜单缩放 */
.menu-item {
  font-size: calc(16px / var(--system-division-factor, 1)) !important;
  padding: calc(8px / var(--system-division-factor, 1)) calc(12px / var(--system-division-factor, 1)) !important;
  height: calc(var(--nav-bar-height) / var(--system-division-factor, 1)) !important;
}

/* 🔥 恢复原始底部菜单滑动系统，但默认显示 */

/* 确保底部菜单容器有正确的基础样式 */
#nav-footer {
  position: fixed !important;
  z-index: 100 !important;
  width: 100% !important;
}

/* 恢复原始滑动类，但修正定位值 */
.footer-slide-down {
  bottom: calc(-1 * var(--bottom-menu-height)) !important;
  /* 🔥 移除固定高度，允许拖动调整 */
  top: auto !important;
  transition: bottom 1s ease !important;
}

.footer-slide-up {
  bottom: 0px !important;
  top: auto !important;
  transition: bottom 1s ease !important;
}

.footer-slide-trans {
  transition: bottom 1s ease !important;
}

/* 确保底部菜单内容容器正确显示 */
#bottom-icons-container {
  width: 100% !important;
  height: var(--bottom-menu-height) !important;
  display: block !important;
  background: transparent !important;
  position: relative !important;
}

/* 🔥🔥🔥 底部菜单过滤器布局 - 专注于flex布局 🔥🔥🔥 */
#bottom-icons-filter {
  /* 核心flex布局 */
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  justify-content: flex-start !important;

  /* 布局属性 */
  width: var(--bottom-filter-width, 185px) !important;
  height: var(--bottom-menu-height, 120px) !important;
  float: left !important;
  padding: 10px !important;
  z-index: 11 !important;
  position: absolute !important;
  overflow-y: scroll !important;
}

/* 🔥🔥🔥 底部菜单核心布局 - 专注于grid布局，透明效果由transparency-unified.css处理 🔥🔥🔥 */
#bottom-icons {
  /* 核心grid布局 */
  display: grid !important;
  grid-template-columns: repeat(auto-fill, var(--bottom-icon-width, 115px)) !important;
  justify-content: center !important;

  /* 布局属性 */
  width: calc(100% - var(--bottom-filter-width, 185px)) !important;
  float: right !important;
  padding: 10px !important;
  row-gap: 5px !important;
  z-index: 11 !important;
  overflow-y: auto !important;
  max-height: var(--bottom-menu-height, 120px) !important;
}

/* 修复Logo缩放 */
#logo-primary {
  width: calc(4vw / var(--system-scale-factor, 1)) !important;
  max-width: calc(150px / var(--system-scale-factor, 1)) !important;
  min-width: calc(70px / var(--system-scale-factor, 1)) !important;
  top: calc((var(--top-menu-height) + 10px) / var(--system-scale-factor, 1)) !important;
  left: calc(10px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 终极底部菜单修复 - 文件末尾最高优先级 🔥🔥🔥 */
/* 这些规则必须在文件最后，确保覆盖所有其他CSS */

/* 底部菜单图标区域 - 横向grid布局 */
html[lang] body #bottom-icons,
html body #bottom-icons,
body #bottom-icons,
#bottom-icons {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, var(--bottom-icon-width, 115px)) !important;
  justify-content: center !important;
  width: calc(100% - var(--bottom-filter-width, 185px)) !important;
  float: right !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  padding: 10px !important;
  row-gap: 5px !important;
  z-index: 11 !important;
}

/* 底部菜单过滤器 - 左侧垂直布局 */
html[lang] body #bottom-icons-filter,
html body #bottom-icons-filter,
body #bottom-icons-filter,
#bottom-icons-filter {
  display: flex !important;
  flex-direction: column !important;
  width: var(--bottom-filter-width, 185px) !important;
  float: left !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  padding: 10px !important;
  z-index: 11 !important;
}

/* 底部菜单容器 - 修复：移除边框避免双重边框 */
html[lang] body #bottom-icons-container,
html body #bottom-icons-container,
body #bottom-icons-container,
#bottom-icons-container {
  display: block !important;
  width: 100% !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  position: relative !important;
  border: none !important; /* 🔥 移除边框，避免与nav-footer双重边框 */
  padding: 0 !important; /* 移除内边距 */
  margin: 0 !important; /* 移除外边距 */
  box-shadow: none !important; /* 强制移除阴影 */
}

/* 拖动区域样式 - 移除视觉干扰但保留功能 */
.drag-resize-handle {
  pointer-events: auto !important;
  z-index: 1000 !important;
  cursor: n-resize !important; /* 确保显示正确的光标 */
  background: transparent !important; /* 透明背景，移除视觉干扰 */
  border: none !important; /* 移除边框，避免粗阴影 */
  height: 4px !important; /* 保持高度用于拖动 */
}

/* 确保主画布区域的鼠标事件正常 */
#keeptrack-canvas {
  pointer-events: auto !important;
  z-index: 1 !important;
}

/* 页脚容器 */
html[lang] body #nav-footer,
html body #nav-footer,
body #nav-footer,
#nav-footer {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-top: 1px solid rgba(255, 255, 255, 0.8) !important; /* 添加1px白色顶部边框 */
  padding: 0 !important; /* 移除内边距 */
  margin: 0 !important; /* 移除外边距 */
  box-shadow: none !important; /* 强制移除阴影 */
}

/* 🔥🔥🔥 终极强制修复 - 使用最高优先级选择器 🔥🔥🔥 */
html[lang="en"] body footer div#nav-footer div#bottom-icons-container div#bottom-icons,
html[lang="en"] body div#nav-footer div#bottom-icons-container div#bottom-icons,
html[lang="en"] body div#bottom-icons-container div#bottom-icons,
html[lang="en"] body div#bottom-icons,
html[lang] body footer div#nav-footer div#bottom-icons-container div#bottom-icons,
html[lang] body div#nav-footer div#bottom-icons-container div#bottom-icons,
html[lang] body div#bottom-icons-container div#bottom-icons,
html[lang] body div#bottom-icons,
html body footer div#nav-footer div#bottom-icons-container div#bottom-icons,
html body div#nav-footer div#bottom-icons-container div#bottom-icons,
html body div#bottom-icons-container div#bottom-icons,
html body div#bottom-icons,
body footer div#nav-footer div#bottom-icons-container div#bottom-icons,
body div#nav-footer div#bottom-icons-container div#bottom-icons,
body div#bottom-icons-container div#bottom-icons,
body div#bottom-icons,
footer div#nav-footer div#bottom-icons-container div#bottom-icons,
div#nav-footer div#bottom-icons-container div#bottom-icons,
div#bottom-icons-container div#bottom-icons,
div#bottom-icons,
#nav-footer #bottom-icons-container #bottom-icons,
#bottom-icons-container #bottom-icons,
#bottom-icons {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, var(--bottom-icon-width, 115px)) !important;
  justify-content: center !important;
  width: calc(100% - var(--bottom-filter-width, 185px)) !important;
  float: right !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  padding: 10px !important;
  row-gap: 5px !important;
  z-index: 11 !important;
  overflow-y: auto !important;
  max-height: var(--bottom-menu-height, 120px) !important;
}

/* 修复表格缩放 */
table,
.table {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

table th,
table td,
.table th,
.table td {
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* 修复复选框和单选框缩放 */
input[type="checkbox"],
input[type="radio"] {
  width: calc(18px / var(--system-division-factor, 1)) !important;
  height: calc(18px / var(--system-division-factor, 1)) !important;
}

/* 标签缩放已移至responsive-design.css统一管理 */

/* 修复进度条缩放 */
.progress,
.progress-bar {
  height: calc(20px / var(--system-division-factor, 1)) !important;
  border-radius: calc(10px / var(--system-division-factor, 1)) !important;
}

/* 修复工具提示缩放 */
.tooltip,
[data-tooltip]:before {
  font-size: calc(12px / var(--system-scale-factor, 1)) !important;
  padding: calc(6px / var(--system-scale-factor, 1)) calc(8px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
}

/* 修复分页器缩放 */
.pagination,
.pagination li {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

.pagination li a {
  padding: calc(8px / var(--system-scale-factor, 1)) calc(12px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

/* 修复面包屑导航缩放 */
.breadcrumb {
  font-size: calc(14px / var(--system-division-factor, 1)) !important;
  padding: calc(8px / var(--system-division-factor, 1)) calc(12px / var(--system-division-factor, 1)) !important;
}

/* 修复徽章缩放 */
.badge,
.chip {
  font-size: calc(12px / var(--system-division-factor, 1)) !important;
  padding: calc(4px / var(--system-division-factor, 1)) calc(8px / var(--system-division-factor, 1)) !important;
  border-radius: calc(12px / var(--system-division-factor, 1)) !important;
}

/* 修复卡片缩放 */
.card {
  border-radius: calc(8px / var(--system-division-factor, 1)) !important;
}

.card-content {
  padding: calc(16px / var(--system-division-factor, 1)) !important;
}

.card-title {
  font-size: calc(20px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(12px / var(--system-scale-factor, 1)) !important;
}

/* 修复选项卡缩放 */
.tabs {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

.tabs .tab a {
  padding: calc(12px / var(--system-scale-factor, 1)) calc(16px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
}

/* 修复手风琴缩放 */
.collapsible-header {
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  padding: calc(12px / var(--system-scale-factor, 1)) calc(16px / var(--system-scale-factor, 1)) !important;
}

.collapsible-body {
  padding: calc(16px / var(--system-scale-factor, 1)) !important;
}

/* 修复侧边导航缩放 */
.sidenav {
  width: calc(300px / var(--system-division-factor, 1)) !important;
}

.sidenav li a {
  font-size: calc(16px / var(--system-division-factor, 1)) !important;
  padding: calc(12px / var(--system-division-factor, 1)) calc(16px / var(--system-division-factor, 1)) !important;
}





/* ========================================
 * 8. 自动填充样式修复
 * ======================================== */
[id$="-menu"] input:-webkit-autofill,
[id$="-menu"] input:-webkit-autofill:hover,
[id$="-menu"] input:-webkit-autofill:focus,
[id$="-menu"] input:-webkit-autofill:active {
  box-shadow: 0 0 0 30px transparent inset !important;
  -webkit-box-shadow: 0 0 0 30px transparent inset !important;
  -webkit-text-fill-color: white !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  transition: background-color 5000s ease-in-out 0s !important;
  animation: none !important;
}



/* 🔥🔥🔥 终极底部菜单修复 - 确保grid布局生效 🔥🔥🔥 */
/* 注意：透明效果已移至transparency-unified.css，这里只处理布局 */

#bottom-icons {
  /* 强制grid布局 */
  display: grid !important;
  grid-template-columns: repeat(auto-fill, var(--bottom-icon-width, 115px)) !important;
  justify-content: center !important;

  /* 布局属性 */
  width: calc(100% - var(--bottom-filter-width, 185px)) !important;
  float: right !important;
  padding: 10px !important;
  row-gap: 5px !important;
  z-index: 11 !important;
  overflow-y: auto !important;
  max-height: var(--bottom-menu-height, 120px) !important;
}

#bottom-icons-filter {
  /* 强制flex布局 */
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;

  /* 布局属性 */
  width: var(--bottom-filter-width, 185px) !important;
  height: var(--bottom-menu-height, 120px) !important;
  float: left !important;
  padding: 10px !important;
  z-index: 11 !important;
  position: absolute !important;
  overflow-y: scroll !important;
}

/* ========================================
 * 🔥🔥🔥🔥🔥 终极按钮背景修复 🔥🔥🔥🔥🔥
 * 这个规则必须放在最后，确保覆盖Materialize的#26a69a默认背景色
 * ======================================== */

/* 重复规则已删除 - 使用统一按钮样式 */

/* 🔥 按钮悬停状态 */
html body .side-menu .btn:hover,
html body .side-menu .btn-large:hover,
html body .side-menu .btn-small:hover,
html body .side-menu button:hover,
html body [id$="-menu"] .btn:hover,
html body [id$="-menu"] .btn-large:hover,
html body [id$="-menu"] .btn-small:hover,
html body [id$="-menu"] button:hover,
html body .side-menu .btn-ui:hover,
html body [id$="-menu"] .btn-ui:hover,
html body #settings-submit:hover,
html body #settings-reset:hover,
html body #reset-sensor-button:hover,
html body #cs-submit:hover,
html body #cs-replace:hover,
html body #cs-clear:hover,
html body #cs-geolocation:hover,
html body #cs-save:hover,
html body #cs-load:hover,
html body #cs-export:hover,
html body #cs-import:hover,
.side-menu .btn:not(.param-btn):not(.satellite-btn):hover,
.side-menu .btn-large:not(.param-btn):not(.satellite-btn):hover,
.side-menu .btn-small:not(.param-btn):not(.satellite-btn):hover,
.side-menu button:not(.param-btn):not(.satellite-btn):hover,
[id$="-menu"] .btn:not(.param-btn):not(.satellite-btn):hover,
[id$="-menu"] .btn-large:not(.param-btn):not(.satellite-btn):hover,
[id$="-menu"] .btn-small:not(.param-btn):not(.satellite-btn):hover,
[id$="-menu"] button:not(.param-btn):not(.satellite-btn):hover,
.side-menu .btn-ui:not(.param-btn):not(.satellite-btn):hover,
[id$="-menu"] .btn-ui:not(.param-btn):not(.satellite-btn):hover,
#settings-submit:hover,
#settings-reset:hover,
#reset-sensor-button:hover,
#cs-submit:hover,
#cs-replace:hover,
#cs-clear:hover,
#cs-geolocation:hover,
#cs-save:hover,
#cs-load:hover,
#cs-export:hover,
#cs-import:hover {
  background-color: rgba(255, 255, 255, 0.1) !important; /* 🔥 悬停时半透明白色背景 */
  background: rgba(255, 255, 255, 0.1) !important;
  background-image: none !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  color: white !important;
}

/* ========================================
 * 🔥🔥🔥🔥🔥 终极按钮修复 - 覆盖Materialize 🔥🔥🔥🔥🔥
 * 这个规则必须放在最后，确保覆盖Materialize的#26a69a默认背景色
 * ======================================== */

/* 🔥 使用最高优先级选择器覆盖Materialize按钮样式 - 排除颜色图例和顶部按钮 */
html body div.side-menu .btn:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body div.side-menu .btn-large:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body div.side-menu .btn-small:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body div.side-menu button:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body div[id$="-menu"] .btn:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body div[id$="-menu"] .btn-large:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body div[id$="-menu"] .btn-small:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body div[id$="-menu"] button:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body .side-menu .btn.btn-ui:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body [id$="-menu"] .btn.btn-ui:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body div.side-menu button.btn:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body div[id$="-menu"] button.btn:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body #settings-submit.btn,
html body #settings-reset.btn,
html body #reset-sensor-button.btn,
html body #cs-submit.btn,
html body #cs-replace.btn,
html body #cs-clear.btn,
html body #cs-geolocation.btn,
html body #cs-save.btn,
html body #cs-load.btn,
html body #cs-export.btn,
html body #cs-import.btn,
div.side-menu .btn:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
div.side-menu .btn-large:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
div.side-menu .btn-small:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
div.side-menu button:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
div[id$="-menu"] .btn:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
div[id$="-menu"] .btn-large:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
div[id$="-menu"] .btn-small:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
div[id$="-menu"] button:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
.side-menu .btn.btn-ui:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
[id$="-menu"] .btn.btn-ui:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
div.side-menu button.btn:not([id*="settings-color-"]):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
div[id$="-menu"] button.btn:not([id*="settings-color-"]):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
#settings-submit.btn,
#settings-reset.btn,
#reset-sensor-button.btn,
#cs-submit.btn,
#cs-replace.btn,
#cs-clear.btn,
#cs-geolocation.btn,
#cs-save.btn,
#cs-load.btn,
#cs-export.btn,
#cs-import.btn {
  background-color: #2196f3 !important; /* 🔥 恢复蓝色背景 */
  background: #2196f3 !important;
  background-image: none !important;
  border-color: #2196f3 !important;
  border: 1px solid #2196f3 !important;
  color: white !important;
}

/* 🔥 悬停状态 */
html body div.side-menu .btn:hover,
html body div.side-menu .btn-large:hover,
html body div.side-menu .btn-small:hover,
html body div.side-menu button:hover,
html body div[id$="-menu"] .btn:hover,
html body div[id$="-menu"] .btn-large:hover,
html body div[id$="-menu"] .btn-small:hover,
html body div[id$="-menu"] button:hover,
html body .side-menu .btn.btn-ui:hover,
html body [id$="-menu"] .btn.btn-ui:hover,
html body div.side-menu button.btn:hover,
html body div[id$="-menu"] button.btn:hover,
html body #settings-submit.btn:hover,
html body #settings-reset.btn:hover,
html body #reset-sensor-button.btn:hover,
html body #cs-submit.btn:hover,
html body #cs-replace.btn:hover,
html body #cs-clear.btn:hover,
html body #cs-geolocation.btn:hover,
html body #cs-save.btn:hover,
html body #cs-load.btn:hover,
html body #cs-export.btn:hover,
html body #cs-import.btn:hover,
div.side-menu .btn:hover,
div.side-menu .btn-large:hover,
div.side-menu .btn-small:hover,
div.side-menu button:hover,
div[id$="-menu"] .btn:hover,
div[id$="-menu"] .btn-large:hover,
div[id$="-menu"] .btn-small:hover,
div[id$="-menu"] button:hover,
.side-menu .btn.btn-ui:hover,
[id$="-menu"] .btn.btn-ui:hover,
div.side-menu button.btn:hover,
div[id$="-menu"] button.btn:hover,
#settings-submit.btn:hover,
#settings-reset.btn:hover,
#reset-sensor-button.btn:hover,
#cs-submit.btn:hover,
#cs-replace.btn:hover,
#cs-clear.btn:hover,
#cs-geolocation.btn:hover,
#cs-save.btn:hover,
#cs-load.btn:hover,
#cs-export.btn:hover,
#cs-import.btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important; /* 🔥 悬停时半透明白色背景 */
  background: rgba(255, 255, 255, 0.1) !important;
  background-image: none !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  border: 1px solid rgba(255, 255, 255, 0.5) !important;
  color: white !important;
}

/* ========================================
 * 🔥🔥🔥🔥🔥 终极颜色图例保护 - 保持原有颜色 🔥🔥🔥🔥🔥
 * ======================================== */

/* 颜色图例专用样式 - 独立于按钮系统 */
.color-legend-btn {
  width: calc(15px / var(--system-scale-factor, 1)) !important;
  height: calc(10px / var(--system-scale-factor, 1)) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: calc(2px / var(--system-scale-factor, 1)) !important;
  cursor: pointer !important;
  display: inline-block !important;
  margin: calc(2px / var(--system-scale-factor, 1)) !important;
  opacity: 1 !important;
  visibility: visible !important;
  /* 不设置背景色，让JavaScript设置 */
}

.color-legend-btn:hover {
  border-color: rgba(255, 255, 255, 0.6) !important;
  transform: scale(1.1) !important;
  transition: all 0.2s ease !important;
}

/* 颜色图例保护 - 只设置必要属性 */
#settings-color-payload,
#settings-color-rocketBody,
#settings-color-debris,
#settings-color-inview,
#settings-color-missile,
#settings-color-missileInview,
#settings-color-special {
  opacity: 1 !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  cursor: pointer !important;
  display: block !important;
  /* 不设置背景色，让JavaScript设置的颜色生效 */
}

/* ========================================
 * 按钮和颜色图例修复 - 最高优先级
 * ======================================== */

/* 按钮样式修复 - 排除图标按钮和设置按钮 */
html body .side-menu button[style*="transparent"]:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body .side-menu .btn[style*="transparent"]:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body [id$="-menu"] button[style*="transparent"]:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body [id$="-menu"] .btn[style*="transparent"]:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body .side-menu button:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body .side-menu .btn:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body .side-menu .btn-large:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body .side-menu .btn-small:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body [id$="-menu"] button:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body [id$="-menu"] .btn:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body [id$="-menu"] .btn-large:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body [id$="-menu"] .btn-small:not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not(.icon-btn),
html body #settings-submit,
html body #settings-reset,
html body #reset-sensor-button,
html body #cs-submit,
html body #cs-replace,
html body #cs-clear,
html body #cs-geolocation,
html body #cs-save,
html body #cs-load,
html body #cs-export,
html body #cs-import,
html body #createSat-submit,
html body #createSat-save,
html body #editSat-submit,
html body #editSat-newTLE,
html body #editSat-save,
html body #editSat-open,
html body #watchlist-save,
html body #watchlist-open,
html body #watchlist-clear,
html body .ui-slider-access button:not([id*="settings-color-"]),
.side-menu button:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not(.icon-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
.side-menu .btn:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not(.icon-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
.side-menu .btn-large:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not(.icon-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
.side-menu .btn-small:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not(.icon-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
[id$="-menu"] button:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not(.icon-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
[id$="-menu"] .btn:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not(.icon-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
[id$="-menu"] .btn-large:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not(.icon-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
[id$="-menu"] .btn-small:not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn):not(.icon-btn):not([id*="settings-btn"]):not([id*="close-btn"]):not([id*="download-btn"]),
#settings-submit,
#settings-reset,
#reset-sensor-button,
#cs-submit,
#cs-replace,
#cs-clear,
#cs-geolocation,
#cs-save,
#cs-load,
#cs-export,
#cs-import,
.ui-slider-access button:not([id*="settings-color-"]) {
  background: #2196f3 !important; /* 🔥 恢复蓝色背景 */
  background-color: #2196f3 !important;
  background-image: none !important;
  border: 1px solid #2196f3 !important; /* 🔥 蓝色边框 */
  color: white !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-block !important;
}

/* 🔥🔥🔥 终极颜色图例修复 - 超级暴力覆盖 🔥🔥🔥 */
html body #settings-color-payload[style*="transparent"],
html body #settings-color-rocketBody[style*="transparent"],
html body #settings-color-debris[style*="transparent"],
html body #settings-color-inview[style*="transparent"],
html body #settings-color-missile[style*="transparent"],
html body #settings-color-missileInview[style*="transparent"],
html body #settings-color-special[style*="transparent"],
html body #settings-color-payload,
html body #settings-color-rocketBody,
html body #settings-color-debris,
html body #settings-color-inview,
html body #settings-color-missile,
html body #settings-color-missileInview,
html body #settings-color-special,
#settings-color-payload,
#settings-color-rocketBody,
#settings-color-debris,
#settings-color-inview,
#settings-color-missile,
#settings-color-missileInview,
#settings-color-special {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  border-color: white !important;
  border-style: solid !important;
  border-width: calc(1px / var(--system-scale-factor, 1)) !important;
  cursor: pointer !important;
  /* 🔥 不强制设置背景色，让原有颜色显示 */
}

/* ========================================
 * 🔥🔥🔥🔥🔥 终极下拉框修复 - 暗色不透明背景 🔥🔥🔥🔥🔥
 * ======================================== */

/* 🔥 使用最高优先级选择器强制下拉框暗色不透明背景 */
html body div.side-menu .dropdown-content,
html body div[id$="-menu"] .dropdown-content,
html body .side-menu .dropdown-content,
html body [id$="-menu"] .dropdown-content,
html body .dropdown-content,
html body select + .dropdown-content,
html body .select-wrapper + .dropdown-content,
html body .input-field .dropdown-content,
div.side-menu .dropdown-content,
div[id$="-menu"] .dropdown-content,
.side-menu .dropdown-content,
[id$="-menu"] .dropdown-content,
.dropdown-content,
select + .dropdown-content,
.select-wrapper + .dropdown-content,
.input-field .dropdown-content {
  background: #000000 !important; /* 🔥 强制不透明黑色背景 */
  background-color: #000000 !important;
  background-image: none !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  opacity: 1 !important; /* 🔥 确保完全不透明 */
  border: 1px solid #333333 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5) !important;
}

/* 🔥 修复下拉框三角箭头为白色 */
html body .side-menu .select-wrapper .caret,
html body [id$="-menu"] .select-wrapper .caret,
.side-menu .select-wrapper .caret,
[id$="-menu"] .select-wrapper .caret,
.select-wrapper .caret {
  fill: white !important;
  color: white !important;
  border-top-color: white !important;
  border-color: white transparent transparent transparent !important;
}

/* 🔥 下拉框选项样式 */
html body div.side-menu .dropdown-content li,
html body div[id$="-menu"] .dropdown-content li,
html body .dropdown-content li,
div.side-menu .dropdown-content li,
div[id$="-menu"] .dropdown-content li,
.dropdown-content li {
  background: #000000 !important; /* 🔥 强制黑色背景 */
  background-color: #000000 !important;
  color: white !important;
}

/* 🔥 下拉框选项悬停状态 */
html body div.side-menu .dropdown-content li:hover,
html body div[id$="-menu"] .dropdown-content li:hover,
html body .dropdown-content li:hover,
div.side-menu .dropdown-content li:hover,
div[id$="-menu"] .dropdown-content li:hover,
.dropdown-content li:hover {
  background: #333333 !important; /* 🔥 悬停时深灰色 */
  background-color: #333333 !important;
  color: white !important;
}

/* 🔥🔥🔥 查找目标页面间距优化 - 减少各个选项的间距 🔥🔥🔥 */
#findByLooks-menu .row,
#findByLooks-content .row {
  margin-bottom: calc(8px / var(--system-scale-factor, 1)) !important;
  margin-top: 0 !important;
}

#findByLooks-menu .input-field,
#findByLooks-content .input-field {
  margin-top: calc(6px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(6px / var(--system-scale-factor, 1)) !important;
}

#findByLooks-menu .input-field label,
#findByLooks-content .input-field label {
  font-size: calc(12px / var(--system-scale-factor, 1)) !important;
  line-height: calc(16px / var(--system-scale-factor, 1)) !important;
}

#findByLooks-menu select,
#findByLooks-menu input,
#findByLooks-content select,
#findByLooks-content input {
  margin-bottom: calc(4px / var(--system-scale-factor, 1)) !important;
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 查找目标页面表单整体间距优化 🔥🔥🔥 */
#findByLooks-form {
  padding: calc(8px / var(--system-scale-factor, 1)) !important;
}

#findByLooks-form .row:first-child {
  margin-top: 0 !important;
}

#findByLooks-form .row:last-child {
  margin-bottom: calc(8px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 查找目标页面特殊元素间距优化 🔥🔥🔥 */
#findByLooks-menu h5 {
  margin-top: calc(10px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(10px / var(--system-scale-factor, 1)) !important;
}

#findByLooks-menu .btn,
#findByLooks-content .btn {
  margin-top: calc(8px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(8px / var(--system-scale-factor, 1)) !important;
}

/* 通用侧边栏菜单间距优化已移至responsive-design.css统一管理 */

/* 移除冲突的字体大小设置，由ultimate-fix.css统一处理 */
.side-menu .input-field label,
[id$="-menu"] .input-field label {
  /* font-size: calc(12px / var(--system-scale-factor, 1)) !important; */
  /* line-height: calc(16px / var(--system-scale-factor, 1)) !important; */
}

/* 侧边栏菜单标题样式已移至responsive-design.css统一管理 */

/* ========================================
 * 🔥🔥🔥🔥🔥 终极按钮居中修复 - 最高优先级 🔥🔥🔥🔥🔥
 * 这个规则必须放在最后，确保覆盖所有其他样式
 * ======================================== */

/* 🔥🔥🔥 修复center-align和center类容器 - 让按钮真正居中 🔥🔥🔥 */
html[lang] body .side-menu .center-align,
html[lang] body .side-menu .center,
html[lang] body [id$="-menu"] .center-align,
html[lang] body [id$="-menu"] .center,
html[lang] body div.side-menu .center-align,
html[lang] body div.side-menu .center,
html[lang] body div[id$="-menu"] .center-align,
html[lang] body div[id$="-menu"] .center {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  width: 100% !important;
}

/* 🔥🔥🔥 确保center-align容器中的按钮保持正确的显示方式 🔥🔥🔥 */
html[lang] body .side-menu .center-align .btn,
html[lang] body .side-menu .center .btn,
html[lang] body [id$="-menu"] .center-align .btn,
html[lang] body [id$="-menu"] .center .btn,
html[lang] body .side-menu .center-align button,
html[lang] body .side-menu .center button,
html[lang] body [id$="-menu"] .center-align button,
html[lang] body [id$="-menu"] .center button {
  margin: 0 auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
}

/* 🔥🔥🔥 强制所有侧边菜单输入框显示下划线 - 最高优先级 */
html[lang] body div [id$="-menu"] input[type="text"],
html[lang] body div [id$="-menu"] input[type="number"],
html[lang] body div [id$="-menu"] input[type="email"],
html[lang] body div [id$="-menu"] input[type="password"],
html[lang] body div [id$="-menu"] input[type="date"],
html[lang] body div [id$="-menu"] input[type="time"],
html[lang] body div [id$="-menu"] input[type="datetime-local"],
html[lang] body div [id$="-menu"] input[type="search"],
html[lang] body div [id$="-menu"] input[type="url"],
html[lang] body div [id$="-menu"] input[type="tel"],
html[lang] body div .side-menu input[type="text"],
html[lang] body div .side-menu input[type="number"],
html[lang] body div .side-menu input[type="email"],
html[lang] body div .side-menu input[type="password"],
html[lang] body div .side-menu input[type="date"],
html[lang] body div .side-menu input[type="time"],
html[lang] body div .side-menu input[type="datetime-local"],
html[lang] body div .side-menu input[type="search"],
html[lang] body div .side-menu input[type="url"],
html[lang] body div .side-menu input[type="tel"] {
  border: none !important;
  border-bottom: 1px solid var(--menu-border-color) !important;
  background: transparent !important;
  color: white !important;
  outline: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* 🔥 输入框聚焦时下划线更亮 - 最高优先级 */
html[lang] body div [id$="-menu"] input[type="text"]:focus,
html[lang] body div [id$="-menu"] input[type="number"]:focus,
html[lang] body div [id$="-menu"] input[type="email"]:focus,
html[lang] body div [id$="-menu"] input[type="password"]:focus,
html[lang] body div [id$="-menu"] input[type="date"]:focus,
html[lang] body div [id$="-menu"] input[type="time"]:focus,
html[lang] body div [id$="-menu"] input[type="datetime-local"]:focus,
html[lang] body div [id$="-menu"] input[type="search"]:focus,
html[lang] body div [id$="-menu"] input[type="url"]:focus,
html[lang] body div [id$="-menu"] input[type="tel"]:focus,
html[lang] body div .side-menu input[type="text"]:focus,
html[lang] body div .side-menu input[type="number"]:focus,
html[lang] body div .side-menu input[type="email"]:focus,
html[lang] body div .side-menu input[type="password"]:focus,
html[lang] body div .side-menu input[type="date"]:focus,
html[lang] body div .side-menu input[type="time"]:focus,
html[lang] body div .side-menu input[type="datetime-local"]:focus,
html[lang] body div .side-menu input[type="search"]:focus,
html[lang] body div .side-menu input[type="url"]:focus,
html[lang] body div .side-menu input[type="tel"]:focus {
  border-bottom: 1px solid rgba(255, 255, 255, 1.0) !important; /* 聚焦时完全不透明 */
}

/* 🔥🔥🔥 最终修复：完全防止关闭按钮受到全局悬停效果影响 🔥🔥🔥 */
/* 这个规则必须放在文件最后，确保最高优先级 */
html body #sat-info-close-btn,
html body span#sat-info-close-btn,
html body #sat-infobox #sat-info-close-btn,
html body div#sat-infobox #sat-info-close-btn,
html body div#sat-infobox span#sat-info-close-btn {
  /* 🔥 完全锁定位置和尺寸 */
  position: absolute !important;
  left: calc(5px / var(--system-scale-factor)) !important;
  top: calc(50% - 2.5px / var(--system-scale-factor)) !important;
  transform: translateY(-50%) !important;
  width: calc(25px / var(--system-scale-factor)) !important;
  height: calc(25px / var(--system-scale-factor)) !important;
  /* 🔥 防止任何悬停效果改变位置 */
  transition: color 0.2s ease, background-color 0.2s ease !important;
}

/* 🔥 关闭按钮悬停时也要锁定位置 */
html body #sat-info-close-btn:hover,
html body span#sat-info-close-btn:hover,
html body #sat-infobox #sat-info-close-btn:hover,
html body div#sat-infobox #sat-info-close-btn:hover,
html body div#sat-infobox span#sat-info-close-btn:hover {
  /* 🔥 悬停时位置完全不变 */
  position: absolute !important;
  left: calc(5px / var(--system-scale-factor)) !important;
  top: calc(50% - 2.5px / var(--system-scale-factor)) !important;
  transform: translateY(-50%) !important; /* 🔥 绝对不允许translateY(-1px) */
  width: calc(25px / var(--system-scale-factor)) !important;
  height: calc(25px / var(--system-scale-factor)) !important;
  /* 🔥 只允许颜色变化 */
  color: #ff4444 !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 🔥🔥🔥 卫星视场菜单最终强制修复 - 最高优先级 🔥🔥🔥 */
html body #satellite-fov-menu .input-field,
html body #satellite-fov-menu .input-field input,
html body #satellite-fov-menu .input-field input[type="text"],
html body #satellite-fov-menu .input-field input[type="number"],
html body #satellite-fov-menu form .input-field,
html body #satellite-fov-menu form .input-field input,
html body #satellite-fov-menu form .input-field input[type="text"],
html body #satellite-fov-menu form .input-field input[type="number"],
html body div#satellite-fov-menu .input-field,
html body div#satellite-fov-menu .input-field input,
html body div#satellite-fov-menu .input-field input[type="text"],
html body div#satellite-fov-menu .input-field input[type="number"],
html body div#satellite-fov-menu form .input-field,
html body div#satellite-fov-menu form .input-field input,
html body div#satellite-fov-menu form .input-field input[type="text"],
html body div#satellite-fov-menu form .input-field input[type="number"] {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.8) !important;
  border-radius: 0 !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  outline: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 🔥🔥🔥 卫星视场菜单标签最终强制修复 🔥🔥🔥 */
html body #satellite-fov-menu .input-field label,
html body #satellite-fov-menu form .input-field label,
html body div#satellite-fov-menu .input-field label,
html body div#satellite-fov-menu form .input-field label {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 🔥🔥🔥 卫星视场菜单聚焦状态 🔥🔥🔥 */
html body #satellite-fov-menu .input-field input:focus,
html body #satellite-fov-menu .input-field input[type="text"]:focus,
html body #satellite-fov-menu .input-field input[type="number"]:focus,
html body div#satellite-fov-menu .input-field input:focus,
html body div#satellite-fov-menu .input-field input[type="text"]:focus,
html body div#satellite-fov-menu .input-field input[type="number"]:focus {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 1.0) !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 🔥 移除重复的国旗规则 - 已在ultimate-fix.css中统一处理 */
/* 注释掉重复规则，避免CSS冲突 */
/*
html body #sat-infobox-fi,
html body span#sat-infobox-fi,
html body #sat-infobox #sat-infobox-fi,
html body div#sat-infobox #sat-infobox-fi,
html body div#sat-infobox span#sat-infobox-fi {
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
  background-color: transparent !important;
  width: calc(24px / var(--system-scale-factor)) !important;
  height: calc(18px / var(--system-scale-factor)) !important;
  position: absolute !important;
  right: calc(5px / var(--system-scale-factor)) !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 5 !important;
}
*/
