import { ToastMsgType } from '@app/interfaces';
import { Telemetry } from '@app/static/telemetry';
import { keepTrackApi } from '../keepTrackApi';
import { isThisNode } from '../static/isThisNode';

export class ErrorManager {
  private readonly ALLOW_DEBUG = false;
  private readonly ALLOW_LOG = false;
  private readonly ALLOW_INFO = true;
  private readonly ALLOW_WARN = true;

  constructor() {}

  error(e: Error, funcName: string, toastMsg?: string) {
    if (!isThisNode() && !Telemetry.isInitialized) {
      Telemetry.initialize(keepTrackApi.getRenderer().gl, keepTrackApi.getRenderer().domElement);
    }

    if (Telemetry.isInitialized) {
      Telemetry.sendErrorData(e, funcName);
    }

    // eslint-disable-next-line no-console
    console.error(e);

    toastMsg ??= e.message || 'Unknown error';

    // Max 1 error per 5 minutes
    // if (url !== '' && Date.now() - this.lastErrorTime > 1000 * 60 * 5) {
    //   window.open(url, '_blank');
    //   this.lastErrorTime = Date.now();
    // }

    const uiManagerInstance = keepTrackApi.getUiManager();

    uiManagerInstance.toast(toastMsg, ToastMsgType.error, true);

    if (isThisNode()) {
      throw e;
    }
  }

  warn(msg: string, isHideFromConsole = false) {
    if (this.ALLOW_WARN) {
      const uiManagerInstance = keepTrackApi.getUiManager();

      uiManagerInstance.toast(msg, ToastMsgType.serious, true);
    }

    if (!isHideFromConsole) {
      // eslint-disable-next-line no-console
      console.warn(msg);
      if (!isThisNode()) {
        // eslint-disable-next-line no-console
        console.trace();
      }
    }
  }

  info(msg: string) {
    if (this.ALLOW_INFO) {
      const uiManagerInstance = keepTrackApi.getUiManager();
      uiManagerInstance.toast(msg, ToastMsgType.normal, true);
    }
  }

  log(msg: string) {
    if (this.ALLOW_LOG) {
      const uiManagerInstance = keepTrackApi.getUiManager();
      uiManagerInstance.toast(msg, ToastMsgType.standby, true);
    }
  }

  debug(msg: string) {
    if (this.ALLOW_DEBUG) {
      const uiManagerInstance = keepTrackApi.getUiManager();
      uiManagerInstance.toast(msg, ToastMsgType.standby, true);
    }
  }
}

export const errorManagerInstance = new ErrorManager();
