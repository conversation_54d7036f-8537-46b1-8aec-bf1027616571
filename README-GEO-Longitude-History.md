# GEO卫星经度历史功能

## 功能概述

新增的GEO卫星经度历史功能允许用户查看和分析地球同步轨道(GEO)卫星的经度变化历史数据。

## 功能特点

### 1. 菜单和界面
- **菜单图标**: 使用与"倾角-高度图"相同的竖条线样式图标
- **菜单位置**: 位于底部工具栏，标签为"GEO经度历史"
- **界面布局**: 全屏界面，类似于"历史轨道数据"页面

### 2. 数据查询
- **NORAD编号输入**: 支持输入特定卫星的NORAD编号，留空则查询所有GEO卫星
- **日期范围**: 开始日期和结束日期输入，默认结束日期为当前日期，开始日期为6个月前
- **数据源**: 从ES数据库获取，与历史轨道页面使用相同的数据源
- **过滤条件**: 只查询object_type为"GEO"且subsat_long字段不为空的记录

### 3. 图表显示
- **坐标系统**:
  - 纵坐标(Y轴): 历史时间，顶部为最早时间，底部为现在时间
  - 横坐标(X轴): 经度值，范围-180°到+180°，支持无缝衔接
- **数据展示**:
  - 不同卫星用不同颜色的点和线条表示
  - 图表顶部显示卫星名称(NORAD编号)
  - 支持鼠标点击选择卫星，高亮显示选中卫星，其他卫星亮度降低到80%
- **交互功能**:
  - 鼠标缩放支持
  - 鼠标滑动支持
  - 经度轴-180°和+180°无缝衔接

### 4. 数据下载
- **下载按钮**: 位于界面右下角
- **下载格式**: JSON格式
- **文件命名**: `geo-longitude-history-YYYY-MM-DD.json`

## 技术实现

### 1. 前端插件
- **文件位置**: `src/plugins/geo-longitude-history/geo-longitude-history.ts`
- **插件ID**: `GeoLongitudeHistoryPlugin`
- **依赖**: 无特殊依赖
- **图标**: 复用waterfall2.png图标

### 2. 后端API
- **API路径**: `/api/es-history` (复用现有API)
- **文件位置**: `src/api/es-history.routes.ts` (已扩展支持GEO查询)
- **查询参数**:
  - `norad_id`: 可选，NORAD编号，支持多个用逗号分隔
  - `start`: 必需，开始日期(YYYY-MM-DD)
  - `end`: 必需，结束日期(YYYY-MM-DD)
  - `geo_only`: 设为true时只查询GEO卫星，支持无NORAD查询

### 3. 数据库查询
- **索引**: 使用与历史轨道相同的ES索引
- **查询字段**:
  - `norad_id`: 卫星编号
  - `time`: 时间戳
  - `subsat_long`: 卫星经度值
  - `object_type`: 目标类型(必须为"GEO")
  - `object_name`: 卫星名称
- **过滤条件**:
  - `object_type = "GEO"`
  - `subsat_long`字段存在且不为空
  - 时间范围过滤

## 使用方法

### 1. 打开功能
1. 在主界面底部工具栏找到"GEO经度历史"图标
2. 点击图标打开全屏界面

### 2. 查询数据
1. **查询所有GEO卫星**:
   - 保持NORAD编号输入框为空
   - 设置开始和结束日期
   - 点击"获取数据"按钮

2. **查询特定卫星**:
   - 在NORAD编号输入框中输入卫星编号
   - 设置开始和结束日期
   - 点击"获取数据"按钮

### 3. 图表交互
1. **选择卫星**: 点击图表中的数据点选择特定卫星
2. **缩放**: 使用鼠标滚轮进行缩放
3. **查看详情**: 鼠标悬停查看数据点详情

### 4. 下载数据
1. 查询数据后，右下角会出现"下载数据"按钮
2. 点击按钮下载JSON格式的查询结果

## 配置说明

### 1. 插件配置
在`src/settings/default-plugins.ts`中已添加:
```typescript
GeoLongitudeHistoryPlugin: {
  enabled: true,
  order: 268,
}
```

### 2. 本地化配置
在`src/locales/zh.json`中已添加:
```json
"GeoLongitudeHistoryPlugin": {
  "bottomIconLabel": "GEO经度历史",
  "title": "GEO卫星经度历史",
  "helpBody": "GEO卫星经度历史插件用于查看和分析地球同步轨道卫星的经度变化历史数据。"
}
```

## 测试

运行测试脚本验证API功能:
```bash
node test-geo-longitude-api.js
```

## 编译和部署

### 编译状态
✅ 所有TypeScript编译错误已修复
✅ 插件集成测试通过
✅ API路由正确注册

### 部署步骤
1. 确保ES数据库配置文件 `es-config.enc` 存在
2. 启动后端API服务器：`npm run start:api`
3. 启动前端开发服务器：`npm run dev`
4. 在浏览器中访问应用

## 注意事项

1. **数据依赖**: 需要ES数据库中有GEO卫星的历史数据
2. **性能考虑**: 大时间范围查询可能返回大量数据，建议合理设置查询范围
3. **数据质量**: 只显示object_type为"GEO"且经度值有效的记录
4. **浏览器兼容**: 使用Canvas绘图，需要现代浏览器支持

## 故障排除

### 常见问题
1. **插件不显示**: 检查插件是否在设置中启用
2. **API请求失败**: 检查ES数据库连接和配置
3. **图表不显示**: 检查浏览器控制台错误信息
4. **数据为空**: 确认查询时间范围内有GEO卫星数据

### 调试工具
- 使用 `test-geo-longitude-api.js` 测试API功能
- 使用 `test-plugin-integration.js` 验证插件集成
- 检查浏览器开发者工具的网络和控制台面板

## 后续优化

1. 添加更多图表交互功能(平移、详细提示等)
2. 支持更多数据导出格式(CSV、Excel等)
3. 添加数据统计和分析功能
4. 优化大数据量的渲染性能
5. 添加实时数据更新功能
