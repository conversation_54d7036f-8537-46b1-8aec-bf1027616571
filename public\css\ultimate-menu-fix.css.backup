/*
 * 终极菜单修复系统 - 一次性解决所有菜单样式问题
 * 此文件使用最高优先级，覆盖所有其他样式
 * 创建时间: 2025-01-16
 * 目标: 彻底统一所有菜单的标签位置和样式
 */

/* ========================================
 * 1. 最高优先级覆盖规则
 * ======================================== */

/* 🚀 超级高优先级选择器 - 强制覆盖所有样式 */
html body div[id$="-menu"]:not(#sat-infobox) .input-field > label:not(.switch label):not(.lever):not(.material-icons),
html body div[id$="-menu"]:not(#sat-infobox) .input-field.col > label:not(.switch label):not(.lever):not(.material-icons),
html body div[id$="-menu"]:not(#sat-infobox) .input-field[class*="col"] > label:not(.switch label):not(.lever):not(.material-icons),
html body div.side-menu-parent:not(#sat-infobox) .input-field > label:not(.switch label):not(.lever):not(.material-icons),
html body div.side-menu-parent:not(#sat-infobox) .input-field.col > label:not(.switch label):not(.lever):not(.material-icons),
html body div.side-menu-parent:not(#sat-infobox) .input-field[class*="col"] > label:not(.switch label):not(.lever):not(.material-icons),
html body [id$="-menu"]:not(#sat-infobox) .input-field label:not(.switch label):not(.lever):not(.material-icons),
html body .side-menu-parent:not(#sat-infobox) .input-field label:not(.switch label):not(.lever):not(.material-icons) {
    /* 🔥 强制16px字体 - 最高优先级 */
    position: absolute !important;
    top: -2px !important;  /* 向上移动2px，增加与输入框的距离 */
    left: 0 !important;

    /* 🔥🔥🔥 强制16px字体 - 绝对优先级 🔥🔥🔥 */
    font-size: 16px !important;
    font-size: 16px !important;
    font-size: 16px !important;
    font-weight: 400 !important;
    line-height: 1.2 !important;

    /* 🔥🔥🔥 强制颜色 🔥🔥🔥 */
    color: #b3e5fc !important;
    color: #b3e5fc !important;
    
    /* 其他属性 */
    pointer-events: none !important;
    transition: all 0.2s ease-out !important;
    transform-origin: 0 0 !important;
    z-index: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    transform: translateY(0) !important;
    cursor: default !important;
}

/* 激活状态的标签 */
html body div[id$="-menu"]:not(#sat-infobox) .input-field > label.active,
html body div[id$="-menu"]:not(#sat-infobox) .input-field.col > label.active,
html body div[id$="-menu"]:not(#sat-infobox) .input-field[class*="col"] > label.active,
html body div.side-menu-parent:not(#sat-infobox) .input-field > label.active,
html body div.side-menu-parent:not(#sat-infobox) .input-field.col > label.active,
html body div.side-menu-parent:not(#sat-infobox) .input-field[class*="col"] > label.active,
html body [id$="-menu"]:not(#sat-infobox) .input-field label.active,
html body .side-menu-parent:not(#sat-infobox) .input-field label.active,
html body div[id$="-menu"]:not(#sat-infobox) .input-field > input:focus + label,
html body div[id$="-menu"]:not(#sat-infobox) .input-field > input:not(:placeholder-shown) + label,
html body div[id$="-menu"]:not(#sat-infobox) .input-field > input[value]:not([value=""]) + label {
    transform: translateY(-0.7rem) scale(0.8) !important;
    top: 0 !important;
    color: #b3e5fc !important;
}

/* ========================================
 * 2. 输入框样式统一
 * ======================================== */

/* ✅ 输入框容器完美设置 - 配合40px高度 */
html body div[id$="-menu"]:not(#sat-infobox) .input-field,
html body div[id$="-menu"]:not(#sat-infobox) .input-field.col,
html body div[id$="-menu"]:not(#sat-infobox) .input-field[class*="col"],
html body div.side-menu-parent:not(#sat-infobox) .input-field,
html body div.side-menu-parent:not(#sat-infobox) .input-field.col,
html body div.side-menu-parent:not(#sat-infobox) .input-field[class*="col"] {
    position: relative !important;
    padding-top: 6px !important;  /* ✅ 配合35px高度的间距 */
    margin-bottom: 5px !important;  /* ✅ 缩短参数间距为一半 */
    min-height: auto !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* ✅ 输入框完美设置 - 35px高度 */
html body div[id$="-menu"]:not(#sat-infobox) .input-field input[type="text"],
html body div[id$="-menu"]:not(#sat-infobox) .input-field input[type="number"],
html body div[id$="-menu"]:not(#sat-infobox) .input-field input[type="email"],
html body div[id$="-menu"]:not(#sat-infobox) .input-field input[type="password"],
html body div.side-menu-parent:not(#sat-infobox) .input-field input[type="text"],
html body div.side-menu-parent:not(#sat-infobox) .input-field input[type="number"],
html body div.side-menu-parent:not(#sat-infobox) .input-field input[type="email"],
html body div.side-menu-parent:not(#sat-infobox) .input-field input[type="password"] {
    height: 35px !important;  /* ✅ 完美的35px高度 */
    margin-top: 3px !important;
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    font-size: 16px !important;  /* ✅ 输入框内容16px */
    line-height: 1.4 !important;
    border: none !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
    background: transparent !important;
    color: white !important;
    box-shadow: none !important;
}

/* ✅ 选择框完美设置 - 35px高度 */
html body div[id$="-menu"]:not(#sat-infobox) .input-field .select-wrapper input.select-dropdown,
html body div.side-menu-parent:not(#sat-infobox) .input-field .select-wrapper input.select-dropdown,
html body .input-field .select-wrapper input.select-dropdown,
html body .select-wrapper input.select-dropdown {
    height: 35px !important;  /* ✅ 强制35px高度 */
    min-height: 35px !important;  /* ✅ 最小高度也是35px */
    margin-top: 3px !important;
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    font-size: 16px !important;  /* ✅ 选择框内容16px - 与标签一致 */
    line-height: 1.4 !important;
    border: none !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
    background: transparent !important;
    color: white !important;
    opacity: 1 !important;
    text-shadow: none !important;
    box-sizing: border-box !important;
}

/* ========================================
 * 3. 特定菜单强制覆盖
 * ======================================== */

/* 针对特定菜单ID的强制覆盖 */
html body div[id="createSat-menu"] .input-field > label,
html body div[id="custom-sensor-menu"] .input-field > label,
html body div[id="history-track-menu"] .input-field > label,
html body div[id="geo-longitude-history-menu"] .input-field > label {
    position: absolute !important;
    top: 2px !important;
    left: 0 !important;
    font-size: 16px !important;
    color: #b3e5fc !important;
    transform: translateY(0) !important;
    cursor: default !important;
}

/* ========================================
 * 4. 终极覆盖规则 - 处理遗漏的标签
 * ======================================== */

/* 捕获所有可能遗漏的标签 */
html body [id$="-menu"]:not(#sat-infobox) label:not(.switch label):not(.lever):not(.material-icons),
html body .side-menu-parent:not(#sat-infobox) label:not(.switch label):not(.lever):not(.material-icons) {
    font-size: 18px !important;
    color: #b3e5fc !important;
    top: 2px !important;
    position: absolute !important;
}

/* ========================================
 * 5. 防止JavaScript覆盖
 * ======================================== */

/* 使用CSS变量标记，供JavaScript检测 */
[id$="-menu"]:not(#sat-infobox) {
    --ultimate-menu-fix-applied: true;
}

/* 强制覆盖任何内联样式 */
html body div[id$="-menu"]:not(#sat-infobox) .input-field > label[style] {
    top: 2px !important;
    position: absolute !important;
    font-size: 18px !important;
    color: #b3e5fc !important;
}

/* ========================================
 * 6. 对抗Materialize框架的原始规则
 * ======================================== */

/* 覆盖Materialize的 top: 100% 规则 */
html body div[id$="-menu"]:not(#sat-infobox) .input-field label:not(.active),
html body div.side-menu-parent:not(#sat-infobox) .input-field label:not(.active) {
    top: 2px !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* 确保即使Materialize重新初始化也不会改变 */
html body div[id$="-menu"]:not(#sat-infobox) .input-field label,
html body div.side-menu-parent:not(#sat-infobox) .input-field label {
    top: 2px !important;
    position: absolute !important;
    left: 0 !important;
    font-size: 18px !important;
    color: #b3e5fc !important;
    pointer-events: none !important;
    z-index: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    cursor: default !important;
    opacity: 1 !important;
    transition: all 0.2s ease-out !important;
    transform-origin: 0 0 !important;
}

/* 特别针对Materialize的.input-field.col规则 */
html body div[id$="-menu"]:not(#sat-infobox) .input-field.col label,
html body div.side-menu-parent:not(#sat-infobox) .input-field.col label {
    left: 0 !important;  /* 覆盖Materialize的left: 0.75rem */
    top: 2px !important;
}

/* ========================================
 * 7. 终极覆盖 - 使用属性选择器
 * ======================================== */

/* 覆盖所有可能的标签，无论类名如何 */
[id$="-menu"]:not(#sat-infobox) .input-field label[for],
[id$="-menu"]:not(#sat-infobox) .input-field label:not(.switch):not(.lever),
.side-menu-parent:not(#sat-infobox) .input-field label[for],
.side-menu-parent:not(#sat-infobox) .input-field label:not(.switch):not(.lever) {
    top: 2px !important;
    position: absolute !important;
    left: 0px !important;
    font-size: 18px !important;
    color: #b3e5fc !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
    transition: all 0.2s ease-out !important;
    transform-origin: 0 0 !important;
    z-index: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    cursor: default !important;
    pointer-events: none !important;
}

/* 激活状态的终极覆盖 */
[id$="-menu"]:not(#sat-infobox) .input-field label.active,
[id$="-menu"]:not(#sat-infobox) .input-field input:focus + label,
.side-menu-parent:not(#sat-infobox) .input-field label.active,
.side-menu-parent:not(#sat-infobox) .input-field input:focus + label {
    transform: translateY(-0.8rem) scale(0.85) !important;
    top: 0px !important;
    color: #b3e5fc !important;
}

/* 复选框字段特殊处理 */
[id$="-menu"]:not(#sat-infobox) .input-field:has(input[type="checkbox"]),
.side-menu-parent:not(#sat-infobox) .input-field:has(input[type="checkbox"]) {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    margin-bottom: 6px !important;
    position: relative !important;
}

/* 复选框标签特殊样式 */
[id$="-menu"]:not(#sat-infobox) .input-field input[type="checkbox"] + label,
.side-menu-parent:not(#sat-infobox) .input-field input[type="checkbox"] + label {
    font-size: 18px !important;
    font-weight: 400 !important;
    color: #b3e5fc !important;
    line-height: 1.2 !important;
    position: relative !important;
    top: auto !important;
    left: auto !important;
    padding-left: 35px !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* ========================================
 * 8. 关注列表特殊间距
 * ======================================== */

/* 关注列表中新增卫星输入框的特殊间距 */
#watchlist-menu #watchlist-new {
    margin-top: 20px !important;
}

/* 关注列表中新增卫星输入框容器的特殊间距 */
#watchlist-menu .input-field:has(#watchlist-new) {
    margin-top: 20px !important;
    padding-top: 10px !important;
}

/* 关注列表中包含新增卫星输入框的行增加上边距 */
#watchlist-menu .row:has(.input-field) {
    margin-top: 20px !important;
    padding-top: 10px !important;
}

/* ========================================
 * 9. ✅ 滑动开关最终设置
 * ======================================== */

/* 滑动开关容器 */
.switch {
    margin: 15px 0 !important;
    padding: 5px 0 !important;
}

/* ✅ 滑动开关标签 - 最终字体设置 */
.switch label {
    font-size: 18px !important;  /* ✅ 与输入框标签一致的18px */
    color: #b3e5fc !important;   /* ✅ 统一的浅蓝色 */
    display: flex !important;
    align-items: center !important;
    cursor: pointer !important;
    line-height: 1.4 !important;
    position: relative !important;
    top: auto !important;
    left: auto !important;
    transform: none !important;
    padding: 0 !important;
    margin: 0 !important;
    pointer-events: auto !important;  /* 确保标签可以接收点击事件 */
}

/* 滑动开关杆 */
.switch label .lever {
    margin: 0 15px !important;
    vertical-align: middle !important;
    flex-shrink: 0 !important;
    pointer-events: auto !important;  /* 确保lever可以接收点击事件 */
    cursor: pointer !important;
}

/* 滑动开关输入框 */
.switch label input[type="checkbox"] {
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    pointer-events: auto !important;  /* 确保可以接收点击事件 */
    z-index: 1 !important;
}

/* ========================================
 * 🔥🔥🔥 终极强制18px字体规则 🔥🔥🔥
 * ======================================== */

/* 超级暴力覆盖 - 所有标签都强制18px */
.input-field label {
    font-size: 18px !important;
    color: #b3e5fc !important;
    font-weight: 400 !important;
}

/* 自定义传感器菜单专用强制规则 */
#custom-sensor-menu .input-field label,
#custom-sensor-menu label {
    font-size: 18px !important;
    color: #b3e5fc !important;
    font-weight: 400 !important;
    top: 2px !important;
}

/* 所有菜单强制规则 */
[id$="-menu"] .input-field label,
[id$="-menu"] label {
    font-size: 18px !important;
    color: #b3e5fc !important;
    font-weight: 400 !important;
}

/* 终极暴力规则 - 覆盖一切 */
* .input-field label:not(.switch label):not(.lever):not(#history-track-menu label):not(#geo-longitude-history-menu label) {
    font-size: 18px !important;
    color: #b3e5fc !important;
    font-weight: 400 !important;
}

/* 🎯 历史轨道页面专用修复 - 使用中心对齐解决基线问题 */

/* 强制容器使用中心对齐 */
#history-track-form,
#history-track-form > div {
    display: flex !important;
    align-items: center !important;
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
}

/* 🎯 GEO经度历史页面专用修复 - 使用中心对齐解决基线问题 */

/* GEO经度历史页面顶部间距调整 */
#geo-longitude-history-menu {
    padding-top: 30px !important; /* 增加顶部间距，距离边框更远 */
}

#geo-longitude-form {
    margin-top: 10px !important; /* 表单额外的上边距 */
}

/* 强制容器使用中心对齐 */
#geo-longitude-form,
#geo-longitude-form > div {
    display: flex !important;
    align-items: center !important;
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
}

/* 重置Materialize对input的默认样式 */
#history-track-menu input,
#history-track-form input,
#history-norad-id,
#history-start-date,
#history-end-date {
    height: 32px !important;
    line-height: 32px !important;
    font-size: 16px !important;
    padding: 0 8px !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    border: none !important;
    border-bottom: 1px solid #9e9e9e !important;
    background-color: transparent !important;
    color: white !important;
    outline: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}

/* 重置Materialize对GEO经度历史页面input的默认样式 */
#geo-longitude-history-menu input,
#geo-longitude-form input,
#geo-longitude-norad-id,
#geo-longitude-start-date,
#geo-longitude-end-date,
#geo-longitude-range {
    height: 32px !important;
    line-height: 32px !important;
    font-size: 16px !important;
    padding: 0 8px !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    border: none !important;
    border-bottom: 1px solid #9e9e9e !important;
    background-color: transparent !important;
    color: white !important;
    outline: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}

/* 🔥🔥🔥 历史轨道页面专用覆盖规则 🔥🔥🔥 */
/* 使用超高优先级选择器，专门覆盖历史轨道页面的标签样式 */
html body div#history-track-menu form#history-track-form label,
html body div#history-track-menu label[for="history-norad-id"],
html body div#history-track-menu label[for="history-start-date"],
html body div#history-track-menu label[for="history-end-date"],
html body form#history-track-form label,
html body #history-track-form label {
    /* 🔥 强制重置为内联布局（水平排列） */
    position: static !important;
    top: auto !important;
    left: auto !important;
    transform: none !important;

    /* 🔥 内联布局样式 */
    display: inline-block !important;
    vertical-align: baseline !important;
    font-size: 16px !important;
    line-height: 32px !important;
    height: 32px !important;
    color: #ffffff !important;
    margin: 0 6px 0 0 !important; /* 减少右边距，让输入框更靠近标签 */
    padding: 0 !important;
    white-space: nowrap !important;
}

/* 🔥 历史轨道页面输入框样式调整 */
html body div#history-track-menu form#history-track-form input,
html body div#history-track-menu input[id="history-norad-id"],
html body div#history-track-menu input[id="history-start-date"],
html body div#history-track-menu input[id="history-end-date"],
html body form#history-track-form input,
html body #history-track-form input {
    margin-left: 0 !important; /* 移除输入框的左边距 */
    margin-right: 20px !important; /* 保持输入框之间的间距 */
    display: inline-block !important;
    vertical-align: baseline !important;
}

/* 确保按钮对齐 */
#history-track-menu button,
#history-track-form button,
#history-get-data {
    height: 32px !important;
    line-height: 32px !important;
    padding: 0 16px !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    display: inline-block !important;
}

/* 🔥🔥🔥 GEO经度历史页面专用覆盖规则 🔥🔥🔥 */
/* 使用超高优先级选择器，专门覆盖GEO经度历史页面的标签样式 */
html body div#geo-longitude-history-menu form#geo-longitude-form label,
html body div#geo-longitude-history-menu label[for="geo-longitude-norad-id"],
html body div#geo-longitude-history-menu label[for="geo-longitude-start-date"],
html body div#geo-longitude-history-menu label[for="geo-longitude-end-date"],
html body div#geo-longitude-history-menu label[for="geo-longitude-range"],
html body form#geo-longitude-form label,
html body #geo-longitude-form label {
    /* 🔥 强制重置为内联布局（水平排列） */
    position: static !important;
    top: auto !important;
    left: auto !important;
    transform: none !important;
    font-size: 16px !important;
    color: #ffffff !important;
    font-weight: normal !important;
    line-height: 32px !important;
    height: auto !important;
    display: inline-block !important;
    vertical-align: baseline !important;
    margin: 0 6px 0 0 !important; /* 减少右边距，让输入框更靠近标签 */
    padding: 0 !important;
    white-space: nowrap !important;
}

/* 🔥🔥🔥 GEO经度历史页面输入框样式调整 - 终极核弹级覆盖 🔥🔥🔥 */
* input#geo-longitude-norad-id,
* input#geo-longitude-start-date,
* input#geo-longitude-end-date,
* input#geo-longitude-range,
html body * input#geo-longitude-norad-id,
html body * input#geo-longitude-start-date,
html body * input#geo-longitude-end-date,
html body * input#geo-longitude-range,
html body div#geo-longitude-history-menu form#geo-longitude-form input,
html body div#geo-longitude-history-menu input[id="geo-longitude-norad-id"],
html body div#geo-longitude-history-menu input[id="geo-longitude-start-date"],
html body div#geo-longitude-history-menu input[id="geo-longitude-end-date"],
html body div#geo-longitude-history-menu input[id="geo-longitude-range"],
html body form#geo-longitude-form input,
html body #geo-longitude-form input,
input#geo-longitude-norad-id,
input#geo-longitude-start-date,
input#geo-longitude-end-date,
input#geo-longitude-range {
    margin-left: 0 !important; /* 移除输入框的左边距 */
    margin-right: 20px !important; /* 保持输入框之间的间距 */
    display: inline-block !important;
    vertical-align: baseline !important;
    height: 32px !important;
    line-height: 32px !important;
    font-size: 16px !important;
    padding: 0 8px !important;
    box-sizing: border-box !important;
    border: none !important;
    border-bottom: 1px solid #9e9e9e !important;
    background: transparent !important; /* 透明背景 */
    background-color: transparent !important; /* 透明背景 */
    background-image: none !important; /* 移除背景图片 */
    color: white !important; /* 白色文字 */
    outline: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

/* 🔥 GEO经度历史页面输入框所有状态的背景色覆盖 - 终极版本 */
html body div#geo-longitude-history-menu form#geo-longitude-form input,
html body div#geo-longitude-history-menu form#geo-longitude-form input:focus,
html body div#geo-longitude-history-menu form#geo-longitude-form input:active,
html body div#geo-longitude-history-menu form#geo-longitude-form input:valid,
html body div#geo-longitude-history-menu form#geo-longitude-form input:invalid,
html body div#geo-longitude-history-menu form#geo-longitude-form input:hover,
html body div#geo-longitude-history-menu form#geo-longitude-form input:visited,
html body div#geo-longitude-history-menu form#geo-longitude-form input:-webkit-autofill,
html body div#geo-longitude-history-menu form#geo-longitude-form input:-webkit-autofill:hover,
html body div#geo-longitude-history-menu form#geo-longitude-form input:-webkit-autofill:focus,
html body div#geo-longitude-history-menu form#geo-longitude-form input:-webkit-autofill:active,
html body div#geo-longitude-history-menu input[id="geo-longitude-norad-id"],
html body div#geo-longitude-history-menu input[id="geo-longitude-norad-id"]:focus,
html body div#geo-longitude-history-menu input[id="geo-longitude-norad-id"]:active,
html body div#geo-longitude-history-menu input[id="geo-longitude-norad-id"]:valid,
html body div#geo-longitude-history-menu input[id="geo-longitude-norad-id"]:hover,
html body div#geo-longitude-history-menu input[id="geo-longitude-norad-id"]:-webkit-autofill,
html body div#geo-longitude-history-menu input[id="geo-longitude-start-date"],
html body div#geo-longitude-history-menu input[id="geo-longitude-start-date"]:focus,
html body div#geo-longitude-history-menu input[id="geo-longitude-start-date"]:active,
html body div#geo-longitude-history-menu input[id="geo-longitude-start-date"]:valid,
html body div#geo-longitude-history-menu input[id="geo-longitude-start-date"]:hover,
html body div#geo-longitude-history-menu input[id="geo-longitude-start-date"]:-webkit-autofill,
html body div#geo-longitude-history-menu input[id="geo-longitude-end-date"],
html body div#geo-longitude-history-menu input[id="geo-longitude-end-date"]:focus,
html body div#geo-longitude-history-menu input[id="geo-longitude-end-date"]:active,
html body div#geo-longitude-history-menu input[id="geo-longitude-end-date"]:valid,
html body div#geo-longitude-history-menu input[id="geo-longitude-end-date"]:hover,
html body div#geo-longitude-history-menu input[id="geo-longitude-end-date"]:-webkit-autofill,
html body form#geo-longitude-form input,
html body form#geo-longitude-form input:focus,
html body form#geo-longitude-form input:active,
html body form#geo-longitude-form input:valid,
html body form#geo-longitude-form input:hover,
html body form#geo-longitude-form input:-webkit-autofill,
html body #geo-longitude-form input,
html body #geo-longitude-form input:focus,
html body #geo-longitude-form input:active,
html body #geo-longitude-form input:valid,
html body #geo-longitude-form input:hover,
html body #geo-longitude-form input:-webkit-autofill,
input#geo-longitude-norad-id,
input#geo-longitude-start-date,
input#geo-longitude-end-date,
input#geo-longitude-norad-id:focus,
input#geo-longitude-start-date:focus,
input#geo-longitude-end-date:focus,
input#geo-longitude-norad-id:active,
input#geo-longitude-start-date:active,
input#geo-longitude-end-date:active,
input#geo-longitude-norad-id:valid,
input#geo-longitude-start-date:valid,
input#geo-longitude-end-date:valid,
input#geo-longitude-norad-id:hover,
input#geo-longitude-start-date:hover,
input#geo-longitude-end-date:hover,
input#geo-longitude-norad-id:-webkit-autofill,
input#geo-longitude-start-date:-webkit-autofill,
input#geo-longitude-end-date:-webkit-autofill,
input#geo-longitude-norad-id:-webkit-autofill:hover,
input#geo-longitude-start-date:-webkit-autofill:hover,
input#geo-longitude-end-date:-webkit-autofill:hover,
input#geo-longitude-norad-id:-webkit-autofill:focus,
input#geo-longitude-start-date:-webkit-autofill:focus,
input#geo-longitude-end-date:-webkit-autofill:focus,
input#geo-longitude-norad-id:-webkit-autofill:active,
input#geo-longitude-start-date:-webkit-autofill:active,
input#geo-longitude-end-date:-webkit-autofill:active {
    background-color: transparent !important;
    background: transparent !important;
    background-image: none !important;
    -webkit-box-shadow: 0 0 0 1000px transparent inset !important; /* 覆盖自动填充背景 */
    box-shadow: 0 0 0 1000px transparent inset !important;
    border-bottom: 1px solid #9e9e9e !important; /* 保持原来的灰色底边框 */
    -webkit-text-fill-color: white !important; /* 覆盖自动填充文字颜色 */
    transition: none !important; /* 禁用过渡动画 */
    animation: none !important; /* 禁用动画 */
}

/* 🌙 GEO经度历史页面placeholder文字变暗 - 类似搜索框样式 */
input#geo-longitude-norad-id::placeholder,
input#geo-longitude-start-date::placeholder,
input#geo-longitude-end-date::placeholder,
input#geo-longitude-range::placeholder,
input#geo-longitude-norad-id::-webkit-input-placeholder,
input#geo-longitude-start-date::-webkit-input-placeholder,
input#geo-longitude-end-date::-webkit-input-placeholder,
input#geo-longitude-range::-webkit-input-placeholder,
input#geo-longitude-norad-id::-moz-placeholder,
input#geo-longitude-start-date::-moz-placeholder,
input#geo-longitude-end-date::-moz-placeholder,
input#geo-longitude-range::-moz-placeholder,
input#geo-longitude-norad-id:-ms-input-placeholder,
input#geo-longitude-start-date:-ms-input-placeholder,
input#geo-longitude-end-date:-ms-input-placeholder,
input#geo-longitude-range:-ms-input-placeholder {
    color: #888888 !important; /* 暗灰色，类似搜索框 */
    opacity: 0.8 !important; /* 适中的透明度 */
    font-size: 14px !important; /* 14px字体大小 */
}

/* 🔥🔥🔥 终极解决方案：直接复制历史轨道页面的成功规则 🔥🔥🔥 */
/* 完全按照 style.css 中的全局规则来处理 GEO 经度历史页面 */

/* 针对 GEO 经度历史页面的输入框，使用与全局规则完全相同的处理方式 */
div#geo-longitude-history-menu input:-webkit-autofill,
div#geo-longitude-history-menu input:-webkit-autofill:hover,
div#geo-longitude-history-menu input:-webkit-autofill:focus,
div#geo-longitude-history-menu input:-webkit-autofill:active {
    /* 完全复制 style.css 中的成功规则 */
    box-shadow: 0 0 0 30px #1a1a1a inset !important;
    -webkit-box-shadow: 0 0 0 30px #1a1a1a inset !important;
    -webkit-text-fill-color: white !important;
    transition: none !important;
    animation: none !important;
}

div#geo-longitude-history-menu input:-webkit-autofill {
    /* 完全复制 style.css 中的成功规则 */
    -webkit-text-fill-color: white !important;
    font-size: 16px;
}

/* 🔥🔥🔥 超级终极解决方案：针对GEO经度历史页面的特殊处理 🔥🔥🔥 */
/* 使用最高优先级的选择器和内联样式覆盖 */
html body div#geo-longitude-history-menu input#geo-longitude-norad-id:-webkit-autofill,
html body div#geo-longitude-history-menu input#geo-longitude-start-date:-webkit-autofill,
html body div#geo-longitude-history-menu input#geo-longitude-end-date:-webkit-autofill,
html body div#geo-longitude-history-menu input#geo-longitude-norad-id:-webkit-autofill:hover,
html body div#geo-longitude-history-menu input#geo-longitude-start-date:-webkit-autofill:hover,
html body div#geo-longitude-history-menu input#geo-longitude-end-date:-webkit-autofill:hover,
html body div#geo-longitude-history-menu input#geo-longitude-norad-id:-webkit-autofill:focus,
html body div#geo-longitude-history-menu input#geo-longitude-start-date:-webkit-autofill:focus,
html body div#geo-longitude-history-menu input#geo-longitude-end-date:-webkit-autofill:focus,
html body div#geo-longitude-history-menu input#geo-longitude-norad-id:-webkit-autofill:active,
html body div#geo-longitude-history-menu input#geo-longitude-start-date:-webkit-autofill:active,
html body div#geo-longitude-history-menu input#geo-longitude-end-date:-webkit-autofill:active {
    /* 使用多重阴影技术，确保100%覆盖 */
    box-shadow:
        0 0 0 30px #1a1a1a inset !important,
        0 0 0 40px #1a1a1a inset !important,
        0 0 0 50px #1a1a1a inset !important;
    -webkit-box-shadow:
        0 0 0 30px #1a1a1a inset !important,
        0 0 0 40px #1a1a1a inset !important,
        0 0 0 50px #1a1a1a inset !important;
    -webkit-text-fill-color: white !important;
    background-color: transparent !important;
    background: transparent !important;
    background-image: none !important;
    transition: none !important;
    animation: none !important;
    /* 强制重置所有可能的样式 */
    border: none !important;
    border-bottom: 1px solid #9e9e9e !important;
    color: white !important;
}

/* 添加延迟应用的样式，确保在DOM加载后生效 */
html body div#geo-longitude-history-menu input#geo-longitude-norad-id,
html body div#geo-longitude-history-menu input#geo-longitude-start-date,
html body div#geo-longitude-history-menu input#geo-longitude-end-date {
    /* 预防性样式，在自动填充触发前就设置好 */
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    border: none !important;
    border-bottom: 1px solid #9e9e9e !important;
    color: white !important;
    -webkit-text-fill-color: white !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
}

/* 确保GEO经度历史页面按钮对齐 */
#geo-longitude-history-menu button,
#geo-longitude-form button,
#geo-longitude-get-data {
    height: 32px !important;
    line-height: 32px !important;
    padding: 0 16px !important;
    margin: 0 !important;
    vertical-align: baseline !important;
    display: inline-block !important;
}



/* 🔥🔥🔥 超级终极强制规则 - 绝对覆盖 🔥🔥🔥 */
html body * [id$="-menu"] .input-field label:not(.switch label):not(.lever):not(.material-icons),
html body * .side-menu-parent .input-field label:not(.switch label):not(.lever):not(.material-icons),
html body * #custom-sensor-menu .input-field label:not(.switch label):not(.lever):not(.material-icons) {
    font-size: 18px !important;
    font-size: 18px !important;
    font-size: 18px !important;
    color: #b3e5fc !important;
    color: #b3e5fc !important;
    font-weight: 400 !important;
    top: 2px !important;
    position: absolute !important;
    left: 0 !important;
}

/* 🎯🎯🎯 专门针对选择框标签的强制规则 🎯🎯🎯 */
html body * [id$="-menu"] .input-field .select-wrapper + label,
html body * [id$="-menu"] .input-field .select-wrapper ~ label,
html body * [id$="-menu"] .input-field:has(.select-wrapper) > label,
html body * .side-menu-parent .input-field .select-wrapper + label,
html body * .side-menu-parent .input-field .select-wrapper ~ label,
html body * .side-menu-parent .input-field:has(.select-wrapper) > label,
html body * #custom-sensor-menu .input-field .select-wrapper + label,
html body * #custom-sensor-menu .input-field .select-wrapper ~ label,
html body * #custom-sensor-menu .input-field:has(.select-wrapper) > label {
    font-size: 18px !important;
    font-size: 18px !important;
    font-size: 18px !important;
    color: #b3e5fc !important;
    color: #b3e5fc !important;
    font-weight: 400 !important;
    top: 2px !important;
    position: absolute !important;
    left: 0 !important;
    pointer-events: none !important;
    z-index: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    cursor: default !important;
    opacity: 1 !important;
    transition: all 0.2s ease-out !important;
    transform-origin: 0 0 !important;
}

/* ========================================
 * 🎯 选择框下拉窗口优化
 * ======================================== */

/* 选择框下拉窗口大小和样式 - 超高优先级 */
html body .dropdown-content,
html body div .dropdown-content,
html body * .dropdown-content {
    max-height: 60vh !important;  /* 增加下拉窗口高度 */
    min-width: 200px !important;  /* 设置最小宽度 */
    width: auto !important;       /* 自动宽度 */
    background: var(--color-dark-background) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* 选择框下拉选项样式 - 超高优先级 */
html body .dropdown-content li,
html body div .dropdown-content li,
html body * .dropdown-content li {
    min-height: 50px !important;  /* 进一步增加选项高度到50px */
    line-height: 1.6 !important;  /* 增加行高 */
    padding: 12px 16px !important;  /* 增加内边距 */
    color: white !important;
    background: transparent !important;
    display: flex !important;
    align-items: center !important;  /* 垂直居中对齐 */
}

.dropdown-content li:hover,
.dropdown-content li.active {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 选择框下拉选项文字样式 */
.dropdown-content li > a,
.dropdown-content li > span {
    font-size: 16px !important;  /* 改为16px，与选择框内容字体一致 */
    color: white !important;
    line-height: 1.6 !important;  /* 增加行高 */
    padding: 8px 0 !important;  /* 增加内边距 */
    display: block !important;
    width: 100% !important;
}

/* ========================================
 * 🚫 sat-info-box 例外规则
 * ======================================== */

/* sat-info-box中的标签保持14px，不受18px规则影响 */
#sat-infobox .sat-info-key,
html body #sat-infobox .sat-info-key,
html body div#sat-infobox .sat-info-key {
    font-size: 14px !important;
    color: white !important;
    font-weight: 400 !important;
}

/* 🔥 专门针对自定义传感器菜单的超级强制规则 */
html body div#custom-sensor-menu .input-field > label,
html body div#custom-sensor-menu .input-field label,
html body #custom-sensor-menu .input-field > label,
html body #custom-sensor-menu .input-field label {
    font-size: 18px !important;
    color: #b3e5fc !important;
    font-weight: 400 !important;
    top: 2px !important;
    position: absolute !important;
    left: 0 !important;
}

/* ========================================
 * 🎯 设置菜单特殊优化 - 减少大空白
 * ======================================== */

/* 设置菜单行间距优化 - 超高优先级 */
html body div#settings-menu .row,
html body #settings-menu .row,
#settings-menu .row {
    margin-bottom: 5px !important;  /* 减少设置菜单中行的间距，从默认20px减少到5px */
}

/* 设置菜单输入框间距优化 - 超高优先级 */
html body div#settings-menu .input-field.col,
html body #settings-menu .input-field.col,
#settings-menu .input-field.col {
    margin-bottom: 2px !important;  /* 减少设置菜单中输入框的间距 */
}

/* 创建卫星和编辑卫星菜单输入框间距优化 - 与自定义传感器菜单保持一致 */
html body div#createSat-menu .input-field,
html body div#createSat-menu .input-field.col,
html body div#createSat-menu .input-field[class*="col"],
html body div#editSat-menu .input-field,
html body div#editSat-menu .input-field.col,
html body div#editSat-menu .input-field[class*="col"],
#createSat-menu .input-field,
#createSat-menu .input-field.col,
#editSat-menu .input-field,
#editSat-menu .input-field.col {
    margin-bottom: 2px !important;  /* 减少创建/编辑卫星菜单中输入框的间距，与自定义传感器菜单一致 */
    padding-top: 4px !important;    /* 减少顶部内边距 */
}

/* 🎯 统一下拉框样式修复 - 适用于所有菜单 */

/* 修复所有下拉框内容大小 - 超高优先级 */
.dropdown-content,
.select-dropdown-content,
ul.dropdown-content {
    max-height: 300px !important;   /* 增加最大高度 */
    min-height: auto !important;    /* 自动最小高度 */
    height: auto !important;        /* 自适应高度 */
    overflow-y: auto !important;    /* 允许滚动 */
    z-index: 9999 !important;       /* 确保在最上层 */
}

/* 修复下拉框选项样式 */
.dropdown-content li,
.select-dropdown-content li {
    height: 40px !important;        /* 每个选项40px高 */
    line-height: 40px !important;   /* 文字垂直居中 */
    padding: 0 15px !important;     /* 左右内边距 */
    font-size: 14px !important;     /* 字体大小 */
    min-height: 40px !important;    /* 确保最小高度 */
}

/* 修复开关间距和样式 - 基于分析结果 */
html body div#settings-menu .switch.row:not(:has(input#settings-drawEcf)),
html body #settings-menu .switch.row:not(:has(input#settings-drawEcf)),
#settings-menu .switch.row:not(:has(input#settings-drawEcf)) {
    height: 25px !important;        /* 强制限制开关高度（除了ECF开关） */
    max-height: 25px !important;    /* 防止异常增高 */
    min-height: 25px !important;    /* 确保最小高度 */
    margin-bottom: 5px !important;  /* 设置开关间距为5px */
    overflow: hidden !important;    /* 防止内容溢出 */
    line-height: 25px !important;   /* 文字垂直居中 */
}

/* 修复开关标签字体大小 */
html body div#settings-menu .switch label,
html body #settings-menu .switch label,
#settings-menu .switch label {
    font-size: 16px !important;     /* 设置标签字体为16px */
    color: #b3e5fc !important;      /* 保持统一颜色 */
    font-weight: 400 !important;    /* 正常字重 */
}

/* 🔥 修复ECF选择框容器间距问题 - 紧凑布局 */
#settings-menu .input-field.col.s12 {
    margin-top: 12px !important;     /* 适中的顶部间距 */
    margin-bottom: 12px !important;  /* 适中的底部间距 */
    padding-top: 0px !important;     /* 移除顶部内边距 */
    padding-bottom: 0px !important;  /* 移除底部内边距 */
    min-height: 60px !important;     /* 确保足够高度容纳标签和选择框 */
    background: transparent !important; /* 确保透明背景 */
}

/* 🔥 专门针对ECF轨道数选择框的间距修复 - 紧凑间距 */
#settings-numberOfEcfOrbitsToDraw {
    margin-top: 0px !important;      /* 移除选择框本身的顶部间距 */
    margin-bottom: 0px !important;   /* 移除选择框本身的底部间距 */
}

/* 🔥 专门针对包含ECF轨道数选择框的容器 - 修复布局 - 超高优先级 */
html body div#settings-menu .input-field:has(#settings-numberOfEcfOrbitsToDraw),
html body div#settings-menu .input-field.col:has(#settings-numberOfEcfOrbitsToDraw),
html body #settings-menu .input-field:has(#settings-numberOfEcfOrbitsToDraw),
html body #settings-menu .input-field.col:has(#settings-numberOfEcfOrbitsToDraw),
#settings-menu .input-field:has(#settings-numberOfEcfOrbitsToDraw),
#settings-menu .input-field.col:has(#settings-numberOfEcfOrbitsToDraw) {
    margin-top: 0px !important;      /* 完全移除容器顶部间距 */
    margin-bottom: 10px !important;  /* 减少容器底部间距 */
    padding-top: 8px !important;     /* 进一步减少容器内边距，降低高度 */
    padding-bottom: 0px !important;  /* 移除容器内边距 */
    background: transparent !important; /* 确保透明背景 */
    position: relative !important;   /* 确保正确定位 */
    z-index: 1 !important;          /* 确保不会遮挡其他元素 */
    min-height: 35px !important;     /* 进一步减少最小高度 */
    max-height: 40px !important;     /* 进一步限制最大高度 */
    height: 38px !important;         /* 进一步降低固定高度 */
}

/* 🔥 专门针对"要绘制的ECF轨道数"标题文本 - 修复选择框标签定位 */
html body div#settings-menu .input-field:has(#settings-numberOfEcfOrbitsToDraw) label,
html body div#settings-menu .input-field.col:has(#settings-numberOfEcfOrbitsToDraw) label,
html body #settings-menu .input-field:has(#settings-numberOfEcfOrbitsToDraw) label,
html body #settings-menu .input-field.col:has(#settings-numberOfEcfOrbitsToDraw) label,
#settings-menu .input-field:has(#settings-numberOfEcfOrbitsToDraw) label,
#settings-menu .input-field.col:has(#settings-numberOfEcfOrbitsToDraw) label,
#settings-menu label[for="settings-numberOfEcfOrbitsToDraw"] {
    margin-top: 0px !important;      /* 移除标题文本的顶部间距 */
    padding-top: 0px !important;     /* 移除标题文本的内边距 */
    display: block !important;       /* 确保标题独占一行 */
    position: absolute !important;   /* 绝对定位 */
    top: -25px !important;           /* 标签位置更远离选择框，避免被遮挡 */
    left: 0px !important;            /* 左对齐 */
    font-size: 14px !important;      /* 较小的字体 */
    color: #b3e5fc !important;       /* 统一颜色 */
    transform: none !important;      /* 移除变换 */
    transform-origin: 0 0 !important; /* 变换原点 */
    z-index: 10 !important;         /* 确保标签在最上层 */
}

/* 修复选择框包装器 */
#settings-menu .select-wrapper {
    height: 25px !important;        /* 进一步减少选择框高度 */
    min-height: 25px !important;    /* 进一步减少最小高度 */
    max-height: 25px !important;    /* 防止异常增高 */
    background: transparent !important; /* 确保透明背景 */
    margin-top: 8px !important;     /* 减少顶部间距 */
    margin-bottom: 0px !important;  /* 移除底部间距 */
}

/* 🔥 修复ECF轨道数选择框与上下开关的间距关系 - 超高优先级 */
/* 确保ECF开关（在ECF中绘制轨道）与选择框之间的间距 - 超大幅增加距离避免冲突 */
/* 🔥 ECF开关特殊样式 - 超高优先级覆盖通用规则 */
html body div#settings-menu .switch.row:has(input#settings-drawEcf),
html body #settings-menu .switch.row:has(input#settings-drawEcf),
#settings-menu .switch.row:has(input#settings-drawEcf) {
    margin-bottom: 50px !important;  /* 超大幅增加ECF开关底部间距，彻底避免遮挡 */
    margin-top: 5px !important;      /* 保持与上面元素的正常间距 */
    height: 35px !important;         /* 增加开关高度以容纳文字 */
    min-height: 35px !important;     /* 确保最小高度 */
    max-height: 35px !important;     /* 防止开关异常增高 */
    display: flex !important;        /* 使用flex布局 */
    align-items: center !important;  /* 垂直居中对齐 */
    padding: 5px 0 !important;       /* 添加上下内边距 */
    overflow: visible !important;    /* 允许内容显示 */
    line-height: 1.2 !important;     /* 适当的行高 */
}

/* 🔧 备用规则 - 针对不支持:has()的浏览器，直接覆盖第3个开关 */
html body div#settings-menu .switch.row:nth-of-type(3),
html body #settings-menu .switch.row:nth-of-type(3),
#settings-menu .switch.row:nth-of-type(3) {
    margin-bottom: 50px !important;  /* ECF开关底部间距 */
    height: 35px !important;         /* ECF开关高度 */
    min-height: 35px !important;     /* 最小高度 */
    max-height: 35px !important;     /* 最大高度 */
    overflow: visible !important;    /* 允许内容显示 */
    line-height: 1.2 !important;     /* 适当的行高 */
}

/* 🔧 修复ECF开关标签与滑动按钮的对齐 */
html body div#settings-menu .switch.row:has(input#settings-drawEcf) label,
html body #settings-menu .switch.row:has(input#settings-drawEcf) label,
#settings-menu .switch.row:has(input#settings-drawEcf) label {
    display: flex !important;        /* 使用flex布局 */
    align-items: center !important;  /* 垂直居中对齐 */
    height: 35px !important;         /* 与容器高度一致 */
    min-height: 35px !important;     /* 确保最小高度 */
    line-height: 1.2 !important;     /* 适当的行高以容纳文字 */
    padding: 0 !important;           /* 移除内边距 */
}

/* 🔧 修复ECF开关滑动杆的对齐 */
html body div#settings-menu .switch.row:has(input#settings-drawEcf) .lever,
html body #settings-menu .switch.row:has(input#settings-drawEcf) .lever,
#settings-menu .switch.row:has(input#settings-drawEcf) .lever {
    margin: 0 15px !important;       /* 保持左右间距 */
    vertical-align: middle !important; /* 垂直居中 */
}

/* 确保选择框与下面开关（绘制协方差椭圆球）之间的间距 - 减少距离 */
html body div#settings-menu .input-field:has(#settings-numberOfEcfOrbitsToDraw) + .switch.row,
html body #settings-menu .input-field:has(#settings-numberOfEcfOrbitsToDraw) + .switch.row,
#settings-menu .input-field:has(#settings-numberOfEcfOrbitsToDraw) + .switch.row {
    margin-top: 18px !important;     /* 适当减少下面开关的顶部间距 */
}

/* 🔧 调试CSS已移除 - ECF轨道数间距修复完成 */

/* 🎨 设置菜单选择框下拉选项白色背景 - 超高优先级 */
/* 专门针对设置菜单中的选择框下拉选项设置白色背景 */
html body div#settings-menu .dropdown-content,
html body #settings-menu .dropdown-content,
#settings-menu .dropdown-content {
    background: white !important;
    border: 1px solid #ccc !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

/* 设置菜单选择框下拉选项文字颜色 - 超高优先级 */
html body div#settings-menu .dropdown-content li,
html body #settings-menu .dropdown-content li,
#settings-menu .dropdown-content li {
    background: white !important;
    color: #333 !important;
    border-bottom: 1px solid #eee !important;
}

/* 设置菜单选择框下拉选项悬停效果 - 超高优先级 */
html body div#settings-menu .dropdown-content li:hover,
html body #settings-menu .dropdown-content li:hover,
#settings-menu .dropdown-content li:hover {
    background: #f5f5f5 !important;
    color: #333 !important;
}

/* 设置菜单选择框下拉选项选中效果 - 超高优先级 */
html body div#settings-menu .dropdown-content li.selected,
html body #settings-menu .dropdown-content li.selected,
#settings-menu .dropdown-content li.selected {
    background: #e3f2fd !important;
    color: #1976d2 !important;
}

/* 🔧 备用CSS规则 - 针对不支持:has()的浏览器 */
/* 直接通过ID选择器来确保样式应用 */
html body div#settings-menu div.input-field.col.s12:nth-of-type(4),
html body #settings-menu div.input-field.col.s12:nth-of-type(4),
#settings-menu div.input-field.col.s12:nth-of-type(4) {
    margin-top: 0px !important;      /* 完全移除ECF轨道数选择框容器顶部间距 */
    margin-bottom: 10px !important;  /* 减少ECF轨道数选择框容器底部间距 */
    padding-top: 8px !important;     /* 进一步减少内边距，降低高度 */
    min-height: 35px !important;     /* 进一步减少最小高度 */
    max-height: 40px !important;     /* 进一步限制最大高度 */
    height: 38px !important;         /* 进一步降低固定高度 */
}

/* 🔧 ECF轨道数选择框样式优化 */
html body div#settings-menu div.input-field.col.s12 select#settings-numberOfEcfOrbitsToDraw,
html body #settings-menu div.input-field.col.s12 select#settings-numberOfEcfOrbitsToDraw,
#settings-menu div.input-field.col.s12 select#settings-numberOfEcfOrbitsToDraw {
    /* 选择框样式已通过容器统一处理 */
}

/* 🔧 最强制的CSS规则 - 直接针对包含特定select的父容器 */
html body div#settings-menu div.input-field.col.s12:has(select#settings-numberOfEcfOrbitsToDraw),
html body #settings-menu div.input-field.col.s12:has(select#settings-numberOfEcfOrbitsToDraw),
#settings-menu div.input-field.col.s12:has(select#settings-numberOfEcfOrbitsToDraw) {
    margin-top: 0px !important;      /* 完全移除强制顶部间距 */
    margin-bottom: 10px !important;  /* 减少强制底部间距 */
    padding-top: 8px !important;     /* 进一步减少强制内边距，降低高度 */
    min-height: 35px !important;     /* 进一步减少最小高度 */
    max-height: 40px !important;     /* 进一步限制最大高度 */
    height: 38px !important;         /* 进一步降低强制固定高度 */
}

/* 🎯 选择框下拉窗口强制优化 - 终极优先级 */
html body div .dropdown-content,
html body .dropdown-content,
.dropdown-content {
    max-height: 300px !important;  /* 强制设置下拉窗口最大高度 */
    min-width: 150px !important;   /* 强制设置最小宽度 */
    width: auto !important;        /* 自动宽度 */
    background: var(--color-dark-background) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    overflow-y: auto !important;   /* 允许垂直滚动 */
}

/* 选择框下拉选项强制优化 - 终极优先级 */
html body div .dropdown-content li,
html body .dropdown-content li,
.dropdown-content li {
    min-height: 40px !important;   /* 强制设置选项高度 */
    line-height: 1.5 !important;   /* 强制设置行高 */
    padding: 8px 16px !important;  /* 强制设置内边距 */
    font-size: 16px !important;    /* 强制设置字体大小 */
}

/* 🔥🔥🔥 终极下拉框修复 - 覆盖所有其他CSS文件 🔥🔥🔥 */
/* 这个规则的优先级最高，用来覆盖 style.css, style.embed.css, materialize.css 等所有文件 */
html[lang] body div .dropdown-content,
html[lang] body .dropdown-content,
html[lang] .dropdown-content,
body div .dropdown-content,
body .dropdown-content,
.dropdown-content {
    max-height: 400px !important;   /* 🔥 强制设置下拉窗口最大高度400px */
    min-height: 150px !important;   /* 🔥 强制设置最小高度150px */
    height: auto !important;        /* 🔥 自适应高度 */
    overflow-y: auto !important;    /* 🔥 允许垂直滚动 */
    width: auto !important;         /* 🔥 自动宽度 */
    min-width: 200px !important;    /* 🔥 最小宽度200px */
    background: var(--color-dark-background) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    z-index: 9999 !important;       /* 🔥 确保在最上层 */
}

/* 🔥🔥🔥 终极下拉框选项修复 - 覆盖所有其他CSS文件 🔥🔥🔥 */
html[lang] body div .dropdown-content li,
html[lang] body .dropdown-content li,
html[lang] .dropdown-content li,
body div .dropdown-content li,
body .dropdown-content li,
.dropdown-content li {
    min-height: 45px !important;    /* 🔥 强制设置选项高度45px */
    height: 45px !important;        /* 🔥 固定高度45px */
    line-height: 45px !important;   /* 🔥 文字垂直居中 */
    padding: 0 16px !important;     /* 🔥 左右内边距16px */
    font-size: 16px !important;     /* 🔥 字体大小16px */
    display: flex !important;       /* 🔥 弹性布局 */
    align-items: center !important; /* 🔥 垂直居中对齐 */
    color: white !important;        /* 🔥 白色文字 */
    background: transparent !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* 🔥 下拉框选项悬停效果 */
html[lang] body div .dropdown-content li:hover,
html[lang] body .dropdown-content li:hover,
html[lang] .dropdown-content li:hover,
body div .dropdown-content li:hover,
body .dropdown-content li:hover,
.dropdown-content li:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 🔧 修复搜索框清除按钮对齐问题 */
#clear-search,
#search-clear,
.search-clear {
    position: absolute !important;
    right: 8px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    z-index: 10 !important;
    background: rgba(0, 0, 0, 0.3) !important;
    border-radius: 50% !important;
    color: white !important;
    font-size: 14px !important;
    line-height: 1 !important;
}

#clear-search:hover,
#search-clear:hover,
.search-clear:hover {
    background: rgba(0, 0, 0, 0.6) !important;
    color: #ff4444 !important;
}

/* ========================================
 * 🎯 菜单间距优化 - 修复过大间距问题
 * ======================================== */

/* 过滤菜单特殊处理 - 减少开关间距 */
html body div#filter-menu .switch.row,
html body #filter-menu .switch.row,
#filter-menu .switch.row {
    margin-top: 8px !important;      /* 减少开关之间的间距 */
    margin-bottom: 8px !important;   /* 减少开关之间的间距 */
    padding: 2px 0 !important;       /* 减少内边距 */
    height: auto !important;
    min-height: auto !important;
}

/* 过滤菜单标签字体统一 */
html body div#filter-menu .switch label,
html body #filter-menu .switch label,
#filter-menu .switch label {
    font-size: 16px !important;      /* 统一字体大小为16px */
    color: #b3e5fc !important;       /* 统一颜色 */
    font-weight: 400 !important;     /* 统一字重 */
    line-height: 1.2 !important;     /* 减少行高 */
}

/* 过滤菜单分类标题间距优化 */
html body div#filter-menu h5,
html body #filter-menu h5,
#filter-menu h5 {
    margin-top: 16px !important;     /* 减少分类标题上方间距 */
    margin-bottom: 8px !important;   /* 减少分类标题下方间距 */
    font-size: 18px !important;      /* 统一标题字体大小 */
}

/* 查找卫星菜单间距优化 */
html body div#findSat-menu .input-field,
html body div#findSat-menu .input-field.col,
html body div#findSat-menu .input-field[class*="col"],
html body #findSat-menu .input-field,
html body #findSat-menu .input-field.col,
html body #findSat-menu .input-field[class*="col"],
#findSat-menu .input-field,
#findSat-menu .input-field.col,
#findSat-menu .input-field[class*="col"] {
    margin-top: 8px !important;      /* 减少顶部间距 */
    margin-bottom: 8px !important;   /* 减少底部间距 */
    padding-top: 4px !important;     /* 减少内边距 */
    min-height: auto !important;     /* 自动高度 */
}

/* 查找卫星菜单行间距优化 */
html body div#findSat-menu .row,
html body #findSat-menu .row,
#findSat-menu .row {
    margin-bottom: 8px !important;   /* 减少行间距 */
}

/* ECF下拉框修复 - 只修复样式，不改变容器 */
.dropdown-content.select-dropdown {
    background: #1a1a1a !important;
    background-color: #1a1a1a !important;
    opacity: 1 !important;
    visibility: visible !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 4px !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.8) !important;
}

.dropdown-content.select-dropdown li {
    background: transparent !important;
    color: white !important;
    padding: 8px 16px !important;
    min-height: 40px !important;
}

.dropdown-content.select-dropdown li:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}


