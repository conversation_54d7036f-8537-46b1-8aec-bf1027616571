import express from 'express';
import { Client } from '@elastic/elasticsearch';
import fs from 'fs';
import crypto from 'crypto';
import path from 'path';

const router = express.Router();

// 读取字段映射配置
let fieldMapping: any = null;
function loadFieldMapping() {
  if (!fieldMapping) {
    try {
      const configPath = path.resolve('es-field-mapping.json');
      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        fieldMapping = JSON.parse(configData);
        console.log('字段映射配置加载成功');
      } else {
        // 使用默认配置
        fieldMapping = {
          elasticsearch: {
            index: "orbital_tle",
            fields: {
              norad_id: "norad_id",
              time: "time",
              object_type: "orbital_elements.object_type.keyword",
              object_name: "satellite_name",
              satellite_name: "satellite_name",
              subsat_long: "orbital_elements.subsat_long",
              subsat_lat: "orbital_elements.subsat_lat"
            },
            geo_satellite: {
              object_type_value: "GEO",
              longitude_field: "orbital_elements.subsat_long",
              latitude_field: "orbital_elements.subsat_lat"
            },
            query_limits: {
              max_records: 50000,
              batch_size: 1000,
              timeout_seconds: 30
            }
          }
        };
        console.log('使用默认字段映射配置');
      }
    } catch (error) {
      console.error('加载字段映射配置失败:', error);
      throw error;
    }
  }
  return fieldMapping;
}

// AES-256-GCM加密密钥（实际项目中应存储在环境变量或密钥管理系统中）
const ENCRYPTION_KEY = process.env.ES_ENCRYPTION_KEY || 'your-secret-key-32-chars-long!!';
const ALGORITHM = 'aes-256-gcm';

// 读取ES配置（使用AES-256-GCM加密）
const esConfigPath = 'es-config.enc';
function loadESConfig() {
  if (!fs.existsSync(esConfigPath)) throw new Error('ES配置文件不存在');
  
  try {
    const encryptedData = fs.readFileSync(esConfigPath, 'utf8');
    const data = JSON.parse(encryptedData);
    
    // 解密数据
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
    const iv = Buffer.from(data.iv, 'hex');
    const authTag = Buffer.from(data.authTag, 'hex');
    const encrypted = Buffer.from(data.encrypted, 'hex');
    
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    decipher.setAuthTag(authTag);
    decipher.setAAD(Buffer.from(''));
    
    let decrypted = decipher.update(encrypted);
    const final = decipher.final();
    decrypted = Buffer.concat([decrypted, final]);
    
    const config = JSON.parse(decrypted.toString('utf8'));
    return config.elasticsearch;
  } catch (error) {
    console.error('解密ES配置失败:', error);
    throw new Error('ES配置文件解密失败');
  }
}

const esConfig = loadESConfig();
const esClient = new Client({
  node: esConfig.url,
  auth: {
    username: esConfig.username,
    password: esConfig.password,
  },
  maxRetries: 3,
  requestTimeout: 30000,
});

// GET /api/es-history?norad_id=25544&start=2025-01-01&end=2025-07-01
// 支持GEO卫星查询: /api/es-history?start=2025-01-01&end=2025-07-01&geo_only=true
// 支持经度范围过滤: /api/es-history?start=2025-01-01&end=2025-07-01&geo_only=true&lon_min=85&lon_max=100
router.get('/', async (req, res) => {
  try {
    // 加载字段映射配置
    const config = loadFieldMapping();
    const fields = config.elasticsearch.fields;
    const geoConfig = config.elasticsearch.geo_satellite;
    const limits = config.elasticsearch.query_limits;

    const noradId = req.query.norad_id;
    const startDate = req.query.start;
    const endDate = req.query.end;
    const geoOnly = req.query.geo_only === 'true';
    const lonMin = req.query.lon_min ? parseFloat(req.query.lon_min as string) : null;
    const lonMax = req.query.lon_max ? parseFloat(req.query.lon_max as string) : null;

    if (!startDate || !endDate) {
      return res.status(400).json({ error: '缺少开始日期或结束日期参数' });
    }

    // 如果不是GEO查询且没有NORAD ID，则返回错误
    if (!geoOnly && !noradId) {
      return res.status(400).json({ error: '缺少NORAD编号参数' });
    }
    let noradIds: number[] = [];
    if (noradId) {
      if (Array.isArray(noradId)) {
        noradIds = noradId.flatMap(id =>
          typeof id === 'string'
            ? id.split(/[,，]/)
            : []
        ).map(Number).filter(n => !isNaN(n));
      } else if (typeof noradId === 'string') {
        noradIds = noradId.split(/[,，]/).map(Number).filter(n => !isNaN(n));
      }
    }

    const startDatetime = `${startDate}T00:00:00Z`;
    const endDatetime = `${endDate}T23:59:59Z`;

    // 构建查询条件
    const mustConditions: any[] = [
      { range: { [fields.time]: { gte: startDatetime, lte: endDatetime } } }
    ];

    // 如果指定了NORAD ID，添加到查询条件
    if (noradIds.length > 0) {
      mustConditions.push({ terms: { [fields.norad_id]: noradIds } });
    }

    // 如果只查询GEO卫星，添加object_type条件
    if (geoOnly) {
      mustConditions.push({ term: { [fields.object_type]: geoConfig.object_type_value } });
      // 确保经度字段存在
      const longitudeField = geoConfig.longitude_field;
      mustConditions.push({ exists: { field: longitudeField } });

      // 如果指定了经度范围，添加范围过滤条件
      if (lonMin !== null && lonMax !== null && !isNaN(lonMin) && !isNaN(lonMax)) {
        mustConditions.push({
          range: {
            [longitudeField]: {
              gte: Math.min(lonMin, lonMax),
              lte: Math.max(lonMin, lonMax)
            }
          }
        });
        console.log(`添加经度范围过滤: ${Math.min(lonMin, lonMax)} ~ ${Math.max(lonMin, lonMax)}`);
      }
    }

    const query = {
      bool: {
        must: mustConditions
      }
    };

    console.log('开始查询 ES 数据...');
    console.log('查询参数:', {
      noradIds,
      startDatetime,
      endDatetime,
      geoOnly,
      longitudeRange: lonMin !== null && lonMax !== null ? { min: lonMin, max: lonMax } : null
    });

    // 直接进行搜索查询，不进行 count 查询
    const batchSize = limits.batch_size;
    let allRecords: any[] = [];
    let searchAfter: any[] | undefined = undefined;
    let hasMore = true;

    // 构建返回字段列表
    const sourceFields = [
      fields.norad_id,
      fields.time,
      fields.object_name,
      'orbital_elements' // 返回完整的orbital_elements对象
    ];

    while (hasMore) {
      const resp = await esClient.search({
        index: esConfig.index,
        size: batchSize,
        query: query,
        sort: [ { [fields.time]: 'asc' }, { [fields.norad_id]: 'asc' } ],
        search_after: searchAfter,
        _source: sourceFields, // 只返回需要的字段
      });
      
      const hits = resp.hits?.hits || [];
      console.log(`获取到 ${hits.length} 条记录`);
      
      if (hits.length === 0) {
        hasMore = false;
        break;
      }
      
      // 转换数据结构，保留完整的orbital_elements
      const transformedRecords = hits.map((h: any) => {
        const source = h._source;
        const transformed: any = {
          norad_id: source.norad_id,
          time: source.time,
          satellite_name: source.satellite_name
        };

        // 保留完整的orbital_elements对象，供前端历史轨道分析使用
        if (source.orbital_elements) {
          transformed.orbital_elements = source.orbital_elements;
        }

        // 如果是GEO查询，添加扁平化的字段
        if (geoOnly && source.orbital_elements) {
          transformed.object_type = source.orbital_elements.object_type;
          transformed.subsat_long = source.orbital_elements.subsat_long;
          if (source.orbital_elements.subsat_lat !== undefined) {
            transformed.subsat_lat = source.orbital_elements.subsat_lat;
          }
        }

        return transformed;
      });

      allRecords.push(...transformedRecords);
      searchAfter = hits[hits.length - 1]?.sort;
      
      if (!searchAfter || allRecords.length >= limits.max_records) { // 限制最大记录数
        hasMore = false;
      }
    }
    
    console.log(`总共获取到 ${allRecords.length} 条记录`);
    return res.json({ count: allRecords.length, data: allRecords });
    
  } catch (err: any) {
    console.error('ES代理查询出错:', err);
    return res.status(500).json({ error: 'ES代理查询失败', detail: err.message });
  }
});

export default router; 