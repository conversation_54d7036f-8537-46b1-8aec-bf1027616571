/**
 * 管理后台脚本 - 修复版本
 */

console.log('Admin script loading...');

class AdminManager {
    constructor() {
        console.log('AdminManager constructor called');
        this.apiBaseUrl = null;
        this.token = localStorage.getItem('authToken');
        console.log('Token found:', !!this.token);
        this.init();
    }

    async getApiBaseUrl() {
        // 使用统一配置加载器
        if (window.configLoader) {
            try {
                const apiUrl = await window.configLoader.getAuthUrl();
                console.log('从配置加载器获取API地址:', apiUrl);
                return apiUrl;
            } catch (error) {
                console.log('配置加载器获取失败:', error.message);
            }
        }

        // 回退到默认地址
        const currentHost = window.location.hostname;
        const protocol = window.location.protocol;
        const port = window.location.hostname === 'localhost' ? '5001' : (protocol === 'https:' ? '443' : '5001');
        const url = `${protocol}//${currentHost}:${port}/api/auth`;
        console.log('API Base URL:', url);
        return url;
    }

    async init() {
        console.log('Initializing AdminManager...');

        // 初始化API地址
        this.apiBaseUrl = await this.getApiBaseUrl();

        if (!this.token) {
            console.log('No token found, redirecting to login');
            alert('未找到认证令牌，请先登录');
            window.location.href = '/login.html';
            return;
        }

        try {
            console.log('Verifying admin privileges...');
            const isAdmin = await this.verifyAdmin();
            
            if (!isAdmin) {
                console.log('Not admin, redirecting');
                alert('需要管理员权限');
                window.location.href = '/login.html';
                return;
            }

            console.log('Admin verified, loading data...');
            this.showLoading(true);
            await this.loadAllData();
            this.showLoading(false);
            this.setupEventListeners();
            console.log('AdminManager initialized successfully');
            
        } catch (error) {
            console.error('Initialization error:', error);
            this.showError('初始化失败：' + error.message);
        }
    }

    async verifyAdmin() {
        try {
            console.log('Sending verify request...');
            const response = await fetch(`${this.apiBaseUrl}/verify`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ token: this.token })
            });

            console.log('Verify response status:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('Verify response data:', data);
                
                if (data.user && data.user.role === 'admin') {
                    const userElement = document.getElementById('currentUser');
                    if (userElement) {
                        userElement.textContent = data.user.username;
                    }
                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error('Token verification failed:', error);
            return false;
        }
    }

    setupEventListeners() {
        console.log('Setting up event listeners...');
        
        // 修改密码表单
        const changePasswordForm = document.getElementById('changePasswordForm');
        if (changePasswordForm) {
            changePasswordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleChangePassword();
            });
            console.log('Password form listener added');
        }
    }

    async loadAllData() {
        console.log('Loading all data...');
        try {
            await this.loadUsers();
            await this.loadStats();
            console.log('All data loaded successfully');
        } catch (error) {
            console.error('Error loading data:', error);
            this.showError('加载数据失败：' + error.message);
        }
    }

    async loadUsers() {
        console.log('Loading users...');
        try {
            const response = await fetch(`${this.apiBaseUrl}/users`, {
                headers: { 'Authorization': `Bearer ${this.token}` }
            });

            console.log('Users response status:', response.status);

            if (response.ok) {
                const users = await response.json();
                console.log('Users loaded:', users.length);
                this.displayUsers(users);
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('Failed to load users:', error);
            this.showError('加载用户数据失败');
        }
    }

    displayUsers(users) {
        console.log('Displaying users:', users.length);
        const tableBody = document.getElementById('users-table');
        if (!tableBody) {
            console.error('Users table not found');
            return;
        }

        tableBody.innerHTML = '';
        
        if (users.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666;">暂无用户数据</td></tr>';
            return;
        }

        users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.id.substring(0, 8)}...</td>
                <td>${user.username}</td>
                <td>${user.email || '-'}</td>
                <td><span class="badge ${user.role}">${user.role === 'admin' ? '管理员' : '普通用户'}</span></td>
                <td><span class="badge ${user.isActive ? 'active' : 'inactive'}">${user.isActive ? '活跃' : '禁用'}</span></td>
                <td>${new Date(user.createdAt).toLocaleString()}</td>
                <td>${user.lastLogin ? new Date(user.lastLogin).toLocaleString() : '-'}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn ${user.isActive ? 'danger' : 'success'}" onclick="window.toggleUser('${user.id}')">
                            ${user.isActive ? '禁用' : '启用'}
                        </button>
                        ${user.username !== 'admin' ? `
                            <button class="btn danger" onclick="window.deleteUser('${user.id}')">
                                删除
                            </button>
                        ` : ''}
                    </div>
                </td>
            `;
            tableBody.appendChild(row);
        });
        console.log('Users displayed successfully');
    }

    async loadStats() {
        console.log('Loading stats...');
        try {
            const response = await fetch(`${this.apiBaseUrl}/users`, {
                headers: { 'Authorization': `Bearer ${this.token}` }
            });

            if (response.ok) {
                const users = await response.json();
                const activeUsers = users.filter(u => u.isActive).length;
                
                this.updateStat('totalUsers', users.length);
                this.updateStat('activeUsers', activeUsers);
                this.updateStat('pendingRegistrations', 0);
                this.updateStat('todayLogins', 0);
                
                console.log('Stats updated');
            }
        } catch (error) {
            console.error('Failed to load stats:', error);
        }
    }

    updateStat(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }

    async toggleUser(userId) {
        console.log('Toggling user:', userId);
        try {
            const response = await fetch(`${this.apiBaseUrl}/toggle-user/${userId}`, {
                method: 'POST',
                headers: { 'Authorization': `Bearer ${this.token}` }
            });

            if (response.ok) {
                this.showSuccess('用户状态已更新');
                await this.loadUsers();
                await this.loadStats();
            } else {
                const errorData = await response.json();
                this.showError(errorData.error || '操作失败');
            }
        } catch (error) {
            console.error('Toggle user error:', error);
            this.showError('操作失败：' + error.message);
        }
    }

    async deleteUser(userId) {
        console.log('Deleting user:', userId);
        if (!confirm('确定要删除这个用户吗？此操作不可撤销！')) return;

        try {
            const response = await fetch(`${this.apiBaseUrl}/users/${userId}`, {
                method: 'DELETE',
                headers: { 'Authorization': `Bearer ${this.token}` }
            });

            if (response.ok) {
                this.showSuccess('用户已删除');
                await this.loadUsers();
                await this.loadStats();
            } else {
                const errorData = await response.json();
                this.showError(errorData.error || '删除失败');
            }
        } catch (error) {
            console.error('Delete user error:', error);
            this.showError('删除失败：' + error.message);
        }
    }

    async handleChangePassword() {
        console.log('Handling password change...');
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (!currentPassword || !newPassword || !confirmPassword) {
            this.showError('请填写所有字段');
            return;
        }

        if (newPassword !== confirmPassword) {
            this.showError('新密码和确认密码不匹配');
            return;
        }

        if (newPassword.length < 8) {
            this.showError('新密码至少需要8位字符');
            return;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/change-password`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    oldPassword: currentPassword,
                    newPassword: newPassword
                })
            });

            if (response.ok) {
                this.showSuccess('密码修改成功');
                document.getElementById('changePasswordForm').reset();
            } else {
                const errorData = await response.json();
                this.showError(errorData.error || '密码修改失败');
            }
        } catch (error) {
            console.error('Change password error:', error);
            this.showError('密码修改失败：' + error.message);
        }
    }

    showError(message) {
        console.error('Error:', message);
        const errorDiv = document.getElementById('errorMessage');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        } else {
            alert('错误：' + message);
        }
    }

    showSuccess(message) {
        console.log('Success:', message);
        const successDiv = document.getElementById('successMessage');
        if (successDiv) {
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 5000);
        } else {
            alert('成功：' + message);
        }
    }

    showLoading(show) {
        const loadingDiv = document.getElementById('mainLoading');
        if (loadingDiv) {
            loadingDiv.style.display = show ? 'block' : 'none';
        }
    }
}

// 全局函数
window.toggleUser = function(userId) {
    console.log('Global toggleUser called:', userId);
    if (window.adminManager) {
        window.adminManager.toggleUser(userId);
    } else {
        console.error('AdminManager not available');
        alert('系统未初始化，请刷新页面');
    }
};

window.deleteUser = function(userId) {
    console.log('Global deleteUser called:', userId);
    if (window.adminManager) {
        window.adminManager.deleteUser(userId);
    } else {
        console.error('AdminManager not available');
        alert('系统未初始化，请刷新页面');
    }
};

window.switchTab = function(tabName) {
    console.log('Switching to tab:', tabName);
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => tab.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));
    
    const tabNames = ['users', 'registrations', 'logs', 'settings'];
    const tabIndex = tabNames.indexOf(tabName);
    if (tabIndex !== -1 && tabs[tabIndex]) {
        tabs[tabIndex].classList.add('active');
    }
    
    const tabContent = document.getElementById(`${tabName}-tab`);
    if (tabContent) {
        tabContent.classList.add('active');
    }
};

window.filterUsers = function() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const rows = document.querySelectorAll('#users-table tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
};

window.logout = function() {
    console.log('Logging out...');
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    window.location.href = '/login.html';
};

// 初始化
console.log('Setting up DOMContentLoaded listener...');
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing AdminManager...');
    window.adminManager = new AdminManager();
});

console.log('Admin script loaded successfully');
