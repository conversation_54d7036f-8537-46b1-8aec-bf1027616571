/**
 * 国家名称英文转中文映射表
 * 用于在界面显示中文国家名称，不影响原始数据
 */
export const countryChineseMap: { [key: string]: string } = {
  // 主要国家
  'United States': '美国',
  'Russia': '俄罗斯',
  'China': '中国',
  'Japan': '日本',
  'Germany': '德国',
  'France': '法国',
  'United Kingdom': '英国',
  'Italy': '意大利',
  'Canada': '加拿大',
  'Australia': '澳大利亚',
  'India': '印度',
  'Brazil': '巴西',
  'South Korea': '韩国',
  'Spain': '西班牙',
  'Netherlands': '荷兰',
  'Sweden': '瑞典',
  'Norway': '挪威',
  'Israel': '以色列',
  'Turkey': '土耳其',
  'Mexico': '墨西哥',
  'Argentina': '阿根廷',
  'Chile': '智利',
  'South Africa': '南非',
  'Egypt': '埃及',
  'Iran': '伊朗',
  'Saudi Arabia': '沙特阿拉伯',
  'United Arab Emirates': '阿联酋',
  'Thailand': '泰国',
  'Indonesia': '印度尼西亚',
  'Malaysia': '马来西亚',
  'Singapore': '新加坡',
  'Philippines': '菲律宾',
  'Vietnam': '越南',
  'New Zealand': '新西兰',
  
  // 欧洲国家
  'Austria': '奥地利',
  'Belgium': '比利时',
  'Switzerland': '瑞士',
  'Denmark': '丹麦',
  'Finland': '芬兰',
  'Greece': '希腊',
  'Ireland': '爱尔兰',
  'Luxembourg': '卢森堡',
  'Portugal': '葡萄牙',
  'Poland': '波兰',
  'Czech Republic': '捷克',
  'Hungary': '匈牙利',
  'Slovakia': '斯洛伐克',
  'Slovenia': '斯洛文尼亚',
  'Croatia': '克罗地亚',
  'Romania': '罗马尼亚',
  'Bulgaria': '保加利亚',
  'Estonia': '爱沙尼亚',
  'Latvia': '拉脱维亚',
  'Lithuania': '立陶宛',
  'Ukraine': '乌克兰',
  'Belarus': '白俄罗斯',
  'Moldova': '摩尔多瓦',
  'Serbia': '塞尔维亚',
  'Montenegro': '黑山',
  'Bosnia and Herzegovina': '波黑',
  'North Macedonia': '北马其顿',
  'Albania': '阿尔巴尼亚',
  'Iceland': '冰岛',
  
  // 亚洲国家
  'Kazakhstan': '哈萨克斯坦',
  'Uzbekistan': '乌兹别克斯坦',
  'Turkmenistan': '土库曼斯坦',
  'Kyrgyzstan': '吉尔吉斯斯坦',
  'Tajikistan': '塔吉克斯坦',
  'Afghanistan': '阿富汗',
  'Pakistan': '巴基斯坦',
  'Bangladesh': '孟加拉国',
  'Sri Lanka': '斯里兰卡',
  'Myanmar': '缅甸',
  'Cambodia': '柬埔寨',
  'Laos': '老挝',
  'Mongolia': '蒙古',
  'North Korea': '朝鲜',
  'Nepal': '尼泊尔',
  'Bhutan': '不丹',
  'Maldives': '马尔代夫',
  
  // 非洲国家
  'Nigeria': '尼日利亚',
  'Kenya': '肯尼亚',
  'Ethiopia': '埃塞俄比亚',
  'Ghana': '加纳',
  'Morocco': '摩洛哥',
  'Algeria': '阿尔及利亚',
  'Tunisia': '突尼斯',
  'Libya': '利比亚',
  'Sudan': '苏丹',
  'Angola': '安哥拉',
  'Rwanda': '卢旺达',
  'Uganda': '乌干达',
  'Tanzania': '坦桑尼亚',
  'Zimbabwe': '津巴布韦',
  'Botswana': '博茨瓦纳',
  'Namibia': '纳米比亚',
  'Zambia': '赞比亚',
  'Malawi': '马拉维',
  'Mozambique': '莫桑比克',
  'Madagascar': '马达加斯加',
  'Mauritius': '毛里求斯',
  'Seychelles': '塞舌尔',
  
  // 美洲国家
  'Colombia': '哥伦比亚',
  'Venezuela': '委内瑞拉',
  'Peru': '秘鲁',
  'Ecuador': '厄瓜多尔',
  'Bolivia': '玻利维亚',
  'Paraguay': '巴拉圭',
  'Uruguay': '乌拉圭',
  'Costa Rica': '哥斯达黎加',
  'Panama': '巴拿马',
  'Guatemala': '危地马拉',
  'Honduras': '洪都拉斯',
  'El Salvador': '萨尔瓦多',
  'Nicaragua': '尼加拉瓜',
  'Cuba': '古巴',
  'Jamaica': '牙买加',
  'Dominican Republic': '多米尼加',
  'Haiti': '海地',
  'Trinidad and Tobago': '特立尼达和多巴哥',
  'Barbados': '巴巴多斯',
  'Bahamas': '巴哈马',
  
  // 大洋洲国家
  'Fiji': '斐济',
  'Papua New Guinea': '巴布亚新几内亚',
  'Samoa': '萨摩亚',
  'Tonga': '汤加',
  'Vanuatu': '瓦努阿图',
  'Solomon Islands': '所罗门群岛',
  'Palau': '帕劳',
  'Marshall Islands': '马绍尔群岛',
  'Micronesia': '密克罗尼西亚',
  'Kiribati': '基里巴斯',
  'Tuvalu': '图瓦卢',
  'Nauru': '瑙鲁',
  
  // 国际组织和特殊地区
  'European Space Agency': '欧洲航天局',
  'International': '国际',
  'Intelsat': '国际通信卫星组织',
  'Inmarsat': '国际海事卫星组织',
  'Globalstar': '全球星',
  'Iridium': '铱星',
  'SES': 'SES卫星',
  'Eutelsat': '欧洲通信卫星',
  'Hong Kong': '香港',
  'Taiwan': '台湾',
  'Puerto Rico': '波多黎各',
  'Bermuda': '百慕大',
  'Greenland': '格陵兰',
  'Antarctica': '南极洲',
  
  // 前苏联国家
  'Armenia': '亚美尼亚',
  'Azerbaijan': '阿塞拜疆',
  'Georgia': '格鲁吉亚',
  
  // 中东国家
  'Iraq': '伊拉克',
  'Syria': '叙利亚',
  'Lebanon': '黎巴嫩',
  'Jordan': '约旦',
  'Kuwait': '科威特',
  'Qatar': '卡塔尔',
  'Bahrain': '巴林',
  'Oman': '阿曼',
  'Yemen': '也门',
  
  // 其他常见映射
  'Unknown': '未知',
  'Other': '其他',
  'TBD': '待定',
  'Czechoslovakia': '捷克斯洛伐克',
  'Soviet Union': '苏联',
  'Yugoslavia': '南斯拉夫',
  'East Germany': '东德',
  'West Germany': '西德'
};

/**
 * 获取国家的中文名称
 * @param englishName 英文国家名称
 * @returns 中文国家名称，如果没有找到映射则返回原英文名称
 */
export function getChineseCountryName(englishName: string): string {
  if (!englishName || englishName.trim() === '') {
    return '未知';
  }
  
  return countryChineseMap[englishName] || englishName;
}

/**
 * 检查是否有中文映射
 * @param englishName 英文国家名称
 * @returns 是否存在中文映射
 */
export function hasChineseMapping(englishName: string): boolean {
  return englishName in countryChineseMap;
}
