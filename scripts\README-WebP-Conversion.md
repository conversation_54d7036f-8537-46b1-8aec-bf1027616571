# 壁纸WebP格式转换指南

## 概述

本指南将帮助您将启动加载屏幕的背景图片从JPG格式转换为WebP格式，以获得更好的压缩率和加载性能。

## 已完成的更改

✅ **代码已更新**：
- `src/keeptrack.ts` 中的导入路径已从 `.jpg` 更改为 `.webp`
- 所有变量名已从 `*Jpg` 更改为 `*Webp`
- 启动屏幕将自动使用WebP格式的图片

## 需要转换的图片文件

以下JPG文件需要转换为WebP格式：

```
public/img/wallpaper/
├── blue-marble.jpg → blue-marble.webp
├── cubesat.jpg → cubesat.webp
├── Earth.jpg → Earth.webp
├── iss.jpg → iss.webp
├── moon.jpg → moon.webp
├── observatory.jpg → observatory.webp
├── rocket.jpg → rocket.webp
├── rocket2.jpg → rocket2.webp
├── rocket3.jpg → rocket3.webp
├── rocket4.jpg → rocket4.webp
├── sat.jpg → sat.webp
├── sat2.jpg → sat2.webp
├── telescope.jpg → telescope.webp
└── thule.jpg → thule.webp
```

## 转换方法

### 方法1：使用提供的脚本

#### Windows用户：
```bash
# 运行批处理脚本
scripts\convert-images.bat
```

#### Linux/Mac用户：
```bash
# 给脚本添加执行权限
chmod +x scripts/convert-images.sh

# 运行脚本
./scripts/convert-images.sh
```

### 方法2：使用Node.js脚本（推荐）

1. 安装Sharp库：
```bash
npm install sharp
```

2. 运行转换脚本：
```bash
node scripts/convert-wallpapers-to-webp.js
```

### 方法3：使用在线工具

如果您没有安装命令行工具，可以使用在线WebP转换器：

1. 访问 https://convertio.co/jpg-webp/ 或类似网站
2. 上传JPG文件
3. 下载转换后的WebP文件
4. 将WebP文件放置在 `public/img/wallpaper/` 目录中

### 方法4：使用图像编辑软件

- **Photoshop**：安装WebP插件后可以直接导出WebP格式
- **GIMP**：支持WebP格式的导出
- **其他工具**：ImageMagick、XnConvert等

## 转换设置建议

- **质量**：85%（平衡文件大小和图像质量）
- **压缩**：有损压缩
- **保持原始尺寸**

## 验证转换结果

转换完成后：

1. 检查所有14个WebP文件是否已创建
2. 验证文件大小（WebP应该比JPG小20-50%）
3. 运行应用程序测试启动屏幕是否正常显示

## 清理工作

转换成功并验证后，您可以：

1. 删除原始的JPG文件以节省空间
2. 提交WebP文件到版本控制系统

## 预期收益

- **文件大小减少**：通常减少20-50%
- **加载速度提升**：更快的网络传输
- **带宽节省**：特别是在移动设备上

## 故障排除

### 如果WebP文件无法显示：

1. 检查浏览器兼容性（现代浏览器都支持WebP）
2. 确认文件路径正确
3. 检查文件是否损坏
4. 验证Webpack配置是否支持WebP文件

### 如果转换失败：

1. 检查原始JPG文件是否存在
2. 确认有足够的磁盘空间
3. 验证转换工具是否正确安装

## 技术细节

WebP格式的优势：
- 更好的压缩算法
- 支持有损和无损压缩
- 支持透明度
- 广泛的浏览器支持

## 联系支持

如果遇到问题，请检查：
1. 控制台错误信息
2. 网络请求状态
3. 文件路径是否正确
