# Keep Track 7 - Vega Viewpoint

"Vega" is one of the brightest stars in the night sky, and "Viewpoint" resonates with the enhanced UI improvements and filter settings.

## Software Release Documentation

This software release introduces significant UI improvements, including new splash screen wallpapers, a new logo, and a redesigned loading screen. Functionality has been greatly enhanced with features like agency dot hiding, a debug menu, and advanced filter settings. Stability and performance are bolstered with numerous bug fixes, ranging from country menu fixes to memory leak resolutions. The infrastructure sees the addition of tests and an upgrade to the 'serve' component. Additionally, important documentation updates ensure users and contributors are well-informed.

## Major Features

### UI & Aesthetics

- Added new splash screen wallpapers.
- Introduced a new logo and a refreshed loading screen.

### Functionality Enhancements

- Introduced settings to hide agency dots.
- Added a debug menu for development and troubleshooting purposes.
- Integrated filter settings for various orbits.
- Enhanced the Field of View (FOV) by adding labels to watchlist items.
- Implemented TLE validation when creating TLEs.

## Minor Features

- Refactored the default lookangles setting, changing it from 7 days to 2 days.
- Enhanced type checking for more robust application performance.

### Bug Fixes

- Resolved an issue with the country menu not functioning correctly.
- Fixed an anomaly where the timeMachine displayed unrelated objects.
- Completed missing components of the country matching code.
- Addressed a bug where search results did not respond to user input.
- Fixed an issue with the watchlist line connected to the satellite.
- Enhanced error catching mechanism to handle unforeseen issues more gracefully.
- Rectified a memory leak issue associated with the line-factory.
- Improved screenshot resolution for clarity and better visual representation.

### Code & Infrastructure Updates

- Refactored code for improved type checking.
- Upgraded the 'serve' component in the build setup.
- Added tests specifically for the colorSchemeChangeAlerts feature.

### Documentation

- Updated the changelog to reflect recent changes.
- Revamped the readme file for better clarity and up-to-date information.
