import { keepTrack<PERSON>pi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import { showLoading } from '@app/lib/showLoading';
import gpsPng from '@public/img/icons/gps.png';

import { KeepTrackApiEvents, MenuMode, ToastMsgType } from '@app/interfaces';
import type { CatalogManager } from '@app/singletons/catalog-manager';
import { errorManagerInstance } from '@app/singletons/errorManager';
import type { GroupsManager } from '@app/singletons/groups-manager';
import { GroupType } from '@app/singletons/object-group';
import { DopMath } from '@app/static/dop-math';
import { Degrees, DetailedSatellite, EciVec3, Kilometers, eci2lla } from 'ootk';
import { ClickDragOptions, KeepTrackPlugin } from '../KeepTrackPlugin';

export class DopsPlugin extends KeepTrackPlugin {
  readonly id = 'DopsPlugin';
  dependencies_ = [];

  menuMode: MenuMode[] = [MenuMode.EXPERIMENTAL, MenuMode.ALL];

  bottomIconImg = gpsPng;
  bottomIconCallback = (): void => {
    if (this.isMenuButtonActive) {
      showLoading(DopsPlugin.updateSideMenu);
    }
  };

  dragOptions: ClickDragOptions = {
    isDraggable: true,
    minWidth: 550,
    maxWidth: 800,
  };

  helpTitle = 'Dilution of Precision (DOP) Menu';
  helpBody = keepTrackApi.html`精度因子（DOP）功能用于计算给定位置和高程遮罩的精度因子（DOP）。
    <br><br>
    HDOP是精度的水平精度因子。它是对水平位置精度的度量。
    <br><br>
    PDOP是精度的位置精度因子。这是对位置准确性的衡量。
    <br><br>
    GDOP是精度的几何精度因子。这是对位置准确性的衡量。
  `;

  sideMenuElementName = 'dops-menu';
  sideMenuElementHtml = keepTrackApi.html`
  <div id="${this.sideMenuElementName}" class="side-menu-parent start-hidden text-select">
    <!-- 右上角关闭按钮 -->
    <div id="dops-close-btn" style="position: fixed; top: 20px; right: 20px; z-index: 99999; width: 40px; height: 40px; background: transparent; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 28px; color: #fff; font-weight: bold; user-select: none;" title="关闭">×</div>
    <div id="dops-content" class="side-menu">
      <form id="dops-form">
        <div class="switch row">
          <h5 class="center-align">DOP 列表</h5>
          <div class="input-field col s3" data-position="bottom" data-offset="60" data-tooltip="纬度">
            <input value="41" id="dops-lat" type="text">
            <label for="dops-lat" class="active">纬度</label>
          </div>
          <div class="input-field col s3" data-position="bottom" data-offset="60" data-tooltip="经度">
            <input value="-71" id="dops-lon" type="text">
            <label for="dops-lon" class="active">经度</label>
          </div>
          <div class="input-field col s3" data-position="bottom" data-offset="60" data-tooltip="高度/km">
            <input value="-71" id="dops-alt" type="text">
            <label for="dops-alt" class="active">高度</label>
          </div>
          <div class="input-field col s3" data-position="bottom" data-offset="60" data-tooltip="GPS锁定最小仰角">
            <input value="15" id="dops-el" type="text">
            <label for="dops-el" class="active">掩码</label>
          </div>
        </div>
        <div class="row center">
          <button id="dops-submit" class="btn btn-ui waves-effect waves-light" type="submit"
            name="action">更新DOP数据 &#9658;
          </button>
        </div>
      </form>
    <div class="row">
      <table id="dops" class="center-align striped-light centered"></table>
    </div>
  </div>`;

  rmbL1ElementName = 'dops-rmb';
  rmbL1Html = keepTrackApi.html`
  <li class="rmb-menu-item" id=${this.rmbL1ElementName}><a href="#">DOPs &#x27A4;</a></li>
`;

  isRmbOnEarth = true;
  isRmbOffEarth = false;
  isRmbOnSat = false;

  rmbL2ElementName = 'dops-rmb-menu';
  rmbL2Html = keepTrackApi.html`
  <ul class='dropdown-contents'>
    <li id="dops-curdops-rmb"><a href="#">当前GPS DOPs</a></li>
    <li id="dops-24dops-rmb"><a href="#">24小时GPS DOPs</a></li>
  </ul>
`;

  rmbCallback = (targetId: string): void => {
    switch (targetId) {
      case 'dops-curdops-rmb': {
        {
          let latLon = keepTrackApi.getInputManager().mouse.latLon;
          const dragPosition = keepTrackApi.getInputManager().mouse.dragPosition;

          if (typeof latLon === 'undefined' || isNaN(latLon.lat) || isNaN(latLon.lon)) {
            errorManagerInstance.debug('latLon undefined!');
            const gmst = keepTrackApi.getTimeManager().gmst;

            latLon = eci2lla(
              {
                x: dragPosition[0],
                y: dragPosition[1],
                z: dragPosition[2],
              } as EciVec3,
              gmst,
            );
          }
          const gpsSatObjects = DopsPlugin.getGpsSats(keepTrackApi.getCatalogManager(), keepTrackApi.getGroupsManager());
          const gpsDOP = DopMath.getDops(keepTrackApi.getTimeManager().simulationTimeObj, gpsSatObjects, latLon.lat, latLon.lon, <Kilometers>0, settingsManager.gpsElevationMask);

          keepTrackApi
            .getUiManager()
            .toast(`水平精度因子: ${gpsDOP.hdop}<br/>垂直精度因子: ${gpsDOP.vdop}<br/>位置: ${gpsDOP.pdop}<br/>几何精度因子: ${gpsDOP.gdop}<br/>时间精度因子: ${gpsDOP.tdop}`, ToastMsgType.standby, true);
        }
        break;
      }
      case 'dops-24dops-rmb': {
        const latLon = keepTrackApi.getInputManager().mouse.latLon;

        if (!this.isMenuButtonActive) {
          (<HTMLInputElement>getEl('dops-lat')).value = latLon.lat.toFixed(3);
          (<HTMLInputElement>getEl('dops-lon')).value = latLon.lon.toFixed(3);
          (<HTMLInputElement>getEl('dops-alt')).value = '0';
          (<HTMLInputElement>getEl('dops-el')).value = settingsManager.gpsElevationMask.toString();
          keepTrackApi.emit(KeepTrackApiEvents.bottomMenuClick, this.bottomIconElementName);
        } else {
          showLoading(DopsPlugin.updateSideMenu);
          this.setBottomIconToEnabled();
          break;
        }
      }
        break;
      default:
        break;
    }
  };

  addJs(): void {
    super.addJs();

    // 添加关闭按钮事件监听器
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target && target.id === 'dops-close-btn') {
        e.preventDefault();
        e.stopPropagation();

        // 隐藏菜单
        const menu = document.getElementById('dops-menu');
        if (menu) {
          menu.classList.add('start-hidden');
          menu.style.display = 'none';
        }

        // 取消底部图标选中状态
        const bottomIcon = document.getElementById(this.bottomIconElementName);
        if (bottomIcon) {
          bottomIcon.classList.remove('bmenu-item-selected');
        }

        // 重置插件状态
        this.isMenuButtonActive = false;
      }
    });

    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {
        getEl('dops-form')!.addEventListener('submit', (e: Event) => {
          e.preventDefault();
          DopsPlugin.updateSideMenu();
        });
      },
    );
  }

  static updateSideMenu(): void {
    const groupManagerInstance = keepTrackApi.getGroupsManager();
    const catalogManagerInstance = keepTrackApi.getCatalogManager();
    const timeManagerInstance = keepTrackApi.getTimeManager();

    const lat = <Degrees>parseFloat((<HTMLInputElement>getEl('dops-lat')).value);
    const lon = <Degrees>parseFloat((<HTMLInputElement>getEl('dops-lon')).value);
    const alt = <Kilometers>parseFloat((<HTMLInputElement>getEl('dops-alt')).value);
    const el = <Degrees>parseFloat((<HTMLInputElement>getEl('dops-el')).value);

    settingsManager.gpsElevationMask = el;
    const gpsSats = DopsPlugin.getGpsSats(catalogManagerInstance, groupManagerInstance);
    const getOffsetTimeObj = (now: number) => timeManagerInstance.getOffsetTimeObj(now);
    const dopsList = DopMath.getDopsList(getOffsetTimeObj, gpsSats, lat, lon, alt, el);

    DopMath.updateDopsTable(dopsList);
  }

  static getGpsSats(catalogManagerInstance: CatalogManager, groupManagerInstance: GroupsManager): DetailedSatellite[] {
    if (!groupManagerInstance.groupList.GPSGroup) {
      groupManagerInstance.groupList.GPSGroup = groupManagerInstance.createGroup(GroupType.NAME_REGEX, /NAVSTAR/iu, 'GPSGroup');
    }
    const gpsSats = groupManagerInstance.groupList.GPSGroup;
    const gpsSatObjects = [] as DetailedSatellite[];

    gpsSats.ids.forEach((id: number) => {
      const sat = catalogManagerInstance.getSat(id);

      if (sat) {
        gpsSatObjects.push(sat);
      }
    });

    return gpsSatObjects;
  }
}
