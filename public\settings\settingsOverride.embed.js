/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

// Settings Manager Overrides
const settingsOverride = {
  /*
   * These are the overrides for the embedded version.
   */
  plugins: {
    DebugMenuPlugin: {
      enabled: false,
      order: 0,
    },
    SensorListPlugin: {
      enabled: false,
      order: 10,
    },
    SensorInfoPlugin: {
      enabled: false,
      order: 11,
    },
    CustomSensorPlugin: {
      enabled: false,
      order: 12,
    },
    SensorFov: {
      enabled: false,
      order: 13,
    },
    SensorSurvFence: {
      enabled: false,
      order: 14,
    },
    ShortTermFences: {
      enabled: false,
      order: 15,
    },
    LookAnglesPlugin: {
      enabled: false,
      order: 20,
    },
    MultiSiteLookAnglesPlugin: {
      enabled: false,
      order: 21,
    },
    SensorTimeline: {
      enabled: false,
      order: 30,
    },
    SatelliteTimeline: {
      enabled: false,
      order: 31,
    },
    WatchlistPlugin: {
      enabled: false,
      order: 40,
    },
    WatchlistOverlay: {
      enabled: false,
      order: 41,
    },
    ReportsPlugin: {
      enabled: false,
      order: 50,
    },
    PolarPlotPlugin: {
      enabled: false,
      order: 60,
    },
    CreateSat: {
      enabled: false,
      order: 70,
    },
    EditSat: {
      enabled: false,
      order: 71,
    },
    NewLaunch: {
      enabled: false,
      order: 72,
    },
    Breakup: {
      enabled: false,
      order: 73,
    },
    MissilePlugin: {
      enabled: false,
      order: 74,
    },
    SatelliteFov: {
      enabled: false,
      order: 75,
    },
    FindSatPlugin: {
      enabled: false,
      order: 80,
    },
    ProximityOps: {
      enabled: false,
      order: 81,
    },
    Collisions: {
      enabled: false,
      order: 90,
    },
    TrackingImpactPredict: {
      enabled: false,
      order: 91,
    },
    StereoMap: {
      enabled: false,
      order: 150,
    },
    SatelliteViewPlugin: {
      enabled: false,
      order: 151,
    },
    Planetarium: {
      enabled: false,
      order: 155,
    },
    Astronomy: {
      enabled: false,
      order: 156,
    },
    SatConstellations: {
      enabled: false,
      order: 230,
    },
    CountriesMenu: {
      enabled: false,
      order: 231,
    },
    ColorMenu: {
      enabled: false,
      order: 232,
    },
    SatellitePhotos: {
      enabled: false,
      order: 240,
    },
    TimeMachine: {
      enabled: false,
      order: 250,
    },
    EciPlot: {
      enabled: false,
      order: 260,
    },
    EcfPlot: {
      enabled: false,
      order: 261,
    },
    RicPlot: {
      enabled: false,
      order: 262,
    },
    Time2LonPlots: {
      enabled: false,
      order: 263,
    },
    Lat2LonPlots: {
      enabled: false,
      order: 264,
    },
    Inc2AltPlots: {
      enabled: false,
      order: 265,
    },
    Inc2LonPlots: {
      enabled: false,
      order: 266,
    },
    NightToggle: {
      enabled: false,
      order: 310,
    },
    DebrisScreening: {
      enabled: false,
      order: 280,
    },
    transponderChannelData: {
      enabled: false,
    },
    NextLaunchesPlugin: {
      enabled: false,
      order: 350,
    },
    LaunchCalendar: {
      enabled: false,
      order: 351,
    },
    Calculator: {
      enabled: false,
      order: 400,
    },
    InitialOrbitDeterminationPlugin: {
      enabled: false,
      order: 410,
    },
    AnalysisMenu: {
      enabled: false,
      order: 420,
    },
    Screenshot: {
      enabled: false,
      order: 450,
    },
    ScreenRecorder: {
      enabled: false,
      order: 451,
    },
    DopsPlugin: {
      enabled: false,
      order: 500,
    },
    SatChangesPlugin: {
      enabled: false, // Backend no longer supports this
      order: 501, // TODO: Update when backend is ready
    },
    VideoDirectorPlugin: {
      enabled: false,
      order: 510,
    },
    SettingsMenuPlugin: {
      enabled: false,
      order: 590,
    },
    GraphicsMenuPlugin: {
      enabled: true,
      order: 591,
    },
    FilterMenuPlugin: {
      enabled: false,
      order: 592,
    },
    AboutMenuPlugin: {
      enabled: false,
      order: 601,
    },
    // Non-Menu plugins
    SatInfoBox: {
      enabled: false,
    },
    TopMenu: {
      enabled: false,
    },
    SocialMedia: {
      enabled: false,
    },
    DateTimeManager: {
      enabled: false,
    },
    ClassificationBar: {
      enabled: false,
    },
    OrbitReferences: {
      enabled: false,
    },
    SoundManager: {
      enabled: false,
    },
    GamepadPlugin: {
      enabled: false,
    },
    // RMB plugins
    EarthPresetsPlugin: {
      enabled: false,
    },
    DrawLinesPlugin: {
      enabled: false,
    },
    ViewInfoRmbPlugin: {
      enabled: false,
    },
  },
  dataSources: {
    tle: 'https://api.spacedefense/v3/sats',
    externalTLEsOnly: false,
    tleDebris: 'https://app.spacedefense/tle/TLEdebris.json',
    vimpel: 'https://r2.spacedefense/vimpel.json',
    /** This determines if tle source is loaded to supplement externalTLEs  */
    isSupplementExternal: false,
  },
  isShowSecondaryLogo: false,
  isEnableJscCatalog: true,
  noMeshManager: true,
  isShowSplashScreen: false,
  isDisableSensors: true,
  isDisableSelectSat: true,
  isDisableLaunchSites: true,
  isDisableKeyboard: true,
  isAllowRightClick: false,
  isShowLoadingHints: false,
  isBlockPersistence: true,
  isDisableBottomMenu: true,
  isDrawSun: false,
  isDrawMilkyWay: false,
  isDisableGodrays: true,
  godraysSamples: -1,
  isDisableMoon: true,
  earthDayTextureQuality: '16k',
  earthNightTextureQuality: '16k',
  isDrawNightAsDay: true,
  earthSpecTextureQuality: '4k',
  isDrawSpecMap: true,
  earthBumpTextureQuality: '4k',
  isDrawBumpMap: false,
  earthCloudTextureQuality: '4k',
  isDrawCloudsMap: false,
  earthPoliticalTextureQuality: '4k',
  isDrawPoliticalMap: false,
  earthTextureStyle: 'earthmap', // 'earthmap' or 'flat'
  isEmbedMode: true,
};

// Expose these to the console
window.settingsOverride = settingsOverride;
