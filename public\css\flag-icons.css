/* 🔥 基础国旗样式 - 不覆盖官方flag-icons库的.fi类 */
/* 注意：这里不重新定义.fi，让官方库的定义生效 */

/* 🔥 自定义尺寸覆盖，但保持官方库的background-image */
.fi {
    width: calc(24px / var(--system-scale-factor, 1)) !important;
    height: calc(18px / var(--system-scale-factor, 1)) !important;
}

.fi-unknown {
    background-image: url(../flags/tbd.png);
}

.fi-cis {
    background-image: url(../flags/cis.svg);
}

.fi-nato {
    background-image: url(../flags/nato.png);
}

.fi-su {
    background-image: url(../flags/ussr.png);
}

.fi-esa {
    background-image: url(../flags/esa.png);
}

.fi-iss {
    background-image: url(../flags/iss.png);
}

/* 🔥 添加缺失的标准国旗定义 - 使用官方flag-icons库的命名规范 */
.fi-gb {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 640 480'%3e%3cpath fill='%23012169' d='M0 0h640v480H0z'/%3e%3cpath fill='%23FFF' d='m75 0 244 181L562 0h78v62L400 241l240 178v61h-80L320 301 81 480H0v-60l239-178L0 64V0h75z'/%3e%3cpath fill='%23C8102E' d='m424 281 216 159v40L369 281h55zm-184 20 6 35L54 480H0l246-179zM640 0v3L391 191l2-44L590 0h50zM0 0l239 176h-60L0 42V0z'/%3e%3cpath fill='%23FFF' d='M241 0v480h160V0H241zM0 160v160h640V160H0z'/%3e%3cpath fill='%23C8102E' d='M0 193v96h640v-96H0zM273 0v480h96V0h-96z'/%3e%3c/svg%3e");
}

.fi-us {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 640 480'%3e%3cpath fill='%23B22234' d='M0 0h640v480H0z'/%3e%3cpath stroke='%23fff' stroke-width='37' d='M0 55.3h640M0 129h640M0 203h640M0 277h640M0 351h640M0 425h640'/%3e%3cpath fill='%233C3B6E' d='M0 0h364.8v258.5H0z'/%3e%3cmarker id='a' markerHeight='30' markerWidth='30'%3e%3cpath fill='%23fff' d='m14 0 9 27L0 10h28L5 27z'/%3e%3c/marker%3e%3cpath fill='none' marker-mid='url(%23a)' d='m0 0 16 11h61 61 61 61 60L47 37h61 61 61 61 60L94 63h61 61 61 61 60l-47 26h61 61 61 61 60l-47 26h61 61 61 61 60l-47 26h61 61 61 61 60l-47 26h61 61 61 61 60l-47 26h61 61 61 61 60'/%3e%3c/svg%3e");
}

.fi-ru {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 640 480'%3e%3cg fill-rule='evenodd' stroke-width='1pt'%3e%3cpath fill='%23fff' d='M0 0h640v160H0z'/%3e%3cpath fill='%230039a6' d='M0 160h640v160H0z'/%3e%3cpath fill='%23d52b1e' d='M0 320h640v160H0z'/%3e%3c/g%3e%3c/svg%3e");
}

.fi-cn {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 640 480'%3e%3cdefs%3e%3cpath id='a' fill='%23ffde00' d='M-.6.8 0-1 .6.8-1-.3h2z'/%3e%3c/defs%3e%3cpath fill='%23de2910' d='M0 0h640v480H0z'/%3e%3cuse width='30' height='20' transform='matrix(71.9991 0 0 72 120 120)' href='%23a'/%3e%3cuse width='30' height='20' transform='matrix(-12.33562 -20.5871 20.58684 -12.33577 240 194.4)' href='%23a'/%3e%3cuse width='30' height='20' transform='matrix(-3.38573 -23.75998 23.75968 -3.38578 288 230.4)' href='%23a'/%3e%3cuse width='30' height='20' transform='matrix(6.5991 -22.80528 22.80523 6.59919 288 288)' href='%23a'/%3e%3cuse width='30' height='20' transform='matrix(14.9991 -18.73557 18.73533 14.99929 240 345.6)' href='%23a'/%3e%3c/svg%3e");
}
