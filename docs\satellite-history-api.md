# 卫星历史数据查询API文档

## 概述

新的标准化卫星历史数据查询API提供了一个统一的端点，智能支持历史轨道数据和历史经度数据查询。这个API设计解决了原有API在支持多种查询类型时的数据格式不一致问题。

## API端点

### 统一查询端点

**端点**: `GET /api/satellite-history`

**用途**: 根据参数智能识别查询类型，支持轨道数据和经度数据查询

### 1. 历史轨道数据查询模式

**参数**:
- `norad_id` (必需): NORAD编号，支持多个ID用逗号分隔
- `start` (必需): 开始日期，格式: YYYY-MM-DD
- `end` (必需): 结束日期，格式: YYYY-MM-DD

**示例请求**:
```
GET /api/satellite-history?norad_id=25544&start=2025-01-01&end=2025-07-01
GET /api/satellite-history?norad_id=25544,39084&start=2025-01-01&end=2025-07-01
```

**响应格式**:
```json
{
  "count": 763,
  "data": [
    {
      "norad_id": 25544,
      "time": "2025-01-01T00:00:00Z",
      "satellite_name": "ISS (ZARYA)",
      "orbital_elements": {
        "inc_deg": 51.6461,
        "raan_deg": 123.4567,
        "ecc": 0.0001234,
        "arg_peri_deg": 89.1234,
        "mean_anom_deg": 270.5678,
        "orbital_period_min": 92.68,
        "sema_km": 6793.45,
        "arg_alt_km": 415.2
      }
    }
  ],
  "query_type": "orbital",
  "parameters": {
    "noradIds": [25544],
    "startDate": "2025-01-01",
    "endDate": "2025-07-01"
  }
}
```

### 2. 历史经度数据查询模式

**参数**:
- `start` (必需): 开始日期，格式: YYYY-MM-DD
- `end` (必需): 结束日期，格式: YYYY-MM-DD
- `geo_only` (必需): 设置为 `true` 启用GEO卫星查询模式
- `lon_min` (可选): 最小经度，范围: -180 到 180
- `lon_max` (可选): 最大经度，范围: -180 到 180

**示例请求**:
```
GET /api/satellite-history?start=2025-01-01&end=2025-07-01&geo_only=true
GET /api/satellite-history?start=2025-01-01&end=2025-07-01&geo_only=true&lon_min=85&lon_max=100
```

**响应格式**:
```json
{
  "count": 1250,
  "data": [
    {
      "norad_id": 39084,
      "time": "2025-01-01T00:00:00Z",
      "satellite_name": "GOES-16",
      "object_type": "GEO",
      "longitude": -75.2,
      "latitude": 0.1
    }
  ],
  "query_type": "longitude",
  "parameters": {
    "startDate": "2025-01-01",
    "endDate": "2025-07-01",
    "lonMin": 85,
    "lonMax": 100
  }
}
```

## 数据字段说明

### 轨道数据字段 (orbital_elements)

| 字段名 | 描述 | 单位 |
|--------|------|------|
| `inc_deg` | 轨道倾角 | 度 |
| `raan_deg` | 升交点赤经 | 度 |
| `ecc` | 偏心率 | 无量纲 |
| `arg_peri_deg` | 近地点幅角 | 度 |
| `mean_anom_deg` | 平近点角 | 度 |
| `orbital_period_min` | 轨道周期 | 分钟 |
| `sema_km` | 半长轴 | 公里 |
| `arg_alt_km` | 轨道高度 | 公里 |

### 经度数据字段

| 字段名 | 描述 | 单位 |
|--------|------|------|
| `longitude` | 星下点经度 | 度 |
| `latitude` | 星下点纬度 | 度 |
| `object_type` | 卫星类型 | 字符串 |

## 错误处理

### 常见错误响应

**400 Bad Request** - 参数错误:
```json
{
  "error": "缺少必需参数",
  "message": "请提供start和end日期参数",
  "example": "/api/satellite-history/orbital?norad_id=25544&start=2025-01-01&end=2025-07-01"
}
```

**500 Internal Server Error** - 服务器错误:
```json
{
  "error": "查询失败",
  "message": "数据库连接超时"
}
```

## 使用限制

- 最大记录数: 50,000条
- 批次大小: 1,000条
- 查询超时: 30秒
- 日期范围: 建议不超过1年

## 前端集成

### 历史轨道数据查询示例

```typescript
async function fetchOrbitalData(noradIds: string[], startDate: string, endDate: string) {
  const params = new URLSearchParams();
  params.append('norad_id', noradIds.join(','));
  params.append('start', startDate);
  params.append('end', endDate);
  
  const response = await fetch(`/api/satellite-history/orbital?${params.toString()}`);
  const data = await response.json();
  
  if (data.data) {
    // 处理轨道数据
    data.data.forEach(record => {
      const orbitalElements = record.orbital_elements;
      console.log(`卫星 ${record.satellite_name} 在 ${record.time} 的轨道高度: ${orbitalElements.arg_alt_km} km`);
    });
  }
}
```

### 历史经度数据查询示例

```typescript
async function fetchLongitudeData(startDate: string, endDate: string, lonMin?: number, lonMax?: number) {
  const params = new URLSearchParams();
  params.append('start', startDate);
  params.append('end', endDate);
  if (lonMin !== undefined) params.append('lon_min', lonMin.toString());
  if (lonMax !== undefined) params.append('lon_max', lonMax.toString());
  
  const response = await fetch(`/api/satellite-history/longitude?${params.toString()}`);
  const data = await response.json();
  
  if (data.data) {
    // 处理经度数据
    data.data.forEach(record => {
      console.log(`GEO卫星 ${record.satellite_name} 在 ${record.time} 的经度: ${record.longitude}°`);
    });
  }
}
```

## 迁移指南

### 从旧API迁移

**旧API**: `/api/es-history`
**新API**: `/api/satellite-history/orbital` 或 `/api/satellite-history/longitude`

**主要变化**:
1. 分离了轨道数据和经度数据查询
2. 轨道数据现在包含完整的 `orbital_elements` 对象
3. 经度数据专门针对GEO卫星优化
4. 响应格式更加标准化和一致

**迁移步骤**:
1. 确定查询类型（轨道数据 vs 经度数据）
2. 更新API端点URL
3. 更新数据解析逻辑以适应新的响应格式
4. 测试并验证数据正确性

## 配置

API使用以下配置文件:
- `es-field-mapping.json`: Elasticsearch字段映射配置
- `es-config.enc`: 加密的Elasticsearch连接配置

确保这些配置文件正确设置以保证API正常工作。
