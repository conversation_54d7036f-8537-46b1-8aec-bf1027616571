{"time": {"days": {"0": "Воскресенье", "1": "Понедельник", "2": "Вторник", "3": "Среда", "4": "Четверг", "5": "Пятница", "6": "Суббота"}, "days-short": {"0": "Вс", "1": "Пн", "2": "Вт", "3": "Ср", "4": "Чт", "5": "Пт", "6": "Сб"}, "months": {"1": "Январь", "2": "Февраль", "3": "Ма<PERSON><PERSON>", "4": "Апрель", "5": "<PERSON><PERSON><PERSON>", "6": "Июнь", "7": "Июль", "8": "Август", "9": "Сентябрь", "10": "Октябрь", "11": "Ноябрь", "12": "Декабрь"}, "calendar": {"time": "Время", "hour": "<PERSON><PERSON><PERSON>", "minute": "Минута", "second": "Секунда", "now": "Сей<PERSON><PERSON>с", "done": "Готово", "pause": "Пауза", "propagation": "Пропагирование"}}, "errorMsgs": {"catalogNotFullyInitialized": "Каталог еще не полностью инициализирован. Подождите несколько секунд и попробуйте снова.", "sensorGroupsApiEmpty": "В API не найдено групп датчиков, возврат к встроенной базе данных датчиков.", "SelectSensorFirst": "Сначала выберите датчик!", "SelectSatelliteFirst": "Сначала выберите спутник!", "SelectSecondarySatellite": "Сначала выберите вторичный спутник!", "SatelliteNotDetailedSatellite": "Спутник не является DetailedSatellite!", "SensorNotFound": "Датчик не найден!", "Scene": {"disablingGodrays": "Ваш компьютер перегружен! Отключение лучей света.", "disablingAurora": "Ваш компьютер перегружен! Отключение авроры.", "disablingAtmosphere": "Ваш компьютер перегружен! Отключение атмосферы.", "disablingMoon": "Ваш компьютер перегружен! Отключение Луны.", "disablingMilkyWay": "Ваш компьютер перегружен! Отключение Млечного Пути.", "disablingSun": "Ваш компьютер перегружен! Отключение Солнца."}, "Breakup": {"CannotCreateBreakupForNonCircularOrbits": "Невозможно создать разрушение для не-круговых орбит. Работаем над исправлением.", "CannotCalcDirectionOfSatellite": "Невозможно вычислить направление спутника. Попробуйте позже.", "ErrorCreatingBreakup": "Ошибка при создании разрушения!", "InvalidStartNum": "Неверный начальный номер спутника! Установлено значение по умолчанию: 90000!", "BreakupGeneratorFailed": "Генератор разрушений не удалось запустить!", "SatelliteNotFound": "Спутник не найден!"}, "Collisions": {"noCollisionsData": "Данные о столкновениях не найдены!", "errorProcessingCollisions": "Ошибка обработки данных SOCRATES!"}, "EditSat": {"errorReadingFile": "Ошибка при чтении файла!", "satelliteNotFound": "Спутник не найден!"}, "CreateSat": {"errorCreatingSat": "Ошибка при создании спутника!"}, "Reports": {"popupBlocker": "Разрешите всплывающие окна для просмотра отчетов."}, "SensorManager": {"errorUpdatingUi": "Ошибка обновления интерфейса датчиков."}}, "hoverManager": {"launched": "Запущен", "launchedPlanned": "Запущен: Запланировано", "launchedUnknown": "Запущен: Неизвестно"}, "loadingScreenMsgs": {"math": "Пытаемся вычислить...", "science": "Ищем науку...", "science2": "Наука найдена...", "dots": "Рисуем точки в космосе...", "satIntel": "Интеграция спутниковых данных...", "painting": "Рисуем Землю...", "coloring": "Закрашиваем внутри линий...", "elsets": "Ищем GPs...", "models": "Создаём 3D-модели...", "cunningPlan": "Разрабатываем хитроумный план...", "copyrightNotice": "KeepTrack™ и spacedefense™ являются товарными знаками Kruczek Labs LLC.<br>Данный экземпляр лицензирован по GNU AGPL v3.0. Атрибуция, доступ к исходному коду и это уведомление должны оставаться видимыми.<br>Коммерческая лицензия не предоставлялась, и правообладателю не выплачивалось вознаграждение.<br>Несанкционированное использование, ребрендинг или удаление атрибуции могут нарушать условия товарного знака и лицензии с открытым исходным кодом.<br>© 2025 Kruczek Labs LLC. Все права защищены. См. LICENSE для полного текста условий.", "copyrightNoticeMobile": "KeepTrack™ и spacedefense™ являются товарными знаками Kruczek Labs LLC.<br>Данный экземпляр лицензирован по GNU AGPL v3.0. Атрибуция, доступ к исходному коду и это уведомление должны оставаться видимыми. Коммерческая лицензия не предоставлялась. Правообладателю не выплачивалось вознаграждение. Несанкционированное использование, ребрендинг или удаление атрибуции могут нарушать условия товарного знака и лицензии с открытым исходным кодом.<br>© 2025 Kruczek Labs LLC. Все права защищены. См. LICENSE для полного текста условий."}, "splashScreens": {"1": "Модели спутников выглядят больше, чем на самом деле. Все остальное масштабировано.", "2": "Нажмите клавишу 'L', чтобы включить/выключить орбиты спутников.", "3": "Нажмите клавишу 'P', чтобы открыть полярный график текущего спутника (Сначала выберите датчик!).", "4": "Сбросить время симуляции можно, нажав клавишу 'T'.", "5": "Продвинуть время симуляции можно, нажав клавишу '>' или '='.", "6": "Отмотать время симуляции назад можно, нажав клавишу '<' или ')'.", "7": "Нажатие клавиши '/' переключит скорость симуляции между 1x и 0x.", "8": "Нажмите клавишу 'V', чтобы изменить режим просмотра.", "9": "Нажмите Shift+F1, чтобы открыть меню помощи в любое время.", "10": "Нажмите клавишу 'R', чтобы включить авто-вращение.", "11": "Нажатие Shift + C при выборе спутника включит/выключит его конус видимости.", "12": "Клавиша 'Home' повернет камеру к текущему датчику.", "13": "Клавиша '`' (тильда) сбросит камеру в стандартный вид.", "14": "Клав<PERSON><PERSON><PERSON> 'M' покажет 2D-карту текущего спутника.", "15": "Меню настроек в нижней панели инструментов содержит множество параметров для настройки интерфейса.", "16": "Многие меню имеют дополнительные настройки, доступные при нажатии значка шестеренки.", "17": "Добавьте спутники в список наблюдения, чтобы получать уведомления, когда они окажутся над текущим датчиком.", "18": "Щелкните правой кнопкой мыши по глобусу, чтобы открыть контекстное меню с дополнительными опциями.", "19": "Нажимайте клавиши '+' или '-' для увеличения и уменьшения масштаба.", "20": "Нажмите 'F11', чтобы включить/выключить полноэкранный режим.", "21": "Вы можете искать спутники по имени или NORAD ID в строке поиска в правом верхнем углу.", "22": "Новый запуск можно создать, выбрав спутник и нажав кнопку 'Новый запуск' в нижнем меню.", "23": "Нажмите клавишу 'N', чтобы переключить режим ночи.", "24": "Нажмите клавишу 'I', чтобы скрыть/показать контекстную информацию о спутнике.", "25": "Нажмите клавишу 'B', чтобы скрыть/показать меню.", "26": "Ускорьте симуляцию, нажав Shift + ';'.", "27": "Замедлите симуляцию, нажав ','.", "28": "Установите объект как вторичный, чтобы видеть его относительное расстояние с основным объектом.", "29": "Переключайте объект между основным/вторичным с помощью клавиши '['."}, "plugins": {"SensorListPlugin": {"bottomIconLabel": "Датчики", "title": "Меню списка датчиков", "helpBody": "Меню датчиков позволяет выбрать датчик для использования в расчетах и функциях других меню. Датчики сгруппированы по сетям, которые они в основном поддерживают. На левой стороне меню указано название датчика, а на правой - страна/организация, которой он принадлежит. <br><br> Выбор опции \"Все... датчики\" выберет все датчики в этой группе. Это полезно для визуализации сетевого покрытия, но в настоящее время не работает для всех расчетов. Если вы пытаетесь рассчитать углы обзора для сети, лучше использовать инструмент многоузловых углов обзора или использовать углы обзора для каждого отдельного датчика в сети. <br><br> В этот список входят механические и фазированные радары, а также оптические датчики: <ul style=\"margin-left: 40px;\"> <li> Фазированные радары обычно ограничены низкой околоземной орбитой (LEO). </li> <li> Механические радары могут использоваться как для LEO, так и для геостационарной орбиты (GEO). </li> <li> Оптические датчики обычно используются для GEO, но могут также использоваться для LEO. </li> <li> Оптические датчики ограничены ночными наблюдениями при ясном небе, тогда как радары можно использовать как днем, так и ночью. </li> </ul> <br> Информация о датчиках основана на общедоступных данных и может быть проверена в меню Информация о датчике. Если у вас есть общедоступные данные о дополнительных датчиках или исправления к существующей информации о датчиках, свяжитесь со мной по адресу <a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>."}, "SensorInfoPlugin": {"bottomIconLabel": "Информация о датчике", "title": "Меню информации о датчике", "helpBody": "Информация о датчике предоставляет сведения о выбранном в настоящее время датчике. Информация основана на общедоступных данных и может быть не всегда на 100% точной. Если у вас есть общедоступные данные о дополнительных датчиках или исправления к существующей информации о датчиках, свяжитесь со мной по адресу <a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>. <br><br> Предоставляемая информация включает: <ul style=\"margin-left: 40px;\"> <li> Название датчика </li> <li> Владелец датчика </li> <li> Тип датчика </li> <li> Поле зрения датчика </li> </ul> <br> Кроме того, из этого меню можно быстро создать линии от датчика к солнцу или луне."}, "CustomSensorPlugin": {"bottomIconLabel": "Пользовательский датчик", "title": "Меню пользовательского датчика", "helpBody": "Это позволяет создать пользовательский датчик для использования в расчетах и функциях других меню. Это может быть совершенно оригинальный датчик или модификация существующего датчика. <br><br> После установки широты, долготы и высоты датчика вы можете установить поле зрения датчика. Выбор телескопа создаст поле зрения 360 градусов с маской возвышения 10 градусов и неограниченным диапазоном. Отмена выбора опции телескопа позволит вам установить поле зрения вручную. <br><br> Если вы пытаетесь отредактировать существующий датчик, вы можете сначала выбрать его из списка датчиков, и пользовательский датчик будет обновлен с информацией о выбранном датчике."}, "LookAnglesPlugin": {"bottomIconLabel": "Углы обзора", "title": "Меню углов обзора", "helpBody": "Меню углов обзора позволяет рассчитывать дальность, азимут и углы возвышения между датчиком и спутником. Перед использованием меню необходимо сначала выбрать спутник и датчик. <br><br> Переключатель \"только время восхода и захода\" будет рассчитывать только время восхода и захода спутника. Это полезно для быстрого определения, когда спутник будет виден датчику. <br><br> Диапазон поиска можно изменить, изменив параметры длительности и интервала."}, "MultiSiteLookAnglesPlugin": {"bottomIconLabel": "Многоузловые углы обзора", "title": "Меню многоузловых углов обзора", "helpBody": "Меню многоузловых углов обзора позволяет рассчитать дальность, азимут и углы возвышения между спутником и несколькими датчиками. Перед использованием меню необходимо сначала выбрать спутник. <br><br> По умолчанию меню будет рассчитывать углы обзора для всех датчиков в Сети наблюдения за космическим пространством. Если вы хотите рассчитать углы обзора для дополнительных датчиков, вы можете экспортировать файл csv внизу меню. Файл csv будет содержать углы обзора для всех датчиков. <br><br> Нажатие на строку в таблице выберет датчик и изменит время симуляции на время угла обзора."}, "SensorTimeline": {"bottomIconLabel": "Временная шкала датчика", "title": "Меню временной шкалы датчика", "helpBody": "Меню временной шкалы датчика показывает, когда список датчиков имеет видимость одного спутника. Временная шкала имеет цветовую кодировку для отображения качества прохода. Красный - плохой проход, желтый - средний проход, зеленый - хороший проход. Нажмите на проход, чтобы изменить датчик и время на этот проход. <br><br> Временную шкалу можно изменить, изменив время начала и окончания симуляции. Кроме того, временную шкалу можно изменить, изменив параметры длительности и интервала."}, "ProximityOps": {"bottomIconLabel": "Сближение и операции сближения", "title": "Сближение и операции сближения (RPO)", "titleSecondary": "Список сближений и операций сближения", "helpBody": "Поиск предстоящих сближений между спутниками", "noradId": "NORAD ID", "maxDistThreshold": "Максимальный порог расстояния (км)", "maxRelativeVelocity": "Максимальная относительная скорость (км/с)", "searchDuration": "Длительность поиска (часы)", "geoText": "Геостационарная", "leoText": "Низкая околоземная орбита", "orbitType": "Тип орбиты", "geoAllVsAll": "Геостационарная все против всех", "geoAllVsAllTooltip": "Поиск RPO между всеми геостационарными спутниками", "comparePayloadsOnly": "Сравнивать только полезные нагрузки", "comparePayloadsOnlyTooltip": "Искать RPO только между полезными нагрузками", "ignoreVimpelRso": "Игнорировать Vimpel RSO", "ignoreVimpelRsoTooltip": "Игнорировать RSO из каталога Vimpel"}, "SatelliteTimeline": {"bottomIconLabel": "Временная шкала спутника", "title": "Меню временной шкалы спутника", "helpBody": "Меню временной шкалы спутника показывает, когда один датчик имеет видимость списка спутников. Временная шкала имеет цветовую кодировку для отображения качества прохода. Красный - плохой проход, желтый - средний проход, зеленый - хороший проход. Нажмите на проход, чтобы изменить спутник и время на этот проход. <br><br> Временную шкалу можно изменить, изменив время начала и окончания симуляции. Кроме того, временную шкалу можно изменить, изменив параметры длительности и интервала."}, "WatchlistPlugin": {"bottomIconLabel": "Список наблюдения", "title": "Меню списка наблюдения", "helpBody": "Меню списка наблюдения позволяет создать список приоритетных спутников для отслеживания. Это позволяет быстро получить доступ к спутникам, которые вас больше всего интересуют. Список сохраняется в локальном хранилище вашего браузера и будет доступен при следующем посещении сайта. <br><br> Когда спутники из списка наблюдения входят в поле зрения выбранного датчика, отображается уведомление, рисуется линия от датчика к спутнику, и номер спутника отображается на глобусе. <br><br> Функция наложения зависит от заполнения списка наблюдения."}, "WatchlistOverlay": {"bottomIconLabel": "Наложение", "title": "Меню наложения", "helpBody": "<p> Наложение списка наблюдения показывает время следующего прохода для каждого спутника в вашем списке наблюдения. Наложение обновляется каждые 10 секунд. </p> <p> Наложение имеет цветовую кодировку для отображения времени до следующего прохода. Цвета следующие: </p> <ul> <li>Желтый - В поле зрения</li> <li>Синий - Время до следующего прохода составляет до 30 минут после текущего времени или 10 минут до текущего времени</li> <li>Белый - Любой будущий проход, не соответствующий вышеуказанным требованиям</li> </ul> <p> Нажатие на спутник в наложении центрирует карту на этом спутнике. </p>"}, "ReportsPlugin": {"bottomIconLabel": "Отчеты", "title": "Меню отчетов", "helpBody": "Меню отчетов - это набор инструментов, помогающих анализировать и понимать данные, которые вы просматриваете."}, "PolarPlotPlugin": {"bottomIconLabel": "Полярный график", "title": "Меню полярного графика", "helpBody": "Меню полярного графика используется для создания 2D полярного графика азимута и возвышения спутника с течением времени."}, "NextLaunchesPlugin": {"bottomIconLabel": "Следующие запуски", "title": "Меню следующих запусков", "helpBody": "Меню следующих запусков получает данные с <a href=\"https://thespacedevs.com/\" target=\"_blank\">The Space Devs</a> для отображения предстоящих запусков."}, "FindSatPlugin": {"bottomIconLabel": "Найти спутник", "title": "Меню поиска спутника", "helpBody": "Меню поиска спутника используется для поиска спутников по орбитальным параметрам или характеристикам спутника. <br><br> Для большинства параметров вы вводите целевое значение слева, а затем погрешность справа. Например, если вы хотите найти все спутники с наклонением 51-52 градуса, вы можете ввести 51.5 в левое поле и 0.5 в правое поле. Поиск найдет все спутники в пределах этих наклонений и отобразит их в строке поиска."}, "ShortTermFences": {"bottomIconLabel": "Краткосрочные барьеры", "title": "Меню краткосрочных барьеров (STF)", "helpBody": "Меню краткосрочных барьеров (STF) используется для визуализации поисковых зон датчиков. <br><br> Это вряд ли будет очень полезно, если вы не владеете/не управляете датчиком с функцией поисковой зоны."}, "Collisions": {"bottomIconLabel": "Столкновения", "title": "Меню столкновений", "helpBody": "Меню столкновений показывает спутники с высокой вероятностью столкновения. <br><br> Нажатие на строку выберет два спутника, участвующих в столкновении, и изменит время на время столкновения."}, "TrackingImpactPredict": {"bottomIconLabel": "Прогноз входа", "title": "Меню отслеживания и прогнозирования входа", "helpBody": "Меню отслеживания и прогнозирования входа (TIP) отображает последние сообщения об отслеживании и прогнозировании входа для спутников. В таблице показаны следующие столбцы:<br><br> <b>NORAD</b>: Каталожный идентификатор NORAD спутника.<br><br> <b>Дата схода</b>: Дата прогнозируемого схода спутника с орбиты.<br><br> <b>Широта</b>: Широта спутника во время схода.<br><br> <b>Долгота</b>: Долгота спутника во время схода.<br><br> <b>Окно (мин)</b>: Временное окно в минутах для прогноза.<br><br> <b>Следующий отчет (ч)</b>: Время в часах до следующего отчета.<br><br> <b>Угол входа (град)</b>: Угол входа спутника в градусах.<br><br> <b>ЭПР (м²)</b>: Эффективная площадь рассеяния спутника в квадратных метрах.<br><br> <b>Возраст GP (ч)</b>: Возраст последнего набора элементов в часах.<br><br> <b>Сухая масса (кг)</b>: Сухая масса спутника в килограммах.<br><br> <b>Объем (м³)</b>: Оценочный объем спутника в кубических метрах.<br><br>"}, "Breakup": {"bottomIconLabel": "Создать фрагментацию", "title": "Меню фрагментации", "helpBody": "Меню фрагментации - это инструмент для моделирования фрагментации спутника. <br><br> Путем дублирования и модификации орбиты спутника мы можем моделировать фрагментацию спутника. После выбора спутника и открытия меню пользователь может выбрать: <ul style=\"margin-left: 40px;\"> <li>Вариация наклонения</li> <li>Вариация RAAN</li> <li>Вариация периода</li> <li>Количество фрагментов</li> </ul> Чем больше вариация, тем больше разброс в моделируемой фрагментации. Значения вариаций по умолчанию достаточны для моделирования фрагментации с разумным разбросом."}, "DebrisScreening": {"bottomIconLabel": "Скрининг обломков", "title": "Меню скрининга обломков", "helpBody": "Меню скрининга обломков используется для создания списка объектов космического мусора, которые потенциально могут быть замечены спутником. Список создается путем расчета орбитальных параметров объектов мусора и сравнения их с орбитальными параметрами спутника. Пользователь может выбрать создание списка с использованием либо TLE, либо SGP4 пропагатора. Пользователь также может фильтровать список по размеру объекта мусора и его яркости. Пользователь также может выбрать создание списка с использованием либо TLE, либо SGP4 пропагатора. Пользователь также может фильтровать список по размеру объекта мусора и его яркости."}, "TransponderChannelData": {"bottomIconLabel": "Информация о транспондерах и каналах", "title": "Информация о транспондерах и каналах спутника", "helpBody": "<p>Эта таблица содержит технические детали для спутниковых каналов, включая ТВ, радио и другие коммуникационные службы:</p><ul><li><strong>спутник:</strong> Название спутника, вещающего канал</li><li><strong>твканал:</strong> Название ТВ/радио канала или коммуникационной службы</li><li><strong>луч:</strong> Направление спутникового луча (напр., Западное полушарие)</li><li><strong>част:</strong> Частота транспондера и поляризация (напр., 3840 В)</li><li><strong>система:</strong> Вещательная система (напр., DVB-S2 8PSK)</li><li><strong>SRFEC:</strong> Символьная скорость и коэффициент FEC (напр., 30000 3/4)</li><li><strong>видео:</strong> Формат сжатия видео/данных (напр., MPEG-4/HD)</li><li><strong>язык:</strong> Доступные аудио/коммуникационные языки (напр., Англ, Кит)</li><li><strong>шифрование:</strong> Используемая система шифрования (напр., PowerVu)</li></ul><p>Эта информация полезна для специалистов по спутниковой связи, техников, энтузиастов и всех, кто настраивает или устраняет неисправности в оборудовании спутникового приема.</p>"}, "EditSat": {"bottomIconLabel": "Редактировать спутник", "title": "Меню редактирования спутника", "helpBody": "Меню редактирования спутника используется для изменения данных спутника. <br><br> <ul> <li> SCC# спутника - Уникальный номер, присвоенный каждому спутнику Космическими силами США. </li> <li> Год эпохи - Год последнего обновления орбиты спутника. </li> <li> День эпохи - День года последнего обновления орбиты спутника. </li> <li> Наклонение - Угол между орбитальной плоскостью спутника и экваториальной плоскостью. </li> <li> Прямое восхождение - Угол между восходящим узлом и положением спутника в момент последнего обновления орбиты. </li> <li> Эксцентриситет - Величина, на которую орбита спутника отклоняется от идеального круга. </li> <li> Аргумент перигея - Угол между восходящим узлом и ближайшей к Земле точкой орбиты спутника. </li> <li> Средняя аномалия - Угол между положением спутника в момент последнего обновления орбиты и ближайшей к Земле точкой спутника. </li> <li> Среднее движение - Скорость изменения средней аномалии спутника. </li> </ul>"}, "NewLaunch": {"bottomIconLabel": "Новый запуск", "title": "Меню нового запуска", "helpBody": "Меню нового запуска используется для создания условных орбитальных запусков путем модификации существующих спутников с похожими параметрами. <br><br> После выбора спутника вы можете выбрать место запуска и северный/южный азимут. Выбранный спутник будет модифицирован для соответствия месту запуска. Затем время изменяется на 00:00:00, чтобы представить относительное время после запуска. Это может быть полезно при расчете покрытия датчиков относительно времени запуска. Взаимосвязь объекта с другими орбитальными объектами будет неверной."}, "MissilePlugin": {"bottomIconLabel": "Ракета", "title": "Меню ракет", "helpBody": "Меню ракет используется для создания условных запусков ракет между странами. <br><br> При использовании ракет, запускаемых с подводных лодок, точка запуска - это пользовательская широта и долгота. При использовании наземных ракет точка запуска - это фиксированная широта и долгота, основанные на открытых источниках информации. <br><br> Помимо пользовательских ракет, доступны несколько предопределенных сценариев с сотнями ракет. <br><br> Все запуски ракет условны и не предназначены для представления реальных событий. Все траектории запуска основаны на одной баллистической модели, но используют разные минимальные и максимальные дальности."}, "StereoMap": {"bottomIconLabel": "Стерео карта", "title": "Меню стереографической карты", "helpBody": "Меню стереографической карты используется для визуализации наземных следов спутников в стереографической проекции. <br/><br/> Вы можете нажать на точку вдоль наземного следа, чтобы изменить время симуляции на момент, когда спутник достигнет этой точки. <br/><br/> Желтые точки указывают, когда спутник находится в поле зрения датчика. Красные точки указывают, когда спутник не находится в поле зрения датчика. Точка, ближайшая к спутнику, соответствует текущему времени."}, "SensorFov": {"bottomIconLabel": "Поле зрения датчика"}, "SensorSurvFence": {"bottomIconLabel": "Барьер датчика"}, "SatelliteViewPlugin": {"bottomIconLabel": "Вид спутника"}, "SatelliteFov": {"bottomIconLabel": "Поле зрения спутника", "title": "Меню поля зрения спутника", "helpBody": "Плагин поля зрения спутника позволяет управлять полем зрения спутника."}, "Planetarium": {"bottomIconLabel": "Вид планетария"}, "NightToggle": {"bottomIconLabel": "Переключение ночи"}, "SatConstellations": {"bottomIconLabel": "Созвездия", "title": "Меню созвездий", "helpBody": "Меню созвездий позволяет просматривать группы спутников. <br><br> Для некоторых созвездий будут отображаться условные восходящие/нисходящие линии связи и/или перекрестные связи между спутниками в созвездии."}, "CountriesMenu": {"bottomIconLabel": "Страны", "title": "Меню стран", "helpBody": "Меню стран позволяет фильтровать спутники по стране происхождения."}, "ColorMenu": {"bottomIconLabel": "Цветовые схемы", "title": "Меню цветовой схемы", "helpBody": "Меню цветов - это место для изменения цветовой темы, используемой для отображения объектов. <br><br> Различные темы могут менять цвета в зависимости от орбит объектов, характеристик объектов или отношения объектов к солнцу и/или земле."}, "Screenshot": {"bottomIconLabel": "Сделать фото"}, "LaunchCalendar": {"bottomIconLabel": "Календарь запусков"}, "TimeMachine": {"bottomIconLabel": "<PERSON>а<PERSON><PERSON><PERSON> времени"}, "SatellitePhotos": {"bottomIconLabel": "Фото спутников", "title": "Меню фото спутников", "helpBody": "Меню фото спутников используется для отображения живых фотографий с избранных спутников. <br><br> Примечание - изменения в API изображений могут привести к выбору неправильного спутника в KeepTrack."}, "ScreenRecorder": {"bottomIconLabel": "Записать видео"}, "Astronomy": {"bottomIconLabel": "Астрономия"}, "Calculator": {"bottomIconLabel": "Преобразование систем отсчета", "title": "Меню преобразования систем отсчета", "helpBody": "Меню преобразования систем отсчета используется для конвертации между различными системами отсчета. <br><br> Меню позволяет конвертировать между следующими системами отсчета: <ul style=\"margin-left: 40px;\"> <li> ECI - Инерциальная система координат с центром в Земле </li> <li> ECEF - Фиксированная система координат с центром в Земле </li> <li> Геодезическая </li> <li> Топоцентрическая </li> </ul>"}, "AnalysisMenu": {"bottomIconLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Меню анализа", "helpBody": "Меню анализа предоставляет ряд инструментов для анализа данных в текущем представлении. Инструменты: <ul style=\"margin-left: 40px;\"> <li>Экспорт официальных TLE - Экспорт реальных двухстрочных элементов.</li> <li>Экспорт 3LES - Экспорт трехстрочных элементов.</li> <li>Экспорт KeepTrack TLE - Экспорт всех двухстрочных элементов KeepTrack, включая аналитиков.</li> <li>Экспорт KeepTrack 3LES - Экспорт всех трехстрочных элементов KeepTrack, включая аналитиков.</li> <li>Найти близкие объекты - Найти объекты, которые находятся близко друг к другу.</li> <li>Найти входы в атмосферу - Найти объекты, которые, вероятно, войдут в атмосферу.</li> <li>Лучшие проходы - Найти лучшие проходы для спутника на основе выбранного датчика.</li> </ul>"}, "SettingsMenuPlugin": {"bottomIconLabel": "Настройки", "title": "Меню настроек", "helpBody": "Меню настроек позволяет настроить приложение."}, "VideoDirectorPlugin": {"bottomIconLabel": "Режиссер видео", "title": "Меню режиссера видео", "helpBody": "Меню режиссера видео используется для манипуляции камерой и объектами в сцене для создания видео."}, "CreateSat": {"bottomIconLabel": "Создать спутник", "title": "Создать спутник", "helpBody": "Меню создания спутника используется для создания спутника из его кеплеровых элементов"}, "DopsPlugin": {"bottomIconLabel": "Просмотр DOP", "title": "Меню разбавления точности", "helpBody": "Меню разбавления точности (DOP) используется для расчета значений DOP для спутника и датчика. <br><br> Значения DOP: <ul style=\"margin-left: 40px;\"> <li> GDOP - Геометрическое разбавление точности </li> <li> PDOP - Позиционное разбавление точности </li> <li> HDOP - Горизонтальное разбавление точности </li> <li> VDOP - Вертикальное разбавление точности </li> <li> TDOP - Временное разбавление точности </li> <li> NDOP - Разбавление точности по количеству спутников </li> </ul>"}, "EciPlot": {"bottomIconLabel": "График ECI", "title": "Меню графика ECI", "helpBody": "Меню графика ECI используется для построения положения спутника в инерциальной системе координат с центром в Земле (ECI)."}, "EcfPlot": {"bottomIconLabel": "График ECF", "title": "Меню графика ECF", "helpBody": "Меню графика ECF используется для построения положения спутника в фиксированной системе координат с центром в Земле (ECF)."}, "RicPlot": {"bottomIconLabel": "Гра<PERSON><PERSON><PERSON> RIC", "title": "Меню графика RIC", "helpBody": "Меню графика RIC используется для построения положения спутника в системе координат Радиальная, По-трассе и Перпендикулярно-трассе (RIC)."}, "Time2LonPlots": {"bottomIconLabel": "Водопадный график", "title": "Меню водопада", "helpBody": "Меню графика Время к Долготе (Водопад) используется для построения долготы спутника с течением времени."}, "Lat2LonPlots": {"bottomIconLabel": "Гра<PERSON>и<PERSON> Широта/Долгота", "title": "Меню графика Широта/Долгота", "helpBody": "Меню графика Широта/Долгота используется для построения графика широты против долготы в поясе GEO."}, "Inc2AltPlots": {"bottomIconLabel": "График Наклонение/Высота", "title": "Меню графика Наклонение/Высота", "helpBody": "Меню графика Наклонение/Высота используется для построения наклонения спутника по отношению к его высоте."}, "Inc2LonPlots": {"bottomIconLabel": "График Наклонение/Долгота", "title": "Меню графика Наклонение/Долгота", "helpBody": "Меню графика Наклонение/Долгота используется для построения графика наклонения против долготы в поясе GEO."}, "GraphicsMenuPlugin": {"bottomIconLabel": "Меню графики", "title": "Меню графики", "helpBody": "Меню графики используется для изменения графических настроек приложения."}}}