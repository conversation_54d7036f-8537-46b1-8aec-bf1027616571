# Keep Track 8.2 - Phoenix

"Nebula Navigator" encapsulates the spirit of this update, which focuses on guiding users through the vast expanse of orbital data. The term "Nebula" alludes to the complexity and mystery of space, while "Navigator" highlights the improved tools and interfaces that help users explore and understand this celestial information.

## Software Release Documentation

This release marks a significant step forward in user experience and data handling capabilities. It introduces new analytical tools, enhances existing features, and improves overall system performance. The update also addresses several bugs and introduces new customization options to cater to diverse user needs.

## Major Features

### UI & Aesthetics

- Introduced new splash screens and a revamped loading screen.
- Enhanced Milky Way textures and implemented optimized image handling techniques.
- Added extensive help menus across various modules, including analysis, sensors, settings, debug, and satellite photos.

### Functionality Enhancements

- Added a variety of analytical plots including ECI, ECF, RIC, and more.
- Incorporated satellite search functionality when a control site is clicked.
- Implemented scenario-creator scaffold for future expansion.
- Added new satellite selection options and updated select boxes.
- Introduced faster searching capabilities and prefetching of splash screens.

### User Guidance

- Implemented comprehensive help menus across different modules to improve user understanding and navigation.

## Minor Features

- Removed dependencies on jQuery, emphasizing better performance and modern web standards.
- Added support to use Keep Track as a React component, increasing flexibility for developers.
- Introduced new and updated catalogs for satellite data.

### Bug Fixes

- Addressed numerous issues including satellite FOV problems, satellite editing glitches, and texture anomalies.
- Enhanced error-catching mechanisms, including improvements like delayed error sounds.
- Resolved minor issues with menus such as countries and constellations.

### Code & Infrastructure Updates

- Upgraded dependencies and addressed potential security vulnerabilities.
- Performed significant code refactoring, including the removal of jQuery and optimization of various modules.

### Documentation

- Continuously updated changelogs and readmes, providing clearer instructions and feature explanations.
