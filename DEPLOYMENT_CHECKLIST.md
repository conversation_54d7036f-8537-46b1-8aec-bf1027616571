# 太空物体模拟平台 - 登录系统部署检查清单

## 🚀 部署前检查

### ✅ 必需文件检查
- [ ] `public/login.html` - 登录页面
- [ ] `public/admin.html` - 管理后台
- [ ] `public/js/login.js` - 登录逻辑
- [ ] `public/js/admin.js` - 管理后台逻辑
- [ ] `public/js/auth-check.js` - 认证检查
- [ ] `src/auth/auth.service.ts` - 认证服务
- [ ] `src/api/auth.routes.ts` - 认证API
- [ ] `data/` 目录存在且可写

### ✅ 依赖检查
- [ ] `express-rate-limit` 已安装
- [ ] `uuid` 已安装
- [ ] 所有TypeScript依赖正常

### ✅ 配置检查
- [ ] 环境变量配置（可选）
- [ ] JWT密钥设置
- [ ] 端口配置正确

## 🔧 功能验证

### ✅ 基础功能
- [ ] API服务器可以启动 (`npm run start:api`)
- [ ] Web服务器可以启动 (`npm start`)
- [ ] 登录页面可以访问
- [ ] 管理后台可以访问

### ✅ 认证功能
- [ ] 默认管理员账户可以登录
- [ ] 错误密码被正确拒绝
- [ ] 登录失败5次后账户锁定
- [ ] JWT令牌正确生成和验证
- [ ] 密码修改功能正常

### ✅ 注册功能
- [ ] 用户可以提交注册申请
- [ ] 重复用户名被拒绝
- [ ] 弱密码被拒绝
- [ ] 管理员可以审批注册

### ✅ 管理功能
- [ ] 用户列表正确显示
- [ ] 用户状态可以切换
- [ ] 用户角色可以修改
- [ ] 登录日志正确记录
- [ ] 系统统计正确显示

### ✅ 安全功能
- [ ] 未登录用户被重定向到登录页
- [ ] 非管理员无法访问管理后台
- [ ] 输入验证正常工作
- [ ] XSS防护生效
- [ ] CSRF保护生效

## 🛡️ 安全检查

### ✅ 密码安全
- [ ] 密码使用PBKDF2哈希存储
- [ ] 强密码策略已实施
- [ ] 默认密码强制修改

### ✅ 会话安全
- [ ] JWT令牌有过期时间
- [ ] 令牌签名验证正常
- [ ] 会话状态正确管理

### ✅ 输入安全
- [ ] 用户输入经过清理
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] 输入格式验证

### ✅ 网络安全
- [ ] API端点访问控制
- [ ] 登录频率限制
- [ ] IP地址限制
- [ ] HTTPS配置（生产环境）

## 📊 性能检查

### ✅ 响应时间
- [ ] 登录响应时间 < 2秒
- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 1秒

### ✅ 资源使用
- [ ] 内存使用合理
- [ ] CPU使用正常
- [ ] 磁盘空间充足

## 🧪 测试验证

### ✅ 自动化测试
```bash
# 运行认证系统测试
node scripts/test-auth.js
```

### ✅ 手动测试场景

#### 登录测试
1. [ ] 正确用户名密码登录成功
2. [ ] 错误密码登录失败
3. [ ] 不存在用户登录失败
4. [ ] 5次失败后账户锁定
5. [ ] 锁定时间过后可以重新登录

#### 注册测试
1. [ ] 有效信息注册成功
2. [ ] 重复用户名注册失败
3. [ ] 弱密码注册失败
4. [ ] 无效邮箱注册失败

#### 管理测试
1. [ ] 管理员可以查看用户列表
2. [ ] 管理员可以审批注册
3. [ ] 管理员可以管理用户
4. [ ] 普通用户无法访问管理功能

#### 安全测试
1. [ ] 未登录访问主页被重定向
2. [ ] 无效令牌被拒绝
3. [ ] 过期令牌被拒绝
4. [ ] XSS攻击被阻止

## 🚀 部署步骤

### 1. 环境准备
```bash
# 安装依赖
npm install

# 构建项目
npm run build
```

### 2. 配置环境变量（可选）
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

### 3. 启动服务
```bash
# 方式1：使用集成启动脚本
npm run start:auth

# 方式2：分别启动服务
npm run start:api &
npm start
```

### 4. 验证部署
```bash
# 运行测试
node scripts/test-auth.js

# 运行安全检查
npm run security-check
```

## 📋 生产环境额外检查

### ✅ HTTPS配置
- [ ] SSL证书已安装
- [ ] HTTP自动重定向到HTTPS
- [ ] 安全头部配置

### ✅ 数据库配置（如需要）
- [ ] 数据库连接正常
- [ ] 数据备份策略
- [ ] 数据恢复测试

### ✅ 监控配置
- [ ] 日志记录配置
- [ ] 错误监控
- [ ] 性能监控
- [ ] 安全监控

### ✅ 备份策略
- [ ] 用户数据备份
- [ ] 配置文件备份
- [ ] 恢复流程测试

## 🔧 故障排除

### 常见问题解决

1. **API服务器无法启动**
   - 检查端口3001是否被占用
   - 确认TypeScript编译正常
   - 查看错误日志

2. **登录功能异常**
   - 检查数据目录权限
   - 确认JWT密钥配置
   - 查看浏览器控制台错误

3. **管理后台无法访问**
   - 确认用户角色为管理员
   - 检查令牌有效性
   - 验证API连接

## 📞 支持联系

### 技术支持
- **邮箱**: <EMAIL>
- **文档**: `docs/LOGIN_SYSTEM.md`
- **测试**: `scripts/test-auth.js`

### 紧急联系
- **公司**: 北京星地探索科技有限公司
- **网站**: www.spacedefense.cn

---

## ✅ 部署确认

部署完成后，请确认以下检查项全部通过：

- [ ] 所有必需文件存在
- [ ] 依赖正确安装
- [ ] 服务正常启动
- [ ] 功能测试通过
- [ ] 安全检查通过
- [ ] 性能指标正常

**部署负责人签名**: ________________

**部署日期**: ________________

**版本号**: ________________
