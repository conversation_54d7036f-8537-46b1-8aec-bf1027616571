#!/usr/bin/env node

/**
 * 启动脚本 - 带认证系统的太空物体模拟平台
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动太空物体模拟平台（带认证系统）\n');

// 检查必要文件
const requiredFiles = [
    'src/api/server.ts',
    'public/login.html',
    'public/admin.html',
    'public/js/login.js',
    'public/js/admin.js',
    'public/js/auth-check.js'
];

console.log('📋 检查必要文件...');
let allFilesExist = true;

requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - 文件不存在`);
        allFilesExist = false;
    }
});

if (!allFilesExist) {
    console.log('\n❌ 缺少必要文件，请检查项目完整性');
    process.exit(1);
}

// 检查数据目录
if (!fs.existsSync('data')) {
    console.log('\n📁 创建数据目录...');
    fs.mkdirSync('data', { recursive: true });
    console.log('✅ 数据目录已创建');
}

console.log('\n🔧 启动API服务器...');

// 启动API服务器
const apiServer = spawn('npx', ['tsx', 'src/api/server.ts'], {
    stdio: 'inherit',
    shell: true
});

apiServer.on('error', (err) => {
    console.error('❌ API服务器启动失败:', err);
    process.exit(1);
});

// 等待一段时间让API服务器启动
setTimeout(() => {
    console.log('\n🌐 启动Web服务器...');
    
    // 启动Web服务器
    const webServer = spawn('npx', ['serve', './dist', '-l', '8080'], {
        stdio: 'inherit',
        shell: true
    });

    webServer.on('error', (err) => {
        console.error('❌ Web服务器启动失败:', err);
        console.log('💡 提示：请先运行 npm run build 构建项目');
        apiServer.kill();
        process.exit(1);
    });

    // 处理退出信号
    process.on('SIGINT', () => {
        console.log('\n🛑 正在关闭服务器...');
        apiServer.kill();
        webServer.kill();
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log('\n🛑 正在关闭服务器...');
        apiServer.kill();
        webServer.kill();
        process.exit(0);
    });

    // 显示访问信息
    setTimeout(() => {
        console.log('\n🎉 服务器启动完成！');
        console.log('\n📍 访问地址:');
        console.log('   主页面: http://localhost:8080');
        console.log('   登录页面: http://localhost:8080/login.html');
        console.log('   管理后台: http://localhost:8080/admin.html');
        console.log('   API服务: http://localhost:3001');
        console.log('\n👤 默认管理员账户:');
        console.log('   用户名: admin');
        console.log('   密码: SpaceDefense2025!');
        console.log('\n⚠️  首次登录后请立即修改密码！');
        console.log('\n按 Ctrl+C 停止服务器');
    }, 3000);

}, 2000);

// 显示帮助信息
console.log('\n📖 使用说明:');
console.log('1. 首次访问会自动跳转到登录页面');
console.log('2. 使用默认管理员账户登录');
console.log('3. 登录后会要求修改默认密码');
console.log('4. 管理员可以在管理后台审批用户注册');
console.log('5. 普通用户需要注册并等待管理员审批');
console.log('');
