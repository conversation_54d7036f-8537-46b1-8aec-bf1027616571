/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

// Settings Manager Overrides
const settingsOverride = {
  plugins: {
    SatInfoBox: { enabled: true },
    TopMenu: { enabled: true },
    SettingsMenuPlugin: { enabled: true },
    ColorMenu: { enabled: true },
    DateTimeManager: { enabled: true },
    // 底部菜单必需插件
    AboutMenuPlugin: { enabled: true },
    Collisions: { enabled: true },
    FindSatPlugin: { enabled: true },
    SatellitePhotos: { enabled: true },
    StereoMap: { enabled: true }
  },
  dataSources: {
    tle: './tle/tle.json',
    externalTLEsOnly: false,
    tleDebris: './tle/TLEdebris.json',
    vimpel: './tle/vimpel.json',
    isSupplementExternal: false
  },
  isDisableBottomMenu: false // 确保底部菜单不被禁用
};

// Expose these to the console
window.settingsOverride = settingsOverride;


