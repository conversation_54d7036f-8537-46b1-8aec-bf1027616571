'use strict';
// 精简版主应用逻辑
console.log('太空物体模拟平台初始化');
if (!window.KeepTrack) {
  window.KeepTrack = class KeepTrack {
    init() {
      console.log("3D场景初始化开始 - 强制创建模式");
      try {
        const loadingScreen = document.getElementById('loading-screen');
        console.log('加载动画元素:', loadingScreen ? '存在' : '不存在');
      } catch (err) {
        console.warn('加载动画检测失败:', err);
      }

      // 确保DOM完全加载
      if (document.readyState !== 'complete') {
        console.log('等待DOM完全加载...');
        return new Promise((resolve) => {
          window.addEventListener('load', () => {
            this._initCanvas().then(resolve);
          });
        });
      }
      return this._initCanvas();
    }

    _initCanvas() {
      console.log("执行Canvas初始化");
      try {
        const canvas = this._createCanvas();
        const state = this._checkCanvasState();
        console.table([
          {
            '元素': '容器',
            '存在': state.container.exists,
            '显示': state.container.visible ? 'visible' : 'hidden',
            '尺寸': state.container.dimensions
          },
          {
            '元素': 'Canvas',
            '存在': state.canvas.exists,
            '显示': state.canvas.visible ? 'visible' : 'hidden',
            '尺寸': state.canvas.dimensions
          }
        ]);
        // 隐藏加载动画
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          loadingScreen.style.transition = 'opacity 0.5s ease';
          loadingScreen.style.opacity = '0';
          setTimeout(() => {
            loadingScreen.style.display = 'none';
          }, 500);
        }
        return state;
      } catch (err) {
        console.error('Canvas初始化失败:', err);
        throw err;
      }
    }

    _checkCanvasState() {
      const container = document.getElementById('keeptrack-root');
      const canvas = document.getElementById('keeptrack-canvas');
      return {
        container: {
          exists: !!container,
          visible: container ? window.getComputedStyle(container).display !== 'none' : false,
          dimensions: container ? `${container.offsetWidth}x${container.offsetHeight}` : 'N/A'
        },
        canvas: {
          exists: !!canvas,
          visible: canvas ? window.getComputedStyle(canvas).display !== 'none' : false,
          dimensions: canvas ? `${canvas.width}x${canvas.height}` : 'N/A'
        }
      };
    }

    _createCanvas() {
      try {
        // 确保document.body已存在
        if (!document.body) {
          throw new Error('document.body未准备好');
        }

        // 确保容器存在
        let container = document.getElementById('keeptrack-root');
        if (!container) {
          console.warn('未找到容器元素，将创建新容器');
          container = document.createElement('div');
          container.id = 'keeptrack-root';
          container.style.cssText = 'position:absolute;top:0;left:0;width:100%;height:100%;z-index:1';
          document.body.appendChild(container);
        }

        // 移除旧Canvas（如果存在）
        const oldCanvas = document.getElementById('keeptrack-canvas');
        if (oldCanvas) oldCanvas.remove();

        // 创建新Canvas
        const canvas = document.createElement('canvas');
        canvas.id = 'keeptrack-canvas';
        canvas.style.cssText = 'width:100%;height:100%;display:block;background:#000';
        container.appendChild(canvas);

        return canvas;
      } catch (err) {
        console.error('创建Canvas失败:', err);
        throw err;
      }
    }

    setupOrbitControls(canvas) {
      console.log("轨道控制已启用");
      if (!canvas.getContext) {
        console.warn('浏览器不支持Canvas 2D/3D上下文');
        return;
      }
      // 检查THREE.js是否加载
      if (typeof THREE === 'undefined') {
        console.error('THREE.js未加载');
        return;
      }
      try {
        const context = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (!context) {
          throw new Error('无法获取WebGL上下文');
        }
        this.controls = new THREE.OrbitControls(context);
      } catch (err) {
        console.error('轨道控制初始化失败:', err);
      }
    }

    loadSatelliteData() {
      return fetch('/data/satellites.json')
        .then(res => {
          if (!res.ok) throw new Error('数据加载失败');
          return res.json();
        })
        .then(data => {
          console.log(`加载了${data.length}个卫星数据`);
          return data;
        })
        .catch(err => {
          console.error('卫星数据加载错误:', err);
          return [];
        });
    }
  }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  if (typeof window.KeepTrack === 'function') {
    new window.KeepTrack().init()
      .catch(err => console.error('初始化失败:', err));
  }
});