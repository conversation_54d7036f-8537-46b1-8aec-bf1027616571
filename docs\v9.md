# Keep Track 9.0 - <PERSON><PERSON>

## Software Release Documentation

Version 9.0, codenamed "Ke<PERSON>," represents a significant leap forward in Keep Track's capabilities. This release focuses on major performance enhancements, advanced visualization features, and expanded analysis tools, setting a new standard for space situational awareness software.

## Major Features

### Data Visualization & Analysis

- Introduced responsive polar plots for advanced orbital visualization
- Implemented new quick reports for enhanced data analysis
- Added sun angle to best pass report for improved solar analysis
- Developed AER (Azimuth, Elevation, Range) report for comprehensive positional data

### User Interface Enhancements

- Implemented responsive design for improved cross-device compatibility
- Updated splash screens for a refreshed user experience
- Enhanced 'New Launch' plugin for improved usability
- Added save button functionality to polar plots for data persistence

### Performance Optimizations

- Optimized godrays effect to significantly reduce performance impact
- Improved handling of color buffer updates for better rendering efficiency
- Enhanced reference orbit updating mechanism for faster calculations
- Implemented performance scaling for sat-cruncher to handle larger datasets

### Functionality Improvements

- Added ability to toggle Starlink and notional satellites for customized visualization
- Improved standard magnitude estimates for better visibility of space objects
- Enhanced mesh orientation for missiles to improve accuracy of representations
- Expanded STF (Short Term Fence) capabilities with azimuth and elevation extensions in kilometers

### Architecture & Infrastructure

- Migrated to OOTK (Orbital Object ToolKit) v4 for improved orbital calculations
- Refactored select sat manager functions out of webgl-renderer for better code organization
- Standardized plugins for improved modularity and easier future expansions
- Enhanced type support across various components for improved code reliability

## Minor Features

### Usability Enhancements

- Improved cross-browser support, with particular focus on mobile devices
- Enhanced offline functionality for use in limited-connectivity environments

### Data Management Improvements

- Updated satellite databases for more accurate and current information

## Bug Fixes

- Resolved issues with godrays breaking after screen resize
- Fixed conflicts between custom sensors and normal sensors
- Addressed problems with new launch plugin functionality
- Corrected issues with lookangles and multi-site lookangles enabling/disabling
- Improved search functionality, particularly for number-only queries

## Documentation

- Updated user guides with interactive tutorials for new visualization tools
- Enhanced API documentation to facilitate integration of new analysis features
- Expanded troubleshooting section to address common issues with performance optimization
