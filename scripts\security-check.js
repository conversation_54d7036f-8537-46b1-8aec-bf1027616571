#!/usr/bin/env node

/**
 * 安全检查脚本
 * 检查登录系统的常见安全漏洞
 */

const fs = require('fs');
const path = require('path');

class SecurityChecker {
    constructor() {
        this.issues = [];
        this.warnings = [];
        this.info = [];
    }

    addIssue(type, message, file = null, line = null) {
        this.issues.push({ type, message, file, line, severity: 'high' });
    }

    addWarning(message, file = null, line = null) {
        this.warnings.push({ message, file, line, severity: 'medium' });
    }

    addInfo(message, file = null, line = null) {
        this.info.push({ message, file, line, severity: 'low' });
    }

    checkFile(filePath) {
        if (!fs.existsSync(filePath)) {
            this.addWarning(`文件不存在: ${filePath}`);
            return;
        }

        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');

        // 检查各种安全问题
        this.checkSQLInjection(content, filePath);
        this.checkXSS(content, filePath);
        this.checkCSRF(content, filePath);
        this.checkPasswordSecurity(content, filePath);
        this.checkInputValidation(content, filePath);
        this.checkAuthenticationSecurity(content, filePath);
        this.checkHTTPS(content, filePath);
        this.checkSecretExposure(content, filePath);
    }

    checkSQLInjection(content, filePath) {
        // 检查SQL注入风险
        const sqlPatterns = [
            /SELECT\s+.*\s+FROM\s+.*\s+WHERE\s+.*\+/gi,
            /INSERT\s+INTO\s+.*\s+VALUES\s*\(.*\+/gi,
            /UPDATE\s+.*\s+SET\s+.*\+/gi,
            /DELETE\s+FROM\s+.*\s+WHERE\s+.*\+/gi
        ];

        sqlPatterns.forEach(pattern => {
            if (pattern.test(content)) {
                this.addIssue('SQL_INJECTION', 'SQL注入风险：发现字符串拼接的SQL查询', filePath);
            }
        });
    }

    checkXSS(content, filePath) {
        // 检查XSS风险
        const xssPatterns = [
            /innerHTML\s*=\s*.*\+/gi,
            /document\.write\s*\(/gi,
            /eval\s*\(/gi,
            /\.html\s*\(.*\+/gi
        ];

        xssPatterns.forEach(pattern => {
            if (pattern.test(content)) {
                this.addIssue('XSS', 'XSS风险：发现动态HTML内容插入', filePath);
            }
        });

        // 检查是否有输入清理
        if (content.includes('innerHTML') && !content.includes('sanitize') && !content.includes('escape')) {
            this.addWarning('建议对用户输入进行清理和转义', filePath);
        }
    }

    checkCSRF(content, filePath) {
        // 检查CSRF保护
        if (content.includes('fetch') || content.includes('XMLHttpRequest')) {
            if (!content.includes('csrf') && !content.includes('token')) {
                this.addWarning('建议添加CSRF保护', filePath);
            }
        }
    }

    checkPasswordSecurity(content, filePath) {
        // 检查密码安全
        if (content.includes('password')) {
            // 检查是否有密码强度验证
            if (!content.includes('length') || !content.includes('regex') || !content.includes('test')) {
                this.addWarning('建议添加密码强度验证', filePath);
            }

            // 检查是否有密码哈希
            if (content.includes('password') && !content.includes('hash') && !content.includes('bcrypt') && !content.includes('pbkdf2')) {
                this.addIssue('PASSWORD_SECURITY', '密码安全风险：密码可能未进行哈希处理', filePath);
            }
        }
    }

    checkInputValidation(content, filePath) {
        // 检查输入验证
        if (content.includes('req.body') || content.includes('input')) {
            if (!content.includes('validate') && !content.includes('sanitize') && !content.includes('trim')) {
                this.addWarning('建议添加输入验证和清理', filePath);
            }
        }
    }

    checkAuthenticationSecurity(content, filePath) {
        // 检查认证安全
        if (content.includes('login') || content.includes('auth')) {
            // 检查是否有登录限制
            if (!content.includes('rate') && !content.includes('limit') && !content.includes('attempt')) {
                this.addWarning('建议添加登录频率限制', filePath);
            }

            // 检查是否有会话管理
            if (!content.includes('session') && !content.includes('token') && !content.includes('jwt')) {
                this.addWarning('建议实现安全的会话管理', filePath);
            }
        }
    }

    checkHTTPS(content, filePath) {
        // 检查HTTPS使用
        if (content.includes('http://') && !content.includes('localhost')) {
            this.addIssue('HTTPS', 'HTTPS安全：发现HTTP连接，建议使用HTTPS', filePath);
        }
    }

    checkSecretExposure(content, filePath) {
        // 检查敏感信息暴露
        const secretPatterns = [
            /password\s*[:=]\s*['"][^'"]+['"]/gi,
            /secret\s*[:=]\s*['"][^'"]+['"]/gi,
            /key\s*[:=]\s*['"][^'"]+['"]/gi,
            /token\s*[:=]\s*['"][^'"]+['"]/gi
        ];

        secretPatterns.forEach(pattern => {
            if (pattern.test(content)) {
                this.addIssue('SECRET_EXPOSURE', '敏感信息暴露：发现硬编码的密钥或密码', filePath);
            }
        });
    }

    checkDirectoryStructure() {
        // 检查目录结构安全
        const sensitiveFiles = [
            'package.json',
            '.env',
            'config.json',
            'data/users.json'
        ];

        sensitiveFiles.forEach(file => {
            if (fs.existsSync(file)) {
                const stats = fs.statSync(file);
                // 在Windows上，这个检查可能不太准确，但仍然有用
                this.addInfo(`敏感文件存在: ${file}，请确保适当的访问控制`, file);
            }
        });
    }

    checkDependencies() {
        // 检查依赖安全
        const packageJsonPath = 'package.json';
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

            // 检查已知的有安全问题的包
            const vulnerablePackages = [
                'lodash', // 某些版本有原型污染问题
                'moment', // 已弃用，建议使用dayjs
                'request' // 已弃用
            ];

            Object.keys(dependencies).forEach(dep => {
                if (vulnerablePackages.includes(dep)) {
                    this.addWarning(`依赖包 ${dep} 可能存在安全问题，建议检查版本或替换`, packageJsonPath);
                }
            });
        }
    }

    run() {
        console.log('🔍 开始安全检查...\n');

        // 检查主要文件
        const filesToCheck = [
            'src/auth/auth.service.ts',
            'src/api/auth.routes.ts',
            'public/js/login.js',
            'public/js/admin.js',
            'public/js/auth-check.js',
            'public/login.html',
            'public/admin.html'
        ];

        filesToCheck.forEach(file => {
            this.checkFile(file);
        });

        // 检查目录结构
        this.checkDirectoryStructure();

        // 检查依赖
        this.checkDependencies();

        // 输出结果
        this.printResults();
    }

    printResults() {
        console.log('📊 安全检查结果:\n');

        if (this.issues.length > 0) {
            console.log('🚨 严重问题:');
            this.issues.forEach((issue, index) => {
                console.log(`${index + 1}. [${issue.type}] ${issue.message}`);
                if (issue.file) console.log(`   文件: ${issue.file}`);
                console.log('');
            });
        }

        if (this.warnings.length > 0) {
            console.log('⚠️  警告:');
            this.warnings.forEach((warning, index) => {
                console.log(`${index + 1}. ${warning.message}`);
                if (warning.file) console.log(`   文件: ${warning.file}`);
                console.log('');
            });
        }

        if (this.info.length > 0) {
            console.log('ℹ️  信息:');
            this.info.forEach((info, index) => {
                console.log(`${index + 1}. ${info.message}`);
                if (info.file) console.log(`   文件: ${info.file}`);
                console.log('');
            });
        }

        // 总结
        console.log('📈 总结:');
        console.log(`严重问题: ${this.issues.length}`);
        console.log(`警告: ${this.warnings.length}`);
        console.log(`信息: ${this.info.length}`);

        if (this.issues.length === 0) {
            console.log('\n✅ 未发现严重安全问题！');
        } else {
            console.log('\n❌ 发现安全问题，请及时修复！');
        }

        // 安全建议
        console.log('\n🛡️  安全建议:');
        console.log('1. 定期更新依赖包');
        console.log('2. 使用HTTPS进行所有通信');
        console.log('3. 实施强密码策略');
        console.log('4. 添加登录频率限制');
        console.log('5. 对所有用户输入进行验证和清理');
        console.log('6. 使用安全的会话管理');
        console.log('7. 定期进行安全审计');
        console.log('8. 备份重要数据');
    }
}

// 运行安全检查
if (require.main === module) {
    const checker = new SecurityChecker();
    checker.run();
}

module.exports = SecurityChecker;
