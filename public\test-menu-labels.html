<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边菜单标签测试</title>
    <link rel="stylesheet" href="css/responsive-design.css">
    <style>
        body {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            margin: 20px;
            line-height: 1.5;
        }
        
        .test-container {
            max-width: 400px;
            background: rgba(0, 0, 0, 0.7);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
        }
        
        .input-field {
            position: relative;
            margin-bottom: 20px;
            padding-top: 25px;
            min-height: 60px;
        }
        
        .input-field label {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            transition: transform 0.2s ease-out, color 0.2s ease-out;
            transform-origin: 0% 100%;
        }
        
        .input-field input,
        .input-field select {
            width: 100%;
            padding: 8px 0;
            border: none;
            border-bottom: 1px solid #666;
            background: transparent;
            color: white;
            outline: none;
        }
        
        .input-field input:focus + label,
        .input-field input:valid + label,
        .input-field select:focus + label {
            transform: translateY(-20px) scale(0.8);
            color: #2196F3;
        }
        
        .switch {
            margin: 15px 0;
        }
        
        .switch label {
            position: relative;
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .switch input[type="checkbox"] {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .lever {
            content: "";
            display: inline-block;
            position: relative;
            width: 36px;
            height: 14px;
            background-color: #818181;
            border-radius: 14px;
            margin-right: 10px;
            transition: background 0.3s ease;
            vertical-align: middle;
            margin: 0 16px 0 0;
        }
        
        .lever:after {
            content: "";
            position: absolute;
            top: -3px;
            left: -3px;
            width: 20px;
            height: 20px;
            background-color: #F1F1F1;
            border-radius: 50%;
            transition: left 0.3s ease, background 0.3s ease, box-shadow 0.1s ease;
        }
        
        input[type="checkbox"]:checked + .lever {
            background-color: #84c7c1;
        }
        
        input[type="checkbox"]:checked + .lever:after {
            left: 16px;
            background-color: #26a69a;
        }
        
        h3 {
            color: #2196F3;
            margin-bottom: 15px;
        }
        
        .font-size-display {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>侧边菜单标签字体大小测试</h1>
    <p>测试侧边栏菜单中标题文字、输入框标题和选择框标题的字体大小是否一致</p>

    <div class="test-container side-menu">
        <h3>侧边菜单样式测试</h3>

        <div class="test-section">
            <h4>输入框标签测试 (模拟"卫星SCC#")</h4>
            <div class="input-field">
                <input type="text" id="test-input" value="90000" required>
                <label for="test-input">卫星SCC#</label>
            </div>
            <div class="font-size-display">当前字体大小: <span id="input-label-size"></span></div>
        </div>

        <div class="test-section">
            <h4>选择框标签测试 (模拟"目标类型")</h4>
            <div class="input-field">
                <select id="test-select">
                    <option value="" disabled>请选择</option>
                    <option value="payload" selected>载荷</option>
                    <option value="rocket">火箭</option>
                    <option value="debris">碎片</option>
                </select>
                <label for="test-select">目标类型</label>
            </div>
            <div class="font-size-display">当前字体大小: <span id="select-label-size"></span></div>
            <div class="font-size-display">下拉框选项字体大小: <span id="dropdown-option-size"></span></div>
        </div>

        <div class="test-section">
            <h4>开关标签测试</h4>
            <div class="switch">
                <label>
                    <input type="checkbox">
                    <span class="lever"></span>
                    开关标题
                </label>
            </div>
            <div class="font-size-display">当前字体大小: <span id="switch-label-size"></span></div>
        </div>

        <div class="test-section">
            <h4>菜单标题测试</h4>
            <h5 class="menu-title">菜单主标题</h5>
            <div class="font-size-display">当前字体大小: <span id="menu-title-size"></span></div>
        </div>
    </div>

    <div class="test-container" id="settings-menu">
        <h3>设置菜单样式测试</h3>
        
        <div class="test-section">
            <div class="input-field">
                <input type="number" id="settings-input" required>
                <label for="settings-input">设置输入框标题</label>
            </div>
            <div class="font-size-display">当前字体大小: <span id="settings-input-label-size"></span></div>
        </div>

        <div class="test-section">
            <div class="input-field">
                <select id="settings-select">
                    <option value="" disabled selected>请选择设置</option>
                    <option value="auto">自动</option>
                    <option value="manual">手动</option>
                </select>
                <label for="settings-select">设置选择框标题</label>
            </div>
            <div class="font-size-display">当前字体大小: <span id="settings-select-label-size"></span></div>
        </div>
    </div>

    <div class="test-container">
        <h3>字体大小对比</h3>
        <div id="font-size-comparison"></div>
    </div>

    <script>
        function getFontSize(element) {
            return window.getComputedStyle(element).fontSize;
        }

        function updateFontSizes() {
            // 获取各个标签的字体大小
            const inputLabel = document.querySelector('#test-input + label');
            const selectLabel = document.querySelector('#test-select + label');
            const switchLabel = document.querySelector('.switch label');
            const menuTitle = document.querySelector('.menu-title');
            const settingsInputLabel = document.querySelector('#settings-input + label');
            const settingsSelectLabel = document.querySelector('#settings-select + label');

            // 显示字体大小
            document.getElementById('input-label-size').textContent = getFontSize(inputLabel);
            document.getElementById('select-label-size').textContent = getFontSize(selectLabel);
            document.getElementById('switch-label-size').textContent = getFontSize(switchLabel);
            document.getElementById('menu-title-size').textContent = getFontSize(menuTitle);
            document.getElementById('settings-input-label-size').textContent = getFontSize(settingsInputLabel);
            document.getElementById('settings-select-label-size').textContent = getFontSize(settingsSelectLabel);

            // 生成对比表
            const sizes = {
                '输入框标题': getFontSize(inputLabel),
                '选择框标题': getFontSize(selectLabel),
                '开关标题': getFontSize(switchLabel),
                '菜单主标题': getFontSize(menuTitle),
                '设置输入框标题': getFontSize(settingsInputLabel),
                '设置选择框标题': getFontSize(settingsSelectLabel)
            };

            let comparisonHTML = '<table style="width: 100%; border-collapse: collapse;">';
            comparisonHTML += '<tr><th style="border: 1px solid #666; padding: 8px;">元素</th><th style="border: 1px solid #666; padding: 8px;">字体大小</th></tr>';
            
            for (const [name, size] of Object.entries(sizes)) {
                comparisonHTML += `<tr><td style="border: 1px solid #666; padding: 8px;">${name}</td><td style="border: 1px solid #666; padding: 8px;">${size}</td></tr>`;
            }
            comparisonHTML += '</table>';

            document.getElementById('font-size-comparison').innerHTML = comparisonHTML;

            // 检查是否一致
            const uniqueSizes = [...new Set(Object.values(sizes))];
            if (uniqueSizes.length === 1) {
                document.getElementById('font-size-comparison').innerHTML += 
                    '<p style="color: #4CAF50; margin-top: 10px;">✅ 所有标签字体大小一致！</p>';
            } else {
                document.getElementById('font-size-comparison').innerHTML += 
                    '<p style="color: #f44336; margin-top: 10px;">❌ 标签字体大小不一致</p>';
            }
        }

        // 页面加载完成后更新字体大小显示
        window.addEventListener('load', updateFontSizes);
        
        // 窗口大小改变时重新检查
        window.addEventListener('resize', updateFontSizes);
    </script>
</body>
</html>
