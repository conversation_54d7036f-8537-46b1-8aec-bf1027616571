# PowerShell脚本：将JPG壁纸转换为WebP格式

Write-Host "将JPG壁纸转换为WebP格式" -ForegroundColor Green
Write-Host ""

# 切换到壁纸目录
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$wallpaperPath = Join-Path (Split-Path -Parent $scriptPath) "public\img\wallpaper"

if (-not (Test-Path $wallpaperPath)) {
    Write-Host "错误：壁纸目录不存在: $wallpaperPath" -ForegroundColor Red
    exit 1
}

Set-Location $wallpaperPath
Write-Host "当前目录: $(Get-Location)" -ForegroundColor Cyan
Write-Host ""

# 检查是否有可用的转换工具
$hasImageMagick = Get-Command "magick" -ErrorAction SilentlyContinue
$hasCWebP = Get-Command "cwebp" -ErrorAction SilentlyContinue

if (-not $hasImageMagick -and -not $hasCWebP) {
    Write-Host "错误：未找到图像转换工具" -ForegroundColor Red
    Write-Host ""
    Write-Host "请安装以下工具之一："
    Write-Host "1. ImageMagick: https://imagemagick.org/script/download.php#windows"
    Write-Host "2. WebP工具: https://developers.google.com/speed/webp/download"
    Write-Host ""
    Write-Host "或者使用在线转换工具：https://convertio.co/jpg-webp/"
    exit 1
}

# 获取所有JPG文件
$jpgFiles = Get-ChildItem -Filter "*.jpg"

if ($jpgFiles.Count -eq 0) {
    Write-Host "警告：未找到JPG文件" -ForegroundColor Yellow
    exit 0
}

Write-Host "找到 $($jpgFiles.Count) 个JPG文件" -ForegroundColor Green
Write-Host "开始转换..." -ForegroundColor Green
Write-Host ""

$successCount = 0
$failCount = 0

foreach ($file in $jpgFiles) {
    $webpFile = $file.BaseName + ".webp"
    Write-Host "转换: $($file.Name) -> $webpFile" -ForegroundColor Cyan
    
    try {
        if ($hasCWebP) {
            # 使用cwebp转换
            & cwebp -q 85 $file.FullName -o $webpFile
        } elseif ($hasImageMagick) {
            # 使用ImageMagick转换
            & magick $file.FullName -quality 85 $webpFile
        }
        
        if (Test-Path $webpFile) {
            $originalSize = $file.Length
            $webpSize = (Get-Item $webpFile).Length
            $savings = [math]::Round(($originalSize - $webpSize) / $originalSize * 100, 1)
            
            Write-Host "  ✓ 成功创建: $webpFile" -ForegroundColor Green
            Write-Host "  原始大小: $([math]::Round($originalSize / 1KB, 1)) KB" -ForegroundColor Gray
            Write-Host "  WebP大小: $([math]::Round($webpSize / 1KB, 1)) KB" -ForegroundColor Gray
            Write-Host "  节省空间: $savings%" -ForegroundColor Gray
            $successCount++
        } else {
            Write-Host "  ✗ 转换失败: $($file.Name)" -ForegroundColor Red
            $failCount++
        }
    } catch {
        Write-Host "  ✗ 转换出错: $($_.Exception.Message)" -ForegroundColor Red
        $failCount++
    }
    
    Write-Host ""
}

Write-Host "转换完成！" -ForegroundColor Green
Write-Host "成功: $successCount 个文件" -ForegroundColor Green
Write-Host "失败: $failCount 个文件" -ForegroundColor Red
Write-Host ""

# 显示生成的WebP文件
$webpFiles = Get-ChildItem -Filter "*.webp"
if ($webpFiles.Count -gt 0) {
    Write-Host "生成的WebP文件：" -ForegroundColor Green
    $webpFiles | ForEach-Object {
        $size = [math]::Round($_.Length / 1KB, 1)
        Write-Host "  $($_.Name) ($size KB)" -ForegroundColor Gray
    }
} else {
    Write-Host "没有生成WebP文件" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "下一步：检查生成的WebP文件质量，如果满意可以删除原始JPG文件" -ForegroundColor Yellow
