/*
 * 🎯 成熟的响应式设计方案
 * 创建时间: 2025-01-28
 * 目标: 使用标准的响应式设计技术适应不同分辨率
 * 
 * 采用成熟的响应式设计原则：
 * 1. 使用rem/em单位而不是px
 * 2. 使用CSS媒体查询适应不同屏幕
 * 3. 使用viewport单位(vw, vh)
 * 4. 使用flexbox和grid布局
 * 5. 使用clamp()函数实现流体排版
 */

/* ========================================
 * 1. 根字体大小设置 - 响应式基础
 * ======================================== */

/* 基础字体大小 - 根据屏幕大小调整 (高分屏进一步缩小) */
html {
  /* 使用clamp()实现流体字体大小 - 高分屏进一步缩小 */
  font-size: clamp(8px, 1.0vw, 13px);
  line-height: 1.5;
}

/* 不同屏幕尺寸的字体大小调整 - 高分屏进一步缩小 */
@media (max-width: 768px) {
  html {
    font-size: 9px; /* 移动设备 */
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  html {
    font-size: 10px; /* 平板设备 */
  }
}

@media (min-width: 1025px) and (max-width: 1440px) {
  html {
    font-size: 11px; /* 桌面设备 */
  }
}

@media (min-width: 1441px) and (max-width: 1920px) {
  html {
    font-size: 12px; /* 大屏幕 */
  }
}

@media (min-width: 1921px) {
  html {
    font-size: 13px; /* 超大屏幕 */
  }
}

/* 高分屏特殊处理 - 进一步缩小 */
@media (min-width: 2560px) {
  html {
    font-size: 12px; /* 2K屏幕 */
  }
}

@media (min-width: 3840px) {
  html {
    font-size: 11px; /* 4K屏幕 */
  }
}

/* ========================================
 * 2. 统一的字体大小系统 - 使用rem单位
 * ======================================== */

:root {
  /* 🎯 严格的字体大小标准体系 - 基于rem */
  --font-xs: 0.75rem;    /* 12px - 顶部菜单、底部菜单文字 */
  --font-sm: 0.875rem;   /* 14px - sat-info-box内容、底部菜单 */
  --font-base: 1rem;     /* 16px - 基础文字 */
  --font-md: 1.125rem;   /* 18px - 未使用 */
  --font-lg: 1.25rem;    /* 20px - 侧边菜单内容、输入框、按钮、标题 */
  --font-xl: 1.5rem;     /* 24px - 大标题 */
  --font-xxl: 2rem;      /* 32px - 超大标题 */
  
  /* 间距变量 - 基于rem (高分屏更紧凑) */
  --spacing-xs: 0.2rem;
  --spacing-sm: 0.4rem;
  --spacing-base: 0.8rem;
  --spacing-lg: 1.2rem;
  --spacing-xl: 1.6rem;
  
  /* 图标大小 - 基于rem (高分屏进一步缩小) */
  --icon-sm: 0.75rem;
  --icon-base: 1rem;
  --icon-lg: 1.5rem;
  --icon-xl: 2rem;
  
  /* 容器宽度 */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* ========================================
 * 3. 顶部菜单响应式设计
 * ======================================== */

#datetime-text,
#jday,
#sensor-selected {
  font-size: var(--font-sm);
  line-height: 1.4;
}

/* 顶部菜单图标 */
.top-menu-icons img,
#sound-icon,
#legend-icon img,
#tutorial-icon img,
#fullscreen-icon img {
  width: var(--icon-base);
  height: var(--icon-base);
  margin: var(--spacing-xs);
}

/* 搜索框与顶部图标对齐 */
#search,
#search-holder input {
  height: var(--icon-base) !important;
  line-height: var(--icon-base) !important;
  font-size: var(--font-sm) !important;
  padding: 0 var(--spacing-sm) !important;
  margin: 0 !important;
}

/* 搜索清除按钮 */
#clear-search {
  width: var(--icon-sm);
  height: var(--icon-sm);
  font-size: var(--font-sm);
}

/* ========================================
 * 4. 侧边菜单响应式设计
 * ======================================== */

.side-menu-parent {
  width: clamp(280px, 25vw, 400px);
  min-width: 280px;
  font-size: var(--font-base);
}

.side-menu {
  padding: var(--spacing-base);
  font-size: var(--font-base);
  line-height: 1.5;
}

/* 🎯 侧边菜单主题标题与菜单项字体大小一致 */
html body .side-menu h1,
html body .side-menu h2,
html body .side-menu h3,
html body .side-menu h4,
html body .side-menu h5,
html body .side-menu h6,
html body [id$="-menu"] h1,
html body [id$="-menu"] h2,
html body [id$="-menu"] h3,
html body [id$="-menu"] h4,
html body [id$="-menu"] h5,
html body [id$="-menu"] h6,
html body .side-menu-parent h1,
html body .side-menu-parent h2,
html body .side-menu-parent h3,
html body .side-menu-parent h4,
html body .side-menu-parent h5,
html body .side-menu-parent h6 {
  font-size: var(--font-lg) !important;
  margin-bottom: var(--spacing-base) !important;
  line-height: 1.3 !important;
  color: white !important;
  font-weight: bold !important;
  text-align: center !important;
}

/* 🎯 侧边菜单标签统一样式 - 输入框标题、选择框标题与下拉框内容完全一致 */
html body .side-menu label:not(.switch label):not(.lever),
html body .side-menu .input-field label:not(.switch label):not(.lever),
html body [id$="-menu"] label:not(.switch label):not(.lever),
html body [id$="-menu"] .input-field label:not(.switch label):not(.lever),
html body .side-menu-parent label:not(.switch label):not(.lever),
html body .side-menu-parent .input-field label:not(.switch label):not(.lever),
/* 特别针对输入框标签 */
html body .side-menu .input-field input + label,
html body [id$="-menu"] .input-field input + label,
html body .side-menu-parent .input-field input + label,
/* 特别针对选择框标签 */
html body .side-menu .input-field select + label,
html body [id$="-menu"] .input-field select + label,
html body .side-menu-parent .input-field select + label,
html body .side-menu .input-field .select-wrapper + label,
html body [id$="-menu"] .input-field .select-wrapper + label,
html body .side-menu-parent .input-field .select-wrapper + label {
  font-size: var(--font-base) !important;
  line-height: 1.4 !important;
  color: #b3e5fc !important;
  font-weight: 400 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  transform: translateY(0.75rem) !important;
  transform-origin: 0% 100% !important;
  transition: transform 0.2s ease-out, color 0.2s ease-out !important;
}

/* 🎯 激活状态的标签样式 - 保持与下拉框内容相同字体大小 */
html body .side-menu .input-field label.active:not(.switch label):not(.lever),
html body .side-menu .input-field input:focus + label:not(.switch label):not(.lever),
html body .side-menu .input-field input:not(:placeholder-shown) + label:not(.switch label):not(.lever),
html body .side-menu .input-field select:focus + label:not(.switch label):not(.lever),
html body [id$="-menu"] .input-field label.active:not(.switch label):not(.lever),
html body [id$="-menu"] .input-field input:focus + label:not(.switch label):not(.lever),
html body [id$="-menu"] .input-field input:not(:placeholder-shown) + label:not(.switch label):not(.lever),
html body [id$="-menu"] .input-field select:focus + label:not(.switch label):not(.lever),
html body .side-menu-parent .input-field label.active:not(.switch label):not(.lever),
html body .side-menu-parent .input-field input:focus + label:not(.switch label):not(.lever),
html body .side-menu-parent .input-field input:not(:placeholder-shown) + label:not(.switch label):not(.lever),
html body .side-menu-parent .input-field select:focus + label:not(.switch label):not(.lever) {
  font-size: var(--font-base) !important;
  line-height: 1.4 !important;
  color: #2196F3 !important;
  transform: translateY(-1.25rem) scale(0.85) !important;
  transform-origin: 0 0 !important;
}

/* 🎯 开关标签统一样式 - 与输入框标签完全一致 */
html body .side-menu .switch label,
html body [id$="-menu"] .switch label,
html body .side-menu-parent .switch label {
  font-size: var(--font-base) !important;
  line-height: 1.4 !important;
  color: white !important;
  font-weight: 400 !important;
}

/* 侧边菜单所有文本元素统一字体大小 - 与输入框一致，排除开关标签 */
.side-menu,
.side-menu .row .col,
.side-menu .input-field,
.side-menu span,
[id$="-menu"],
[id$="-menu"] .row .col,
[id$="-menu"] .input-field,
[id$="-menu"] span,
.side-menu-parent,
.side-menu-parent .row .col,
.side-menu-parent .input-field,
.side-menu-parent span {
  font-size: var(--font-lg) !important;
  line-height: 1.5 !important;
}

/* 开关容器字体大小单独处理，不影响开关标签 */
.side-menu .switch:not(label),
[id$="-menu"] .switch:not(label),
.side-menu-parent .switch:not(label) {
  font-size: var(--font-sm) !important;
  line-height: 1.5 !important;
}

/* 选择框下拉内容统一字体大小 - 与侧边菜单一致 */
.side-menu .input-field .select-wrapper input.select-dropdown,
[id$="-menu"] .input-field .select-wrapper input.select-dropdown,
.side-menu-parent .input-field .select-wrapper input.select-dropdown {
  font-size: var(--font-lg) !important;
  line-height: 1.4 !important;
  color: white !important;
  height: auto !important;
  min-height: 2.2rem !important;
  padding: var(--spacing-xs) 0 !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* 输入框和选择框统一字体大小 - 与侧边菜单一致 */
.side-menu input,
.side-menu select,
.side-menu textarea,
[id$="-menu"] input,
[id$="-menu"] select,
[id$="-menu"] textarea,
.side-menu-parent input,
.side-menu-parent select,
.side-menu-parent textarea {
  font-size: var(--font-lg) !important;
  padding: var(--spacing-xs) !important;
  height: auto !important;
  min-height: 2.2rem !important;
  line-height: 1.4 !important;
}

/* 🎯 下拉框选项列表统一样式 - 更大尺寸，防止字体大小变化 */
html body .dropdown-content,
html body .side-menu .dropdown-content,
html body [id$="-menu"] .dropdown-content,
html body .side-menu-parent .dropdown-content {
  background: #001a33 !important;
  border: 1px solid #003366 !important;
  max-height: 25rem !important;
  min-height: 8rem !important;
  height: auto !important;
  min-width: 12rem !important;
  width: auto !important;
  overflow-y: auto !important;
  font-size: var(--font-base) !important;
  line-height: 1.4 !important;
  z-index: 9999 !important;
}

html body .dropdown-content li,
html body .side-menu .dropdown-content li,
html body [id$="-menu"] .dropdown-content li,
html body .side-menu-parent .dropdown-content li {
  font-size: var(--font-base) !important;
  line-height: 1.5 !important;
  min-height: 2.5rem !important;
  height: auto !important;
  padding: var(--spacing-sm) var(--spacing-base) !important;
  color: white !important;
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  display: flex !important;
  align-items: center !important;
  transition: background-color 0.2s ease !important;
}

html body .dropdown-content li:hover,
html body .side-menu .dropdown-content li:hover,
html body [id$="-menu"] .dropdown-content li:hover,
html body .side-menu-parent .dropdown-content li:hover {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

html body .dropdown-content li > span,
html body .dropdown-content li > a,
html body .side-menu .dropdown-content li > span,
html body .side-menu .dropdown-content li > a,
html body [id$="-menu"] .dropdown-content li > span,
html body [id$="-menu"] .dropdown-content li > a,
html body .side-menu-parent .dropdown-content li > span,
html body .side-menu-parent .dropdown-content li > a {
  font-size: var(--font-base) !important;
  line-height: 1.5 !important;
  color: white !important;
  padding: 0 !important;
  display: block !important;
  width: 100% !important;
}

/* 确保下拉框选项在所有状态下字体大小一致 */
html body .dropdown-content li.selected,
html body .dropdown-content li.active,
html body .dropdown-content li:focus {
  font-size: var(--font-base) !important;
  line-height: 1.5 !important;
}

.side-menu input,
.side-menu select,
.side-menu textarea {
  font-size: var(--font-base);
  padding: var(--spacing-xs);
  height: auto;
  min-height: 1.8rem;
  line-height: 1.4;
}

.side-menu button,
.side-menu .btn {
  font-size: var(--font-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  height: auto;
  min-height: 1.8rem;
  line-height: 1.4;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  vertical-align: middle;
  flex-direction: row;
  flex-wrap: nowrap;
}

/* 🎯 侧边菜单蓝色按钮统一样式 - 最高优先级，覆盖所有冲突 */
html[lang] body .side-menu .btn:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn),
html[lang] body .side-menu .btn-large:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn),
html[lang] body .side-menu .btn-small:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn),
html[lang] body .side-menu button:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn),
html[lang] body [id$="-menu"] .btn:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn),
html[lang] body [id$="-menu"] .btn-large:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn),
html[lang] body [id$="-menu"] .btn-small:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn),
html[lang] body [id$="-menu"] button:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn),
html[lang] body .side-menu-parent .btn:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn),
html[lang] body .side-menu-parent .btn-large:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn),
html[lang] body .side-menu-parent .btn-small:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn),
html[lang] body .side-menu-parent button:not(.icon-btn):not([id$="-close-btn"]):not([id$="-settings-btn"]):not([id*="settings-color-"]):not(.param-btn):not(.satellite-btn) {
  font-size: var(--font-lg) !important;
  padding: var(--spacing-sm) var(--spacing-base) !important;
  min-height: 2.8rem !important;
  height: 2.8rem !important;
  line-height: 1.4 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  vertical-align: middle !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  background: #2196f3 !important;
  background-color: #2196f3 !important;
  background-image: none !important;
  border: 1px solid #2196f3 !important;
  border-radius: 4px !important;
  color: white !important;
  font-weight: 500 !important;
  text-transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  outline: none !important;
  text-decoration: none !important;
}

/* ========================================
 * 5. 底部菜单响应式设计
 * ======================================== */

#bottom-icons {
  font-size: var(--font-sm);
}

/* 🎯 底部菜单项统一缩放 - 图标和文字成比例，文字更大 */
.bmenu-item,
.bmenu-filter-item {
  font-size: var(--font-sm);
  line-height: 1.2;
  width: clamp(60px, 6vw, 90px);
  height: clamp(45px, 4.5vh, 65px);
}

.bmenu-item img,
.bmenu-filter-item img {
  width: clamp(16px, 1.8vw, 32px);
  height: clamp(16px, 1.8vw, 32px);
}

.bmenu-item span,
.bmenu-filter-item span,
.bmenu-filter-title {
  font-size: var(--font-sm);
  line-height: 1.1;
  margin-top: var(--spacing-xs);
}

/* ========================================
 * 6. 卫星信息框响应式设计
 * ======================================== */

#sat-infobox {
  font-size: var(--font-base);
  line-height: 1.5;
  width: clamp(300px, 30vw, 500px);
}

/* 🎯 卫星信息框标题与菜单项字体大小一致 */
#sat-info-title-name {
  font-size: var(--font-lg) !important;
  line-height: 1.3 !important;
  font-weight: bold !important;
}

/* 🎯 sat-info-box关闭按钮缩放 */
#sat-info-close-btn {
  width: var(--icon-base) !important;
  height: var(--icon-base) !important;
  font-size: var(--font-base) !important;
  line-height: var(--icon-base) !important;
}

/* 🎯 sat-info-box内容文字调小一个档次 - 最高优先级 */
html body #sat-infobox .sat-info-key,
html body #sat-infobox .sat-info-value,
html body .sat-info-box .sat-info-key,
html body .sat-info-box .sat-info-value,
#sat-infobox .sat-info-key,
#sat-infobox .sat-info-value,
.sat-info-box .sat-info-key,
.sat-info-box .sat-info-value {
  font-size: var(--font-sm) !important;
  line-height: 1.4 !important;
}

/* 🎯 顶部菜单与侧边菜单字体大小统一 */
#datetime-text,
#jday,
#sensor-selected {
  font-size: var(--font-xs) !important;
  line-height: 1.4 !important;
}

/* 🎯 确保所有UI组件使用统一的缩放比例 */
html body .side-menu,
html body [id$="-menu"],
html body .side-menu-parent {
  font-size: var(--font-lg) !important;
  line-height: 1.4 !important;
}

html body #sat-infobox,
html body .sat-info-box {
  font-size: var(--font-sm) !important;
  line-height: 1.4 !important;
}

/* 🎯 设置菜单特殊修复 - 解决重叠和显示不全问题 */
#settings-menu {
  max-height: calc(100vh - 4rem) !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

#settings-menu .side-menu {
  padding: var(--spacing-sm) !important;
  max-height: none !important;
}

/* 设置菜单行间距 - 防止重叠 */
#settings-menu .row {
  margin-bottom: var(--spacing-sm) !important;
  clear: both !important;
  min-height: auto !important;
}

/* 设置菜单输入框间距 */
#settings-menu .input-field {
  margin-bottom: var(--spacing-base) !important;
  min-height: 2.5rem !important;
  padding-top: var(--spacing-sm) !important;
  padding-bottom: var(--spacing-xs) !important;
}

/* 设置菜单开关样式 - 防止异常高度 */
#settings-menu .switch {
  height: auto !important;
  max-height: 2rem !important;
  min-height: 2rem !important;
  margin-bottom: var(--spacing-sm) !important;
  margin-top: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  line-height: 2rem !important;
}

#settings-menu .switch.row {
  height: 2rem !important;
  max-height: 2rem !important;
  min-height: 2rem !important;
  margin-bottom: var(--spacing-sm) !important;
  margin-top: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  line-height: 2rem !important;
}

/* 设置菜单标题与其他侧边菜单完全一致 */
#settings-menu h4,
#settings-menu h5,
#settings-menu .menu-title {
  font-size: var(--font-lg) !important;
  line-height: 1.3 !important;
  margin: var(--spacing-base) 0 var(--spacing-sm) 0 !important;
  color: white !important;
  font-weight: bold !important;
  text-align: center !important;
}

/* 设置菜单按钮间距 */
#settings-menu .btn,
#settings-menu button {
  margin: var(--spacing-xs) 0 !important;
  font-size: var(--font-xs) !important;
  min-height: 1.8rem !important;
  padding: var(--spacing-xs) var(--spacing-sm) !important;
}

/* 设置菜单表单间距 */
#settings-menu form {
  padding: 0 !important;
  margin: 0 !important;
}

/* 设置菜单列间距 */
#settings-menu .col {
  padding: 0 var(--spacing-xs) !important;
  margin-bottom: var(--spacing-xs) !important;
}

/* ECF开关特殊高度设置 - 防止被下面元素遮挡 */
#settings-menu .switch:has(input#settings-drawEcf) {
  min-height: 3rem !important;
  height: 3rem !important;
  max-height: 3rem !important;
  margin-bottom: var(--spacing-lg) !important;
  line-height: 3rem !important;
  display: flex !important;
  align-items: center !important;
}

#settings-menu .switch.row:has(input#settings-drawEcf) {
  min-height: 3rem !important;
  height: 3rem !important;
  max-height: 3rem !important;
  margin-bottom: var(--spacing-lg) !important;
  line-height: 3rem !important;
  display: flex !important;
  align-items: center !important;
}

/* 移除时光穿梭开关下面的黑色带 - 最高优先级 */
html body #settings-menu .switch:has(input#settings-time-machine-toasts),
html body #settings-menu .switch.row:has(input#settings-time-machine-toasts),
#settings-menu .switch:has(input#settings-time-machine-toasts),
#settings-menu .switch.row:has(input#settings-time-machine-toasts) {
  border: none !important;
  border-bottom: none !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
  box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

html body #settings-menu .switch:has(input#settings-time-machine-toasts)::before,
html body #settings-menu .switch:has(input#settings-time-machine-toasts)::after,
html body #settings-menu .switch.row:has(input#settings-time-machine-toasts)::before,
html body #settings-menu .switch.row:has(input#settings-time-machine-toasts)::after,
#settings-menu .switch:has(input#settings-time-machine-toasts)::before,
#settings-menu .switch:has(input#settings-time-machine-toasts)::after,
#settings-menu .switch.row:has(input#settings-time-machine-toasts)::before,
#settings-menu .switch.row:has(input#settings-time-machine-toasts)::after {
  display: none !important;
  content: none !important;
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

/* ECF区域特殊间距 - 在"绘制轨道"下面添加更多空间 */
#settings-menu .switch:has(input#settings-drawEcf) {
  margin-bottom: var(--spacing-lg) !important;
}

/* ECF轨道数输入框上方额外间距 */
#settings-menu .input-field:has(input#settings-numberOfEcfOrbitsToDraw) {
  margin-top: var(--spacing-base) !important;
  padding-top: var(--spacing-base) !important;
}

/* 🎯 搜索结果框层级修复 - 确保不被sat-info-box遮挡 */
html body #search-results,
body #search-results,
#search-results {
  z-index: 1002 !important;
}

#sat-add-watchlist,
#sat-remove-watchlist,
#sat-infobox-fi {
  width: var(--icon-base);
  height: var(--icon-sm);
}

/* 卫星悬停框 */
#sat-hoverbox,
#sat-minibox {
  font-size: var(--font-sm);
  line-height: 1.3;
}

/* ========================================
 * 7. 表格和数据显示响应式设计
 * ======================================== */

table,
.table,
.data-table {
  font-size: var(--font-base);
  line-height: 1.5;
}

table th,
table td,
.table th,
.table td {
  font-size: var(--font-base);
  padding: var(--spacing-sm);
}

table th,
.table th {
  font-weight: bold;
}

/* ========================================
 * 8. 表单元素响应式设计
 * ======================================== */

input,
select,
textarea,
button,
.btn {
  font-size: var(--font-base);
  line-height: 1.4;
}

input,
select,
textarea {
  padding: var(--spacing-sm);
  height: auto;
  min-height: 2.5rem;
}

button,
.btn {
  padding: var(--spacing-sm) var(--spacing-base);
  height: auto;
  min-height: 2.5rem;
}

/* ========================================
 * 9. 消息和提示响应式设计
 * ======================================== */

.toast,
.toast-container,
.status-box {
  font-size: var(--font-base);
  line-height: 1.5;
  padding: var(--spacing-sm) var(--spacing-base);
}

/* ========================================
 * 10. 图表和可视化响应式设计
 * ======================================== */

.chart,
.chart-container,
.visualization {
  font-size: var(--font-sm);
}

.chart text,
.chart-container text,
.visualization text,
svg text {
  font-size: var(--font-sm);
  line-height: 1.2;
}

.chart-label,
.chart-legend,
.axis-label,
.legend {
  font-size: var(--font-sm);
  line-height: 1.2;
}

/* ========================================
 * 11. 弹窗和模态对话框响应式设计
 * ======================================== */

.modal,
.modal-content,
.dialog,
.popup,
.overlay {
  font-size: var(--font-base);
  line-height: 1.5;
  width: clamp(300px, 80vw, 800px);
  max-width: 90vw;
}

.modal h1,
.modal h2,
.modal h3,
.dialog h1,
.dialog h2,
.dialog h3 {
  font-size: var(--font-lg);
  line-height: 1.3;
}

/* ========================================
 * 12. 高DPI屏幕优化
 * ======================================== */

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* 高DPI屏幕的特殊处理 */
  img,
  .icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* ========================================
 * 13. 打印样式
 * ======================================== */

@media print {
  html {
    font-size: 12pt;
  }
  
  .side-menu-parent,
  #bottom-icons,
  .modal,
  .overlay {
    display: none !important;
  }
}

/* ========================================
 * 14. 可访问性增强
 * ======================================== */

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 用户偏好大字体时 */
@media (prefers-contrast: high) {
  :root {
    --font-base: 1.125rem;
    --font-lg: 1.375rem;
  }
}
