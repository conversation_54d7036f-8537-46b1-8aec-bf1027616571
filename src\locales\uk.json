{"time": {"days": {"0": "Неділя", "1": "Понеділок", "2": "Вівторок", "3": "Середа", "4": "Четвер", "5": "П'ятниця", "6": "Субота"}, "days-short": {"0": "Нд", "1": "Пн", "2": "Вт", "3": "Ср", "4": "Чт", "5": "Пт", "6": "Сб"}, "months": {"1": "Січень", "2": "Лю<PERSON><PERSON>", "3": "Березень", "4": "Квітень", "5": "Травень", "6": "Червень", "7": "Липень", "8": "Серпень", "9": "Вересень", "10": "Жовтень", "11": "Листопад", "12": "Гру<PERSON><PERSON><PERSON>ь"}, "calendar": {"time": "<PERSON><PERSON><PERSON>", "hour": "Год<PERSON><PERSON>", "minute": "Х<PERSON><PERSON><PERSON><PERSON><PERSON>", "second": "Секунда", "now": "За<PERSON><PERSON><PERSON>", "done": "Готово", "pause": "Пауза", "propagation": "Пропагування"}}, "errorMsgs": {"catalogNotFullyInitialized": "Каталог ще не повністю ініціалізований. Будь ласка, зачекайте кілька секунд і спробуйте знову.", "sensorGroupsApiEmpty": "API не знайдено груп сенсорів, повертаємося до включеної бази даних груп сенсорів.", "SelectSensorFirst": "Спочатку виберіть сенсор!", "SelectSatelliteFirst": "Спочатку виберіть супутник!", "SelectSecondarySatellite": "Спочатку виберіть вторинний супутник!", "SatelliteNotDetailedSatellite": "Супутник не є DetailedSatellite!", "SensorNotFound": "Сенсор не знайдено!", "Scene": {"disablingGodrays": "Ваш комп'ютер працює повільно! Вимикаємо сонячні промені.", "disablingAurora": "Ваш комп'ютер працює повільно! Вимикаємо північне сяйво.", "disablingAtmosphere": "Ваш комп'ютер працює повільно! Вимикаємо атмосферу.", "disablingMoon": "Ваш комп'ютер працює повільно! Вимикаємо Місяць.", "disablingMilkyWay": "Ваш комп'ютер працює повільно! Вимикаємо Чумацький Шлях.", "disablingSun": "Ваш комп'ютер працює повільно! Вимикаємо Сонце."}, "Breakup": {"CannotCreateBreakupForNonCircularOrbits": "Неможливо створити руйнування для некругових орбіт. Працюємо над виправленням.", "CannotCalcDirectionOfSatellite": "Неможливо розрахувати напрямок супутника. Спробуйте пізніше.", "ErrorCreatingBreakup": "Помилка при створенні руйнування!", "InvalidStartNum": "Недійсний початковий номер супутника! За замовчуванням встановлено 90000!", "BreakupGeneratorFailed": "Генератор руйнування не спрацював!", "SatelliteNotFound": "Супутник не знайдено!"}, "Collisions": {"noCollisionsData": "Дані про зіткнення не знайдено!", "errorProcessingCollisions": "Помилка обробки даних SOCRATES!"}, "EditSat": {"errorReadingFile": "Помилка при читанні файлу!", "satelliteNotFound": "Супутник не знайдено!"}, "CreateSat": {"errorCreatingSat": "Помилка при створенні супутника!"}, "Reports": {"popupBlocker": "Будь ласка, дозвольте спливаючі вікна для цього сайту, щоб переглядати звіти."}, "SensorManager": {"errorUpdatingUi": "Помилка оновлення стилів інтерфейсу сенсора."}}, "hoverManager": {"launched": "Запущено", "launchedPlanned": "Запуск: Запланований", "launchedUnknown": "Запуск: Невідомо"}, "loadingScreenMsgs": {"math": "Спроба математичних обчислень...", "science": "Пошук науки...", "science2": "Наука знайдена...", "dots": "Малювання точок у космосі...", "satIntel": "Інтеграція супутникових даних...", "painting": "Малювання Землі...", "coloring": "Розфарбовування всередині ліній...", "elsets": "Пошук GPs...", "models": "Створення 3D-моделей...", "cunningPlan": "Розробка хитрої схеми...", "copyrightNotice": "KeepTrack™ та spacedefense™ є торговельними марками Kruczek Labs LLC.<br>Цей екземпляр ліцензовано за GNU AGPL v3.0. Атрибуція, доступ до вихідного коду та це повідомлення мають залишатися видимими.<br>Жодної комерційної ліцензії не надано, і жодної компенсації правовласнику не виплачено.<br>Несанкціоноване використання, ребрендинг або видалення атрибуції можуть порушувати умови торговельної марки та ліцензії з відкритим кодом.<br>© 2025 Kruczek Labs LLC. Всі права захищено. Див. LICENSE для повних умов.", "copyrightNoticeMobile": "KeepTrack™ та spacedefense™ є торговельними марками Kruczek Labs LLC.<br>Цей екземпляр ліцензовано за GNU AGPL v3.0. Атрибуція, доступ до вихідного коду та це повідомлення мають залишатися видимими. Жодної комерційної ліцензії не надано. Жодної компенсації правовласнику не виплачено. Несанкціоноване використання, ребрендинг або видалення атрибуції можуть порушувати умови торговельної марки та ліцензії з відкритим кодом.<br>© 2025 Kruczek Labs LLC. Всі права захищено. Див. LICENSE для повних умов."}, "splashScreens": {"1": "Моделі супутників виглядають більшими, ніж вони є насправді. Все інше в масштабі.", "2": "Натисніть клавішу 'L', щоб увімкнути/вимкнути орбіти супутників.", "3": "Натисніть клавішу 'P', щоб відкрити полярну діаграму поточного супутника (Спочатку потрібно вибрати сенсор!).", "4": "Скиньте час симуляції, натиснувши клавішу 'T'.", "5": "Просуньте час симуляції вперед, натиснувши клавішу '>' або '='.", "6": "Поверніть час симуляції назад, натиснувши клавішу '<' або ')'.", "7": "Натискання клавіші '/' перемикає швидкість симуляції між 1x та 0x.", "8": "Натисніть клавішу 'V', щоб змінити режим перегляду.", "9": "Натисніть Shift+F1, щоб відкрити меню довідки в будь-який час.", "10": "Натисніть клавішу 'R', щоб увімкнути автоповорот.", "11": "Натискання Shift + C при вибраному супутнику вмикає/вимикає його конус видимості.", "12": "Клавіша 'Home' поверне камеру до поточного сенсора.", "13": "Клавіша '`' (тильда) скине камеру до перегляду за замовчуванням.", "14": "Клав<PERSON><PERSON><PERSON> 'M' покаже 2D-карту поточного супутника.", "15": "Меню налаштувань, розташоване на нижній панелі інструментів, містить багато опцій для налаштування вашого досвіду.", "16": "Багато меню мають додаткові налаштування, доступ до яких можна отримати, натиснувши на значок шестерні.", "17": "Додайте супутники до списку спостереження, щоб отримувати сповіщення, коли вони знаходяться над поточним сенсором.", "18": "Клацніть правою кнопкою миші по земній кулі, щоб відкрити контекстне меню з додатковими опціями.", "19": "Натисніть клавіші '+' або '-' для збільшення та зменшення масштабу.", "20": "Натисніть 'F11', щоб увімкнути/вимкнути повноекранний режим.", "21": "Ви можете шукати супутники за назвою або ID NORAD у рядку пошуку у верхньому правому куті.", "22": "Новий номінальний запуск можна створити, вибравши супутник і натиснувши кнопку 'Новий запуск' у нижньому меню.", "23": "Натисніть клавішу 'N', щоб перемкнути нічний режим.", "24": "Натисніть клавішу 'I', щоб сховати/відобразити контекстну інформацію про супутник.", "25": "Натисніть клавішу 'B', щоб сховати/відобразити меню.", "26": "Прискорте симуляцію, натиснувши Shift + ';'.", "27": "Сповільніть симуляцію, натиснувши клавішу ','.", "28": "Встановіть об'єкт як Вторинний, щоб бачити його відносну відстань з основним об'єктом.", "29": "Переключіть об'єкт між Основним/Вторинним клавішею '['."}, "plugins": {"SensorListPlugin": {"bottomIconLabel": "Сенсори", "title": "Меню списку сенсорів", "helpBody": "Меню сенсорів дозволяє вибрати сенсор для використання в обчисленнях та інших функціях меню. Сенсори згруповані за мережами, які вони переважно підтримують. Ліворуч у меню — назва сенсора, праворуч — країна/організація, якій він належить. <br><br> Вибір опції \"Всі...сенсори\" вибирає всі сенсори цієї групи. Це корисно для візуалізації покриття мережі, але наразі не працює для всіх обчислень. Якщо ви хочете розрахувати кути огляду для мережі, краще скористатися інструментом багатосайтових кутів огляду або розрахувати кути огляду для кожного сенсора окремо. <br><br> У цьому списку є механічні та фазовані радіолокатори, а також оптичні сенсори: <ul style=\"margin-left: 40px;\"> <li> Фазовані радіолокатори зазвичай обмежені низькою навколоземною орбітою (LEO). </li> <li> Механічні радари можна використовувати як для LEO, так і для геостаціонарної орбіти (GEO). </li> <li> Оптичні сенсори зазвичай використовуються для GEO, але можуть застосовуватись і для LEO. </li> <li> Оптичні сенсори обмежені нічними спостереженнями за ясної погоди, тоді як радари працюють і вдень, і вночі. </li> </ul> <br> Інформація про сенсори базується на відкритих даних і може бути перевірена в меню інформації про сенсор. Якщо у вас є відкриті дані про додаткові сенсори або виправлення до існуючої інформації, будь ласка, напишіть на <a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>."}, "SensorInfoPlugin": {"bottomIconLabel": "Інфо про сенсор", "title": "Меню інформації про сенсор", "helpBody": "Інформація про сенсор надає відомості про поточний вибраний сенсор. Інформація базується на відкритих даних і може бути не завжди на 100% точною. Якщо у вас є відкриті дані про додаткові сенсори або виправлення до існуючої інформації, будь ласка, напишіть на <a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>. <br><br> Надається така інформація: <ul style=\"margin-left: 40px;\"> <li> Назва сенсора </li> <li> Власник сенсора </li> <li> Тип сенсора </li> <li> Поле зору сенсора </li> </ul> <br> Також з цього меню можна швидко створити лінії від сенсора до Сонця або Місяця."}, "CustomSensorPlugin": {"bottomIconLabel": "Користувацький сенсор", "title": "Меню користувацького сенсора", "helpBody": "Це дозволяє створити користувацький сенсор для використання в обчисленнях та інших функціях меню. Це може бути повністю оригінальний сенсор або модифікація існуючого. <br><br> Після встановлення широти, довготи та висоти сенсора можна задати його поле зору. Вибір телескопа створить поле зору 360 градусів з маскою піднесення 10 градусів і необмеженою дальністю. Якщо не вибирати телескоп, можна задати поле зору вручну. <br><br> Я<PERSON><PERSON><PERSON> ви хочете редагувати існуючий сенсор, спочатку виберіть його зі списку сенсорів, і користувацький сенсор буде оновлено з інформацією вибраного сенсора."}, "LookAnglesPlugin": {"bottomIconLabel": "Кути огляду", "title": "Ме<PERSON>ю кутів огляду", "helpBody": "Меню кутів огляду дозволяє розрахувати відстань, азимут і кут піднесення між сенсором і супутником. Спочатку потрібно вибрати супутник і сенсор, щоб скористатися меню. <br><br> Перемикач \"лише часи сходу та заходу\" дозволяє розрахувати тільки часи сходу та заходу супутника. Це корисно для швидкого визначення, коли супутник буде видимий для сенсора. Діапазон пошуку можна змінити, змінивши параметри довжини та інтервалу."}, "MultiSiteLookAnglesPlugin": {"bottomIconLabel": "Багатосайтові кути", "title": "Меню багатосайтових кутів огляду", "helpBody": "Меню багатосайтових кутів огляду дозволяє розрахувати відстань, азимут і кут піднесення між супутником і кількома сенсорами. Спочатку потрібно вибрати супутник, щоб скористатися меню. <br><br> За замовчуванням меню розраховує кути огляду для всіх сенсорів мережі космічного спостереження. Якщо ви хочете розрахувати кути огляду для додаткових сенсорів, можна експортувати csv-файл у нижній частині меню. Файл міститиме кути огляду для всіх сенсорів. <br><br> Клік по рядку таблиці вибирає сенсор і змінює час симуляції на час цього кута огляду."}, "SensorTimeline": {"bottomIconLabel": "Тай<PERSON><PERSON><PERSON><PERSON>н сенсора", "title": "Меню таймлайну сенсора", "helpBody": "Меню таймлайну сенсора показує, коли список сенсорів має видимість одного супутника. Таймлайн кольоровий для відображення якості проходу. Червоний — поганий прохід, жовтий — середній, зелений — хороший. Натисніть на прохід, щоб змінити сенсор і час на цей прохід. <br><br> Таймлайн можна змінити, змінивши початковий і кінцевий час симуляції. Також таймлайн можна змінити, змінивши довжину та інтервал."}, "ProximityOps": {"bottomIconLabel": "Зближення та операції зближення", "title": "Зближення та операції зближення (RPO)", "titleSecondary": "Список зближень та операцій зближення", "helpBody": "Знайти майбутні зближення між супутниками", "noradId": "NORAD ID", "maxDistThreshold": "Максимальний поріг відстані (км)", "maxRelativeVelocity": "Максимальна відносна швидкість (км/с)", "searchDuration": "Тривалість пошуку (години)", "geoText": "Геостаціонарна", "leoText": "Низька навколоземна орбіта", "orbitType": "Тип орбіти", "geoAllVsAll": "Геостаціонарна всі проти всіх", "geoAllVsAllTooltip": "Пошук RPO між усіма геостаціонарними супутниками", "comparePayloadsOnly": "Порівнювати лише корисні навантаження", "comparePayloadsOnlyTooltip": "Шукати RPO лише між корисними навантаженнями", "ignoreVimpelRso": "Ігнорувати Vimpel RSO", "ignoreVimpelRsoTooltip": "Ігнорувати RSO з каталогу Vimpel"}, "RPOCalculator": {"bottomIconLabel": "Калькулятор RPO", "title": "Меню калькулятора RPO", "helpBody": "Знайти майбутні зближення між супутниками"}, "SatelliteTimeline": {"bottomIconLabel": "Тай<PERSON><PERSON>айн супутника", "title": "Меню таймлайну супутника", "helpBody": "Меню таймлайну супутника показує, коли один сенсор має видимість списку супутників. Таймлайн кольоровий для відображення якості проходу. Червоний — поганий прохід, жовтий — середній, зелений — хороший. Натисніть на прохід, щоб змінити супутник і час на цей прохід. <br><br> Таймлайн можна змінити, змінивши початковий і кінцевий час симуляції. Також таймлайн можна змінити, змінивши довжину та інтервал."}, "WatchlistPlugin": {"bottomIconLabel": "Список спостереження", "title": "Меню списку спостереження", "helpBody": "Меню списку спостереження дозволяє створити список пріоритетних супутників для відстеження. Це дозволяє швидко отримати доступ до супутників, які вас найбільше цікавлять. Список зберігається у локальному сховищі вашого браузера і буде доступний під час наступного відвідування сайту. <br><br> Коли супутники зі списку спостереження входять у поле зору вибраного сенсора, з'явиться сповіщення, буде проведена лінія від сенсора до супутника, а номер супутника буде відображено на глобусі. <br><br> Функція накладання залежить від заповнення списку спостереження."}, "WatchlistOverlay": {"bottomIconLabel": "Накладання", "title": "Меню накладання", "helpBody": "<p> Накладання списку спостереження показує час наступного проходу для кожного супутника у вашому списку спостереження. Накладання оновлюється кожні 10 секунд. </p> <p> Накладання кольорове для відображення часу до наступного проходу. Кольори такі: </p> <ul> <li>Жовтий — у полі зору</li> <li>Синій — час до наступного проходу до 30 хвилин після поточного часу або 10 хвилин до поточного часу</li> <li>Білий — будь-який майбутній прохід, що не підпадає під вищезазначені вимоги</li> </ul> <p> Натискання на супутник у накладанні центрує карту на цьому супутнику. </p>"}, "ReportsPlugin": {"bottomIconLabel": "Звіти", "title": "Ме<PERSON><PERSON> звітів", "helpBody": "Меню звітів — це набір інструментів для аналізу та розуміння даних, які ви переглядаєте."}, "PolarPlotPlugin": {"bottomIconLabel": "Полярний графік", "title": "Меню полярного графіка", "helpBody": "Меню полярного графіка використовується для створення 2D-полярного графіка азимуту та висоти супутника з часом."}, "NextLaunchesPlugin": {"bottomIconLabel": "Наступні запуски", "title": "Меню наступних запусків", "helpBody": "Меню наступних запусків отримує дані з <a href=\"https://thespacedevs.com/\" target=\"_blank\">The Space Devs</a> для відображення майбутніх запусків."}, "FindSatPlugin": {"bottomIconLabel": "Знайти супутник", "title": "Меню пошуку супутника", "helpBody": "Меню пошуку супутника використовується для пошуку супутників за орбітальними параметрами або характеристиками супутника. <br><br> Для більшості параметрів введіть цільове значення зліва, а потім допустиме відхилення справа. Наприклад, щоб знайти всі супутники з нахилом 51-52 градуси, введіть 51.5 у ліве поле та 0.5 у праве. Пошук знайде всі супутники в цьому діапазоні та відобразить їх у рядку пошуку."}, "ShortTermFences": {"bottomIconLabel": "Короткостроковий пошук", "title": "Меню короткострокових пошуків (STF)", "helpBody": "Меню короткострокових пошуків (STF) використовується для візуалізації пошукових зон сенсора. <br><br> Це навряд чи буде корисно, якщо ви не володієте/не керуєте сенсором із функцією пошукової зони."}, "Collisions": {"bottomIconLabel": "Зіткнення", "title": "Меню зіткнень", "helpBody": "Меню зіткнень показує супутники з високою ймовірністю зіткнення. <br><br> Натискання на рядок вибирає два супутники, що беруть участь у зіткненні, і змінює час на час зіткнення."}, "TrackingImpactPredict": {"bottomIconLabel": "Прогноз входу", "title": "Меню прогнозу входу та впливу", "helpBody": "Меню прогнозу входу та впливу (TIP) відображає останні повідомлення про відстеження та прогноз входу для супутників. Таблиця містить такі стовпці:<br><br> <b>NORAD</b>: Каталожний номер NORAD супутника.<br><br> <b>Дата зниження</b>: Дата прогнозованого зниження супутника.<br><br> <b>Широта</b>: Широта супутника під час зниження.<br><br> <b>Довгота</b>: Довгота супутника під час зниження.<br><br> <b>Вікно (хв)</b>: Часове вікно прогнозу у хвилинах.<br><br> <b>Наступний звіт (год)</b>: Час до наступного звіту у годинах.<br><br> <b>Кут входу (град)</b>: Кут входу супутника у градусах.<br><br> <b>ЕПР (м²)</b>: Ефективна площа розсіювання супутника у квадратних метрах.<br><br> <b>Вік GP (год)</b>: Вік останнього набору елементів у годинах.<br><br> <b>Суха маса (кг)</b>: Суха маса супутника у кілограмах.<br><br> <b>Об'єм (м³)</b>: Оцінений об'єм супутника у кубічних метрах.<br><br>"}, "Breakup": {"bottomIconLabel": "Створити руйнування", "title": "Меню руйнування", "helpBody": "Меню руйнування — це інструмент для моделювання руйнування супутника. <br><br> Шляхом дублювання та зміни орбіти супутника можна змоделювати його руйнування. Після вибору супутника та відкриття меню користувач може вибрати: <ul style=\"margin-left: 40px;\"> <li>Варіація нахилу</li> <li>Варіація RAAN</li> <li>Варіація періоду</li> <li>Кількість уламків</li> </ul> Чим більша варіація, тим ширше розповсюдження уламків у моделюванні. Типові варіації достатні для моделювання руйнування з розумним розповсюдженням."}, "DebrisScreening": {"bottomIconLabel": "Скрин<PERSON>н<PERSON> уламків", "title": "Меню скринінгу уламків", "helpBody": "Меню скринінгу уламків використовується для створення списку уламків, які потенційно можуть бути помічені супутником. Список створюється шляхом розрахунку орбітальних параметрів уламків і порівняння їх із параметрами супутника. Користувач може вибрати створення списку за допомогою TLE або SGP4-прогнозу. Також можна відфільтрувати список за розміром уламків і зоряною величиною. Можна також створити список за допомогою TLE або SGP4-прогнозу. Також можна відфільтрувати список за розміром уламків і зоряною величиною."}, "TransponderChannelData": {"bottomIconLabel": "Транспондери та канали", "title": "Інформація про транспондери та канали супутника", "helpBody": "<p>Ця таблиця містить технічні деталі каналів супутника, включаючи ТБ, радіо та інші комунікаційні сервіси:</p><ul><li><strong>satellite:</strong> Назва супутника, що транслює канал</li><li><strong>tvchannel:</strong> Назва ТБ/радіоканалу або сервісу зв'язку</li><li><strong>beam:</strong> Напрямок променя супутника (наприклад, західна півкуля)</li><li><strong>freq:</strong> Частота транспондера та поляризація (наприклад, 3840 V)</li><li><strong>system:</strong> Система мовлення (наприклад, DVB-S2 8PSK)</li><li><strong>SRFEC:</strong> Швидкість символів і співвідношення FEC (наприклад, 30000 3/4)</li><li><strong>video:</strong> Формат стиснення відео/даних (наприклад, MPEG-4/HD)</li><li><strong>lang:</strong> Доступні мови аудіо/зв'язку (наприклад, Eng, Chi)</li><li><strong>encryption:</strong> Використовувана система шифрування (наприклад, PowerVu)</li></ul><p>Ця інформація корисна для фахівців із супутникового зв'язку, техніків, ентузіастів і всіх, хто налаштовує або діагностує обладнання супутникового прийому.</p>"}, "EditSat": {"bottomIconLabel": "Редагувати супутник", "title": "Меню редагування супутника", "helpBody": "Меню редагування супутника використовується для редагування даних супутника. <br><br> <ul> <li> SCC# супутника — унікальний номер, присвоєний кожному супутнику Космічними силами США. </li> <li> Рік епохи — рік останнього оновлення орбіти супутника. </li> <li> День епохи — день року останнього оновлення орбіти супутника. </li> <li> Нахил — кут між площиною орбіти супутника та екваторіальною площиною. </li> <li> Пряме сходження — кут між висхідним вузлом і положенням супутника на момент останнього оновлення орбіти. </li> <li> Ексцентриситет — ступінь відхилення орбіти супутника від ідеального кола. </li> <li> Аргумент перигею — кут між висхідним вузлом і найближчою до Землі точкою орбіти супутника. </li> <li> Середня аномалія — кут між положенням супутника на момент останнього оновлення орбіти та найближчою до Землі точкою орбіти. </li> <li> Середній рух — швидкість зміни середньої аномалії супутника. </li> </ul>"}, "NewLaunch": {"bottomIconLabel": "Новий запуск", "title": "Меню нового запуску", "helpBody": "Меню нового запуску використовується для створення умовних орбітальних запусків шляхом модифікації існуючих супутників із подібними параметрами. <br><br> Після вибору супутника можна вибрати місце запуску та північний/південний азимут. Вибраний супутник буде змінено для вирівнювання з місцем запуску. Годинник буде скинуто на 00:00:00 для відображення відносного часу після запуску. Це може бути корисно для розрахунку покриття сенсором відносно часу запуску. Взаємозв'язок об'єкта з іншими орбітальними об'єктами буде некоректним."}, "MissilePlugin": {"bottomIconLabel": "Ракета", "title": "Меню ракет", "helpBody": "Меню ракет використовується для створення умовних запусків ракет між країнами. <br><br> При використанні підводних ракет точка запуску — це довільна широта та довгота. При використанні наземних ракет точка запуску — це фіксована широта та довгота на основі відкритих джерел. <br><br> Окрім користувацьких ракет, доступно кілька попередньо визначених сценаріїв із сотнями ракет. <br><br> Усі запуски ракет умовні й не призначені для відображення реальних подій. Траєкторії запуску базуються на одній баллістичній моделі, але використовують різні мінімальні та максимальні дальності."}, "StereoMap": {"bottomIconLabel": "Стереокарта", "title": "Меню стереографічної карти", "helpBody": "Меню стереографічної карти використовується для візуалізації наземних трас супутників у стереографічній проекції. <br/><br/> Ви можете натиснути на точку на трасі, щоб змінити час симуляції на момент, коли супутник досягне цієї точки. <br/><br/> Жовті точки вказують, коли супутник у полі зору сенсора. Червоні точки — коли супутник поза полем зору. Точка, найближча до супутника, — поточний час."}, "SensorFov": {"bottomIconLabel": "Поле зору сенсора"}, "SensorSurvFence": {"bottomIconLabel": "Пошуковий бар'єр сенсора"}, "SatelliteViewPlugin": {"bottomIconLabel": "Вид супутника"}, "SatelliteFov": {"bottomIconLabel": "Поле зору супутника", "title": "Меню поля зору супутника", "helpBody": "Плагін поля зору супутника дозволяє керувати полем зору супутника."}, "Planetarium": {"bottomIconLabel": "Вид планета<PERSON><PERSON>ю"}, "NightToggle": {"bottomIconLabel": "Нічний режим"}, "SatConstellations": {"bottomIconLabel": "Сузір'я", "title": "Меню сузір'їв", "helpBody": "Меню сузір'їв дозволяє переглядати групи супутників. <br><br> Для деяких сузір'їв умовні аплінки/даунлінки та/або міжсупутникові лінії будуть намальовані між супутниками сузір'я."}, "CountriesMenu": {"bottomIconLabel": "Країни", "title": "Меню країн", "helpBody": "Меню країн дозволяє фільтрувати супутники за країною походження."}, "ColorMenu": {"bottomIconLabel": "Кольорові схеми", "title": "Меню кольорових схем", "helpBody": "Меню кольорів — це місце для зміни кольорової теми, що використовується для відображення об'єктів. <br><br> Різні теми можуть змінювати кольори залежно від орбіт об'єктів, їхніх характеристик або відношення до Сонця та/або Землі."}, "Screenshot": {"bottomIconLabel": "Зробити фото"}, "LaunchCalendar": {"bottomIconLabel": "Календар запусків"}, "TimeMachine": {"bottomIconLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> часу"}, "SatellitePhotos": {"bottomIconLabel": "Фото супутників", "title": "Меню фото супутників", "helpBody": "Меню фото супутників використовується для відображення живих фото з окремих супутників. <br><br> Примітка: зміни в API зображень можуть призвести до вибору неправильного супутника в KeepTrack."}, "ScreenRecorder": {"bottomIconLabel": "Записати відео"}, "Astronomy": {"bottomIconLabel": "Астрономія"}, "Calculator": {"bottomIconLabel": "Перетворення систем координат", "title": "Меню перетворення систем координат", "helpBody": "Меню перетворення систем координат використовується для конвертації між різними системами координат. <br><br> Меню дозволяє конвертувати між такими системами координат: <ul style=\"margin-left: 40px;\"> <li> ECI — інерціальна система, центрована на Землі </li> <li> ECEF — земна система, центрована на Землі </li> <li> Геодезична </li> <li> Топоцентрична </li> </ul>"}, "AnalysisMenu": {"bottomIconLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Меню аналізу", "helpBody": "Меню аналізу надає низку інструментів для аналізу даних у поточному вигляді. Інструменти: <ul style=\"margin-left: 40px;\"> <li>Експорт офіційних TLE — експорт справжніх дворядкових елементів.</li> <li>Експорт 3LES — експорт трирядкових елементів.</li> <li>Експорт KeepTrack TLE — експорт усіх дворядкових елементів KeepTrack, включаючи аналітиків.</li> <li>Експорт KeepTrack 3LES — експорт усіх трирядкових елементів KeepTrack, включаючи аналітиків.</li> <li>Знайти близькі об'єкти — знайти об'єкти, які знаходяться близько один до одного.</li> <li>Знайти повторні входи — знайти об'єкти, які, ймовірно, знову увійдуть в атмосферу.</li> <li>Найкращі проходи — знайти найкращі проходи для супутника на основі вибраного сенсора.</li> </ul>"}, "SettingsMenuPlugin": {"bottomIconLabel": "Налаштування", "title": "Меню налаштувань", "helpBody": "Меню налаштувань дозволяє налаштувати застосунок."}, "VideoDirectorPlugin": {"bottomIconLabel": "Відеор<PERSON><PERSON><PERSON><PERSON><PERSON>р", "title": "Меню відеорежисера", "helpBody": "Меню відеорежисера використовується для керування камерою та об'єктами у сцені для створення відео."}, "CreateSat": {"bottomIconLabel": "Створити супутник", "title": "Створити супутник", "helpBody": "Меню створення супутника використовується для створення супутника за його кеплеровими елементами"}, "DopsPlugin": {"bottomIconLabel": "Перегляд DOP", "title": "Меню розбавлення точності", "helpBody": "Меню розбавлення точності (DOP) використовується для розрахунку значень DOP для супутника та сенсора. <br><br> Значення DOP: <ul style=\"margin-left: 40px;\"> <li> GDOP — геометричне розбавлення точності </li> <li> PDOP — розбавлення точності позиції </li> <li> HDOP — розбавлення точності по горизонталі </li> <li> VDOP — розбавлення точності по вертикалі </li> <li> TDOP — розбавлення точності часу </li> <li> NDOP — розбавлення точності за кількістю супутників </li> </ul>"}, "EciPlot": {"bottomIconLabel": "Графік ECI", "title": "Меню графіка ECI", "helpBody": "Меню графіка ECI використовується для побудови положення супутника у системі координат ECI (інерціальна, центрована на Землі)."}, "EcfPlot": {"bottomIconLabel": "Графік ECF", "title": "Меню графіка ECF", "helpBody": "Меню графіка ECF використовується для побудови положення супутника у системі координат ECF (земна, центрована на Землі)."}, "RicPlot": {"bottomIconLabel": "Гра<PERSON>ік RIC", "title": "Меню графіка RIC", "helpBody": "Меню графіка RIC використовується для побудови положення супутника у системі координат RIC (радіальна, по трасі, поперечна)."}, "Time2LonPlots": {"bottomIconLabel": "Водоспадний графік", "title": "Меню водоспадного графіка", "helpBody": "Меню графіка час-довгота (водоспад) використовується для побудови довготи супутника з часом."}, "Lat2LonPlots": {"bottomIconLabel": "Графік широта-довгота", "title": "Меню графіка широта-довгота", "helpBody": "Меню графіка широта-довгота використовується для побудови широти проти довготи у GEO-поясі."}, "Inc2AltPlots": {"bottomIconLabel": "Гра<PERSON><PERSON><PERSON> нахил-висота", "title": "Меню графіка нахил-висота", "helpBody": "Меню графіка нахил-висота використовується для побудови нахилу супутника проти його висоти."}, "Inc2LonPlots": {"bottomIconLabel": "Гра<PERSON><PERSON><PERSON> нахил-довгота", "title": "Меню графіка нахил-довгота", "helpBody": "Меню графіка нахил-довгота використовується для побудови нахилу проти довготи у GEO-поясі."}, "GraphicsMenuPlugin": {"bottomIconLabel": "Меню графіки", "title": "Меню графіки", "helpBody": "Меню графіки використовується для зміни графічних налаштувань застосунку."}}}