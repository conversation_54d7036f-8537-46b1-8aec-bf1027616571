/* 透明菜单样式 - 设置侧边菜单、底部菜单和sat-info-box背景透明 */
/* 使用高优先级选择器确保样式能够覆盖现有设置 */

/* CSS变量定义 */
:root {
  --color-dark-background: #001a33;
  --color-dark-border: #003366;
}

/* ========================================
 * 侧边菜单透明效果
 * ======================================== */

/* 侧边菜单背景透明 - 排除国旗元素 */
html body .side-menu:not(.fi):not([class*="fi-"]),
html body [id$="-menu"]:not(.fi):not([class*="fi-"]),
html body .side-menu-parent:not(.fi):not([class*="fi-"]),
body .side-menu:not(.fi):not([class*="fi-"]),
body [id$="-menu"]:not(.fi):not([class*="fi-"]),
body .side-menu-parent:not(.fi):not([class*="fi-"]),
.side-menu:not(.fi):not([class*="fi-"]),
[id$="-menu"]:not(.fi):not([class*="fi-"]),
.side-menu-parent:not(.fi):not([class*="fi-"]) {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: none !important;
  box-shadow: none !important;
}

/* 菜单内容区域透明 */
html body .side-menu .menu-content,
html body [id$="-menu"] .menu-content,
body .side-menu .menu-content,
body [id$="-menu"] .menu-content,
.side-menu .menu-content,
[id$="-menu"] .menu-content {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* ========================================
 * 底部菜单透明效果
 * ======================================== */

/* 底部菜单容器 - 修复双重边框问题 */
html body #nav-footer,
body #nav-footer,
#nav-footer {
  background: rgba(0, 0, 0, 0.7) !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  background-image: none !important;
  border: none !important;
  border-top: 1px solid rgba(255, 255, 255, 0.8) !important;
  box-shadow: none !important;
}

html body #bottom-icons-container,
body #bottom-icons-container,
#bottom-icons-container {
  background: rgba(0, 0, 0, 0.7) !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  background-image: none !important;
  border: none !important; /* 🔥 内层容器无边框，避免双重边框 */
  box-shadow: none !important;
}

/* 底部图标透明 */
html body #bottom-icons .bottom-icon,
body #bottom-icons .bottom-icon,
#bottom-icons .bottom-icon {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* ========================================
 * sat-info-box透明效果
 * ======================================== */

/* sat-info-box主容器透明 */
html body #sat-infobox,
body #sat-infobox,
#sat-infobox {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: none !important;
  box-shadow: none !important;
}

/* sat-info-box标题区域保持深蓝色不透明 */
html body #sat-info-title,
body #sat-info-title,
#sat-info-title {
  background: rgba(0, 26, 51, 1.0) !important;
  background-color: rgba(0, 26, 51, 1.0) !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* sat-info-box章节标题保持蓝色 */
html body #sat-infobox .sat-info-section-header,
body #sat-infobox .sat-info-section-header,
#sat-infobox .sat-info-section-header {
  background: rgba(13, 71, 161, 0.8) !important;
  background-color: rgba(13, 71, 161, 0.8) !important;
  background-image: none !important;
}

/* ========================================
 * 悬停效果
 * ======================================== */

/* 菜单项悬停 */
[id$="-menu"] .menu-item:hover,
.side-menu .menu-item:hover {
  background: var(--transparency-light) !important;
  background-color: var(--transparency-light) !important;
}

/* 🔥🔥🔥 关键修复：sat-info-box 参数行悬停效果 - 蓝色 🔥🔥🔥 */
html body #sat-infobox .sat-info-row:hover,
body #sat-infobox .sat-info-row:hover,
#sat-infobox .sat-info-row:hover {
  background: rgba(33, 150, 243, 0.3) !important;
  background-color: rgba(33, 150, 243, 0.3) !important;
  background-image: none !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.2s ease !important;
  cursor: default !important;
}

/* 可点击元素的更明显悬停效果 */
html body #sat-infobox .sat-info-row[onclick]:hover,
html body #sat-infobox .sat-info-row[data-tooltip]:hover,
body #sat-infobox .sat-info-row[onclick]:hover,
body #sat-infobox .sat-info-row[data-tooltip]:hover,
#sat-infobox .sat-info-row[onclick]:hover,
#sat-infobox .sat-info-row[data-tooltip]:hover {
  background: rgba(33, 150, 243, 0.5) !important;
  background-color: rgba(33, 150, 243, 0.5) !important;
  background-image: none !important;
  cursor: pointer !important;
}

/* 🔥 禁用顶部标题的悬停效果 */
html body #sat-info-title:hover,
body #sat-info-title:hover,
#sat-info-title:hover {
  background: rgba(0, 26, 51, 1.0) !important;
  background-color: rgba(0, 26, 51, 1.0) !important;
  background-image: none !important;
  cursor: move !important;
}

/* 章节标题悬停效果 */
html body #sat-infobox .sat-info-section-header:hover,
body #sat-infobox .sat-info-section-header:hover,
#sat-infobox .sat-info-section-header:hover {
  background: #0d47a1 !important;
  background-color: #0d47a1 !important;
  background-image: none !important;
  cursor: pointer !important;
}

/* ========================================
 * 卫星悬停信息框 - 移除边框
 * ======================================== */

/* 卫星悬停信息框 - 强制移除边框 */
html body #sat-hoverbox,
body #sat-hoverbox,
#sat-hoverbox {
  border: none !important;
  border-width: 0 !important;
  box-shadow: none !important;
}

/* ========================================
 * 国旗图标特殊处理 - 确保显示
 * ======================================== */

/* 🔥 移除重复的国旗规则 - 已在ultimate-fix.css中统一处理 */
/* 注释掉重复规则，避免CSS冲突 */
/*
html body #sat-infobox-fi,
body #sat-infobox-fi,
#sat-infobox-fi {
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
  width: calc(24px / var(--system-scale-factor, 1)) !important;
  height: calc(18px / var(--system-scale-factor, 1)) !important;
  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  position: absolute !important;
  right: calc(5px / var(--system-scale-factor, 1)) !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 5 !important;
  background-color: transparent !important;
  background-image: inherit !important;
}
*/

/* 🔥 额外保护：确保国旗元素不被任何透明样式影响 */
html body #sat-infobox-fi.fi,
body #sat-infobox-fi.fi,
#sat-infobox-fi.fi,
html body #sat-infobox-fi[class*="fi-"],
body #sat-infobox-fi[class*="fi-"],
#sat-infobox-fi[class*="fi-"] {
  background-image: inherit !important;
}

/* ========================================
 * 搜索结果框透明效果
 * ======================================== */

/* 搜索结果框透明模糊背景 - 与其他窗口保持一致 */
html body #search-results,
body #search-results,
#search-results {
  background: rgba(0, 0, 0, 0.7) !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* 搜索结果项悬停效果 - 与sat-info-box保持一致 */
html body #search-results .search-result:hover,
body #search-results .search-result:hover,
#search-results .search-result:hover {
  background: rgba(33, 150, 243, 0.3) !important;
  background-color: rgba(33, 150, 243, 0.3) !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.2s ease !important;
}

/* ========================================
 * 保持图表和弹出窗口背景不变
 * ======================================== */

/* 确保图表容器不受透明效果影响 */
#chart-container,
.chart-wrapper,
#waterfall-chart,
#lat-lon-chart {
  background: var(--color-dark-background) !important;
  background-color: var(--color-dark-background) !important;
}

/* 确保模态框和弹出窗口保持原有样式 */
.modal,
.popup,
.dialog {
  background: var(--color-dark-background) !important;
  background-color: var(--color-dark-background) !important;
}
