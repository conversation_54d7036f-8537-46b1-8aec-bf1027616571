<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 太空物体模拟平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        .header {
            background: #1a237e;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            background: #1a237e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #3f51b5;
        }
        .btn.danger {
            background: #f44336;
        }
        .btn.success {
            background: #4caf50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .message {
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            display: none;
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        .tabs {
            display: flex;
            border-bottom: 2px solid #e0e0e0;
            margin-bottom: 20px;
        }
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: #1a237e;
            color: #1a237e;
            font-weight: bold;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            max-width: 300px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .admin-strength-weak { color: #f44336; }
        .admin-strength-medium { color: #ff9800; }
        .admin-strength-strong { color: #4caf50; }

        /* 过期用户样式 */
        tr.expired {
            background-color: rgba(244, 67, 54, 0.1);
        }
        tr.expiring-soon {
            background-color: rgba(255, 152, 0, 0.1);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #1a237e;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>管理后台</h1>
        <div>
            <span id="username">管理员</span>
            <button class="btn" onclick="logout()" style="margin-left: 15px;">退出</button>
        </div>
    </div>

    <div class="container">
        <!-- 统计卡片 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">0</div>
                <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeUsers">0</div>
                <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingRegs">0</div>
                <div class="stat-label">待审批</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayLogins">0</div>
                <div class="stat-label">今日登录</div>
            </div>
        </div>

        <!-- 消息显示 -->
        <div id="errorMsg" class="message error"></div>
        <div id="successMsg" class="message success"></div>
        <div id="loading" class="loading">加载中...</div>

        <!-- 标签页 -->
        <div class="card">
            <div class="tabs">
                <div class="tab active" onclick="showTab('users')">用户管理</div>
                <div class="tab" onclick="showTab('registrations')">注册审批</div>
                <div class="tab" onclick="showTab('password')">修改密码</div>
                <div class="tab" onclick="showTab('logs')">系统日志</div>
            </div>

            <!-- 用户管理 -->
            <div id="users-tab" class="tab-content active">
                <h3>用户管理</h3>
                <div style="margin-bottom: 20px;">
                    <button class="btn" onclick="loadUsers()">刷新用户列表</button>
                    <button class="btn success" onclick="showCreateUserForm()">创建新用户</button>
                </div>

                <!-- 创建用户表单 -->
                <div id="createUserForm" style="display: none; background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h4>创建新用户</h4>
                    <form onsubmit="createUser(event)" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; max-width: 600px;">
                        <div class="form-group">
                            <label>用户名:</label>
                            <input type="text" id="newUsername" required>
                        </div>
                        <div class="form-group">
                            <label>邮箱:</label>
                            <input type="email" id="newEmail" required>
                        </div>
                        <div class="form-group">
                            <label>密码:</label>
                            <input type="password" id="newPassword" required minlength="8" oninput="validateAdminPassword()">
                            <small style="color: #666; font-size: 12px; margin-top: 5px; display: block;">
                                至少8位，包含大小写字母、数字和特殊字符
                            </small>
                            <div id="adminPasswordStrength" style="margin-top: 5px; font-size: 12px;"></div>
                        </div>
                        <div class="form-group">
                            <label>角色:</label>
                            <select id="newRole" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="user">普通用户</option>
                                <option value="admin">管理员</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>有效期:</label>
                            <input type="date" id="newExpiresAt" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            <small style="color: #666; font-size: 12px; margin-top: 5px; display: block;">
                                留空表示永不过期
                            </small>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <button type="submit" class="btn success">创建用户</button>
                            <button type="button" class="btn" onclick="hideCreateUserForm()">取消</button>
                        </div>
                    </form>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>角色</th>
                            <th>状态</th>
                            <th>有效期</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="usersTable">
                        <tr>
                            <td colspan="7" style="text-align: center;">点击"刷新用户列表"加载数据</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 设置有效期对话框 -->
            <div id="setExpirationDialog" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 8px; min-width: 400px;">
                    <h3 id="expirationDialogTitle">设置账号有效期</h3>
                    <form onsubmit="setUserExpiration(event)">
                        <div class="form-group">
                            <label>用户名:</label>
                            <input type="text" id="expirationUsername" readonly style="background: #f5f5f5;">
                        </div>
                        <div class="form-group">
                            <label>有效期:</label>
                            <input type="date" id="expirationDate" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            <small style="color: #666; font-size: 12px; margin-top: 5px; display: block;">
                                留空表示永不过期
                            </small>
                        </div>
                        <div style="margin-top: 20px; text-align: right;">
                            <button type="button" class="btn" onclick="hideSetExpirationDialog()">取消</button>
                            <button type="submit" class="btn success">确定</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 注册审批 -->
            <div id="registrations-tab" class="tab-content">
                <h3>注册审批</h3>
                <button class="btn" onclick="loadRegistrations()">刷新注册申请</button>
                <table>
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>申请时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="registrationsTable">
                        <tr>
                            <td colspan="5" style="text-align: center;">点击"刷新注册申请"加载数据</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 修改密码 -->
            <div id="password-tab" class="tab-content">
                <h3>修改密码</h3>
                <form onsubmit="changePassword(event)">
                    <div class="form-group">
                        <label>当前密码:</label>
                        <input type="password" id="currentPwd" required>
                    </div>
                    <div class="form-group">
                        <label>新密码:</label>
                        <input type="password" id="newPwd" required minlength="8">
                    </div>
                    <div class="form-group">
                        <label>确认新密码:</label>
                        <input type="password" id="confirmPwd" required>
                    </div>
                    <button type="submit" class="btn">修改密码</button>
                </form>
            </div>

            <!-- 系统日志 -->
            <div id="logs-tab" class="tab-content">
                <h3>系统日志</h3>
                <button class="btn" onclick="loadLogs()">刷新日志</button>
                <div id="logsContent" style="margin-top: 15px; background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                    点击"刷新日志"查看系统日志
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let API_BASE = '';
        let AUTH_TOKEN = '';

        // 获取API服务器地址
        function getApiUrl() {
            // 首先尝试从localStorage获取（登录时保存的）
            const savedApiUrl = localStorage.getItem('apiBaseUrl');
            if (savedApiUrl) {
                API_BASE = savedApiUrl;
                console.log('从localStorage获取API地址:', API_BASE);
                return;
            }

            // 使用当前主机名
            API_BASE = `http://${window.location.hostname}:3001/api/auth`;
            console.log('使用默认API地址:', API_BASE);
        }

        // 初始化
        function init() {
            console.log('=== 管理后台初始化开始 ===');

            // 获取API地址
            getApiUrl();
            console.log('API地址:', API_BASE);
            
            // 获取认证令牌
            AUTH_TOKEN = localStorage.getItem('authToken');
            console.log('认证令牌:', AUTH_TOKEN ? '已找到' : '未找到');
            
            if (!AUTH_TOKEN) {
                showError('未找到认证令牌，请重新登录');
                setTimeout(() => {
                    window.location.href = '/login.html';
                }, 2000);
                return;
            }
            
            // 验证管理员权限
            verifyAdmin();
        }

        // 验证管理员权限
        async function verifyAdmin() {
            console.log('验证管理员权限...');
            showLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/verify`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token: AUTH_TOKEN })
                });
                
                console.log('验证响应状态:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('验证响应数据:', data);
                    
                    if (data.user && data.user.role === 'admin') {
                        document.getElementById('username').textContent = data.user.username;
                        showSuccess('管理员权限验证成功');
                        loadUsers(); // 自动加载用户列表
                        console.log('=== 初始化完成 ===');
                    } else {
                        showError('需要管理员权限');
                        setTimeout(() => {
                            window.location.href = '/login.html';
                        }, 2000);
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('验证失败:', error);
                showError('权限验证失败: ' + error.message);
                setTimeout(() => {
                    window.location.href = '/login.html';
                }, 2000);
            } finally {
                showLoading(false);
            }
        }

        // 加载用户列表
        async function loadUsers() {
            console.log('加载用户列表...');
            showLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/users`, {
                    headers: { 'Authorization': `Bearer ${AUTH_TOKEN}` }
                });
                
                console.log('用户列表响应状态:', response.status);
                
                if (response.ok) {
                    const users = await response.json();
                    console.log('用户数据:', users);
                    displayUsers(users);
                    updateStats(users);
                    showSuccess(`成功加载 ${users.length} 个用户`);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('加载用户失败:', error);
                showError('加载用户失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 显示用户列表
        function displayUsers(users) {
            const tbody = document.getElementById('usersTable');
            tbody.innerHTML = '';
            
            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无用户数据</td></tr>';
                return;
            }
            
            users.forEach(user => {
                const row = document.createElement('tr');

                // 处理有效期显示
                let expirationDisplay = '永不过期';
                let expirationClass = '';
                if (user.expiresAt) {
                    const expirationDate = new Date(user.expiresAt);
                    const now = new Date();
                    const daysUntilExpiration = Math.ceil((expirationDate - now) / (1000 * 60 * 60 * 24));

                    if (expirationDate < now) {
                        expirationDisplay = `<span style="color: #f44336; font-weight: bold;">已过期</span>`;
                        expirationClass = 'expired';
                    } else if (daysUntilExpiration <= 7) {
                        expirationDisplay = `<span style="color: #ff9800; font-weight: bold;">${daysUntilExpiration}天后过期</span>`;
                        expirationClass = 'expiring-soon';
                    } else {
                        expirationDisplay = expirationDate.toLocaleDateString();
                    }
                }

                row.className = expirationClass;
                row.innerHTML = `
                    <td>${user.username}</td>
                    <td>${user.email || '-'}</td>
                    <td>${user.role === 'admin' ? '管理员' : '普通用户'}</td>
                    <td>${user.isActive ? '活跃' : '禁用'}</td>
                    <td>${expirationDisplay}</td>
                    <td>${new Date(user.createdAt).toLocaleString()}</td>
                    <td>
                        <button class="btn" onclick="showSetExpirationDialog('${user.id}', '${user.username}', '${user.expiresAt || ''}')">
                            设置有效期
                        </button>
                        <button class="btn ${user.isActive ? 'danger' : 'success'}"
                                onclick="toggleUser('${user.id}', ${user.isActive})">
                            ${user.isActive ? '禁用' : '启用'}
                        </button>
                        ${user.username !== 'admin' ? `
                            <button class="btn danger" onclick="deleteUser('${user.id}', '${user.username}')">
                                删除
                            </button>
                        ` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 更新统计数据
        function updateStats(users) {
            const total = users.length;
            const active = users.filter(u => u.isActive).length;
            
            document.getElementById('totalUsers').textContent = total;
            document.getElementById('activeUsers').textContent = active;
            document.getElementById('pendingRegs').textContent = '0';
            document.getElementById('todayLogins').textContent = '0';
        }

        // 切换用户状态
        async function toggleUser(userId, currentStatus) {
            const action = currentStatus ? '禁用' : '启用';
            if (!confirm(`确定要${action}这个用户吗？`)) return;
            
            console.log(`${action}用户:`, userId);
            showLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/toggle-user/${userId}`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${AUTH_TOKEN}` }
                });
                
                if (response.ok) {
                    showSuccess(`用户${action}成功`);
                    loadUsers(); // 重新加载列表
                } else {
                    const error = await response.json();
                    throw new Error(error.error || `${action}失败`);
                }
            } catch (error) {
                console.error(`${action}用户失败:`, error);
                showError(`${action}用户失败: ` + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 删除用户
        async function deleteUser(userId, username) {
            if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可撤销！`)) return;
            
            console.log('删除用户:', userId);
            showLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/users/${userId}`, {
                    method: 'DELETE',
                    headers: { 'Authorization': `Bearer ${AUTH_TOKEN}` }
                });
                
                if (response.ok) {
                    showSuccess(`用户 "${username}" 删除成功`);
                    loadUsers(); // 重新加载列表
                } else {
                    const error = await response.json();
                    throw new Error(error.error || '删除失败');
                }
            } catch (error) {
                console.error('删除用户失败:', error);
                showError('删除用户失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 修改密码
        async function changePassword(event) {
            event.preventDefault();
            
            const currentPwd = document.getElementById('currentPwd').value;
            const newPwd = document.getElementById('newPwd').value;
            const confirmPwd = document.getElementById('confirmPwd').value;
            
            if (newPwd !== confirmPwd) {
                showError('新密码和确认密码不匹配');
                return;
            }
            
            if (newPwd.length < 8) {
                showError('新密码至少需要8位字符');
                return;
            }
            
            console.log('修改密码...');
            showLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/change-password`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        oldPassword: currentPwd,
                        newPassword: newPwd
                    })
                });
                
                if (response.ok) {
                    showSuccess('密码修改成功');
                    document.querySelector('form').reset();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || '密码修改失败');
                }
            } catch (error) {
                console.error('修改密码失败:', error);
                showError('修改密码失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 加载系统日志
        function loadLogs() {
            const logsContent = document.getElementById('logsContent');
            logsContent.innerHTML = `
                <div>系统日志功能开发中...</div>
                <div>当前时间: ${new Date().toLocaleString()}</div>
                <div>API地址: ${API_BASE}</div>
                <div>认证状态: ${AUTH_TOKEN ? '已认证' : '未认证'}</div>
            `;
        }

        // 标签页切换
        function showTab(tabName) {
            // 移除所有active类
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // 添加active类
            event.target.classList.add('active');
            document.getElementById(`${tabName}-tab`).classList.add('active');
            
            console.log('切换到标签页:', tabName);
        }

        // 显示错误消息
        function showError(message) {
            console.error('错误:', message);
            const errorDiv = document.getElementById('errorMsg');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // 显示成功消息
        function showSuccess(message) {
            console.log('成功:', message);
            const successDiv = document.getElementById('successMsg');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 3000);
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // 加载注册申请
        async function loadRegistrations() {
            console.log('加载注册申请...');
            showLoading(true);

            try {
                const response = await fetch(`${API_BASE}/pending-registrations`, {
                    headers: { 'Authorization': `Bearer ${AUTH_TOKEN}` }
                });

                console.log('注册申请响应状态:', response.status);

                if (response.ok) {
                    const registrations = await response.json();
                    console.log('注册申请数据:', registrations);
                    displayRegistrations(registrations);
                    // 移除自动更新统计，改为手动刷新
                    showSuccess(`成功加载 ${registrations.length} 个注册申请`);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('加载注册申请失败:', error);
                showError('加载注册申请失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 显示注册申请列表
        function displayRegistrations(registrations) {
            const tbody = document.getElementById('registrationsTable');
            tbody.innerHTML = '';

            if (registrations.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">暂无待审批的注册申请</td></tr>';
                return;
            }

            registrations.forEach(reg => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${reg.username}</td>
                    <td>${reg.email}</td>
                    <td>${new Date(reg.createdAt).toLocaleString()}</td>
                    <td>待审批</td>
                    <td>
                        <button class="btn success" onclick="approveRegistration('${reg.id}', '${reg.username}')">
                            批准
                        </button>
                        <button class="btn danger" onclick="rejectRegistration('${reg.id}', '${reg.username}')">
                            拒绝
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 更新注册申请统计
        function updateRegistrationStats(registrations) {
            document.getElementById('pendingRegs').textContent = registrations.length;
        }

        // 批准注册申请
        async function approveRegistration(regId, username) {
            if (!confirm(`确定要批准用户 "${username}" 的注册申请吗？`)) return;

            console.log('批准注册申请:', regId);
            showLoading(true);

            try {
                const response = await fetch(`${API_BASE}/approve-registration/${regId}`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${AUTH_TOKEN}` }
                });

                if (response.ok) {
                    showSuccess(`用户 "${username}" 的注册申请已批准`);
                    loadRegistrations(); // 重新加载列表
                    loadUsers(); // 重新加载用户列表
                } else {
                    const error = await response.json();
                    throw new Error(error.error || '批准失败');
                }
            } catch (error) {
                console.error('批准注册申请失败:', error);
                showError('批准失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 拒绝注册申请
        async function rejectRegistration(regId, username) {
            if (!confirm(`确定要拒绝用户 "${username}" 的注册申请吗？`)) return;

            console.log('拒绝注册申请:', regId);
            showLoading(true);

            try {
                const response = await fetch(`${API_BASE}/reject-registration/${regId}`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${AUTH_TOKEN}` }
                });

                if (response.ok) {
                    showSuccess(`用户 "${username}" 的注册申请已拒绝`);
                    loadRegistrations(); // 重新加载列表
                } else {
                    const error = await response.json();
                    throw new Error(error.error || '拒绝失败');
                }
            } catch (error) {
                console.error('拒绝注册申请失败:', error);
                showError('拒绝失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 管理界面密码强度验证
        function validateAdminPassword() {
            const password = document.getElementById('newPassword').value;
            const strengthDiv = document.getElementById('adminPasswordStrength');

            if (!password) {
                strengthDiv.innerHTML = '';
                return;
            }

            const checks = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
            };

            const passedChecks = Object.values(checks).filter(Boolean).length;
            let strength = '';
            let className = '';

            if (passedChecks < 3) {
                strength = '密码强度：弱';
                className = 'admin-strength-weak';
            } else if (passedChecks < 5) {
                strength = '密码强度：中等';
                className = 'admin-strength-medium';
            } else {
                strength = '密码强度：强';
                className = 'admin-strength-strong';
            }

            const details = [];
            if (!checks.length) details.push('至少8位');
            if (!checks.uppercase) details.push('大写字母');
            if (!checks.lowercase) details.push('小写字母');
            if (!checks.number) details.push('数字');
            if (!checks.special) details.push('特殊字符');

            if (details.length > 0) {
                strength += ` (缺少: ${details.join('、')})`;
            }

            strengthDiv.innerHTML = `<span class="${className}">${strength}</span>`;
        }

        // 显示创建用户表单
        function showCreateUserForm() {
            document.getElementById('createUserForm').style.display = 'block';
        }

        // 隐藏创建用户表单
        function hideCreateUserForm() {
            document.getElementById('createUserForm').style.display = 'none';
            document.getElementById('createUserForm').querySelector('form').reset();
            document.getElementById('adminPasswordStrength').innerHTML = '';
        }

        // 创建用户
        async function createUser(event) {
            event.preventDefault();

            const username = document.getElementById('newUsername').value;
            const email = document.getElementById('newEmail').value;
            const password = document.getElementById('newPassword').value;
            const role = document.getElementById('newRole').value;
            const expiresAt = document.getElementById('newExpiresAt').value;

            // 验证密码强度
            const checks = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
            };

            const passedChecks = Object.values(checks).filter(Boolean).length;
            if (passedChecks < 5) {
                showError('密码强度不足，请确保包含大小写字母、数字和特殊字符，且至少8位');
                return;
            }

            showLoading(true);

            try {
                const requestBody = { username, email, password, role };
                if (expiresAt) {
                    requestBody.expiresAt = expiresAt + 'T23:59:59.999Z'; // 设置为当天结束时间
                }

                const response = await fetch(`${API_BASE}/create-user`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${AUTH_TOKEN}`
                    },
                    body: JSON.stringify(requestBody)
                });

                if (response.ok) {
                    const result = await response.json();
                    showSuccess(`${role === 'admin' ? '管理员' : '用户'} "${username}" 创建成功`);
                    hideCreateUserForm();
                    loadUsers(); // 刷新用户列表
                } else {
                    const error = await response.json();
                    throw new Error(error.error || '创建用户失败');
                }
            } catch (error) {
                console.error('创建用户失败:', error);
                showError('创建用户失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 显示设置有效期对话框
        function showSetExpirationDialog(userId, username, currentExpiration) {
            document.getElementById('expirationUsername').value = username;
            document.getElementById('expirationDate').value = currentExpiration ? currentExpiration.split('T')[0] : '';
            document.getElementById('setExpirationDialog').style.display = 'block';

            // 保存用户ID到对话框
            document.getElementById('setExpirationDialog').setAttribute('data-user-id', userId);
        }

        // 隐藏设置有效期对话框
        function hideSetExpirationDialog() {
            document.getElementById('setExpirationDialog').style.display = 'none';
            document.getElementById('expirationDate').value = '';
        }

        // 设置用户有效期
        async function setUserExpiration(event) {
            event.preventDefault();

            const userId = document.getElementById('setExpirationDialog').getAttribute('data-user-id');
            const username = document.getElementById('expirationUsername').value;
            const expirationDate = document.getElementById('expirationDate').value;

            showLoading(true);

            try {
                const requestBody = {};
                if (expirationDate) {
                    requestBody.expiresAt = expirationDate + 'T23:59:59.999Z';
                }

                const response = await fetch(`${API_BASE}/set-user-expiration/${userId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${AUTH_TOKEN}`
                    },
                    body: JSON.stringify(requestBody)
                });

                if (response.ok) {
                    const expirationText = expirationDate ? `到 ${expirationDate}` : '永不过期';
                    showSuccess(`用户 "${username}" 的有效期已设置为${expirationText}`);
                    hideSetExpirationDialog();
                    loadUsers(); // 刷新用户列表
                } else {
                    const error = await response.json();
                    throw new Error(error.error || '设置有效期失败');
                }
            } catch (error) {
                console.error('设置有效期失败:', error);
                showError('设置有效期失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('authToken');
                localStorage.removeItem('user');
                localStorage.removeItem('apiBaseUrl');
                window.location.href = '/login.html';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
        
        console.log('=== 管理后台脚本加载完成 ===');
    </script>
</body>
</html>
