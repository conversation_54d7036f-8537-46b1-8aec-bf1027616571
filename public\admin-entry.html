<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理入口 - 太空物体模拟平台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 15px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 15px 0;
            text-decoration: none;
            transition: background 0.3s;
        }
        .btn:hover {
            background: rgba(255,255,255,0.3);
        }
        .btn.primary {
            background: #4caf50;
            border-color: #4caf50;
        }
        .btn.primary:hover {
            background: #45a049;
        }
        .status {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
        }
        .company-info {
            margin-top: 30px;
            opacity: 0.8;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀 太空物体模拟平台</div>
        <h2>管理入口</h2>
        
        <div id="status" class="status">检查登录状态...</div>
        
        <div id="loginSection">
            <p style="margin-bottom: 20px;">请先登录以访问管理功能</p>
            <a href="/login.html" class="btn primary">前往登录</a>
        </div>
        
        <div id="adminSection" style="display: none;">
            <p style="margin-bottom: 20px;">欢迎，<span id="username"></span>！</p>
            <a href="/admin-simple.html" class="btn primary">进入管理后台</a>
            <a href="/" class="btn">返回主系统</a>
            <button class="btn" onclick="logout()">退出登录</button>
        </div>
        
        <div class="company-info">
            <p>北京星地探索科技有限公司</p>
            <p><EMAIL></p>
        </div>
    </div>

    <script>
        let API_BASE = '';

        function getApiUrl() {
            const savedApiUrl = localStorage.getItem('apiBaseUrl');
            if (savedApiUrl) {
                API_BASE = savedApiUrl;
            } else {
                API_BASE = `http://${window.location.hostname}:5001/api/auth`;
            }
        }

        async function checkLoginStatus() {
            const statusDiv = document.getElementById('status');
            const loginSection = document.getElementById('loginSection');
            const adminSection = document.getElementById('adminSection');
            
            try {
                const token = localStorage.getItem('authToken');
                if (!token) {
                    statusDiv.textContent = '未登录';
                    loginSection.style.display = 'block';
                    adminSection.style.display = 'none';
                    return;
                }

                statusDiv.textContent = '验证登录状态...';

                const response = await fetch(`${API_BASE}/verify`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.valid && data.user) {
                        if (data.user.role === 'admin') {
                            statusDiv.textContent = '管理员已登录';
                            document.getElementById('username').textContent = data.user.username;
                            loginSection.style.display = 'none';
                            adminSection.style.display = 'block';
                        } else {
                            statusDiv.textContent = '权限不足：需要管理员权限';
                            loginSection.style.display = 'block';
                            adminSection.style.display = 'none';
                        }
                    } else {
                        throw new Error('登录状态无效');
                    }
                } else {
                    throw new Error('验证失败');
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                statusDiv.textContent = '登录状态验证失败';
                localStorage.removeItem('authToken');
                localStorage.removeItem('user');
                loginSection.style.display = 'block';
                adminSection.style.display = 'none';
            }
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('authToken');
                localStorage.removeItem('user');
                localStorage.removeItem('apiBaseUrl');
                window.location.reload();
            }
        }

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', () => {
            getApiUrl();
            checkLoginStatus();
        });
    </script>
</body>
</html>
