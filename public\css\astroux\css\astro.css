@charset "UTF-8";

:root {
  --thumbSize: var(--controlOptionSize);
  --thumbShadow: 0 3px 5px rgba(0, 0, 0, 0.14), 0 1px 9px rgba(0, 0, 0, 0.12), 0 1px 3px rgba(0, 0, 0, 0.2);
  --thumbShadowHover: 0 6px 10px rgba(0, 0, 0, 0.14), 0 1px 18px rgba(0, 0, 0, 0.12), 0 3px 5px rgba(0, 0, 0, 0.2);
  --thumbShadowActive: inset 0 0 0 4px var(--primary), 0 1px 3px rgba(0, 0, 0, 0.14), 0 1px 4px rgba(0, 0, 0, 0.12), 0 1px 1px rgba(0, 0, 0, 0.2);
  --trackHeight: 3px;
  --trackCursor: pointer;
  --step: 1;
  --min: 0;
  --max: 100;
  --ticksThickness: 1px;
  --ticksHeight: 3px;
  --ticksColor: var(--primaryDark);

  --progressPadding: 2px 0 0 2px;
  --progressRadius: 10px 10px 10px 10px;
  --progressHeight: 14px;
  --progressWidth: calc(100% - 4px);

  --padding: 0.5rem;
  --paddingLeft: 0.5rem;
  --paddingRight: 0.5rem;
  --paddingTop: 0.25rem;
  --paddingBottom: 0.25rem;

  --disabledControlOpacity: 0.4;
  --disabledControlCursor: not-allowed;
  --disabledOpacity: 0.4;
  --disabledCursor: not-allowed;
  --buttonBorderRadius: 3px;
  --defaultBorderRadius: 3px;
  --controlOptionSize: 1.125rem;
  /* 18px */
  --controlBorderRadius: 3px;
  --labelFontSize: 1rem;
  --smallLabelFontSize: 0.875rem;
  --largeLabelFontSize: 1.125rem;
  /* Typography */
  --fontFamily: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  --fontSize: 1rem;
  /* 16px */
  --fontSizeXL: 1.125rem;
  --fontSizeLG: 1rem;
  --fontSizeMD: 0.875rem;
  --fontSizeSM: 0.75rem;
  --fontSizeXS: 0.65rem;
  --fontFamilyLight: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  --fontFamilyMono: 'Roboto Mono', monospace;
  --colorBlack: rgb(0, 0, 0);
  --colorWhite: rgb(255, 255, 255);
  --colorGray: rgb(204, 204, 204);
  --colorPrimary: rgb(0, 90, 143);
  --colorSecondary: rgb(77, 172, 255);
  --colorTertiary: rgb(40, 63, 88);
  --colorQuaternary: rgb(206, 214, 228);
  --statusDarkCritical: rgb(255, 56, 56);
  --statusDarkSerious: rgb(255, 179, 0);
  --statusDarkCaution: rgb(252, 232, 58);
  --statusDarkNormal: rgb(86, 240, 0);
  --statusDarkStandby: rgb(45, 204, 255);
  --statusDarkOff: rgb(158, 167, 173);
  --statusLightCritical: rgb(255, 42, 4);
  --statusLightSerious: rgb(255, 175, 61);
  --statusLightCaution: rgb(250, 216, 0);
  --statusLightNormal: rgb(0, 226, 0);
  --statusLightStandby: rgb(100, 217, 255);
  --statusLightOff: rgb(142, 154, 163);
  --classificationTopSecretSCIBackgroundColor: rgba(252, 232, 58);
  --classificationTopSecretBackgroundColor: rgba(255, 140, 0);
  --classificationSecretBackgroundColor: rgba(200, 16, 46);
  --classificationConfidentialBackgroundColor: rgba(0, 51, 160);
  --classificationControlledBackgroundColor: rgba(80, 43, 133);
  --classificationUnclassifiedBackgroundColor: rgba(0, 122, 51);
  --colorTag1: rgb(25, 199, 202);
  --colorTag2: rgb(120, 112, 208);
  --colorTag3: rgb(160, 13, 190);
  --colorTag4: rgb(216, 83, 30);
  --colorPrimaryLighten5: rgb(51, 123, 165);
  --colorPrimaryLighten1: rgb(51, 123, 165);
  --colorPrimaryLighten2: rgb(102, 156, 188);
  --colorPrimaryLighten3: rgb(153, 189, 210);
  --colorPrimaryLighten4: rgb(204, 222, 233);
  --colorPrimaryDarken1: rgb(0, 72, 114);
  --colorPrimaryDarken2: rgb(0, 54, 86);
  --colorPrimaryDarken3: rgb(0, 36, 57);
  --colorPrimaryDarken4: rgb(0, 18, 29);
  --colorSecondaryLighten1: rgb(113, 189, 255);
  --colorSecondaryLighten2: rgb(146, 203, 255);
  --colorSecondaryLighten3: rgb(184, 222, 255);
  --colorSecondaryLighten4: rgb(219, 238, 255);
  --colorSecondaryDarken1: rgb(62, 138, 204);
  --colorSecondaryDarken2: rgb(46, 103, 153);
  --colorSecondaryDarken3: rgb(31, 69, 102);
  --colorSecondaryDarken4: rgb(15, 34, 51);
  --colorTertiaryLighten1: rgb(82, 102, 122);
  --colorTertiaryLighten2: rgb(126, 140, 155);
  --colorTertiaryLighten3: rgb(169, 178, 188);
  --colorTertiaryLighten4: rgb(212, 217, 222);
  --colorTertiaryDarken1: rgb(31, 51, 71);
  --color-dark-background: rgb(24, 38, 53);
  --color-dark-border: rgb(16, 25, 35);
  --colorTertiaryDarken4: rgb(8, 13, 18);
  --colorQuaternaryLighten1: rgb(217, 222, 233);
  --colorQuaternaryLighten2: rgb(225, 230, 239);
  --colorQuaternaryLighten3: rgb(236, 239, 244);
  --colorQuaternaryLighten4: rgb(245, 246, 249);
  --colorQuaternaryDarken1: rgb(166, 171, 182);
  --colorQuaternaryDarken2: rgb(124, 128, 136);
  --colorQuaternaryDarken3: rgb(83, 86, 91);
  --colorQuaternaryDarken4: rgb(41, 43, 45);
  --colorCriticalLighten1: rgb(255, 96, 96);
  --colorCriticalLighten2: rgb(255, 136, 136);
  --colorCriticalLighten3: rgb(255, 175, 175);
  --colorCriticalLighten4: rgb(255, 215, 215);
  --colorCriticalDarken1: rgb(204, 45, 45);
  --colorCriticalDarken2: rgb(153, 34, 34);
  --colorCriticalDarken3: rgb(102, 22, 22);
  --colorCriticalDarken4: rgb(51, 11, 11);
  --colorSeriousLighten1: rgb(255, 194, 51);
  --colorSeriousLighten2: rgb(255, 209, 102);
  --colorSeriousLighten3: rgb(255, 225, 153);
  --colorSeriousLighten4: rgb(255, 240, 204);
  --colorSeriousDarken1: rgb(204, 143, 0);
  --colorSeriousDarken2: rgb(153, 107, 0);
  --colorSeriousDarken3: rgb(102, 72, 0);
  --colorSeriousDarken4: rgb(51, 36, 0);
  --colorCautionLighten1: rgb(253, 237, 97);
  --colorCautionLighten2: rgb(253, 241, 137);
  --colorCautionLighten3: rgb(254, 246, 176);
  --colorCautionLighten4: rgb(254, 250, 216);
  --colorCautionDarken1: rgb(202, 186, 46);
  --colorCautionDarken2: rgb(151, 139, 35);
  --colorCautionDarken3: rgb(101, 93, 23);
  --colorCautionDarken4: rgb(50, 46, 12);
  --colorNormalLighten1: rgb(120, 243, 51);
  --colorNormalLighten2: rgb(154, 246, 102);
  --colorNormalLighten3: rgb(187, 249, 153);
  --colorNormalLighten4: rgb(221, 252, 204);
  --colorNormalDarken1: rgb(69, 192, 0);
  --colorNormalDarken2: rgb(52, 144, 0);
  --colorNormalDarken3: rgb(34, 96, 0);
  --colorNormalDarken4: rgb(17, 48, 0);
  --colorStandbyLighten1: rgb(87, 214, 255);
  --colorStandbyLighten2: rgb(129, 224, 255);
  --colorStandbyLighten3: rgb(171, 235, 255);
  --colorStandbyLighten4: rgb(213, 245, 255);
  --colorStandbyDarken1: rgb(36, 163, 204);
  --colorStandbyDarken2: rgb(27, 122, 153);
  --colorStandbyDarken3: rgb(18, 82, 102);
  --colorStandbyDarken4: rgb(9, 41, 51);
  --colorOffLighten1: rgb(177, 185, 189);
  --colorOffLighten2: rgb(197, 202, 206);
  --colorOffLighten3: rgb(216, 220, 222);
  --colorOffLighten4: rgb(236, 237, 239);
  --colorOffDarken1: rgb(126, 134, 138);
  --colorOffDarken2: rgb(95, 100, 104);
  --colorOffDarken3: rgb(63, 67, 69);
  --colorOffDarken4: rgb(32, 33, 35);
  --colorTag1Lighten1: rgb(71, 210, 213);
  --colorTag1Lighten2: rgb(117, 221, 223);
  --colorTag1Lighten3: rgb(163, 233, 234);
  --colorTag1Lighten4: rgb(209, 244, 244);
  --colorTag1Darken1: rgb(20, 159, 162);
  --colorTag1Darken2: rgb(15, 119, 121);
  --colorTag1Darken3: rgb(10, 80, 81);
  --colorTag1Darken4: rgb(5, 40, 40);
  --colorTag2Lighten1: rgb(147, 141, 217);
  --colorTag2Lighten2: rgb(174, 169, 227);
  --colorTag2Lighten3: rgb(201, 198, 236);
  --colorTag2Lighten4: rgb(228, 226, 246);
  --colorTag2Darken1: rgb(96, 90, 166);
  --colorTag2Darken2: rgb(72, 67, 125);
  --colorTag2Darken3: rgb(48, 45, 83);
  --colorTag2Darken4: rgb(24, 22, 42);
  --colorTag3Lighten1: rgb(179, 61, 203);
  --colorTag3Lighten2: rgb(198, 110, 216);
  --colorTag3Lighten3: rgb(217, 158, 229);
  --colorTag3Lighten4: rgb(236, 207, 242);
  --colorTag3Darken1: rgb(128, 10, 152);
  --colorTag3Darken2: rgb(96, 8, 114);
  --colorTag3Darken3: rgb(64, 5, 76);
  --colorTag3Darken4: rgb(32, 3, 38);
  --colorTag4Lighten1: rgb(224, 117, 75);
  --colorTag4Lighten2: rgb(232, 152, 120);
  --colorTag4Lighten3: rgb(239, 186, 165);
  --colorTag4Lighten4: rgb(247, 221, 210);
  --colorTag4Darken1: rgb(173, 66, 24);
  --colorTag4Darken2: rgb(130, 50, 18);
  --colorTag4Darken3: rgb(86, 33, 12);
  --colorTag4Darken4: rgb(43, 17, 6);
  --colorWhiteLighten1: rgb(255, 255, 255);
  --colorWhiteLighten2: rgb(255, 255, 255);
  --colorWhiteLighten3: rgb(255, 255, 255);
  --colorWhiteLighten4: rgb(255, 255, 255);
  --colorWhiteDarken1: rgb(204, 204, 204);
  --colorWhiteDarken2: rgb(153, 153, 153);
  --colorWhiteDarken3: rgb(102, 102, 102);
  --colorWhiteDarken4: rgb(51, 51, 51);
  --colorBlackLighten1: rgb(51, 51, 51);
  --colorBlackLighten2: rgb(102, 102, 102);
  --colorBlackLighten3: rgb(153, 153, 153);
  --colorBlackLighten4: rgb(204, 204, 204);
  --colorBlackDarken1: rgb(0, 0, 0);
  --colorBlackDarken2: rgb(0, 0, 0);
  --colorBlackDarken3: rgb(0, 0, 0);
  --colorBlackDarken4: rgb(0, 0, 0);
  --colorGrayLighten1: rgb(214, 214, 214);
  --colorGrayLighten2: rgb(224, 224, 224);
  --colorGrayLighten3: rgb(235, 235, 235);
  --colorGrayLighten4: rgb(245, 245, 245);
  --colorGrayDarken1: rgb(163, 163, 163);
  --colorGrayDarken2: rgb(122, 122, 122);
  --colorGrayDarken3: rgb(82, 82, 82);
  --colorGrayDarken4: rgb(41, 41, 41);
}

/* Regular */
@font-face {
  font-family: 'Roboto';
  src: url('../fonts/RobotoRegular.woff2') format('woff2'), url('../fonts/RobotoRegular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
}

.light-theme {
  /*

    Global Colors
    ==========================================================================
	*/
  /* Astro 5 Simplified Color Palette */
  --backgroundColor: var(--colorQuaternaryLighten3, #eaeef4);
  --defaultText: var(--colorQuaternaryDarken4, #292a2d);
  --secondaryText: var(--colorQuaternaryDarken3, #51555b);
  --globalAppHeader: var(--color-dark-background, #172635);
  --surfaceElements: var(--colorWhite, #ffffff);
  --primary: var(--colorPrimary, #005a8f);
  --primaryLight: var(--colorPrimaryLighten1, #2f7aa7);
  --primaryDark: var(--colorPrimaryDarken1, #004872);
  --primaryDarkHover: #0048724d;
  /* TODO: this is a temporary fix, the use of opacity from Sketch is new and not accounted for in CSS */
  --primaryElementText: var(--colorWhite, #ffffff);
  --inputBackground: var(--colorWhite, #ffffff);
  --inputDark: #080c11;
  /* styles */
  --fontColor: var(--defaultText);
  --fontLowContrastColor: var(--secondaryText);
  --fontInvertedColor: var(--primary);
  --fontInvertedBackgroundColor: var(--backgroundColor);
  --fontLinkColor: var(--primary);
  --fontLinkHoverColor: var(--primaryDark);
  --wcagCompliance: rgba(0, 0, 0, 0.5);
  --criticalBorder: #7f1c1c;
  --colorCritical: var(--statusLightCritical, rgb(255, 42, 4));
  --colorSerious: var(--statusLightSerious, rgb(255, 175, 61));
  --colorCaution: var(--statusLightCaution, rgb(250, 216, 0));
  --colorNormal: var(--statusLightNormal, rgb(0, 226, 0));
  --colorStandby: var(--statusLightStandby, rgb(100, 217, 255));
  --colorOff: var(--statusLightOff, rgb(142, 154, 163));
  /*

    Button Colors
    ==========================================================================
    For standard and outline buttons

  */
  /* Button */
  --buttonTextColor: var(--primaryElementText);
  --buttonBackgroundColor: var(--primary);
  --buttonBorderColor: var(--primary);
  /* Button Hover State */
  --buttonHoverTextColor: var(--primaryElementText);
  --buttonHoverBackgroundColor: var(--primaryDark);
  --buttonHoverBorderColor: var(--primaryDark);
  /* Button Active State */
  --buttonActiveControlTextColor: var(--defaultText);
  --buttonActiveBackgroundColor: var(--primary);
  --buttonActiveBorderColor: var(--primary);
  /* Outline Button Variant */
  --buttonOutlineTextColor: var(--colorPrimary, rgb(0, 90, 143));
  --buttonOutlineBackgroundColor: transparent;
  --buttonOutlineBorderColor: var(--colorPrimary, rgb(0, 90, 143));
  /* Outline Button Variant Hover State */
  --buttonOutlineHoverTextColor: var(--primaryDark);
  --buttonOutlineHoverBackgroundColor: transparent;
  --buttonOutlineHoverBorderColor: var(--primaryDark);
  /*

    Control Colors
    ==========================================================================
    For checkboxes, radio buttons etc …

  */
  --controlTextColor: var(--primaryElementText);
  --controlLabelColor: var(--defaultText);
  --controlBackgroundColor: var(--primary);
  --controlBorderColor: var(--primary);
  --controlAccentColor: var(--primary);
  --controlSelectedTextColor: var(--defaultText);
  --controlSelectedBackgroundColor: transparent;
  --controlSelectedBorderColor: var(--primary);
  --controlHoverOutlineBackgroundColor: none;
  --controlHoverBorderColor: var(--primaryDark);
  --controlSelectedOutlineBorderColor: var(--primary);
  --controlSelectedOutlineBackgroundColor: none;
  /*

    Progress Bar Colors
    ==========================================================================

  */
  --progressDeterminateBarBackgroundColor: var(--primary);
  --progressDeterminateTrackBackgroundColor: var(--inputBackground);
  --progressDeterminateTrackBorderColor: var(--primaryDark);
  --progressIndeterminate: url("data:image/svg+xml,%3Csvg width='66' height='66' xmlns='http://www.w3.org/2000/svg'%3E %3Cdefs%3E %3ClinearGradient x1='70.883%25' y1='4.637%25' x2='50%25' y2='100%25' id='a'%3E %3Cstop stop-color='%235CB3FF' offset='0%25'/%3E %3Cstop stop-color='%23F6F7F8' stop-opacity='0' offset='100%25'/%3E %3C/linearGradient%3E %3C/defs%3E %3Cg fill='none' fill-rule='evenodd'%3E %3Cpath d='M33 65.5C15.05 65.5.5 50.95.5 33S15.05.5 33 .5 65.5 15.05 65.5 33 50.95 65.5 33 65.5zm0-7c14.083 0 25.5-11.417 25.5-25.5S47.083 7.5 33 7.5 7.5 18.917 7.5 33 18.917 58.5 33 58.5z' stroke='%23D7DDE2' fill='%23FFF'/%3E %3Cpath d='M51.908 8.236l-2.358 3.245A26.894 26.894 0 0 0 32 5C17.088 5 5 17.088 5 32s12.088 27 27 27c1.129 0 2.242-.07 3.334-.204l4.435 3.222C37.286 62.66 34.683 63 32 63 14.88 63 1 49.12 1 32 1 14.88 14.88 1 32 1c7.579 0 14.522 2.72 19.908 7.236z' fill='url(%23a)' transform='translate(1 1)'/%3E %3Cpath d='M48.564 13c1.92 0 3.557-.64 4.075-2.367.112-.375.361-.67.361-1.08C53 7.248 51.572 5 49.234 5S45 6.867 45 9.17c0 2.304 1.225 3.83 3.564 3.83z' stroke='%234DACFF' fill='%2352AEFF'/%3E %3C/g%3E %3C/svg%3E ");
  /*

    Popup Menu Colors
    ==========================================================================

  */
  --popupMenuBackgroundColor: var(--inputBackground);
  --popupMenuBorderColor: var(--primary);
  --popupMenuTextColor: var(--defaultText);
  --popupCaretBackgroundColor: var(--primary);
  --popupMenuItemBackgroundColor: var(--inputBackground);
  --popupMenuItemHoverBackgroundColor: var(--primaryDark);
  --popupMenuItemHoverTextColor: var(--primaryElementText);
  --popupMenuItemSeperatorBorderColor: var(--defaultText);
  /*

    Slider Colors
    ==========================================================================

  */
  --sliderThumbBackgroundColor: var(--primaryDark);
  --sliderThumbBorderColor: var(--inputBackground);
  --sliderHoverThumbBackgroundColor: var(--primaryDark);
  --sliderHoverThumbBorderColor: var(--inputBackground);
  --sliderTrackBorderColor: var(--secondaryText);
  --sliderTrackBackgroundColor: var(--inputBackground);
  --sliderSelectedThumbBorderColor: var(--primaryLight);
  --sliderSelectedTrackBackgroundColor: var(--primary);
  --sliderThumbBorderSize: 1px;
  --sliderTrackBorderSize: 0.25px;
  /*

    Segmented Button Colors
    ==========================================================================

  */
  --segmentedButtonBorderColor: var(--primary);
  --segmentedButtonBackgroundColor: none;
  --segmentedButtonTextColor: var(--primary);
  /* Segmented Hover */
  --segmentedButtonHoverBackgroundColor: var(--primaryDark);
  --segmentedButtonHoverTextColor: var(--primaryElementText);
  --segmentedButtonHoverBorderColor: var(--primaryDark);
  /* Segmented Select */
  --segmentedButtonSelectedBackgroundColor: var(--primary);
  --segmentedButtonSelectedTextColor: var(--primaryElementText);
  /*

    Drop Down/Select Colors
    ==========================================================================

  */
  --selectHoverOptBackgroundColor: var(--primaryDark);
  --selectCaret: url('data:image/svg+xml,%3Csvg%20width%3D%2210%22%20height%3D%225%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill%3D%22%23080c11%22%20d%3D%22M0%200h10L5%205z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E');
  /*

    Switch Colors
    ==========================================================================

  */
  --switchOffColor: var(--secondaryText);
  --switchOnColor: var(--primary);
  --switchHoverOnColor: var(--primaryDark);
  --switchHoverOffColor: var(--primaryDark);
  --switchDisabledOnColor: var(--primary);
  --switchDisabledOffColor: var(--secondaryText);
  /*

    Push Button Colors
    ==========================================================================

  */
  --pushbuttonBackgroundColor: none;
  --pushbuttonBorderColor: var(--primary);
  --pushbuttonTextColor: var(--primary);
  --pushbuttonSelectedBackgroundColor: var(--colorNormal);
  --pushbuttonSelectedBorderColor: var(--colorNormal);
  --pushbuttonSelectedTextColor: var(--defaultText);
  /*

    Clock Colors
    ==========================================================================

  */
  --clockTextColor: var(--primaryElementText);
  --clockBackgroundColor: #101923;
  --clockBorderColor: #1b2d3e;
  --clockLabelColor: var(--primaryElementText);
  /*

    Modal Colors
    ==========================================================================

  */
  --modalTitleColor: var(--primaryElementText);
  --modalTextColor: var(--defaultText);
  --modalBackgroundColor: var(--surfaceElements);
  --modalBorderColor: var(--primary);
  /*

    Log Colors
    ==========================================================================

  */
  --logTextColor: var(--defaultText);
  --logBackgroundColor: var(--surfaceElements);
  --logBorderColor: var(--secondaryText);
  --logHeaderTextColor: var(--defaultText);
  --logHeaderBackgroundColor: var(--backgroundColor);
  --logFilterBackgroundColor: var(--primaryLight);
  --logFilterTextColor: var(--primaryElementText);
  /*

    Tree Colors
    ==========================================================================

  */
  --treeTextColor: var(--defaultText);
  --treeBackgroundColor: var(--surfaceElements);
  --treeBorderColor: var(--primaryDark);
  --treeItemBorderColor: var(--primaryDark);
  --treeAccentColor: var(--primary);
  /* Hover */
  --treeHoverBackgroundColor: var(--primaryDark);
  --treeHoverTextColor: var(--primaryElementText);
  --treeHoverAccentColor: var(--primary);
  /* Selected */
  --treeSelectedBackgroundColor: var(--surfaceElements);
  --treeSelectedBorderColor: var(--primaryDark);
  --treeSelectedTextColor: var(--defaultText);
  --treeSelectedAccentColor: var(--primary);
  --treeChildrenBackgroundColor: var(--surfaceElements);
  --treeExpandedBorderColor: var(--backgroundColor);
  /*

    Tab Colors
    ==========================================================================

  */
  --tabTextColor: var(--primary);
  --tabBackgroundColor: transparent;
  --tabBorderColor: transparent;
  --tabHoverTextColor: var(--defaultText);
  --tabSelectedTextColor: var(--defaultText);
  --tabSelectedBorderColor: var(--primary);
  /*

    Notification Colors
    ==========================================================================

	*/
  --notificationTextColor: var(--defaultText);
  /*

    Classification Colors
    ==========================================================================

	*/
  --classificationTextColorLight: var(--primaryElementText);
  --classificationTextColorDark: var(--defaultText);
  /*

    Card Colors
    ==========================================================================

  */
  --cardBackgroundColor: var(--colorQuaternaryLighten2);
  --cardHeaderBackgroundColor: var(--colorQuaternaryLighten1);
  --cardHeaderTextColor: var(--colorWhite, #fff);
  /*

    Input/Text Field Colors
    ==========================================================================

  */
  --inputBackgroundColor: var(--inputBackground);
  --inputBorderColor: var(--primaryLight);
  --inputBorderColorAlt: var(--inputDark);
  --inputBorderColorDisabled: #292a2d;
  --inputTextColor: var(--defaultText);
  --inputFocusBorderColor: var(--primary);
  --inputFocusTextColor: var(--defaultText);
  --inputInvalidBorderColor: var(--colorCritical);
  --inputSearchIcon: url("data:image/svg+xml,%3Csvg width='40' height='40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M35.37 26.457a15.282 15.282 0 000-21.918c-6.176-6.052-16.187-6.052-22.361 0a15.274 15.274 0 00-1.541 20.166c-.367.147-.713.37-1.014.665L.926 34.709a3.056 3.056 0 000 4.383 3.208 3.208 0 004.472 0l9.528-9.339c.352-.345.604-.753.756-1.186 6.137 3.831 14.347 3.124 19.687-2.11zM24.193 4.043c6.454 0 11.686 5.129 11.686 11.455 0 6.326-5.232 11.455-11.686 11.455-6.455 0-11.687-5.129-11.687-11.455 0-6.326 5.232-11.455 11.687-11.455z' fill='%233a87cf' fill-rule='evenodd'/%3E%3C/svg%3E");
  --inputSearchCancel: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 128 128'%3E%3Cpath fill='%23004872' fill-rule='evenodd' d='M69.028 64l22.628 22.627-5.029 5.029L64 69.028 41.373 91.656l-5.029-5.029L58.972 64 36.344 41.373l5.029-5.029L64 58.972l22.627-22.628 5.029 5.029L69.028 64z'/%3E%3C/svg%3E");
  /*

    Icon Default Colors
    ==========================================================================

	*/
  --iconDefaultColor: var(--primary);
  /*

    Status Symbols
    ==========================================================================

  */
  --statusSymbols: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMAAAAAgCAMAAABzRoe3AAAA81BMVEUAAABaPhVcDwE7CQA6gJIAAAAATQAkTlsATgBcPxYrHAciGgkAAABcPxU0coUARgAxNTgnU2IoV2YzNjoAUgAtYXEzODovZngAUQAAUAAwNDg0dIk2d4sAOgAjKCgAGAAADQA+hJVZPBRcPxb62AAA4gD/rz1VSQBk2f//KgQiSldXDgEATQCOmqNXOxQwNDdoRhiGWx+tHAJiEAFZw+b1KAMAywDlnTb5qjv7rDzpnzdMpcNJn7xFlrGMmKGEj5g4eY82d4xxe4IyboIwaHskT15GTFEA2gAArAAAnAAAfgAAfABg0vddyu5cZGpg0fbOjTE6oyFAAAAAJHRSTlMA7cc4Kgj9+vbBJB4GypVn9vPu5t/Y18O8n4B/ckY6FRMdmtlkVn4PAAACq0lEQVRYw+2YaVPaUBSGqWbTWsK+b9pC6ek1lypGW1T2RUDb//9rmpsm3Nyc1DQfOhMc3k/cyUzmfeY5BxISh+xXzi7eJ/Y5ZyfGu30msPobQQSf/5KEmKZeymlarqQ3g28vJfMZVc3kk1LUYkdfA3OE+/duv3GCaACtskKcKOVWQP0i7FKMiHAcDHAc0L9zxwgQwKeAiAB1jZD1ylwuzdWaEK3u71BTAUaD/nbbH4wA1FpUBVdffLliAnD/DieIBKArZGO2nZgboujiLZIA437XSX8MkIyo4PpG7H9zzQTg/pwgEkBdIU9tT56IIjioATx0PXkAqEVWgAXg/pwgEkBLY/1FAq3lmX+V9RcJVLwHcqWQShUqcpgCLgD35wRRAMpk0/ZlQ8r8BkUY27XvZ/P57N7+OIaiv2QjS+1kG2EKuADcHxOEAzQVYvoBTKI0dwIA2Pw/T8HO9JntAYDk60/pYjIcThaUNkIUcAG4PyYIB9DJuo2yJjrf4FHXyhTSVUmqpmHKTiPfHstZ+vJ4aeXxhWblYAVYAO6PCcIBSmSFAVak5F7Pw4DND6TP2ek8DWyKBpAXClbogvVnBAtaCVaABeD+mCAcIMcmCM9Qzr2esSdoBtU/xyrM7BnKCAULdHLpZEILr2wBEnBhGL86OHc94+M/AmhkiQGWRHOvq7C1Gs/doZdgbh23oAr9UnToAgxp6pUvIrQB8gej9wP1/24ZOP2fAD+jAHAFXAAmwP1jNUK2Ai4AE+D+sVpiRwEXgAhQ/3h9jboKuABMIPaP1w8ZV8AFIAKhf8weJXYKBAGIgPeP2cMcVyAIwARu/7g9TnMFggBM4PSP2wuNR4EgABPc2v1j90rpUSAIwAQG6x+7l3qvAkEAJmD94/e3ileBIAATnJwmDjnkTeU3PTFjExFNx+YAAAAASUVORK5CYII=');
  /*


    Scrollbar Colors -- Light
    ==========================================================================

  */
  --scrollBarThumbBackgroundColor: var(--colorQuaternaryDarken1, #9ba0aa);
  --scrollBarThumbBackgroundHoverColor: var(--colorQuaternaryDarken2, #676a71);
  --scrollBarTrackBackgroundColor: transparent;
  --scrollBarTrackCornerBackgroundColor: var(--colorQuaternaryLighten4, #f2f4f7);
  /*

    Timeline Colors -- Dark
    ==========================================================================

  */
  --timelineHeaderBackgroundColor: var(--surfaceElements);
  --timelineHeaderTextColor: var(--defaultText);
  --timelineRulerBackgroundColor: var(--surfaceElements);
  --timelineRulerTextColor: var(--defaultText);
  --timelineTrackLabelBackgroundColor: var(--surfaceElements);
  --timelineTrackBackgroundColor: var(--surfaceElements);
  --timelineRegionBackgroundColor: var(--backgroundColor);
  --timelineRegionTextColor: var(--defaultText);
  --timelineRegionBorderColor: var(--primary);
  --timelineRegionSelectedBorderColor: var(--defaultText);
  --timelineRegionSelectedBackgroundColor: var(--primaryDark);
  --timelineRegionSelectedTextColor: var(--primaryElementText);
  --timelineRegionStatusColorNormal: var(--colorNormal);
  --timelineRegionStatusColorCritical: var(--colorCritical);
  --timelineRegionStatusColorSerious: var(--colorSerious);
  --timelineRegionStatusColorCaution: var(--colorCautiom);
  --timelineRegionStatusColorStandby: var(--colorStandby);
  --timelineRegionStatusColorOff: var(--colorOff);
  /*

    ACCORDION COLORS
    ==========================================================================

  */
  --accordionBorderColor: rgb(207, 214, 227);
  --accordionContentBackground: rgb(226, 230, 238);
  --accordionClosedLabelBackground: rgb(255, 255, 255);
  --accordionOpenLabelBackground: rgb(245, 247, 249);
  --accordionHoverLabelBackground: rgb(236, 239, 244);
  --accordionColor: rgb(0, 0, 0);
  /*

    TABLE COLORS
    ==========================================================================

  */
  --tableBorderColor: var(--backgroundColor);
  --tableHeaderBackgroundColor: var(--globalAppHeader);
  --tableHeaderBorderColor: var(--surfaceElements);
  --tableHeaderTextColor: var(--primaryElementText);
  --tableHeaderBoxShadow: 0 0.1rem 0.45rem rgba(0, 0, 0, 0.2);
  --tableHeaderAccentColor: var(--primary);
  --tableRowBackgroundColor: var(--surfaceElements);
  --tableRowBorderColor: var(--backgroundColor);
  --tableRowTextColor: var(--defaultText);
  --tableRowHoverBackgroundColor: var(--primaryDarkHover);
  --tableRowHoverTextColor: var(--defaultText);
  --tableRowSelectedBackgroundColor: var(--backgroundColor);
  --tableRowSelectedBorderColor: var(--primary);
  --tableControlsBackgroundColor: var(--primary);
  --tableFilterBorderColor: var(--primary);
  --tableFilterDisabledBorderColor: var(--backgroundColor);
  --tableFilterDisabledBackgroundColor: var(--surfaceElements);
}

li.light-theme {
  display: none;
}

.dark-theme {
  /*

    Global Colors
    =========================================================================
	*/
  /* Astro 5 Simplified Colors */
  --backgroundColor: var(--color-dark-border, #101923);
  --defaultText: var(--colorWhite, #ffffff);
  --secondaryText: var(--colorTertiaryLighten4, #d4d8dd);
  --globalAppHeader: var(--color-dark-background, #172635);
  --surfaceElements: #1b2d3e;
  /* TODO: this is an unofficial Astro color, but a required KM color */
  --primary: var(--colorSecondary, #4dacff);
  --primaryLight: var(--colorSecondaryLighten2, #92cbff);
  --primaryLightHover: #92cbff4d;
  /* TODO: this is a temporary fix, the use of opacity from Sketch is new and not accounted for in CSS */
  --primaryDark: var(--colorSecondaryDarken1, #3a87cf);
  --primaryDarkHover: #6b8ca5;
  --primaryElementText: var(--colorTertiaryDarken4, #080c11);
  --inputBackground: var(--colorWhite, #ffffff);
  --inputDark: var(--colorTertiaryDarken4, #080c11);
  /* styles */
  --fontColor: var(--defaultText);
  --fontLowContrastColor: var(--secondaryText);
  --fontInvertedColor: var(--primary);
  --fontInvertedBackgroundColor: var(--backgroundColor);
  --fontLinkColor: var(--primary);
  --fontLinkHoverColor: var(--primaryLight);
  /* Status Custom */
  --wcagCompliance: rgba(255, 255, 255, 0);
  --criticalBorder: var(--colorCritical, rgb(255, 56, 56));
  --colorCritical: var(--statusDarkCritical, rgb(255, 42, 4));
  --colorSerious: var(--statusDarkSerious, rgb(255, 175, 61));
  --colorCaution: var(--statusDarkCaution, rgb(250, 216, 0));
  --colorNormal: var(--statusDarkNormal, rgb(0, 226, 0));
  --colorStandby: var(--statusDarkStandby, rgb(45, 204, 255));
  --colorOff: var(--statusDarkOff, rgb(142, 154, 163));
  /*

    Button Colors
    ==========================================================================
    For standard and outline buttons

  */
  /* Button */
  --buttonTextColor: var(--primaryElementText);
  --buttonBackgroundColor: var(--primary);
  --buttonBorderColor: transparent;
  /* Button Hover State */
  --buttonHoverTextColor: var(--primaryElementText);
  --buttonHoverBackgroundColor: var(--primaryLight);
  --buttonHoverBorderColor: transparent;
  /* Button Active State */
  --buttonActiveControlTextColor: var(--defaultText);
  --buttonActiveBackgroundColor: var(--primary);
  --buttonActiveBorderColor: var(--primary);
  /* Outline Button Variant */
  --buttonOutlineTextColor: var(--primary);
  --buttonOutlineBackgroundColor: transparent;
  --buttonOutlineBorderColor: var(--colorPrimary, rgb(0, 90, 143));
  /* Outline Button Variant Hover State */
  --buttonOutlineHoverTextColor: var(--primaryLight);
  --buttonOutlineHoverBackgroundColor: transparent;
  --buttonOutlineHoverBorderColor: var(--primaryLight);
  /*

    Control Colors
    ==========================================================================
    For checkboxes, radio buttons etc …

  */
  --controlTextColor: var(--primaryElementText);
  --controlLabelColor: var(--defaultText);
  --controlBackgroundColor: var(--primary);
  --controlBorderColor: var(--primary);
  --controlAccentColor: var(--primary);
  --controlSelectedTextColor: var(--defaultText);
  --controlSelectedBackgroundColor: var(--primaryDark);
  --controlSelectedBorderColor: var(--primary);
  --controlHoverBorderColor: var(--primaryLight);
  --controlSelectedOutlineBorderColor: var(--primary);
  /*

    Progress Colors
    ==========================================================================

  */
  --progressDeterminateBarBackgroundColor: var(--colorSecondary, rgb(77, 172, 255));
  --progressDeterminateTrackBackgroundColor: rgba(0, 0, 0, 0.3);
  --progressDeterminateTrackBorderColor: var(--primaryDark);
  --progressIndeterminate: url("data:image/svg+xml,%3Csvg width='64' height='64' xmlns='http://www.w3.org/2000/svg'%3E %3Cdefs%3E %3ClinearGradient x1='65.479%25' y1='-8.436%25' x2='50%25' y2='100%25' id='a'%3E %3Cstop stop-color='%234dacff' offset='0%25'/%3E %3Cstop stop-color='%234dacff' stop-opacity='0' offset='100%25'/%3E %3C/linearGradient%3E %3C/defs%3E %3Cg fill='none' fill-rule='evenodd'%3E %3Cpath d='M32 64C14.327 64 0 49.673 0 32 0 14.327 14.327 0 32 0c17.673 0 32 14.327 32 32 0 17.673-14.327 32-32 32zm0-6c14.36 0 26-11.64 26-26S46.36 6 32 6 6 17.64 6 32s11.64 26 26 26z' fill='rgba(0,0,0,.3)'/%3E %3Cpath d='M51.908 8.236l-2.358 3.245A26.894 26.894 0 0 0 32 5C17.088 5 5 17.088 5 32s12.088 27 27 27c1.129 0 2.242-.07 3.334-.204l4.435 3.222C37.286 62.66 34.683 63 32 63 14.88 63 1 49.12 1 32 1 14.88 14.88 1 32 1c7.579 0 14.522 2.72 19.908 7.236z' fill='url(%23a)'/%3E %3Cpath d='M47.564 12c1.92 0 3.557-.64 4.075-2.367.112-.375.361-.67.361-1.08C52 6.248 50.572 4 48.234 4S44 5.867 44 8.17c0 2.304 1.225 3.83 3.564 3.83z' fill='%234dacff'/%3E %3C/g%3E %3C/svg%3E ");
  /*

    Slider Colors
    ==========================================================================

  */
  --sliderThumbBackgroundColor: #ff9800; /* 橙色点 */
  --sliderThumbBorderColor: var(--inputBackground);
  --sliderHoverThumbBackgroundColor: #ff9800; /* 橙色点 */
  --sliderHoverThumbBorderColor: var(--inputBackground);
  --sliderTrackBorderColor: transparent;
  --sliderTrackBackgroundColor: #2196f3; /* 蓝色横线 */
  --sliderSelectedThumbBorderColor: var(--primaryLight);
  --sliderSelectedTrackBackgroundColor: #2196f3; /* 蓝色横线 */
  --sliderThumbBorderSize: 1px;
  --sliderTrackBorderSize: 1px;
  /*

    Segmented Button Colors
    ==========================================================================

  */
  --segmentedButtonBorderColor: var(--primary);
  --segmentedButtonBackgroundColor: none;
  --segmentedButtonTextColor: var(--primary);
  /* Segmented Hover */
  --segmentedButtonHoverBackgroundColor: var(--primaryLight);
  --segmentedButtonHoverTextColor: var(--primaryElementText);
  --segmentedButtonHoverBorderColor: var(--primaryLight);
  /* Segmented Select */
  --segmentedButtonSelectedBackgroundColor: var(--primary);
  --segmentedButtonSelectedTextColor: var(--primaryElementText);
  /*

    Drop Down/Select Colors
    ==========================================================================

  */
  --selectHoverOptBackgroundColor: var(--primaryLight);
  /*

    Switch Colors
    ==========================================================================

  */
  --switchOffColor: var(--secondaryText);
  --switchOnColor: var(--primary);
  --switchHoverOnColor: var(--primaryLight);
  --switchHoverOffColor: var(--primaryLight);
  --switchDisabledOnColor: var(--primary);
  --switchDisabledOffColor: var(--secondaryText);
  /*

    Popup Menu Colors
    ==========================================================================

  */
  --popupMenuBackgroundColor: var(--inputBackground);
  --popupMenuBorderColor: var(--primary);
  --popupMenuTextColor: var(--primaryElementText);
  --popupCaretBackgroundColor: var(--primary);
  --popupMenuItemBackgroundColor: var(--primary);
  --popupMenuItemHoverBackgroundColor: var(--primaryLight);
  --popupMenuItemHoverTextColor: var(--primaryElementText);
  --popupMenuItemSeperatorBorderColor: var(--primaryElementText);
  /*

    Pushbutton Colors
    ==========================================================================

  */
  --pushbuttonBackgroundColor: none;
  --pushbuttonBorderColor: var(--primary);
  --pushbuttonTextColor: var(--primary);
  --pushbuttonSelectedBackgroundColor: var(--colorNormal);
  --pushbuttonSelectedBorderColor: var(--colorNormal);
  --pushbuttonSelectedTextColor: var(--primaryElementText);
  /*

    Clock Colors
    ==========================================================================

  */
  --clockTextColor: var(--defaultText);
  --clockBackgroundColor: var(--backgroundColor);
  --clockBorderColor: var(--surfaceElements);
  --clockLabelColor: var(--defaultText);
  /*

    Modal Colors
    ==========================================================================

  */
  --modalTitleColor: var(--primaryElementText);
  --modalTextColor: var(--defaultText);
  --modalBackgroundColor: var(--surfaceElements);
  --modalBorderColor: var(--primary);
  /*

  /*

    Log Colors
    ==========================================================================

  */
  --logTextColor: var(--defaultText);
  --logBackgroundColor: var(--surfaceElements);
  --logBorderColor: var(--backgroundColor);
  --logHeaderTextColor: var(--defaultText);
  --logHeaderBackgroundColor: var(--globalAppHeader);
  --logFilterBackgroundColor: var(--primaryDark);
  --logFilterTextColor: var(--primaryElementText);
  /*

    Tree Colors
    ==========================================================================

  */
  --treeTextColor: var(--defaultText);
  --treeBackgroundColor: var(--surfaceElements);
  --treeBorderColor: var(--primaryDark);
  --treeItemBorderColor: var(--primaryDark);
  --treeAccentColor: var(--primary);
  /* Hover*/
  --treeHoverBackgroundColor: var(--primaryLight);
  --treeHoverTextColor: var(--primaryElementText);
  --treeHoverAccentColor: var(--primary);
  /* Selected */
  --treeSelectedBackgroundColor: var(--surfaceElements);
  --treeSelectedBorderColor: var(--primaryDark);
  --treeSelectedTextColor: var(--defaultText);
  --treeSelectedAccentColor: var(--primary);
  --treeChildrenBackgroundColor: var(--surfaceElements);
  --treeExpandedBorderColor: var(--backgroundColor);
  /*

    Tab Colors
    ==========================================================================

  */
  --tabTextColor: var(--primary);
  --tabBackgroundColor: transparent;
  --tabBorderColor: transparent;
  --tabHoverTextColor: var(--defaultText);
  --tabSelectedTextColor: var(--defaultText);
  --tabSelectedBorderColor: var(--primary);
  /*

    Notification Colors
    ==========================================================================

	*/
  --notificationTextColor: var(--primaryElementText);
  /*

    Classification Colors
    ==========================================================================

	*/
  --classificationTextColorLight: var(--defaultText);
  --classificationTextColorDark: var(--primaryElementText);
  /*

    Card Colors
    ==========================================================================

  */
  --cardBackgroundColor: var(--colorTertiaryDarken1);
  --cardHeaderBackgroundColor: var(--color-dark-border);
  --cardHeaderTextColor: var(--colorWhite, #fff);
  /*

    Input/Text Field Colors
    ==========================================================================

  */
  --inputBackgroundColor: var(--inputBackground);
  --inputBorderColor: var(--surfaceElements);
  --inputBorderColorAlt: var(--inputDark);
  --inputBorderColorDisabled: #292a2d;
  --inputTextColor: var(--primaryElementText);
  /* Input Focus */
  --inputFocusBorderColor: var(--primary);
  --inputFocusTextColor: var(--primaryElementText);
  --inputInvalidBorderColor: var(--colorCritical);
  --inputSearchIcon: url("data:image/svg+xml,%3Csvg width='40' height='40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M35.37 26.457a15.282 15.282 0 000-21.918c-6.176-6.052-16.187-6.052-22.361 0a15.274 15.274 0 00-1.541 20.166c-.367.147-.713.37-1.014.665L.926 34.709a3.056 3.056 0 000 4.383 3.208 3.208 0 004.472 0l9.528-9.339c.352-.345.604-.753.756-1.186 6.137 3.831 14.347 3.124 19.687-2.11zM24.193 4.043c6.454 0 11.686 5.129 11.686 11.455 0 6.326-5.232 11.455-11.686 11.455-6.455 0-11.687-5.129-11.687-11.455 0-6.326 5.232-11.455 11.687-11.455z' fill='%23005a92' fill-rule='evenodd'/%3E%3C/svg%3E");
  --inputSearchCancel: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 128 128'%3E%3Cpath fill='%233a87cf' fill-rule='evenodd' d='M69.028 64l22.628 22.627-5.029 5.029L64 69.028 41.373 91.656l-5.029-5.029L58.972 64 36.344 41.373l5.029-5.029L64 58.972l22.627-22.628 5.029 5.029L69.028 64z'/%3E%3C/svg%3E");
  /*

    Icon Default Colors
    ==========================================================================

	*/
  --iconDefaultColor: var(--primary);
  /*

    Status Symbols
    ==========================================================================

  */
  --statusSymbols: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMAAAAAgCAMAAABzRoe3AAAAgVBMVEUAAAD/tQv/swL/OTn/PDzftl9W8QBW8AAtzP8v0P9Z9gD/tAP/uQb/tAItzf8tzf+eqK2ep60uzP9X8QCfqK4uzf8uzf9X8QAuzP9X8ACeqK4vzP9Y8QAuzv9Y8gCeqq4wz/8xzv9A1f9Y8QD/tAP86DpW8AD/swL/ODgtzP+ep63tgXPUAAAAJXRSTlMAGNfAQAv98/IdG8Io52zz8+DZ2dC9trabm4F+a05OQjAaDG6dAJcYcwAAAfdJREFUWMPtmNlygkAQRScyrBp3QTZFNBj8/w9MCGI3dBOHMg9jyvOmVFnn1HUSULx4LibjN/HMTN7Po2cu+PY/cwUfPYg2XhKsbHsVJJ5gcaf+cjZb+lN3qJjxyWIw/lAwNMALreKKFTIJh215Y3sYFiD5AEn9oWBowM4uEPau65A6JcJJ/2ACg/pDwdCAo1W0sI6ixbTsMH18Akn9oWBgwK7xh4LWBmlJSB+dwCD+pEA5wLMLgo3OwcGpvzhxludZfH1Bz4EZrefzdWSqTSCJPylQDggLhhA+oD6/G7d+5W7qk9yV3C8uPyz2KhMYxJ8UKAd4Fhdg3SZwa3/4wLrA7fhfbuwVJpDEnxQoByQFS9I6wQ7ydR16js0FBCzMuxMY1J8WqAYEfEDQXPcr3RjbxNU7vsBEF0R0dwJJ/LkCxYAVH7Bqri8r3QzbZNU7S4FZ44B1/ymgA4zPfZwUA2w+wG6uzyrdHMvk1TszgZnjgLmgyL4T8Dbq8R+bWgUIgw4ABby/Vl8hIckAUMD7a3WIYQIYgC0Af73+jOIJpKAF1F+vf2QwAQxAC7C/ZrcSeAIpegvAX7ObOZgABiAF2F+322mYAAagBeCv2wMNTEAGIAWVv3aPlDABDMAW1P7aPdQjDBiALaj89ftZBSGl+LXgZIoXL/4VXyptNwzuHR/QAAAAAElFTkSuQmCC');
  /*

    Scrollbar Colors -- Dark
    ==========================================================================

  */
  --scrollBarThumbBackgroundColor: var(--colorSecondaryDarken2, rgb(46, 103, 153));
  --scrollBarThumbBackgroundHoverColor: var(--colorSecondaryDarken1, rgb(58, 129, 191));
  --scrollBarTrackBackgroundColor: var(--colorTertiaryDarken1, rgb(32, 50, 70));
  --scrollBarTrackCornerBackgroundColor: var(--colorTertiaryDarken1, rgb(32, 50, 70));
  /*

    Timeline Colors -- Dark
    ==========================================================================

  */
  --timelineHeaderBackgroundColor: var(--surfaceElements);
  --timelineHeaderTextColor: var(--defaultText);
  --timelineRulerBackgroundColor: var(--surfaceElements);
  --timelineRulerTextColor: var(--defaultText);
  --timelineTrackLabelBackgroundColor: var(--surfaceElements);
  --timelineTrackBackgroundColor: var(--surfaceElements);
  --timelineRegionBackgroundColor: var(--backgroundColor);
  --timelineRegionTextColor: var(--defaultText);
  --timelineRegionBorderColor: var(--primary);
  --timelineRegionSelectedBorderColor: var(--inputBackground);
  --timelineRegionSelectedBackgroundColor: var(--primaryDark);
  --timelineRegionSelectedTextColor: var(--defaultText);
  --timelineRegionStatusColorNormal: var(--colorNormal);
  --timelineRegionStatusColorCritical: var(--colorCritical);
  --timelineRegionStatusColorSerious: var(--colorSerious);
  --timelineRegionStatusColorCaution: var(--colorCautiom);
  --timelineRegionStatusColorStandby: var(--colorStandby);
  --timelineRegionStatusColorOff: var(--colorOff);
  /*

    ACCORDION COLORS
    ==========================================================================

  */
  --accordionBorderColor: rgb(40, 63, 88);
  --accordionContentBackground: rgb(20, 32, 44);
  --accordionClosedLabelBackground: rgb(32, 50, 70);
  --accordionOpenLabelBackground: rgb(40, 63, 88);
  --accordionHoverLabelBackground: rgb(46, 103, 153);
  --accordionColor: rgb(255, 255, 255);
  /*

    TABLE COLORS
    ==========================================================================

  */
  --tableBorderColor: var(--backgroundColor);
  --tableHeaderBackgroundColor: var(--globalAppHeader);
  --tableHeaderBorderColor: var(--surfaceElements);
  --tableHeaderTextColor: var(--defaultText);
  --tableHeaderBoxShadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.45);
  --tableHeaderAccentColor: var(--primary);
  --tableRowBackgroundColor: var(--surfaceElements);
  --tableRowTextColor: var(--defaultText);
  --tableRowBorderColor: var(--backgroundColor);
  --tableRowHoverBackgroundColor: var(--primaryLightHover);
  --tableRowHoverTextColor: var(--defaultText);
  --tableRowSelectedBackgroundColor: var(--backgroundColor);
  --tableRowSelectedBorderColor: var(--primary);
  --tableControlsBackgroundColor: var(--primary);
  --tableFilterBorderColor: var(--primary);
  --tableFilterDisabledBorderColor: var(--backgroundColor);
  --tableFilterDisabledBackgroundColor: var(--surfaceElements);
}

li.dark-theme {
  display: none;
}

h1,
h2,
h3 {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
  font-weight: 400;
  color: var(--defaultText);
  margin: 0 0 0.5rem 0;
}

h1,
.h1 {
  font-size: 2.125rem;
}

h2,
.h2 {
  font-size: 1.5rem;
}

h3,
.h3 {
  font-size: 1.25rem;
  font-weight: 500;
}

p {
  margin: 0 0 1rem 0;
  color: var(--defaultText);
}

a {
  color: var(--fontLinkColor);
  text-decoration: none;
}

a:hover {
  color: var(--fontLinkHoverColor);
}

dt {
  font-weight: 600;
  margin: 0;
}

dd {
  margin: 0 0 0.5rem 0;
}

.light {
  font-weight: 300;
}

.regular {
  font-weight: 500;
}

.semi-bold {
  font-weight: 600;
}

.bold {
  font-weight: 700;
}

.italic {
  font-style: italic;
}

.condensed {
  font-stretch: condensed;
}

.monospace {
  font-family: 'Roboto Mono', sans-serif;
}

.invert,
.inverted {
  background-color: var(--fontInvertedBackgroundColor);
  color: var(--fontInvertedColor);
  padding: 0 0.25rem;
}

.low-contrast {
  opacity: 0.2;
}

/* Size Options */
.xl {
  font-size: 1.125rem;
  font-size: var(--fontSizeXL);
}

.lg {
  font-size: 1rem;
  font-size: var(--fontSize);
}

.md {
  font-size: 0.875rem;
  font-size: var(--fontSizeMD);
}

.sm {
  font-size: 0.75rem;
  font-size: var(--fontSizeSM);
}

.xs {
  font-size: 0.65rem;
  font-size: var(--fontSizeXS);
}

/*
**	This is as close as we get to a CSS reset in Astro, moving everything to
**	the old IE border-box model of including padding in the overall size.
*/
html {
  box-sizing: border-box;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/*
**
**
**
*/
body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
  font-size: 1rem;
  color: white;
  color: var(--fontColor, white);
  background-color: var(--backgroundColor);
  scrollbar-color: var(--scrollBarThumbBackgroundColor) var(--scrollBarTrackCornerBackgroundColor);
}

label {
  user-select: none;
}

/*
** CSS-styled Scrollbars for IE & Webkit browsers
** Dark theme by default.
**
*/
::-webkit-scrollbar {
  width: 18px;
  height: 18px;
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #2e6799;
  background-color: var(--scrollBarThumbBackgroundColor, #2e6799);
  border-radius: 10px;
  border: 3px solid transparent;
  background-clip: padding-box;
}

/* visually "centers" because the dark edge of the shadow gives the illusion this is offset */
::-webkit-scrollbar-thumb:vertical {
  border-left-width: 4px;
}

::-webkit-scrollbar-thumb:horizontal {
  border-top-width: 4px;
}

::-webkit-scrollbar-thumb:active,
::-webkit-scrollbar-thumb:hover {
  background-color: #3a81bf;
  background-color: var(--scrollBarThumbBackgroundHoverColor, #3a81bf);
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-corner {
  background-color: #203246;
  background-color: var(--scrollBarTrackCornerBackgroundColor, #203246);
}

::-webkit-scrollbar-track:vertical {
  box-shadow: inset 2px 0 4px rgba(0, 0, 0, 0.15);
}

::-webkit-scrollbar-track:horizontal {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15);
}

/*
**
** 	ASTRO BUTTON
** 	==========================================================================
**  3.0 Notes
**  - Removed Master Off Button Style
**  - Replaced various properties with css custom properties to support
**  - Removed .satcom class definition
**  - Removed narrow/short definitions
**	1.4 Notes
**	- Added rux_ and BEM compatible classes to all satcom_ NOTE: satcom_ will be removed in a future version
**	- Disabled user selection of text on all buttons
**	- Removed redundant background hover from 'disabled' state by using :not() on the :hover state
** 	- Removed redundant background hover from 'master off' by using :not() on the :hover state // deprecate after 1.4
**  - Fixed Firefox alignment issue where text was misaligned vertically
**  - Renamed half-height to short and half-width to narrow (Note: rux_ only, satcom_ retains old syntax)
**  - Removed user-select and placed it in astro.css to apply to all input types
**  - Embedded master off icon and removed the additional states required to handle icons and gradient backgrounds
*/
.rux-button-group {
  display: flex;
  margin-left: auto;
  align-self: flex-end;
}

.rux-button-group .rux-button:not(:last-child) {
  margin-right: 0.625rem;
}

/* Global Button Styles */
.rux-button {
  display: flex;
  position: relative;
  margin: 0;
  padding: 0 1rem;
  height: 2.125rem;
  min-width: 2.25rem;
  /* max-width: 10.125rem; */
  border-radius: 3px;
  border-radius: var(--buttonBorderRadius);
  color: var(--buttonTextColor);
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
  font-size: 1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  justify-content: center;
  align-items: center;
  user-select: none;
}

/*

  Disabled States

*/
/* disabled state */
.rux-button:disabled {
  opacity: 0.4;
  opacity: var(--disabledOpacity);
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

.rux-button:focus {
  outline: none;
}

.rux-button:not(.rux-button--outline) {
  border: 1px solid var(--buttonBorderColor);
  background-color: var(--buttonBackgroundColor);
}

/* Outline Button Specific Styles */
.rux-button--outline {
  color: var(--buttonOutlineTextColor);
  background-color: var(--buttonOutlineBackgroundColor);
  border: 1px solid var(--buttonOutlineBorderColor);
}

/*

  Press/Active States

*/
.rux-button:active:not([hover]):not([disabled]) {
  border-color: var(--buttonActiveBorderColor) !important;
  background-color: var(--buttonActiveBackgroundColor) !important;
}

.rux-button--outline:active:not([hover]):not([disabled]) {
  border-color: var(--buttonOutlineBorderColor) !important;
  background-color: var(--buttonOutlineBackgroundColor) !important;
}

/*

  Hover States

*/
.rux-button:hover:not([active]):not([disabled]):not(.rux-button--outline) {
  border-color: var(--buttonHoverBorderColor);
  background-color: var(--buttonHoverBackgroundColor);
}

.rux-button--outline:hover:not([disabled]) {
  color: var(--buttonOutlineTextColor);
  background-color: var(--buttonOutlineHoverBackgroundColor);
  border-color: var(--buttonOutlineHoverBorderColor);
}

/*

  Icons

*/
.rux-button--small {
  font-size: 0.875rem;
  font-size: var(--smallLabelFontSize);
  height: 1.625rem;
  padding: 0 1rem;
  line-height: 1;
}

.rux-button--large {
  font-size: 1.125rem;
  font-size: var(--largeLabelFontSize);
  height: 2.875rem;
  min-width: 3rem;
  padding: 0 1rem;
}

.rux-button__icon {
  height: 1.5rem;
  width: 1.5rem;
  margin-right: 0.625rem;
  margin-left: -0.625rem;
}

.rux-button--icon-only .rux-button__icon {
  margin-left: -0.625rem;
  margin-right: -0.625rem;
}

.rux-button--large.rux-button--icon-only .rux-button__icon {
  margin-left: -1rem;
  margin-right: -1rem;
}

.rux-button--small .rux-button__icon {
  height: 0.875rem;
  width: 0.875rem;
}

.rux-button--large .rux-button__icon {
  height: 1.75rem;
  width: 1.75rem;
  margin-left: -0.8rem;
  /* margin: -0.65rem 0.25rem -0.3rem calc((1.5rem - 0.625rem) * -1); */
}

.rux-button__icon .rux-icon {
  height: auto;
  width: 100%;
  fill: var(--buttonTextColor);
  color: var(--buttonTextColor);
}

.rux-button.rux-button--critical,
.rux-button.rux-button--critical:active:not([hover]):not([disabled]):not(.rux-button--outline) {
  background-color: rgb(204, 45, 45) !important;
  background-color: var(--colorCriticalDarken1) !important;
  border-color: rgb(204, 45, 45) !important;
  border-color: var(--colorCriticalDarken1) !important;
}

.rux-button.rux-button--critical:hover:not([active]):not([disabled]) {
  background-color: var(--colorCritical);
  border-color: var(--colorCritical);
}

.rux-card {
  background-color: #1e2f42;
  background-color: var(--cardBackgroundColor, #1e2f42);
  padding: 1px;
  overflow: hidden;
}

.rux-card__header {
  color: white;
  color: var(--cardHeaderTextColor, white);
  background-color: #14202c;
  background-color: var(--cardHeaderBackgroundColor, #14202c);
  margin: 0;
  padding: 0.325rem 0.625rem;
  display: flex;
  align-items: center;
}

.rux-card__header h1 {
  padding: 0;
  margin: 0;
  font-size: 1.75rem;
}

.rux-card__content {
  padding: 0.625rem;
}

/*
**
** 	ASTRO CHECKBOX BUTTON
** 	==========================================================================
**  3.0 Notes
**  - Added Custom CSS Properties to support light/dark theming
**  - Added fallback properties for IE11
**  2.0 Notes
**  - Removed hand cursor on checkbox
**  - Updated styles to Astro 2.0
**	1.4 Notes
**	- Added rux_ and BEM compatible classes to all satcom_ NOTE: satcom_ will be removed in a future version
**	- Removed prefixed linear gradients
**  - Added line-height to label to fix minor alignment issue
**  - Moved user-select to parent element
**  - Removed user-select and placed it in astro.css to apply to all input types
*/
.rux-checkbox {
  display: flex;
  position: relative;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.rux-checkbox input[type='checkbox'] {
  -webkit-appearance: none;
  display: none;
}

.rux-checkbox input[type='checkbox']+label {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: var(--controlLabelColor);
  letter-spacing: 0.5px;
  cursor: pointer;
}

/* Box */
.rux-checkbox input[type='checkbox']+label::before {
  display: flex;
  flex-shrink: 0;
  flex-grow: 0;
  content: '';
  align-self: start;
  height: 1.125rem;
  height: var(--controlOptionSize);
  width: 1.125rem;
  width: var(--controlOptionSize);
  margin: 0 0.625rem 0 0;
  border: 1px solid var(--controlBorderColor);
  border-radius: 2px;
}

.rux-checkbox input[type='checkbox']:checked+label::before {
  background-color: var(--primary);
  border-color: var(--controlSelectedOutlineBorderColor);
}

.rux-checkbox input[type='checkbox']:not(:disabled):hover+label:before,
.rux-checkbox input[type='checkbox']:not(:disabled):checked:hover+label:before {
  border-color: var(--controlHoverBorderColor);
}

.rux-checkbox input[type='checkbox']:not(:disabled):checked:hover+label:before {
  background-color: var(--controlHoverBorderColor);
}

/* Checkmark */
.rux-checkbox input[type='checkbox']:checked+label::after {
  position: absolute;
  top: 5px;
  display: flex;
  content: '';
  height: 6px;
  width: 12px;
  left: 3px;
  border-right: 2px solid var(--controlTextColor);
  border-top: 2px solid var(--controlTextColor);
  transform: rotate(125deg);
}

.rux-checkbox--indeterminate input[type='checkbox']:checked+label::after {
  width: 10px;
  height: 5px;
  transform: rotate(0deg);
  border-right: 0px;
  border-top: 0px;
  border-bottom: 2px solid var(--controlTextColor);
  left: 4px;
}

.rux-checkbox input[type='checkbox']:disabled+label {
  cursor: not-allowed;
  cursor: var(--disabledCursor);
  opacity: 0.4;
  opacity: var(--disabledOpacity);
}

.rux-form-element {
  display: flex;
  align-items: baseline;
}

.rux-form-element label {
  color: white;
}

.rux-form__invalid {
  border: 1px solid var(--colorCritical);
  border-left-width: 20px;
  color: var(--colorCritical);
  padding: 20px 10px;
  background-color: var(--inputBackground);
}

.rux-form__invalid p {
  line-height: 100%;
  text-align: left;
  vertical-align: middle;
  color: var(--colorCritical);
  font-size: 0.875rem;
  font-size: var(--fontSizeMD);
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
  font-weight: bold;
  margin: 0;
  padding-left: 1.625rem;
  width: fit-content;
  background-image: url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20128%20128%22%3E%0A%20%20%3Cpath%20fill%3D%22%23FF3030%22%20fill-rule%3D%22evenodd%22%20d%3D%22M64.031%205c8.461%200%2068.88%20107.243%2063.648%20114.184-5.232%206.942-120.805%205.477-127.212%200C-5.941%20113.708%2055.57%205%2064.03%205zm3.45%2075.894l1.822-34.893H56.946l1.82%2034.893h8.715zM56.803%2093.108c0%201.929.547%203.423%201.643%204.483%201.095%201.06%202.642%201.589%204.642%201.589%201.953%200%203.477-.542%204.572-1.625%201.095-1.084%201.643-2.566%201.643-4.447%200-1.952-.542-3.452-1.625-4.5-1.084-1.047-2.613-1.571-4.59-1.571-2.047%200-3.607.512-4.678%201.536-1.072%201.023-1.607%202.535-1.607%204.535z%22%2F%3E%0A%3C%2Fsvg%3E');
  background-repeat: no-repeat;
  background-size: 1rem;
  background-position: center left 0rem;
}

.rux-help-text {
  color: var(--secondaryText);
  font-size: 0.875rem;
  font-size: var(--fontSizeMD);
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
  font-weight: normal;
  letter-spacing: 0.5px;
}

.rux-form-field .rux-help-text {
  -webkit-order: 3;
  order: 3;
  margin-top: 0.625rem;
}

.rux-error-text {
  color: var(--colorCritical);
  font-size: 0.875rem;
  font-size: var(--fontSizeMD);
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
  font-weight: bold;
}

.rux-form-field .rux-error-text,
.rux-select+.rux-error-text {
  padding-left: 1.625rem;
  background-image: url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20128%20128%22%3E%0A%20%20%3Cpath%20fill%3D%22%23FF3030%22%20fill-rule%3D%22evenodd%22%20d%3D%22M64.031%205c8.461%200%2068.88%20107.243%2063.648%20114.184-5.232%206.942-120.805%205.477-127.212%200C-5.941%20113.708%2055.57%205%2064.03%205zm3.45%2075.894l1.822-34.893H56.946l1.82%2034.893h8.715zM56.803%2093.108c0%201.929.547%203.423%201.643%204.483%201.095%201.06%202.642%201.589%204.642%201.589%201.953%200%203.477-.542%204.572-1.625%201.095-1.084%201.643-2.566%201.643-4.447%200-1.952-.542-3.452-1.625-4.5-1.084-1.047-2.613-1.571-4.59-1.571-2.047%200-3.607.512-4.678%201.536-1.072%201.023-1.607%202.535-1.607%204.535z%22%2F%3E%0A%3C%2Fsvg%3E');
  background-repeat: no-repeat;
  background-size: 1rem;
  background-position: center left 0rem;
  text-align: left;
  width: fit-content;
  -webkit-order: 3;
  order: 3;
  margin-top: 0.625rem;
}

/*
**
** 	ASTRO INPUT TEXT FIELDS
** 	==========================================================================
**	1.4 Notes
**	- Added rux_ and BEM compatible classes to all satcom_ NOTE: satcom_ will be removed in a future version
**	- Removed prefixed linear gradients
**  - Updated text entry field to WCAG
*/
/*
**
** 	INPUT TEXT
** 	==========================================================================
*/
/* 	REQUIRED CLASSES */
.rux-form-field {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
  font-size: 1rem;
  font-size: var(--fontSize);
  color: var(--fontColor);
}

.rux-form-field input:required+label::after {
  content: '*';
  margin-left: 0.25rem;
  color: var(--inputTextColor);
}

.rux-form-field__label {
  display: flex;
  order: 1;
  margin: 0 0 0.15rem 0;
  width: 100%;
  align-content: center;
  align-items: center;
  color: var(--defaultText);
  user-select: none;
}

.rux-form-field input {
  box-sizing: border-box;
  order: 2;
  height: 2.125rem;
  width: 100%;
  padding: 0 0.625rem;
  border: 1px solid var(--inputBorderColor);
  border-radius: 4px;
  font-size: 1rem;
  font-size: var(--fontSize, 1rem);
  color: var(--inputTextColor);
}

.rux-form-field textarea {
  box-sizing: border-box;
  -webkit-order: 2;
  order: 2;
  min-height: 4.25rem;
  width: 100%;
  padding: 0.5rem;
  border: 1px solid transparent;
  border: 1px solid var(--inputBorderColor);
  border-radius: 3px;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
  font-size: 1rem;
  font-size: var(--fontSize, 1rem);
  color: black;
  color: var(--inputTextColor);
}

/* input */
.rux-form-field input::-webkit-input-placeholder,
.rux-form-field input::-moz-placeholder,
.rux-form-field input:-ms-input-placeholder,
.rux-form-field textarea::-webkit-input-placeholder,
.rux-form-field textarea::-moz-placeholder,
.rux-form-field textarea:-ms-input-placeholder {
  font-size: 1rem;
  font-weight: normal;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
}

.rux-form-field input[type='date']::-webkit-inner-spin-button,
.rux-form-field input[type='date']::-webkit-outer-spin-button,
.rux-form-field input[type='date']::-webkit-calendar-picker-indicator {
  -webkit-appearance: none;
  display: none;
}

/* VALIDATION */
.rux-form-field input:invalid {
  border: 1px solid var(--inputInvalidBorderColor);
}

/* FOCUS RULES */
.rux-form-field input:focus,
.rux-form-field input:invalid:focus {
  /* outline: none; */
}

.rux-form-field input:not([type='search']):focus,
.rux-form-field input:not([type='search']):invalid:focus {
  border: 1px solid var(--inputFocusBorderColor) !important;
}

.rux-form-field input::selection {
  background-color: rgb(184, 222, 255);
  background-color: var(--colorSecondaryLighten3);
}

/*
.rux-form-field input:invalid + label::before {
  content: "";
  display: block;
  height: 1rem;
  width: 1.25rem;
  margin-right: 0.25rem;

  background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20128%20128%22%3E%0A%20%20%3Cpath%20fill%3D%22%23FF3030%22%20fill-rule%3D%22evenodd%22%20d%3D%22M64.031%205c8.461%200%2068.88%20107.243%2063.648%20114.184-5.232%206.942-120.805%205.477-127.212%200C-5.941%20113.708%2055.57%205%2064.03%205zm3.45%2075.894l1.822-34.893H56.946l1.82%2034.893h8.715zM56.803%2093.108c0%201.929.547%203.423%201.643%204.483%201.095%201.06%202.642%201.589%204.642%201.589%201.953%200%203.477-.542%204.572-1.625%201.095-1.084%201.643-2.566%201.643-4.447%200-1.952-.542-3.452-1.625-4.5-1.084-1.047-2.613-1.571-4.59-1.571-2.047%200-3.607.512-4.678%201.536-1.072%201.023-1.607%202.535-1.607%204.535z%22%2F%3E%0A%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-size: 1rem;
  background-position: center center;
} */
.rux-form-field__validation-message {
  display: none;
  position: absolute;
  max-width: 16rem;
  background-color: var(--colorCritical);
  width: 100%;
  padding: 0.25rem;
  right: 0;
  font-size: 0.875rem;
  font-size: var(--fontSizeMD);
}

.rux-form-field input:invalid .rux-form-field__validation-message {
  display: block;
}

/* .rux-form-field
  input:not([type="search"]):not([type="checkbox"]):not([type="radio"]):optional
  + label::after {
  display: block;
  content: "(optional)";
  font-size: 0.875rem;
  margin: 0.1rem auto 0 0.25rem;

  opacity: 0.4;
} */
.rux-form-field input:disabled {
  opacity: 0.4;
  opacity: var(--disabledOpacity);
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

.rux-form-field--large,
.rux-form-field--large label,
.rux-form-field--large input,
.rux-form-field--large textarea {
  font-size: 1.125rem;
  font-size: var(--fontSizeXL);
}

.rux-form-field--small,
.rux-form-field--small label,
.rux-form-field--small input,
.rux-form-field--small textarea {
  font-size: 0.875rem;
  font-size: var(--fontSizeMD);
}

.rux-form-field--large input,
.rux-form-field--large textarea {
  padding: 0.5rem;
}

.rux-form-field--large input {
  height: 2.5rem;
}

.rux-form-field--small input,
.rux-form-field--small textarea {
  padding: 0.3rem;
}

.rux-form-field--small input {
  height: 1.625rem;
}

.rux-form-field--large input:invalid {
  background-size: 1.375rem;
  background-position: center right 0.5rem;
  padding: 0.5rem 1.875rem 0.5rem 0.5rem;
}

.rux-form-field input:focus,
.rux-form-field input:invalid:focus,
.rux-form-field textarea:focus {
  border-color: var(--inputFocusBorderColor);
  outline: none;
  color: var(--inputFocusTextColor);
}

.rux-form-field input[type='search']::-webkit-search-decoration {
  -webkit-appearance: textfield;
}

/* SEARCH VARIANT */
.rux-form-field input[type='search'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  padding: 0.5rem 0.5rem 0.5rem 2rem;
  background: var(--inputBackgroundColor) var(--inputSearchIcon) 10px/0.975rem no-repeat;
}

.rux-form-field--large input[type='search'] {
  padding: 0.5rem 0 0.5rem 2rem;
}

.rux-form-field--small input[type='search'] {
  padding: 0.3rem 0 0.3rem 2rem;
}

.rux-form-field input[type='search']::-webkit-search-cancel-button {
  position: relative;
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background-image: var(--inputSearchCancel);
  background-repeat: no-repeat;
}

.rux-form-field input[type='search']::-ms-clear {
  position: relative;
  right: 0.25rem;
  width: 20px;
  height: 20px;
  background-color: var(--inputInvalidBorderColor);
}

/*
**
** 	ASTRO RADIO BUTTONS
** 	==========================================================================
**  3.0 Notes
**  - Added Custom CSS Properties to support light/dark theming
**  - Added fallback properties for IE11
**  2.0 Notes
**  - Removed hand cursor on checkbox
**  - Updated styles to Astro 2.0
**	1.4 Notes
**	- Added rux_ and BEM compatible classes to all satcom_ NOTE: satcom_ will be removed in a future version
**	- Removed prefixed linear gradients
**  - Added line-height to label to fix minor alignment issue
**  - Moved user-select to parent element
**  - Removed user-select and placed it in astro.css to apply to all input types
*/
.rux-radio-button {
  display: flex;
  position: relative;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.rux-radio-button input[type='radio'] {
  -webkit-appearance: none;
  display: none;
}

.rux-radio-button input[type='radio']+label {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: var(--controlLabelColor);
  letter-spacing: 0.5px;
  cursor: pointer;
}

.rux-radio-button input[type='radio']+label::before {
  display: flex;
  flex-shrink: 0;
  flex-grow: 0;
  content: '';
  align-self: start;
  height: 1.125rem;
  height: var(--controlOptionSize);
  width: 1.125rem;
  width: var(--controlOptionSize);
  margin: 0 0.625rem 0 0;
  border: 1px solid var(--controlBorderColor);
  border-radius: 100%;
  background-color: transparent;
}

.rux-radio-button input[type='radio']:checked+label::before {
  background-color: var(--controlSelectedOutlineBackgroundColor);
  border-color: var(--controlSelectedOutlineBorderColor);
}

.rux-radio-button input[type='radio']:not(:disabled):hover+label:before,
.rux-radio-button input[type='radio']:not(:disabled):checked:hover+label:before {
  border-color: var(--controlHoverBorderColor);
}

.rux-radio-button input[type='radio']:not(:disabled):checked:hover+label:after {
  background-color: var(--controlHoverBorderColor);
}

.rux-radio-button input[type='radio']:checked+label::after {
  position: absolute;
  top: 5px;
  display: flex;
  content: '';
  left: 5px;
  height: 8px;
  width: 8px;
  border-radius: 100%;
  /* box-shadow: inset 0 0 1px 0 rgba(255, 255, 255, 0.9); */
  background-color: var(--primary);
}

.rux-radio-button input[type='radio']:disabled+label {
  cursor: not-allowed;
  cursor: var(--disabledCursor);
  opacity: 0.4;
  opacity: var(--disabledOpacity);
}

/*
**
** 	ASTRO SELECT
** 	==========================================================================
**	2.0.1 Notes
*/
.rux-select {
  --selectInactiveCaret: url('data:image/svg+xml,%3Csvg%20width%3D%2210%22%20height%3D%225%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill%3D%22%23080c11%22%20d%3D%22M0%200h10L5%205z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E');
  --selectActiveCaret: url('data:image/svg+xml,%3Csvg%20width%3D%2210%22%20height%3D%225%22%20style%3D%22transform%3A%20rotate%28180deg%29%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill%3D%22%23080c11%22%20d%3D%22M0%200h10L5%205z%22%20fill-rule%3D%22evenodd%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E');
  position: relative;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: 1px solid var(--inputBorderColorAlt);
  border-radius: 3px;
  border-radius: var(--buttonBorderRadius);
  color: var(--inputDark);
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
  font-weight: normal;
  font-size: 1rem;
  padding: 0.438rem 3.125rem 0.438rem 0.5rem;
  background-color: var(--inputBackground);
  background-image: var(--selectInactiveCaret);
  background-position: center right 0.625rem;
  background-repeat: no-repeat;
  user-select: none;
}

.rux-select .rux-select optgroup,
.rux-select option {
  color: var(--inputDark);
  background-color: var(--inputBackground);
}

.rux-select .rux-select optgroup:hover,
.rux-select option:hover {
  background-color: var(--selectHoverOptBackgroundColor);
}

.rux-select:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  border-color: var(--inputBorderColorDisabled);
}

.rux-select:disabled:hover {
  cursor: not-allowed;
}

.rux-select:focus {
  outline: none;
  border: 1px solid var(--primary);
  border-radius: 3px;
  border-radius: var(--buttonBorderRadius);
}

.rux-select:active:not(:disabled) {
  background-image: var(--selectActiveCaret);
}

.rux-select:hover {
  cursor: pointer;
}

.rux-select::-ms-expand {
  display: none;
}

.rux-select:focus::-ms-value {
  background: transparent;
}

.rux-select:invalid {
  border: 1px solid var(--inputInvalidBorderColor);
}

/*
**
** 	TABLE
** 	==========================================================================
*/
.rux-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0px;
  color: var(--tableRowTextColor);
  border-style: solid;
  border-width: 1px;
  border-color: var(--tableBorderColor);
  background: var(--tableRowBackgroundColor);
  text-align: left;
  overflow: scroll;
}

.rux-table__column-head th,
.rux-table th {
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: var(--tableHeaderBorderColor);
  background: var(--tableHeaderBackgroundColor);
  color: var(--tableHeaderTextColor);
  font-size: 1.125rem;
  font-size: var(--fontSizeXL);
  font-weight: 400;
  height: 2.625rem;
  padding: 0.625rem 1rem;
  white-space: nowrap;
}

.rux-table__column-head {
  box-shadow: var(--tableHeaderBoxShadow);
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: var(--tableHeaderBorderColor);
}

.rux-table tr[data-selected],
.rux-table tr.selected {
  background: var(--tableRowSelectedBackgroundColor);
}

.rux-table tr[data-selected] td,
.rux-table tr.selected td {
  border-color: var(--tableRowSelectedBorderColor);
}

.rux-table tr:hover:not([data-selected]),
.rux-table tr.selected:hover:not([data-selected]) {
  background: var(--tableRowHoverBackgroundColor);
  color: var(--tableRowHoverTextColor);
}

.rux-table td {
  border-width: 1px 0;
  border-style: solid;
  border-color: var(--tableRowBorderColor);
  padding: 0.625rem 1rem;
  white-space: nowrap;
}

.rux-table__column-head th:first-child,
.rux-table th:first-child,
.rux-table td:first-child {
  padding-left: 1.875rem;
}

.rux-table__column-head th:last-child,
.rux-table th:last-child,
.rux-table td:last-child {
  padding-right: 1.875rem;
}

.rux-table .rux-checkbox {
  margin: 0;
}

.rux-table .rux-checkbox input[type='checkbox']:checked+label::before,
.rux-table .rux-radio-button input[type='radio']:checked+label::before {
  background-color: var(--controlBackgroundColor);
  border-color: var(--controlBorderColor);
}

.rux-table .rux-checkbox input[type='checkbox']:checked+label::after,
.rux-table .rux-radio-button input[type='radio']:checked+label::after {
  border-color: var(--inputBackground);
}

.rux-table tr:hover .rux-checkbox input[type='checkbox']:not(:checked)+label::before,
.rux-table tr:hover .rux-checkbox input[type='radio']+label::before {
  border-color: var(--controlSelectedBorderColor);
}

.rux-advanced-status {
  position: relative;
  margin: 0 0.75rem;
  line-height: 0;
  /* width: 6.25rem; */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.rux-advanced-status__icon-group {
  position: relative;
  display: flex;
  justify-content: center;
  max-width: 6.25rem;
  min-width: 4rem;
  /* fauxicon grid. Usefull for gross alignment */
  /*  border: 1px solid red;

  background-image: linear-gradient(
    to right,
    rgba(255, 0, 0, 0) 0,
    rgba(255, 0, 0, 0) 49%,
    rgba(0, 255, 0, 1) 50%,
    rgba(0, 255, 0, 1) 51%,
    rgba(0, 255, 0, 0) 52%,
    rgba(0, 255, 0, 0) 100%
  ); */
}

.rux-advanced-status__status-icon {
  margin: 0 2px 0 auto;
  order: 1;
}

.rux-advanced-status__icon {
  order: 2;
  margin: 0 auto;
}

.rux-advanced-status__icon::before {
  content: '';
  display: block;
  position: relative;
  margin-bottom: -12px;
  margin-left: -18px !important;
  height: 16px;
  width: 16px;
}

.rux-advanced-status__badge:empty {
  display: none;
}

.rux-advanced-status__badge {
  display: block;
  z-index: 2;
  order: 3;
  position: absolute;
  bottom: -0.75rem;
  right: -0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.6);
  border-radius: 3px;
  padding: 0.65rem 0.25rem;
  font-size: 0.775rem;
  text-align: center;
  color: #fff;
  background-color: #000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.rux-advanced-status__label {
  text-align: center;
  color: white;
  color: var(--fontColor, white);
  font-size: 0.875rem;
  line-height: 1.2;
  margin-top: 1rem;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 6.25rem;
  white-space: nowrap;
}

.rux-advanced-status__label__sub-label {
  font-size: 0.65em;
  color: rgba(255, 255, 255, 0.6);
  display: block;
}

.rux-clock {
  display: flex;
  color: var(--clockTextColor);
  font-size: 1.15rem;
}

.rux-clock__segment {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.rux-clock__segment__value {
  display: flex;
  align-items: center;
  font-family: 'Roboto Mono', monospace;
  font-family: var(--fontFamilyMono);
  font-weight: 700;
  border: 1px solid var(--clockBorderColor);
  background-color: var(--clockBackgroundColor);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow-y: hidden;
  text-overflow: ellipsis;
}

.rux-clock--small .rux-clock__segment__value {
  height: 2.75rem;
  padding: 0 0.75rem;
  font-size: 1.15rem;
  font-weight: 500;
}

.rux-clock__day-of-the-year .rux-clock__segment__value {
  border-right: none;
}

.rux-clock:not(.rux-clock--small) .rux-clock__segment__value {
  font-size: 1.75rem;
  height: 2.75rem;
  padding: 0 0.75rem;
}

.rux-clock__segment__label {
  font-size: 0.875rem;
  color: var(--clockLabelColor);
}

.rux-clock__aos {
  margin-left: 1em;
}

.rux-clock__los {
  margin-left: 0.5em;
}

.rux-icon {
  margin: 0 auto;
  width: 2.8rem;
  height: 2.8rem;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  fill: var(--iconDefaultColor);
  -webkit-mask-size: cover;
  mask-size: cover;
}

.rux-icon svg {
  fill: var(--iconDefaultColor);
}

i.rux-icon {
  display: block;
  width: 2.8rem;
  height: 2.8rem;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: rgb(77, 172, 255);
  background-color: var(--colorSecondary);
}

.rux-log {
  display: block;
  font-size: 1rem;
  background-color: var(--logBackgroundColor);
}

.rux-log-header {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  justify-content: space-between;
  padding: 0.5rem;
  background-color: var(--logHeaderBackgroundColor);
}

.rux-log-header-title {
  margin: 0 0 1rem 0;
  display: none;
  font-size: 1.25rem;
  font-weight: 300;
}

.rux-log__header-labels {
  display: flex;
  width: 100%;
  color: var(--logHeaderTextColor);
}

.rux-log__header-labels,
.rux-log__events {
  padding: 0;
  margin: 0;
  list-style: none;
}

.rux-log__header-labels,
.rux-log__log-event {
  display: flex;
  align-content: flex-start;
}

.rux-log__events {
  height: 100%;
  overflow-y: scroll;
}

.log-event__timestamp {
  flex-shrink: 0;
  text-align: left;
  width: 5rem;
}

.rux-log__log-event {
  display: flex;
  flex-shrink: 0;
  align-items: flex-start;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--logBorderColor);
}

.rux-log__log-event:last-child {
  border-bottom: none;
}

.rux-log__header-labels li:not(:first-child),
.rux-log__log-event>* {
  margin: 0 0.5rem;
}

.rux-log__header-labels li:first-child {
  margin: 0 0.5rem 0 0;
}

.rux-log__log-event .log-event__timestamp {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
}

.log-event__status {
  flex-grow: 0;
  flex-shrink: 0;
  text-align: center;
  width: 1rem;
  overflow: hidden;
}

.log-event__message {
  flex-grow: 1;
  text-align: left;
}

.log-header__message {
  display: flex;
  justify-content: space-between;
}

/* ol li:nth-child(even) {
  background-color: #283f58;
} */
.rux-log__filter-enabled {
  position: sticky;
  top: 0;
  left: 0;
  align-content: center;
  color: var(--logFilterTextColor);
  background-color: var(--logFilterBackgroundColor);
  padding: 0.5rem;
}

.rux-log__filter-enabled .rux-icon {
  margin-right: 0.5rem;
}

.rux-modal {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: var(--modalBackgroundColor);
  width: 28rem;
  height: 13.5rem;
  border: 2px solid var(--modalBorderColor);
  border-radius: 4px;
  margin: auto;
  padding: 0;
  user-select: none;
  box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 3px rgba(0, 0, 0, 0.12), 0 4px 5px 0 rgba(0, 0, 0, 0.2);
}

.rux-modal__titlebar {
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 2rem;
  background-color: var(--modalBorderColor);
  user-select: none;
  cursor: move;
}

.rux-modal__titlebar h1 {
  font-size: 1rem;
  font-weight: 600;
  padding: 0;
  margin: 0;
  line-height: 1.2;
  color: var(--modalTitleColor);
}

.rux-modal__content {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 1rem;
  color: var(--defaultText);
}

.rux-modal__message {
  margin: 0.5rem 1.875rem 2.5rem 1.875rem;
}

.rux-modal .rux-button {
  box-shadow: none !important;
}

/*
**
** 	ASTRO NOTIFICATION
** 	==========================================================================
**	2.0 Notes
**
*/
.rux-notification {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  flex-grow: 1;
  align-items: center;
  align-content: center;
  top: -4.25rem;
  left: 0;
  height: 4.375rem;
  width: 100%;
  padding: 0.7rem 1.25rem;
  background-color: rgb(87, 214, 255);
  background-color: var(--colorStandbyLighten1);
  transition: all 0.5s ease;
  box-sizing: border-box;
  font-size: 1.125rem;
  font-size: var(--fontSizeXL);
  color: var(--notificationTextColor);
}

.rux-notification::after {
  position: relative;
  display: block;
  content: '';
  height: 60px;
}

.visible {
  margin-top: 0;
}

.notification-buffer.show {
  margin-top: 0;
}

.show .rux-notification-container {
  top: 0;
}

.show .rux-notification-buffer {
  height: 60px;
}

.rux-notification_close-button {
  border: 3px solid rgb(36, 163, 204);
  border: 3px solid var(--colorStandbyDarken1);
  color: rgb(36, 163, 204);
  color: var(--colorStandbyDarken1);
  background-color: transparent;
  height: 2.2rem;
  width: 2.2rem;
  border-radius: 50%;
  position: relative;
  margin-left: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.rux-notification_close-button::after,
.rux-notification_close-button::before {
  display: block;
  position: absolute;
  height: 2px;
  width: 66%;
  margin-left: -32%;
  margin-top: -1px;
  content: '';
  background-color: rgb(36, 163, 204);
  background-color: var(--colorStandbyDarken1);
}

@supports (--css: variables) {

  .rux-notification_close-button::after,
  .rux-notification_close-button::before {
    margin: 0;
  }
}

.rux-notification_close-button::after {
  transform: rotate(-45deg);
}

.rux-notification_close-button::before {
  transform: rotate(45deg);
}

/* Critical Variant */
.rux-notification--critical {
  background-color: rgb(255, 96, 96);
  background-color: var(--colorCriticalLighten1);
}

.rux-notification--critical .rux-notification_close-button {
  border-color: rgb(204, 45, 45);
  border-color: var(--colorCriticalDarken1);
}

.rux-notification--critical .rux-notification_close-button::after,
.rux-notification--critical .rux-notification_close-button::before {
  background-color: rgb(204, 45, 45);
  background-color: var(--colorCriticalDarken1);
}

/* Caution Variant */
.rux-notification--caution {
  background-color: rgb(253, 237, 97);
  background-color: var(--colorCautionLighten1);
}

.rux-notification--caution .rux-notification_close-button {
  border-color: rgb(202, 186, 46);
  border-color: var(--colorCautionDarken1);
}

.rux-notification--caution .rux-notification_close-button::after,
.rux-notification--caution .rux-notification_close-button::before {
  background-color: rgb(202, 186, 46);
  background-color: var(--colorCautionDarken1);
}

/* Normal Variant */
.rux-notification--normal {
  background-color: rgb(154, 246, 102);
  background-color: var(--colorNormalLighten2);
}

.rux-notification--normal .rux-notification_close-button {
  border-color: rgb(69, 192, 0);
  border-color: var(--colorNormalDarken1);
}

.rux-notification--normal .rux-notification_close-button::after,
.rux-notification--normal .rux-notification_close-button::before {
  background-color: rgb(69, 192, 0);
  background-color: var(--colorNormalDarken1);
}

/* Info Variant */
.rux-notification--info {
  background-color: rgb(87, 214, 255);
  background-color: var(--colorStandbyLighten1);
}

.rux-notification--info .rux-notification_close-button {
  border-color: rgb(36, 163, 204);
  border-color: var(--colorStandbyDarken1);
}

.rux-notification--info .rux-notification_close-button::after,
.rux-notification--info .rux-notification_close-button::before {
  background-color: rgb(36, 163, 204);
  background-color: var(--colorStandbyDarken1);
}

/*
**
** 	ASTRO POP-UPS
** 	==========================================================================
**	1.4 Notes
**	- Added rux_ and BEM compatible classes to all satcom_ NOTE: satcom_ will be removed in a future version
**	- Removed prefixed transform
**  - Updated colors to WCAG
*/
/* 	REQUIRED CLASSES */
.rux-pop-up {
  font-size: 1rem;
  display: inline-block;
  margin: 1em;
  min-width: 15em;
  position: relative;
  border: 1px solid rgb(77, 172, 255);
  border: 1px solid var(--colorSecondary);
  background-color: rgb(77, 172, 255);
  background-color: var(--colorSecondary);
  border-radius: 3px;
}

.rux-pop-up ul {
  position: relative;
  list-style: none;
  padding: 0;
  margin: 0;
  background: none;
  background-color: rgb(255, 255, 255);
  background-color: var(--colorWhite);
  border-radius: 2px;
  z-index: 2;
}

/* .rux-pop-up li,
.satcom-pop-up li {
  border-bottom: 1px solid #f0f1f3;
} */
.rux-pop-up a {
  display: block;
  padding: 0.5em;
  color: var(--popupMenuTextColor);
  text-decoration: none;
  min-width: 15em;
  max-width: 20em;
}

.rux-pop-up a:hover,
.rux-pop-up li:hover {
  background-color: rgb(184, 222, 255);
  background-color: var(--colorSecondaryLighten3);
  color: var(--popupMenuItemHoverTextColor);
}

.rux-pop-up li:first-child a {
  border-top-right-radius: 2px;
  border-top-left-radius: 2px;
}

.rux-pop-up li:last-child a {
  border: none;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 2px;
}

.rux-pop-up--top {
  border-top: 3px solid rgb(77, 172, 255);
  border-top: 3px solid var(--colorSecondary);
}

.rux-pop-up--top::before {
  content: '';
  display: block;
  position: absolute;
  width: 1.1875rem;
  height: 1.1875rem;
  background-color: rgb(77, 172, 255);
  background-color: var(--colorSecondary);
  z-index: 1;
  margin: -12px 0 0 16px;
  transform: rotate(45deg);
}

.rux-pop-up--bottom {
  border-bottom: 3px solid rgb(77, 172, 255);
  border-bottom: 3px solid var(--colorSecondary);
}

.rux-pop-up--bottom::after {
  content: '';
  display: block;
  position: absolute;
  border-bottom: 1px solid rgb(77, 172, 255);
  border-bottom: 1px solid var(--colorSecondary);
  border-right: 1px solid rgb(77, 172, 255);
  border-right: 1px solid var(--colorSecondary);
  width: 1.1875rem;
  height: 1.1875rem;
  background-color: rgb(77, 172, 255);
  background-color: var(--colorSecondary);
  margin: -7px 0 0 16px;
  transform: rotate(45deg);
}

.rux-pop-up--left {
  border-left: 3px solid rgb(77, 172, 255);
  border-left: 3px solid var(--colorSecondary);
}

.rux-pop-up--left::before {
  content: '';
  display: block;
  position: absolute;
  border-bottom: 1px solid rgb(77, 172, 255);
  border-bottom: 1px solid var(--colorSecondary);
  border-left: 1px solid rgb(77, 172, 255);
  border-left: 1px solid var(--colorSecondary);
  width: 1.1875rem;
  height: 1.1875rem;
  background-color: rgb(77, 172, 255);
  background-color: var(--colorSecondary);
  margin: 16px 0 0 -12px;
  transform: rotate(45deg);
}

.rux-pop-up--right {
  border-right: 3px solid rgb(77, 172, 255);
  border-right: 3px solid var(--colorSecondary);
}

.rux-pop-up--right::before {
  content: '';
  display: block;
  position: absolute;
  border-top: 1px solid rgb(77, 172, 255);
  border-top: 1px solid var(--colorSecondary);
  border-right: 1px solid rgb(77, 172, 255);
  border-right: 1px solid var(--colorSecondary);
  width: 1.1875rem;
  height: 1.1875rem;
  background-color: rgb(77, 172, 255);
  background-color: var(--colorSecondary);
  right: 0;
  margin: 16px -12px 0 0;
  transform: rotate(45deg);
}

/*
**
** 	ASTRO PROGRESS
** 	==========================================================================
**	2.0 Notes
**  - Updated indeterminate progress to use animated SVG and the :indeterminate pseudo class
**	1.4 Notes
**	- Added rux_ and BEM compatible classes to all satcom_ NOTE: satcom_ will be removed in a future version
**  - In addition to rux_ added the correct spelling of indeterminate as an additional selector
**  - Combined indeterminate and determinate progress styles
**  - DETERMINATE
**  - Made container a flex element
**  - Made percentage readout have an appropriate margin (NOTE: without a text rep the progress bar will scale to full width. Flexbox is neat.
**  - Fixed alignment issue in Safari/Chrome where the progress bar was 2-3 pixels too low
**  - Fixed width (on Chrome/Safari) of 100% width progress bar expanding past the border of the track
**  - INDETERMINATE
**  - Removed prefixed animation. Safari 8 was the last browser that required it
**  - [REMOVED] Embeded SVG graphics embeded SVG graphic stopped working
**  -
**  - !! NOTE !!
**  - The whole progress bar needs a rewrite. Better native elements and CSS properties should be used
**  -
*/
.rux-progress {
  display: flex;
  position: relative;
  justify-content: space-between;
  align-items: center;
  height: 2rem;
}

.rux-progress progress[value] {
  appearance: none;
  /* background: #21384f; */
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--progressDeterminateTrackBackgroundColor, rgba(0, 0, 0, 0.3));
  border: 1px solid #14202c;
  border: 1px solid var(--progressDeterminateTrackBorderColor, #14202c);
  border-radius: 10px 10px 10px 10px;
  border-radius: var(--progressRadius);
  height: 20px;
  width: 100%;
}

.rux-progress__value {
  margin: 0 0 0 0.5rem;
  text-align: right;
  font-size: 24px;
  width: 11%;
  color: white;
  color: var(--controlLabelColor, white);
}

.rux-progress progress[value]::-webkit-progress-bar {
  background-color: transparent;
}

.rux-progress progress[value]::-webkit-progress-value {
  border-radius: 10px 10px 10px 10px;
  border-radius: var(--progressRadius);
  height: 14px;
  height: var(--progressHeight);
  margin: 2px 0 0 2px;
  margin: var(--progressPadding);
  max-width: calc(100% - 4px);
  max-width: var(--progressWidth);
  background-color: #4dacff;
  background-color: var(--progressDeterminateBarBackgroundColor, #4dacff);
}

.rux-progress progress[value]::-ms-fill {
  border-radius: 10px;
  /*  var(--progressRadius, 10px);*/
  border: none;
  height: 14px;
  /* var(--progressHeight), 14px); */
  margin: 2px;
  max-width: calc(100% - 6px);
  /* var(--progressWidth);*/
  background-color: #4dacff;
  background-color: var(--progressDeterminateBarBackgroundColor, #4dacff);
}

.rux-progress progress[value]::-moz-progress-bar {
  border-radius: 10px 10px 10px 10px;
  border-radius: var(--progressRadius);
  margin: 2px 2px 0 2px;
  height: 14px;
  height: var(--progressHeight);
  max-width: calc(100% - 4px);
  max-width: var(--progressWidth);
  background-color: #4dacff;
  background-color: var(--progressDeterminateBarBackgroundColor, #4dacff);
}

/* Indeterminate */
.rux-progress progress:indeterminate {
  -webkit-appearance: none;
  -moz-appearance: none;
  box-sizing: border-box;
  position: relative;
  height: 5rem;
  width: 5rem;
  background-image: url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2264%22%20height%3D%2264%22%20viewBox%3D%220%200%2064%2064%22%3E%0A%20%20%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%0A%20%20%20%20%3Cpath%20fill%3D%22%23000000%22%20fill-opacity%3D%22.3%22%20stroke%3D%22%2314202c%22%20d%3D%22M32%2C63.5%20C49.3969696%2C63.5%2063.5%2C49.3969696%2063.5%2C32%20C63.5%2C14.6030304%2049.3969696%2C0.5%2032%2C0.5%20C14.6030304%2C0.5%200.5%2C14.6030304%200.5%2C32%20C0.5%2C49.3969696%2014.6030304%2C63.5%2032%2C63.5%20Z%20M32%2C56.5%20C18.4690236%2C56.5%207.5%2C45.5309764%207.5%2C32%20C7.5%2C18.4690236%2018.4690236%2C7.5%2032%2C7.5%20C45.5309764%2C7.5%2056.5%2C18.4690236%2056.5%2C32%20C56.5%2C45.5309764%2045.5309764%2C56.5%2032%2C56.5%20Z%22%2F%3E%0A%20%20%20%20%3Cpath%20fill%3D%22%234dacff%22%20fill-rule%3D%22nonzero%22%20d%3D%22M32%2C61.9354839%20C30.9333606%2C61.9354839%2030.0686785%2C61.0708018%2030.0686785%2C60.0041623%20C30.0686785%2C58.9375229%2030.9333606%2C58.0728408%2032%2C58.0728408%20C40.1954904%2C58.0728408%2047.7578267%2C54.2689176%2052.6662672%2C47.8987622%20C56.1526582%2C43.3741373%2058.0728408%2C37.8356396%2058.0728408%2C32%20C58.0728408%2C17.6003676%2046.3996324%2C5.92715921%2032%2C5.92715921%20C17.6003676%2C5.92715921%205.92715921%2C17.6003676%205.92715921%2C32%20C5.92715921%2C33.0666394%205.0624771%2C33.9313215%203.99583767%2C33.9313215%20C2.92919824%2C33.9313215%202.06451613%2C33.0666394%202.06451613%2C32%20C2.06451613%2C15.4670888%2015.4670888%2C2.06451613%2032%2C2.06451613%20C48.5329112%2C2.06451613%2061.9354839%2C15.4670888%2061.9354839%2C32%20C61.9354839%2C38.6961574%2059.7285058%2C45.0618765%2055.7259583%2C50.2563674%20C50.0938506%2C57.5656952%2041.4065535%2C61.9354839%2032%2C61.9354839%20Z%22%20%2F%3E%0A%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E');
  background-image: var(--progressIndeterminate,
      url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2264%22%20height%3D%2264%22%20viewBox%3D%220%200%2064%2064%22%3E%0A%20%20%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%0A%20%20%20%20%3Cpath%20fill%3D%22%23000000%22%20fill-opacity%3D%22.3%22%20stroke%3D%22%2314202c%22%20d%3D%22M32%2C63.5%20C49.3969696%2C63.5%2063.5%2C49.3969696%2063.5%2C32%20C63.5%2C14.6030304%2049.3969696%2C0.5%2032%2C0.5%20C14.6030304%2C0.5%200.5%2C14.6030304%200.5%2C32%20C0.5%2C49.3969696%2014.6030304%2C63.5%2032%2C63.5%20Z%20M32%2C56.5%20C18.4690236%2C56.5%207.5%2C45.5309764%207.5%2C32%20C7.5%2C18.4690236%2018.4690236%2C7.5%2032%2C7.5%20C45.5309764%2C7.5%2056.5%2C18.4690236%2056.5%2C32%20C56.5%2C45.5309764%2045.5309764%2C56.5%2032%2C56.5%20Z%22%2F%3E%0A%20%20%20%20%3Cpath%20fill%3D%22%234dacff%22%20fill-rule%3D%22nonzero%22%20d%3D%22M32%2C61.9354839%20C30.9333606%2C61.9354839%2030.0686785%2C61.0708018%2030.0686785%2C60.0041623%20C30.0686785%2C58.9375229%2030.9333606%2C58.0728408%2032%2C58.0728408%20C40.1954904%2C58.0728408%2047.7578267%2C54.2689176%2052.6662672%2C47.8987622%20C56.1526582%2C43.3741373%2058.0728408%2C37.8356396%2058.0728408%2C32%20C58.0728408%2C17.6003676%2046.3996324%2C5.92715921%2032%2C5.92715921%20C17.6003676%2C5.92715921%205.92715921%2C17.6003676%205.92715921%2C32%20C5.92715921%2C33.0666394%205.0624771%2C33.9313215%203.99583767%2C33.9313215%20C2.92919824%2C33.9313215%202.06451613%2C33.0666394%202.06451613%2C32%20C2.06451613%2C15.4670888%2015.4670888%2C2.06451613%2032%2C2.06451613%20C48.5329112%2C2.06451613%2061.9354839%2C15.4670888%2061.9354839%2C32%20C61.9354839%2C38.6961574%2059.7285058%2C45.0618765%2055.7259583%2C50.2563674%20C50.0938506%2C57.5656952%2041.4065535%2C61.9354839%2032%2C61.9354839%20Z%22%20%2F%3E%0A%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E'));
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  animation-name: spin;
  animation-duration: 1.367s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  background-color: transparent;
  border: none;
}

/* Removes the default animation from IE */
.rux-progress progress:indeterminate::-ms-fill {
  animation-name: none;
}

.rux-progress progress:indeterminate::-moz-progress-bar {
  background-color: transparent;
}

.rux-progress progress:indeterminate::-webkit-progress-value,
.rux-progress progress:indeterminate::-webkit-progress-bar {
  background-color: transparent;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/*
**
** 	ASTRO PUSHBUTTONS
** 	==========================================================================
**  3.0 Notes
**	- Updated with css custom properties for light/dark theme
**  2.1 Notes
**	- Moved Pushbuttons to its own style
**	1.4 Notes
**	- Added rux_ and BEM compatible classes to all satcom_ NOTE: satcom_ will be removed in a future version
**	- Removed prefixed linear gradients
**	- Removed prefixed transition
**  - Fixed added colon to checked pseudo class (e.g., checked became :checked)
**  - Alignment issue fixed on toggle button label
**  - Updated to WCAG colors
**  - Updated transition speed
*/
.rux-pushbutton {
  display: inline-block;
  height: 1.3125rem;
  /* width: auto; */
  -webkit-font-smoothing: subpixel-antialiased;
}

.rux-pushbutton__input {
  display: none;
}

.rux-pushbutton__button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 1.375rem;
  font-size: 0.75rem !important;
  font-weight: 600;
  margin: 0;
  padding: 0 0.625rem;
  color: var(--pushbuttonTextColor);
  background-color: var(--pushbuttonBackgroundColor);
  border-radius: 3px;
  border-radius: var(--defaultBorderRadius, 3px);
  border: 1px solid var(--pushbuttonBorderColor);
}

.rux-pushbutton__button label {
  color: var(--pushbuttonTextColor);
}

.rux-pushbutton__input:checked+.rux-pushbutton__button {
  display: flex;
  color: var(--pushbuttonSelectedTextColor);
  background-color: var(--pushbuttonSelectedBackgroundColor);
  border-color: var(--pushbuttonSelectedBorderColor);
}

.rux-pushbutton__input:not(:checked)+.rux-pushbutton__button .on {
  display: none;
}

.rux-pushbutton__input:disabled+.rux-pushbutton__button {
  opacity: 0.4;
  opacity: var(--disabledOpacity);
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

/*
**
** 	ASTRO SEGMENTED BUTTON
** 	==========================================================================
**  3.0 Notes
**  Don‘t forget to update Sketch with outline border color

*/
.rux-segmented-button {
  display: inline-flex;
  height: 1.6875rem;
  overflow: hidden;
  padding: 0;
  margin: 0;
  list-style: none;
  border-radius: 3px;
  border-radius: var(--controlBorderRadius);
  border: 1px solid var(--segmentedButtonBorderColor);
}

.rux-segmented-button:hover {
  border-color: var(--segmentedButtonHoverBorderColor);
}

.rux-segmented-button__segment {
  height: 1.6875rem;
  width: auto;
  margin: 0;
  padding: 0;
}

.rux-segmented-button__segment label {
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto;
  height: 1.5625rem;
  margin: 0;
  padding: 0 0.75rem;
  border: none;
  border-right: 1px solid var(--segmentedButtonBorderColor);
  color: var(--segmentedButtonTextColor);
  background-color: var(--segmentedButtonBackgroundColor);
  font-size: 1rem;
  user-select: none;
}

.rux-segmented-button__segment:nth-child(2):not(:last-child) label {
  border-right: none !important;
}

.rux-segmented-button__segment input {
  display: none !important;
}

.rux-segmented-button__segment label:hover {
  background-color: var(--segmentedButtonHoverBackgroundColor);
  border-color: var(--segmentedButtonHoverBorderColor);
  color: var(--segmentedButtonHoverTextColor);
  outline: none;
}

.rux-segmented-button__segment input:checked+label {
  background-color: var(--segmentedButtonSelectedBackgroundColor);
  color: var(--segmentedButtonSelectedTextColor);
}

.rux-segmented-button:nth-child(2):not(:last-child) label {
  border-right: none;
  border-left: none;
}

.rux-segmented-button:first-child label {
  border-radius: 3px 0 0 3px;
  border-radius: var(--controlBorderRadius) 0 0 var(--controlBorderRadius);
}

.rux-segmented-button:last-child label {
  border-radius: 0 3px 3px 0;
  border-radius: 0 var(--controlBorderRadius) var(--controlBorderRadius) 0;
}

/*
**
** 	ASTRO SLIDER
** 	==========================================================================
**  3.0 Notes
**  Removed SVG dependancy for the thumb element
**  Updated to 3.0 look/feel
**  Added CSS Custom Property support for light/dark theming
**  2.1 Notes
**  - Added support for bifurcated range slider
**	1.4 Notes
**	- Added rux_ compatible classes to all satcom_ NOTE: satcom_ will be removed in a future version
**	- Removed prefixed linear gradients
**  - Removed focus ring from Chrome (probably need an actual focus solution)
**  - Updated SVG
*/
/*
    Internal Variables
    Because Chrome, Firefox and IE all require explicit declarations to style
    the slider/range component these minimize the need to add repeat values
    between the three
  */
.rux-slider {
  display: flex;
  flex-direction: column;
}

.rux-slider__label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.25rem;
  margin-bottom: 9px;
}

.rux-slider__control {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex-grow: 1;
  position: relative;
}

.rux-slider label input {
  margin-left: auto;
  margin-right: 0;
}

.rux-range {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  background: none;
  width: 100%;
  margin: 0px;
  color: transparent;
}

input[type='range']:focus {
  outline: none;
}

/****** Track ******/
/* Track -> WebKit */
.rux-range::-webkit-slider-runnable-track {
  display: flex;
  align-items: center;
  max-width: 100%;
  /* width: 100%; */
  height: 3px;
  height: var(--trackHeight, 2px);
  cursor: pointer;
  cursor: var(--trackCursor, pointer);
  background-color: var(--sliderTrackBackgroundColor);
  outline: var(--sliderTrackBorderSize) solid transparent;
  outline: var(--sliderTrackBorderSize) solid var(--sliderTrackBorderColor, transparent);
  background-image: linear-gradient(to right,
      var(--sliderSelectedTrackBackgroundColor) 0%,
      var(--sliderSelectedTrackBackgroundColor) calc(0.99% * var(--value)),
      var(--sliderTrackBackgroundColor) calc(0.99% * var(--value)),
      var(--sliderTrackBackgroundColor) 100%);
}

.rux-range:disabled::-webkit-slider-runnable-track {
  opacity: 0.4;
  opacity: var(--disabledOpacity, 0.4);
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

/* Track -> Moz */
.rux-range::-moz-range-track {
  display: flex;
  align-items: center;
  /* width: 100%; */
  height: 3px;
  height: var(--trackHeight);
  cursor: pointer;
  cursor: var(--trackCursor, pointer);
  background-color: var(--sliderTrackBackgroundColor);
  outline: var(--sliderTrackBorderSize) solid var(--sliderTrackBorderColor);
}

.rux-range:disabled::-moz-range-track,
.rux-range:disabled::-moz-range-progress {
  opacity: 0.4;
  opacity: var(--disabledOpacity, 0.4);
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

.rux-range::-moz-range-progress {
  background-color: var(--sliderSelectedTrackBackgroundColor);
}

.rux-input:disabled {
  opacity: 0.4;
  opacity: var(--disabledOpacity, 0.4);
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

/* Track -> Ms */
.rux-range::-ms-track {
  display: flex;
  align-items: center;
  /* width: 100%; */
  height: 1.25rem;
  padding: 2px 0;
  cursor: pointer;
  color: transparent;
  background-color: transparent;
  border: none;
  /* background-color: var(--sliderTrackBackgroundColor); */
  outline: var(--sliderTrackBorderSize) solid transparent;
}

.rux-range::-ms-fill-lower {
  height: 2px;
  background-color: #4dacff;
}

.rux-range::-ms-fill-upper {
  height: 2px;
  background-color: var(--sliderTrackBackgroundColor);
}

/*****  Thumb ******/
/* Thumb -> Webkit */
.rux-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  position: relative;
  margin-top: calc(1.125rem / -2);
  margin-top: calc(var(--thumbSize) / -2);
  transform: translateX(-35%);
  height: 1.125rem;
  height: var(--thumbSize);
  width: 1.125rem;
  width: var(--thumbSize);
  border-radius: 100%;
  border: var(--sliderThumbBorderSize) solid var(--sliderThumbBorderColor);
  background-color: var(--sliderThumbBackgroundColor);
  cursor: pointer;
  box-shadow: inset 0 0 1px 0 rgba(255, 255, 255, 0.9), 0 3px 5px rgba(0, 0, 0, 0.14), 0 1px 9px rgba(0, 0, 0, 0.12), 0 1px 3px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0 1px 0 rgba(255, 255, 255, 0.9), var(--thumbShadow);
  z-index: 6;
}

.rux-range::-webkit-slider-thumb:hover {
  border-color: var(--sliderHoverThumbBorderColor);
}

.rux-range:disabled::-webkit-slider-thumb {
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

.rux-range:not(:disabled)::-webkit-slider-thumb:active {
  border-color: var(--sliderSelectedThumbBorderColor);
  background: radial-gradient(circle, white 40%, var(--primaryDark) 40%);
  background-color: var(--inputBackground);
  -webkit-radial-gradient: radial-gradient(circle, white 40%, var(--primaryDark) 40%);
  box-shadow: inset 0 0 0 4px var(--primary), 0 1px 3px rgba(0, 0, 0, 0.14), 0 1px 4px rgba(0, 0, 0, 0.12), 0 1px 1px rgba(0, 0, 0, 0.2);
  box-shadow: var(--thumbShadowActive);
}

.rux-range:not(:disabled)::-webkit-slider-thumb:focus,
.rux-range:not(:disabled)::-webkit-slider-thumb:hover:not(:active) {
  background-color: var(--sliderHoverThumbBackgroundColor);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.14), 0 1px 18px rgba(0, 0, 0, 0.12), 0 3px 5px rgba(0, 0, 0, 0.2);
  box-shadow: var(--thumbShadowHover);
}

/* Thumb -> Moz */
.rux-range::-moz-range-thumb {
  -moz-appearance: none;
  position: relative;
  top: calc(1.125rem / -2);
  top: calc(var(--thumbSize) / -2);
  transform: translateX(-35%);
  height: 1.125rem;
  height: var(--thumbSize);
  width: 1.125rem;
  width: var(--thumbSize);
  border-radius: 100%;
  border: var(--sliderThumbBorderSize) solid var(--sliderThumbBorderColor);
  background-color: var(--sliderThumbBackgroundColor);
  cursor: pointer;
  box-shadow: inset 0 0 1px 0 rgba(255, 255, 255, 0.9), 0 3px 5px rgba(0, 0, 0, 0.14), 0 1px 9px rgba(0, 0, 0, 0.12), 0 1px 3px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0 1px 0 rgba(255, 255, 255, 0.9), var(--thumbShadow);
}

.rux-range:not(:disabled)::-moz-range-thumb:active {
  background: radial-gradient(circle, white 40%, var(--primaryDark) 40%);
}

.rux-range::-moz-range-thumb:hover {
  border-color: var(--sliderHoverThumbBorderColor);
}

input:-moz-focusring {
  outline: none;
}

.rux-range:disabled::-moz-range-thumb {
  opacity: 0.4;
  opacity: var(--disabledOpacity, 0.4);
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

/* Thumb -> Ms */
.rux-range::-ms-thumb {
  position: relative;
  top: -10px;
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 100%;
  border: var(--sliderThumbBorderSize) solid var(--sliderThumbBorderColor);
  background-color: var(--sliderThumbBackgroundColor);
  cursor: pointer;
  box-shadow: inset 0 0 1px 0 rgba(255, 255, 255, 0.9), 0 3px 5px rgba(0, 0, 0, 0.14), 0 1px 9px rgba(0, 0, 0, 0.12), 0 1px 3px rgba(0, 0, 0, 0.2);
}

.rux-range:disabled::-ms-thumb {
  opacity: 0.4;
  cursor: not-allowed;
}

/* Labels */
.rux-slider__control__labels {
  position: relative;
  display: flex;
  justify-content: space-between;
  list-style: none;
  padding: 0;
  margin: 10px 0 0 0;
  color: var(--fontColor);
  font-size: 0.875rem;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-family: var(--fontFamily);
}

.rux-range:disabled+.rux-slider__control__labels {
  opacity: 0.4;
  opacity: var(--disabledOpacity, 0.4);
}

.rux-slider__control__labels li {
  padding: 0px;
  text-align: left;
}

.rux-slider__control__labels li:first-child,
.rux-slider__control__labels li:last-child {
  margin: 0px;
}

.rux-slider__control-ticks {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 3px;
  height: var(--ticksHeight);
  background: linear-gradient(to right, var(--primaryDark) 1px, transparent 1px) repeat-x;
  background: linear-gradient(to right, var(--ticksColor) var(--ticksThickness), transparent 1px) repeat-x;
  background-size: calc(100% / ((100 - 0) / 1) - 0.18%) 3px;
  background-size: calc(100% / ((var(--max) - var(--min)) / var(--step)) - 0.18%) var(--ticksHeight);
  background-position: 0;
  z-index: 5;
}

.disabled {
  opacity: 0.4;
  opacity: var(--disabledOpacity, 0.4);
  cursor: not-allowed;
  cursor: var(--disabledCursor);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.rux-slider__input {
  margin-right: 0;
  margin-bottom: 0.75rem;
  width: 4rem !important;
}

/*
  Fake tick marks, sort of works, but label using flex are imprecise
  .rux-slider__control__labels li::before {
    position: absolute;
    content: "|";
    font-size: 0.5rem;
    top: -100%;
  } */
input[type='range']::-moz-focus-outer {
  border: 0;
}

/*
**
** 	ASTRO STATUS INDICATOR
** 	==========================================================================
**  4.1 Notes
**  Removed satcom prefix
**  Replaced SVGs with a single PNG and used sprite pattern to improve performance
**  Removed ::before psuedo element in favor of simpler background on the div
**  Removed undocumented and unused small status variant
**  General clean up
**  3.0 Notes
**  Removed margin on status symbols
**	1.4 Notes
**	- Added rux_ and BEM compatible classes to all satcom_ NOTE: satcom_ will be removed in a future version
**  - Embeded SVG graphics
*/
.rux-status-indicator,
.rux-status {
  display: block;
  height: 1rem;
  width: 1rem;
  margin: 0.125rem;
  background-size: cover;
  background-repeat: no-repeat;
  background-position-x: 1rem;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMAAAAAgCAMAAABzRoe3AAAAgVBMVEUAAAD/tQv/swL/OTn/PDzftl9W8QBW8AAtzP8v0P9Z9gD/tAP/uQb/tAItzf8tzf+eqK2ep60uzP9X8QCfqK4uzf8uzf9X8QAuzP9X8ACeqK4vzP9Y8QAuzv9Y8gCeqq4wz/8xzv9A1f9Y8QD/tAP86DpW8AD/swL/ODgtzP+ep63tgXPUAAAAJXRSTlMAGNfAQAv98/IdG8Io52zz8+DZ2dC9trabm4F+a05OQjAaDG6dAJcYcwAAAfdJREFUWMPtmNlygkAQRScyrBp3QTZFNBj8/w9MCGI3dBOHMg9jyvOmVFnn1HUSULx4LibjN/HMTN7Po2cu+PY/cwUfPYg2XhKsbHsVJJ5gcaf+cjZb+lN3qJjxyWIw/lAwNMALreKKFTIJh215Y3sYFiD5AEn9oWBowM4uEPau65A6JcJJ/2ACg/pDwdCAo1W0sI6ixbTsMH18Akn9oWBgwK7xh4LWBmlJSB+dwCD+pEA5wLMLgo3OwcGpvzhxludZfH1Bz4EZrefzdWSqTSCJPylQDggLhhA+oD6/G7d+5W7qk9yV3C8uPyz2KhMYxJ8UKAd4Fhdg3SZwa3/4wLrA7fhfbuwVJpDEnxQoByQFS9I6wQ7ydR16js0FBCzMuxMY1J8WqAYEfEDQXPcr3RjbxNU7vsBEF0R0dwJJ/LkCxYAVH7Bqri8r3QzbZNU7S4FZ44B1/ymgA4zPfZwUA2w+wG6uzyrdHMvk1TszgZnjgLmgyL4T8Dbq8R+bWgUIgw4ABby/Vl8hIckAUMD7a3WIYQIYgC0Af73+jOIJpKAF1F+vf2QwAQxAC7C/ZrcSeAIpegvAX7ObOZgABiAF2F+322mYAAagBeCv2wMNTEAGIAWVv3aPlDABDMAW1P7aPdQjDBiALaj89ftZBSGl+LXgZIoXL/4VXyptNwzuHR/QAAAAAElFTkSuQmCC');
  background-image: var(--statusSymbols,
      url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMAAAAAgCAMAAABzRoe3AAAAgVBMVEUAAAD/tQv/swL/OTn/PDzftl9W8QBW8AAtzP8v0P9Z9gD/tAP/uQb/tAItzf8tzf+eqK2ep60uzP9X8QCfqK4uzf8uzf9X8QAuzP9X8ACeqK4vzP9Y8QAuzv9Y8gCeqq4wz/8xzv9A1f9Y8QD/tAP86DpW8AD/swL/ODgtzP+ep63tgXPUAAAAJXRSTlMAGNfAQAv98/IdG8Io52zz8+DZ2dC9trabm4F+a05OQjAaDG6dAJcYcwAAAfdJREFUWMPtmNlygkAQRScyrBp3QTZFNBj8/w9MCGI3dBOHMg9jyvOmVFnn1HUSULx4LibjN/HMTN7Po2cu+PY/cwUfPYg2XhKsbHsVJJ5gcaf+cjZb+lN3qJjxyWIw/lAwNMALreKKFTIJh215Y3sYFiD5AEn9oWBowM4uEPau65A6JcJJ/2ACg/pDwdCAo1W0sI6ixbTsMH18Akn9oWBgwK7xh4LWBmlJSB+dwCD+pEA5wLMLgo3OwcGpvzhxludZfH1Bz4EZrefzdWSqTSCJPylQDggLhhA+oD6/G7d+5W7qk9yV3C8uPyz2KhMYxJ8UKAd4Fhdg3SZwa3/4wLrA7fhfbuwVJpDEnxQoByQFS9I6wQ7ydR16js0FBCzMuxMY1J8WqAYEfEDQXPcr3RjbxNU7vsBEF0R0dwJJ/LkCxYAVH7Bqri8r3QzbZNU7S4FZ44B1/ymgA4zPfZwUA2w+wG6uzyrdHMvk1TszgZnjgLmgyL4T8Dbq8R+bWgUIgw4ABby/Vl8hIckAUMD7a3WIYQIYgC0Af73+jOIJpKAF1F+vf2QwAQxAC7C/ZrcSeAIpegvAX7ObOZgABiAF2F+322mYAAagBeCv2wMNTEAGIAWVv3aPlDABDMAW1P7aPdQjDBiALaj89ftZBSGl+LXgZIoXL/4VXyptNwzuHR/QAAAAAElFTkSuQmCC'));
}

/* Specific Status Iconography */
.rux-status--off,
.rux-status-indicator.rux-status--off,
.rux-status--off .rux-advanced-status__icon {
  background-position-x: -5rem;
}

.rux-status--standby,
.rux-status-indicator.rux-status--standby,
.rux-status--standby .rux-advanced-status__icon {
  background-position-x: -4rem;
}

.rux-status--normal,
.rux-status-indicator.rux-status--normal,
.rux-status-indicator.rux-status--ok,
.rux-status--normal .rux-advanced-status__icon,
.rux-status--ok .rux-advanced-status__icon {
  background-position-x: -3rem;
}

.rux-status--caution,
.rux-status-indicator.rux-status--caution,
.rux-status--caution .rux-advanced-status__icon {
  background-position-x: -2rem;
}

.rux-status--serious,
.rux-status-indicator.rux-status--serious,
.rux-status-indicator.rux-status--error,
.rux-status--serious .rux-advanced-status__icon,
.rux-status--error .rux-advanced-status__icon {
  background-position-x: -1rem;
}

.rux-status--critical,
.rux-status-indicator.rux-status--critical,
.rux-status-indicator.rux-status--alert,
.rux-status--critical .rux-advanced-status__icon,
.rux-status--alert .rux-advanced-status__icon,
.rux-status--emergency .rux-advanced-status__icon {
  background-position-x: 0;
}

/* Specific Color */
.rux-status--off svg,
.rux-icon.rux-status--off {
  fill: #c6ccd1;
  fill: var(--colorOff, #c6ccd1);
  color: #c6ccd1;
  color: var(--colorOff, #c6ccd1);
}

.rux-status--standby svg,
.rux-icon.rux-status--standby {
  fill: #4dacff;
  fill: var(--colorStandby, #4dacff);
  color: #4dacff;
  color: var(--colorStandby, #4dacff);
}

.rux-icon.rux-status--normal,
.rux-icon.rux-status--ok,
.rux-status--normal svg,
.rux-status--ok svg {
  fill: #5bff00;
  fill: var(--colorNormal, #5bff00);
  color: #5bff00;
  color: var(--colorNormal, #5bff00);
}

.rux-status--caution svg,
.rux-icon.rux-status--caution {
  fill: #f8e71d;
  fill: var(--colorCaution, #f8e71d);
  color: #f8e71d;
  color: var(--colorCaution, #f8e71d);
}

.rux-status--serious svg,
.rux-status--error svg,
.rux-icon.rux-status--serious,
.rux-icon.rux-status--error {
  fill: #ffb000;
  fill: var(--colorSerious, #ffb000);
  color: #ffb000;
  color: var(--colorSerious, #ffb000);
}

.rux-status--critical svg,
.rux-status--emergency svg,
.rux-icon.rux-status--critical,
.rux-icon.rux-status--emergency {
  fill: #ff3030;
  fill: var(--colorCritical, #ff3030);
  color: #ff3030;
  color: var(--colorCritical, #ff3030);
}

/*
**
** 	ASTRO TABS
** 	==========================================================================
**	1.4 Notes
**	- Added rux_ and BEM compatible classes to all satcom_ NOTE: satcom_ will be removed in a future version
**  - Removed prefixed transitions
**  - Removed prefixed gradients
**  - Updated colors for WCAG compliance
**  - Swapped condensed font for standard
**  2.0 Ideas
**  - Replace use a radio-button based structure for tabs? (http://alistapart.com/article/radio-controlled-web-design)
*/
/* 	REQUIRED CLASSES */
.rux-tabs {
  font-size: 1.5rem;
  width: 100%;
  margin: 0;
  padding: 0;
  height: 5.625rem;
  user-select: none;
  border-bottom: 5px solid var(--tabBorderColor);
}

.rux-tabs--small {
  height: 3.125rem;
  font-size: 1.125rem;
  font-size: var(--fontSizeXL);
  border-bottom: 3px solid var(--tabBorderColor);
}

.rux-tabs ul {
  padding: 0;
  margin: 0;
  height: 100%;
  display: flex;
  list-style: none;
}

.rux-tabs li {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  /* outline: 1px solid green; */
}

/* .rux-tabs li:not(:last-child),
.satcom-tabs li:not(:last-child) {
   border-right: 1px solid var(--tabBorderColor, rgb(20, 32, 44));
} */
.rux-tabs li::before {
  display: none !important;
}

.rux-tabs a {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 3px 2rem 0;
  text-decoration: none;
  color: var(--tabTextColor);
  /* background-color: var(--tabBackgroundColor, rgb(30, 47, 66)); */
  /* overflow: auto; */
}

.rux-tabs a[disabled] {
  opacity: 0.4;
  opacity: var(--disabledOpacity);
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

.rux-tabs .selected a {
  color: var(--tabSelectedTextColor);
}

.rux-tabs--small .selected a,
.rux-tabs--small .selected a:hover {
  color: var(--tabSelectedTextColor);
}

.rux-tabs .selected a:hover {
  color: var(--tabSelectedTextColor);
}

.rux-tabs a:hover:not([disabled]) {
  color: var(--tabHoverTextColor);
}

.rux-tabs .rux-tab {
  border-bottom: 5px solid var(--tabBorderColor);
}

/*
**
** 	ASTRO TOGGLE BUTTONS
** 	==========================================================================
**  3.0 Notes
**  - Breaking change to markup of toggle button
**  2.1 Notes
**	- Moved Pushbuttons to its own style sheet
**	1.4 Notes
**	- Added rux_ and BEM compatible classes to all satcom_ NOTE: satcom_ will be removed in a future version
**	- Removed prefixed linear gradients
**	- Removed prefixed transition
**  - Fixed added colon to checked pseudo class (e.g., checked became :checked)
**  - Alignment issue fixed on toggle button label
**  - Updated to WCAG colors
**  - Updated transition speed
*/
.rux-toggle--legacy,
.satcom-toggle--legacy {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
  -webkit-font-smoothing: subpixel-antialiased;
  height: 1.3125rem;
  width: 4.375rem;
  border-radius: 3px;
  border-radius: var(--defaultBorderRadius, 0.1875rem);
  border: 1px solid black;
  border: 1px solid var(--toggleBaseBorderColor, black);
  /* box-shadow: inset 0 0 2px rgba(0, 0, 0, 1); */
  user-select: none;
  overflow: hidden;
}

.rux-toggle--legacy__input,
.satcom-toggle--legacy input {
  display: none !important;
}

.rux-toggle--legacy__button {
  position: absolute;
  display: flex;
  justify-content: space-around;
  align-content: center;
  align-items: center;
  top: 0;
  left: 0;
  font-size: 0.75rem !important;
  height: 100%;
  width: 100%;
}

.rux-toggle--legacy__button span {
  display: flex;
  align-content: center;
  justify-content: center;
  align-items: center;
  width: 50%;
  height: 100%;
  z-index: 1;
  text-transform: uppercase;
}

.rux-toggle--legacy__button::before {
  position: absolute;
  content: '';
  display: block;
  height: 100%;
  width: 100%;
  border-radius: 0.1875rem;
  background: black;
  background: var(--toggleBaseBackgroundColor, black);
}

.rux-toggle--legacy__button::after {
  content: '';
  display: block;
  position: absolute;
  box-sizing: border-box;
  top: 0;
  left: 0;
  z-index: 10;
  transition: left 0.1s ease-out;
  width: 50%;
  height: 100%;
  border-radius: 0.125rem;
  background: #005a8f url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='6' height='6'%3E %3Cdefs%3E %3ClinearGradient id='a' x1='50%25' x2='50%25' y1='0%25' y2='100%25'%3E %3Cstop offset='0%25' stop-color='%23000'/%3E %3Cstop offset='100%25' stop-color='%23023861' stop-opacity='0'/%3E %3C/linearGradient%3E %3C/defs%3E %3Ccircle cx='3' cy='3' r='3' fill='url(%23a)' fill-rule='evenodd'/%3E %3C/svg%3E") center center no-repeat;
  background: var(--toggleButtonBackgroundColor, #005a8f) url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='6' height='6'%3E %3Cdefs%3E %3ClinearGradient id='a' x1='50%25' x2='50%25' y1='0%25' y2='100%25'%3E %3Cstop offset='0%25' stop-color='%23000'/%3E %3Cstop offset='100%25' stop-color='%23023861' stop-opacity='0'/%3E %3C/linearGradient%3E %3C/defs%3E %3Ccircle cx='3' cy='3' r='3' fill='url(%23a)' fill-rule='evenodd'/%3E %3C/svg%3E") center center no-repeat;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.5), 1px 0 2px rgba(0, 0, 0, 0.6);
  box-shadow: var(--toggleButtonBoxShadow, 0 0 3px rgba(0, 0, 0, 0.5), 1px 0 2px rgba(0, 0, 0, 0.6));
}

.rux-toggle--legacy .on {
  font-weight: 600;
  color: #5bff00;
  color: var(--toggleBaseSelectedTextColor, #5bff00);
}

.rux-toggle--legacy .off {
  color: white;
  color: var(--toggleBaseTextColor, white);
}

.rux-toggle--legacy__input:disabled+.rux-toggle--legacy__button {
  opacity: 0.4;
  opacity: var(--disabledOpacity, 0.4);
  cursor: not-allowed;
}

.rux-toggle--legacy__input:checked+.rux-toggle--legacy__button::after {
  left: 50%;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.5), -1px 0 2px rgba(0, 0, 0, 0.6);
  box-shadow: var(--toggleButtonSelectedBoxShadow, 0 0 3px rgba(0, 0, 0, 0.5), -1px 0 2px rgba(0, 0, 0, 0.6));
}

.rux-switch {
  position: relative;
  display: flex;
  height: 22px;
  width: 42px;
  overflow: hidden;
}

.rux-switch__input {
  display: none;
}

.rux-switch__button {
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* Track */
.rux-switch__button::before {
  position: relative;
  display: flex;
  content: '';
  border-radius: 5.5px;
  border: 1px solid;
  border-color: var(--switchOffColor);
  background-color: var(--switchOffColor);
  height: 11px;
  width: 38px;
  z-index: 2;
  transition: 0.1s background-color linear;
}

/* Track Active */
.rux-switch__input:checked+.rux-switch__button::before {
  border-color: var(--switchOnColor);
  background-color: var(--switchOnColor);
}

/* Track Hover Unchecked */
.rux-switch:hover .rux-switch__input:not(:disabled)+.rux-switch__button:before {
  border-color: var(--switchHoverOffColor);
  background-color: var(--switchHoverOffColor);
}

/* Track Hover Checked */
.rux-switch:hover .rux-switch__input:checked:not(:disabled)+.rux-switch__button:before {
  border-color: var(--switchHoverOnColor);
  background-color: var(--switchHoverOnColor);
}

/* Button */
.rux-switch__button::after {
  position: absolute;
  content: '';
  top: 1px;
  left: 0;
  z-index: 3;
  height: 19px;
  width: 19px;
  border-radius: 50%;
  border: 1px solid var(--switchOffColor);
  background-color: var(--inputBackground);
  transition: 0.1s left linear, 0.1s border-color linear;
}

/* Button Active */
.rux-switch__input:checked+.rux-switch__button::after {
  left: 50%;
  border-color: var(--switchOnColor);
  background-color: var(--inputBackground);
}

/* Button Hover Unchecked */
.rux-switch:hover .rux-switch__input:not(:disabled)+.rux-switch__button:after {
  border-color: var(--switchHoverOffColor);
}

/* Button Hover Checked */
.rux-switch:hover .rux-switch__input:checked:not(:disabled)+.rux-switch__button:after {
  border-color: var(--switchHoverOnColor);
}

/* Disabled */
.rux-switch__input:disabled+.rux-switch__button::after {
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

.rux-switch__input:checked:disabled+.rux-switch__button::after {
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

.rux-switch__input:disabled+.rux-switch__button::before {
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

.rux-switch__input:disabled+.rux-switch__button {
  opacity: 0.4;
  opacity: var(--disabledOpacity);
  cursor: not-allowed;
  cursor: var(--disabledCursor);
}

.rux-tree {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  padding: 0;
  margin: 0;
  font-size: 1rem;
  color: var(--treeTextColor);
  border: solid 1px var(--treeBorderColor);
  background-color: var(--treeBackgroundColor);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.rux-tree--compact {
  font-size: 0.875rem;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.rux-tree ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.rux-tree li {
  font-weight: bold;
}

/* Parent Elements */
.rux-tree__parent {
  display: flex;
  align-items: center;
  padding: 0 0.5rem;
  height: 2rem;
}

.rux-tree__parent:hover,
:not([aria-selected='true'])>.rux-tree__parent:hover::after {
  background: var(--treeHoverBackgroundColor);
  color: var(--treeHoverTextColor);
}

:not([aria-selected='true'])>.rux-tree__parent:hover .rux-tree__arrow::after {
  border-color: transparent transparent transparent var(--treeHoverTextColor);
}

.rux-tree__label {
  flex-grow: 1;
  padding: 0.5rem;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: calc(100% - 7px);
}

.rux-tree--compact .rux-tree__label {
  padding: 0.35rem;
}

.rux-tree__arrow {
  position: relative;
  cursor: pointer;
  width: 7px;
  visibility: hidden;
}

.rux-tree__arrow::after {
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 7px 0 7px 7px;
  border-color: transparent transparent transparent var(--treeAccentColor);
  display: inline-block;
}

.has-children .rux-tree__arrow {
  visibility: visible;
}

/* Child Elements */
.rux-tree__children {
  width: 100%;
  display: none;
  padding: 0;
  margin: 0;
  height: 0;
}

.rux-tree__child {
  padding-left: 3rem;
}

/* Expanded */
.expanded .rux-tree__arrow::after {
  transform: rotate(90deg);
}

.expanded .rux-tree__children {
  display: block;
  height: auto;
}

[aria-expanded='true'][aria-level='1']:not([aria-selected='true'])>.rux-tree__parent:after {
  border-bottom: solid 1px var(--treeExpandedBorderColor);
}

.rux-tree .selected,
[aria-selected='true']>.rux-tree__parent::after,
[aria-selected='true']>.rux-tree__parent:hover::after {
  background-color: var(--treeSelectedBackgroundColor);
  box-shadow: inset 0.25rem 0 0 var(--treeSelectedAccentColor);
  color: var(--treeSelectedTextColor);
}

.rux-tree .selected .rux-tree__arrow::after {
  border-color: transparent transparent transparent var(--treeSelectedAccentColor);
}

[aria-selected='true']>.rux-tree__parent::after {
  border-top: 1px solid var(--treeSelectedBorderColor);
  border-bottom: 1px solid var(--treeSelectedBorderColor);
}

li[aria-selected='true']>.rux-tree__children li:not([aria-selected='undefined']) .rux-tree__parent:hover:after {
  box-shadow: inset 0.25rem 0 0 var(--treeSelectedAccentColor) !important;
  background-color: var(--treeHoverBackgroundColor) !important;
}

li[aria-selected='true']>.rux-tree__children li:not([aria-selected='undefined']) .rux-tree__parent:hover {
  color: var(--treeHoverTextColor);
}

li[aria-selected='true']>.rux-tree__children li:not([aria-selected='undefined']) .rux-tree__parent:hover .rux-tree__arrow:after {
  border-color: transparent transparent transparent var(--treeHoverTextColor);
}