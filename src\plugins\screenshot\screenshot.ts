/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * planetarium.ts is a plugin for showing the satellites above from the perspective
 * of a view on the earth.
 *
 * http://www.spacesecure.cn
 *
 * @Copyright 北京星地探索科技有限公司
 *
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { KeepTrackApiEvents, MenuMode } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { errorManagerInstance } from '@app/singletons/errorManager';
import { Classification } from '@app/static/classification';
import cameraPng from '@public/img/icons/camera.png';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
export class Screenshot extends KeepTrackPlugin {
  readonly id = 'Screenshot';
  dependencies_ = [];
  bottomIconCallback = () => {
    this.saveHiResPhoto('4k');
  };

  logo: HTMLImageElement;
  secondaryLogo: HTMLImageElement;

  constructor() {
    super();
    try {
      this.logo = new Image();
      this.logo.onerror = () => {
        errorManagerInstance.warn('无法加载logo图像。');
      };
      this.logo.src = `${settingsManager.installDirectory}img/logo-primary.png`;
      if (settingsManager.isShowSecondaryLogo) {
        this.secondaryLogo = new Image();
        this.secondaryLogo.onerror = () => {
          errorManagerInstance.warn('无法加载次logo图像。');
        };
        this.secondaryLogo.src = `${settingsManager.installDirectory}img/logo-secondary.png`;
      }
    } catch {
      // If the logo fails to load, we will still continue without it.
    }
  }

  // This is 'disabled' since it does not turn green after being clicked like other buttons.
  isIconDisabled = true;

  menuMode: MenuMode[] = [MenuMode.BASIC, MenuMode.ADVANCED, MenuMode.ALL];

  bottomIconImg = cameraPng;
  rmbCallback = (targetId: string): void => {
    switch (targetId) {
      case 'save-hd-rmb':
        this.saveHiResPhoto('hd');
        break;
      case 'save-4k-rmb':
        this.saveHiResPhoto('4k');
        break;
      case 'save-8k-rmb':
        this.saveHiResPhoto('8k');
        break;
      default:
        break;
    }
  };

  rmbL1ElementName = 'save-rmb';
  rmbL1Html = keepTrackApi.html`<li class="rmb-menu-item" id="${this.rmbL1ElementName}"><a href="#">保存图片 &#x27A4;</a></li>`;

  isRmbOnEarth = true;
  isRmbOffEarth = true;
  isRmbOnSat = true;
  rmbMenuOrder = 20;

  rmbL2ElementName = 'save-rmb-menu';
  rmbL2Html = keepTrackApi.html`
    <ul class='dropdown-contents'>
      <li id="save-hd-rmb"><a href="#">HD (1920 x 1080)</a></li>
      <li id="save-4k-rmb"><a href="#">4K (3840 x 2160)</a></li>
      <li id="save-8k-rmb"><a href="#">8K (7680 x 4320)</a></li>
    </ul>
  `;

  saveHiResPhoto = (resolution: string) => {
    switch (resolution) {
      case 'hd':
        settingsManager.hiResWidth = 1920;
        settingsManager.hiResHeight = 1080;
        break;
      case '4k':
        settingsManager.hiResWidth = 3840;
        settingsManager.hiResHeight = 2160;
        break;
      case '8k':
        settingsManager.hiResWidth = 7680;
        settingsManager.hiResHeight = 4320;
        break;
      default:
        break;
    }

    this.queuedScreenshot_ = true;
  };

  addJs(): void {
    super.addJs();
    keepTrackApi.on(
      KeepTrackApiEvents.altCanvasResize,
      () => this.queuedScreenshot_,
    );

    keepTrackApi.on(
      KeepTrackApiEvents.endOfDraw,
      () => {
        if (this.queuedScreenshot_) {
          this.takeScreenShot();
        }
      },
    );
  }

  private queuedScreenshot_ = false;

  /**
   * Take a screenshot of the current canvas
   *
   * Canvas is autoresized if queuedScreenshot_ is true at the start of the draw loop.
   * Screenshot is then taken at the end of the draw loop
   */
  takeScreenShot() {
    const link = document.createElement('a');

    link.download = 'SpaceDefense.png';

    link.href = this.watermarkedDataUrl_();
    link.click();
    this.queuedScreenshot_ = false;
  }

  private watermarkedDataUrl_() {
    const canvas = keepTrackApi.getRenderer().domElement;

    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');

    if (!tempCtx) {
      errorManagerInstance.warn('无法获取临时画布的2D上下文，无法创建屏幕截图。');

      return '';
    }


    const cw = tempCanvas.width;
    const ch = tempCanvas.height;

    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;

    const logoHeight = 200;
    let logoWidth: number; // with will be calculated based on height
    const padding = 50;

    tempCtx.drawImage(canvas, 0, 0);

    if (settingsManager.isShowSecondaryLogo && this.secondaryLogo) {
      // Draw secondary logo on the left
      logoWidth = this.secondaryLogo.width * (logoHeight / this.secondaryLogo.height);
      tempCtx.drawImage(this.secondaryLogo, padding, canvas.height - logoHeight - padding, logoWidth, logoHeight);
      // Draw primary logo to the right of secondary logo
      logoWidth = this.logo.width * (logoHeight / this.logo.height);
      tempCtx.drawImage(this.logo, padding + logoWidth + padding, canvas.height - logoHeight - padding, logoWidth, logoHeight);
    } else {
      // Draw only primary logo on the right
      logoWidth = this.logo.width * (logoHeight / this.logo.height);
      tempCtx.drawImage(this.logo, canvas.width - logoWidth - padding, canvas.height - logoHeight - padding, logoWidth, logoHeight);
    }

    const { classificationstr, classificationColor } = Screenshot.calculateClassificationText_();

    if (classificationstr !== '') {
      tempCtx.font = '24px nasalization';
      tempCtx.globalAlpha = 1.0;

      tempCtx.fillStyle = classificationColor;

      const textWidth = tempCtx.measureText(classificationstr).width;

      tempCtx.fillText(classificationstr, cw / 2 - textWidth, ch - 20);
      tempCtx.fillText(classificationstr, cw / 2 - textWidth, 34);
    }

    keepTrackApi.containerRoot.appendChild(tempCanvas);
    const image = tempCanvas.toDataURL();

    tempCanvas.parentNode!.removeChild(tempCanvas);

    return image;
  }

  private static calculateClassificationText_(): { classificationstr: string; classificationColor: string } {
    if (settingsManager.classificationStr === '') {
      return { classificationstr: '', classificationColor: '' };
    }

    return {
      classificationstr: settingsManager.classificationStr ?? '',
      classificationColor: Classification.getColors(settingsManager.classificationStr).backgroundColor,
    };

  }
}

