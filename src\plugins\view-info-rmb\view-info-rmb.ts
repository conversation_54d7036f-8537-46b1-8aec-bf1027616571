import { keepTrack<PERSON><PERSON> } from '@app/keepTrackApi';

import { GetSatType, KeepTrackApiEvents, ToastMsgType } from '@app/interfaces';
import { openColorbox } from '@app/lib/colorbox';
import { hideEl } from '@app/lib/get-el';
import { LaunchSite } from '@app/singletons/catalog-manager/LaunchFacility';
import { errorManagerInstance } from '@app/singletons/errorManager';
import { DetailedSatellite, DetailedSensor, eci2lla } from 'ootk';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { SelectSatManager } from '../select-sat-manager/select-sat-manager';
import { SensorInfoPlugin } from '../sensor/sensor-info-plugin';

export class ViewInfoRmbPlugin extends KeepTrackPlugin {
  readonly id = 'ViewInfoRmbPlugin';
  dependencies_ = [];

  rmbL1ElementName = 'view-rmb';
  rmbL1Html = keepTrackApi.html`<li class="rmb-menu-item" id="view-rmb"><a href="#">显示 &#x27A4;</a></li>`;
  rmbL2ElementName = 'view-rmb-menu';
  rmbL2Html = keepTrackApi.html`
  <ul class='dropdown-contents'>
    <li id="view-info-rmb"><a href="#">地球坐标</a></li>
    <li id="view-sensor-info-rmb"><a href="#">传感器信息</a></li>
    <li id="view-launchsite-info-rmb"><a href="#">发射场信息</a></li>
    <li id="view-sat-info-rmb"><a href="#">卫星信息</a></li>
    <li id="view-related-sats-rmb"><a href="#">相关卫星</a></li>
  </ul>
  `;
  rmbMenuOrder = 1;
  isRmbOnEarth = true;
  isRmbOffEarth = true;
  isRmbOnSat = true;

  rmbCallback = (targetId: string, clickedSat?: number): void => {
    switch (targetId) {
      case 'view-info-rmb':
        {
          let latLon = keepTrackApi.getInputManager().mouse.latLon;
          const dragPosition = keepTrackApi.getInputManager().mouse.dragPosition;

          if (typeof latLon === 'undefined' || isNaN(latLon.lat) || isNaN(latLon.lon)) {
            errorManagerInstance.debug('latLon undefined!');
            const gmst = keepTrackApi.getTimeManager().gmst;

            latLon = eci2lla({ x: dragPosition[0], y: dragPosition[1], z: dragPosition[2] }, gmst);
          }
          keepTrackApi.toast(`纬度: ${latLon.lat.toFixed(5)}<br>经度: ${latLon.lon.toFixed(5)}`, ToastMsgType.standby, true);
        }
        break;
      case 'view-sat-info-rmb':
        keepTrackApi.getPlugin(SelectSatManager)?.selectSat(clickedSat ?? -1);
        break;
      case 'view-sensor-info-rmb':
        this.viewSensorInfoRmb(clickedSat);
        break;
      case 'view-launchsite-info-rmb':
        {
          const launchSite = keepTrackApi.getCatalogManager().getObject(clickedSat) as LaunchSite;

          if (typeof launchSite === 'undefined' || launchSite === null) {
            errorManagerInstance.warn('未找到发射场信息');

            return;
          }

          if (launchSite.wikiUrl) {
            openColorbox(launchSite.wikiUrl);
          }
        }
        break;
      case 'view-related-sats-rmb':
        {
          const intldes = keepTrackApi.getCatalogManager().getSat(clickedSat ?? -1, GetSatType.EXTRA_ONLY)?.intlDes;

          if (!intldes) {
            keepTrackApi.toast('Time 1 is Invalid!', ToastMsgType.serious);
          }
          const searchStr = intldes?.slice(0, 8) ?? '';

          keepTrackApi.getUiManager().doSearch(searchStr);
        }
        break;
      default:
        break;
    }
  };

  addJs() {
    super.addJs();

    keepTrackApi.on(KeepTrackApiEvents.rightBtnMenuOpen, (_isEarth, clickedSatId) => {
      if (typeof clickedSatId === 'undefined') {
        return;
      }
      const sat = keepTrackApi.getCatalogManager().getObject(clickedSatId);

      if (typeof sat === 'undefined' || sat === null) {
        return;
      }

      if (sat instanceof DetailedSatellite === false) {
        hideEl('view-sat-info-rmb');
        hideEl('view-related-sats-rmb');
      }

      if (sat instanceof DetailedSensor === false) {
        hideEl('view-sensor-info-rmb');
      }

      if (sat instanceof LaunchSite === false) {
        hideEl('view-launchsite-info-rmb');
      }
    });
  }

  viewSensorInfoRmb(clickedSat?: number): void {
    keepTrackApi.getPlugin(SelectSatManager)?.selectSat(clickedSat ?? -1);

    const sensorInfoPluginInstance = keepTrackApi.getPlugin(SensorInfoPlugin);

    if (!sensorInfoPluginInstance) {
      return;
    }
    sensorInfoPluginInstance.setBottomIconToSelected();
    if (!sensorInfoPluginInstance.isMenuButtonActive) {
      sensorInfoPluginInstance.openSideMenu();
    }
    sensorInfoPluginInstance.getSensorInfo();
  }
}
