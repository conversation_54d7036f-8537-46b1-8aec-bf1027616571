/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * sat-math-api.ts contains the `SatMathApi` singleton class, which provides a
 * wrapper around the `SatMath` class. It is used to provide a more convenient
 * interface for the `SatMath` class.
 * https://spacedefense
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { Degrees, DetailedSatellite, Kilometers } from 'ootk';
import { keepTrackApi } from '../keepTrackApi';
import { SatMath } from '../static/sat-math';

/**
 * `SatMathApi` is a singleton class that provides a wrapper around the `SatMath` class.
 * It is used to provide a more convenient interface for the `SatMath` class.
 *
 * All access to `SatMath` using singletons from `keepTrackContainer` should be done
 * through this class.
 */
export class SatMathApi {
  static getEcfOfCurrentOrbit(sat: DetailedSatellite, points: number) {
    const cb = (offset: number) => keepTrackApi.getTimeManager().getOffsetTimeObj(offset);


    return SatMath.getEcfOfCurrentOrbit(sat, points, cb);
  }

  static getEciOfCurrentOrbit(sat: DetailedSatellite, points: number) {
    const cb = (offset: number) => keepTrackApi.getTimeManager().getOffsetTimeObj(offset);


    return SatMath.getEciOfCurrentOrbit(sat, points, cb);
  }

  /**
   * Retrieves the LLA (Latitude, Longitude, Altitude) coordinates of the current orbit for a given satellite.
   *
   * @param sat - The detailed satellite object.
   * @param points - The number of points to calculate along the orbit.
   * @returns An array of LLA coordinates representing the current orbit in latitude, longitude, altitude, and time.
   */
  static getLlaOfCurrentOrbit(sat: DetailedSatellite, points: number) {
    const cb = (offset: number) => keepTrackApi.getTimeManager().getOffsetTimeObj(offset);


    return SatMath.getLlaOfCurrentOrbit(sat, points, cb) as { lat: Degrees; lon: Degrees; alt: Kilometers; time: number }[];
  }

  static getRicOfCurrentOrbit(sat: DetailedSatellite, sat2: DetailedSatellite, points: number, orbits = 1) {
    const cb = (offset: number) => keepTrackApi.getTimeManager().getOffsetTimeObj(offset);


    return SatMath.getRicOfCurrentOrbit(sat, sat2, points, cb, orbits);
  }
}

export const satMathApi = new SatMathApi();
