# 管理后台故障排除指南

## 🔍 问题诊断步骤

### 1. 基础检查

#### 检查服务器状态
```bash
# 确保API服务器正在运行
npm run start:api

# 确保Web服务器正在运行
npm start
```

#### 检查端口占用
- API服务器：http://localhost:3001
- Web服务器：http://localhost:8080

### 2. 使用测试页面

#### 访问测试页面
1. **基础测试**: http://localhost:8080/admin-test.html
2. **调试版后台**: http://localhost:8080/admin-debug.html

#### 测试步骤
1. 打开 `admin-test.html`
2. 按顺序点击所有测试按钮
3. 查看测试结果
4. 如果所有测试通过，点击"进入管理后台"

### 3. 浏览器调试

#### 打开开发者工具
- 按 F12 或右键选择"检查"
- 切换到 Console 标签页
- 查看是否有错误信息

#### 常见错误及解决方案

**错误1: 网络请求失败**
```
Failed to fetch
```
**解决方案**: 检查API服务器是否启动

**错误2: 认证失败**
```
401 Unauthorized
```
**解决方案**: 重新登录获取有效令牌

**错误3: 权限不足**
```
403 Forbidden
```
**解决方案**: 确保使用管理员账户登录

**错误4: 脚本加载失败**
```
Failed to load resource: js/admin.js
```
**解决方案**: 检查文件路径和Web服务器

### 4. 手动登录测试

#### 使用默认管理员账户
1. 访问: http://localhost:8080/login.html
2. 用户名: `admin`
3. 密码: `SpaceDefense2025!`
4. 登录成功后访问管理后台

#### 检查本地存储
在浏览器控制台中运行：
```javascript
// 检查认证令牌
console.log('Token:', localStorage.getItem('authToken'));

// 检查用户信息
console.log('User:', localStorage.getItem('user'));

// 手动设置令牌（如果需要）
// localStorage.setItem('authToken', 'your-token-here');
```

### 5. API测试

#### 直接测试API端点
在浏览器控制台中运行：

```javascript
// 测试登录
fetch('http://localhost:3001/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        username: 'admin',
        password: 'SpaceDefense2025!'
    })
})
.then(r => r.json())
.then(console.log)
.catch(console.error);

// 测试用户列表（需要先登录获取token）
const token = localStorage.getItem('authToken');
fetch('http://localhost:3001/api/auth/users', {
    headers: { 'Authorization': `Bearer ${token}` }
})
.then(r => r.json())
.then(console.log)
.catch(console.error);
```

## 🛠️ 常见问题解决

### 问题1: 页面空白或无响应

**可能原因**:
- JavaScript错误
- API服务器未启动
- 认证失败

**解决步骤**:
1. 检查浏览器控制台错误
2. 确认API服务器运行状态
3. 重新登录获取有效令牌
4. 使用调试版页面测试

### 问题2: 数据无法加载

**可能原因**:
- 网络连接问题
- API权限问题
- 数据文件损坏

**解决步骤**:
1. 检查网络连接
2. 验证管理员权限
3. 检查data目录权限
4. 重启API服务器

### 问题3: 登录后仍然跳转到登录页

**可能原因**:
- 令牌验证失败
- 用户角色不是管理员
- 本地存储问题

**解决步骤**:
1. 清除浏览器缓存和本地存储
2. 重新登录
3. 检查用户角色设置

### 问题4: 功能按钮无响应

**可能原因**:
- JavaScript函数未定义
- 事件监听器未绑定
- 权限不足

**解决步骤**:
1. 检查控制台JavaScript错误
2. 刷新页面重新加载脚本
3. 验证用户权限

## 📋 检查清单

### 启动前检查
- [ ] Node.js已安装
- [ ] 项目依赖已安装 (`npm install`)
- [ ] 项目已构建 (`npm run build`)
- [ ] 端口3001和8080未被占用

### 运行时检查
- [ ] API服务器正在运行 (http://localhost:3001)
- [ ] Web服务器正在运行 (http://localhost:8080)
- [ ] 登录页面可以访问
- [ ] 默认管理员账户可以登录

### 功能检查
- [ ] 管理后台页面可以加载
- [ ] 用户列表可以显示
- [ ] 统计数据正确显示
- [ ] 标签页切换正常
- [ ] 搜索功能正常

## 🔧 高级调试

### 启用详细日志
在admin.js开头添加：
```javascript
window.DEBUG = true;
console.log('Debug mode enabled');
```

### 网络请求监控
在浏览器开发者工具中：
1. 切换到 Network 标签页
2. 刷新页面
3. 查看所有网络请求状态

### 本地存储检查
在Application标签页中：
1. 展开Local Storage
2. 查看authToken和user数据
3. 必要时清除数据重新登录

## 📞 获取帮助

如果以上步骤都无法解决问题：

1. **收集信息**:
   - 浏览器类型和版本
   - 控制台错误信息
   - 网络请求状态
   - 操作系统信息

2. **联系支持**:
   - 邮箱: <EMAIL>
   - 提供详细的错误描述和截图

3. **临时解决方案**:
   - 使用调试版管理后台
   - 直接通过API进行管理操作
   - 重新部署系统

---

**注意**: 在生产环境中，请确保移除所有调试代码和测试页面。
