/**
 * 定时器管理器 - 优化系统资源使用
 * 替代分散的setInterval和setTimeout调用
 * 创建时间: 2025-01-28
 */

export interface TimerTask {
  id: string;
  callback: () => void;
  interval: number;
  lastRun: number;
  enabled: boolean;
  priority: 'high' | 'medium' | 'low';
}

export class TimerManager {
  private static instance: TimerManager;
  private tasks: Map<string, TimerTask> = new Map();
  private rafId: number | null = null;
  private isRunning = false;

  // 性能监控
  private frameCount = 0;
  private lastFpsCheck = 0;
  private currentFps = 60;

  private constructor() {
    // 🔥 优化：不自动启动，避免与主游戏循环冲突
    // this.start();
  }

  static getInstance(): TimerManager {
    if (!TimerManager.instance) {
      TimerManager.instance = new TimerManager();
    }
    return TimerManager.instance;
  }

  /**
   * 添加定时任务
   */
  addTask(
    id: string,
    callback: () => void,
    interval: number,
    priority: 'high' | 'medium' | 'low' = 'medium'
  ): void {
    this.tasks.set(id, {
      id,
      callback,
      interval,
      lastRun: 0,
      enabled: true,
      priority
    });

    // 🔥 优化：只在有任务时启动定时器管理器
    this.startIfNeeded();
  }

  /**
   * 移除定时任务
   */
  removeTask(id: string): void {
    this.tasks.delete(id);

    // 🔥 优化：如果没有任务了，自动停止定时器管理器
    if (this.tasks.size === 0 && this.isRunning) {
      this.stop();
    }
  }

  /**
   * 启用/禁用任务
   */
  setTaskEnabled(id: string, enabled: boolean): void {
    const task = this.tasks.get(id);
    if (task) {
      task.enabled = enabled;
    }
  }

  /**
   * 主循环 - 使用requestAnimationFrame优化
   */
  private loop = (currentTime: number): void => {
    if (!this.isRunning) return;

    // 计算FPS
    this.updateFps(currentTime);

    // 根据FPS动态调整任务执行频率
    const shouldRunTasks = this.shouldRunTasks(currentTime);

    if (shouldRunTasks) {
      this.executeTasks(currentTime);
    }

    this.rafId = requestAnimationFrame(this.loop);
  };

  /**
   * 更新FPS计算
   */
  private updateFps(currentTime: number): void {
    this.frameCount++;
    if (currentTime - this.lastFpsCheck >= 1000) {
      this.currentFps = this.frameCount;
      this.frameCount = 0;
      this.lastFpsCheck = currentTime;
    }
  }

  /**
   * 根据性能决定是否执行任务
   */
  private shouldRunTasks(currentTime: number): boolean {
    // 如果FPS过低，降低任务执行频率
    if (this.currentFps < 30) {
      return currentTime % 4 === 0; // 每4帧执行一次
    } else if (this.currentFps < 45) {
      return currentTime % 2 === 0; // 每2帧执行一次
    }
    return true; // 正常执行
  }

  /**
   * 执行到期的任务
   */
  private executeTasks(currentTime: number): void {
    const sortedTasks = Array.from(this.tasks.values())
      .filter(task => task.enabled)
      .sort((a, b) => {
        // 按优先级排序
        const priorityOrder = { high: 0, medium: 1, low: 2 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });

    for (const task of sortedTasks) {
      if (currentTime - task.lastRun >= task.interval) {
        try {
          task.callback();
          task.lastRun = currentTime;
        } catch (error) {
          console.warn(`定时任务 ${task.id} 执行失败:`, error);
        }
      }
    }
  }

  /**
   * 启动定时器管理器
   */
  start(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    this.rafId = requestAnimationFrame(this.loop);
    console.log('🔧 定时器管理器已启动');
  }

  /**
   * 🔥 优化：条件启动 - 只在有任务时启动
   */
  startIfNeeded(): void {
    if (this.tasks.size > 0 && !this.isRunning) {
      this.start();
    }
  }

  /**
   * 停止定时器管理器
   */
  stop(): void {
    this.isRunning = false;
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }
    console.log('🔧 定时器管理器已停止');
  }

  /**
   * 获取性能统计
   */
  getStats(): {
    fps: number;
    taskCount: number;
    enabledTasks: number;
  } {
    return {
      fps: this.currentFps,
      taskCount: this.tasks.size,
      enabledTasks: Array.from(this.tasks.values()).filter(t => t.enabled).length
    };
  }

  /**
   * 清理所有任务
   */
  clear(): void {
    this.tasks.clear();
  }
}

// 导出单例实例
export const timerManager = TimerManager.getInstance();

/**
 * 便捷函数：添加定时任务
 */
export function addTimerTask(
  id: string,
  callback: () => void,
  interval: number,
  priority: 'high' | 'medium' | 'low' = 'medium'
): void {
  timerManager.addTask(id, callback, interval, priority);
}

/**
 * 便捷函数：移除定时任务
 */
export function removeTimerTask(id: string): void {
  timerManager.removeTask(id);
}

/**
 * 便捷函数：替代setInterval
 */
export function optimizedInterval(
  callback: () => void,
  interval: number,
  id?: string,
  priority: 'high' | 'medium' | 'low' = 'medium'
): string {
  const taskId = id || `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  timerManager.addTask(taskId, callback, interval, priority);
  return taskId;
}

/**
 * 便捷函数：替代setTimeout（一次性任务）
 */
export function optimizedTimeout(
  callback: () => void,
  delay: number,
  id?: string
): string {
  const taskId = id || `timeout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  const wrappedCallback = () => {
    callback();
    timerManager.removeTask(taskId); // 执行后自动移除
  };
  
  timerManager.addTask(taskId, wrappedCallback, delay, 'high');
  return taskId;
}
