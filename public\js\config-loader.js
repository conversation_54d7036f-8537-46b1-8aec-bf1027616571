/**
 * 统一配置加载器
 * 负责从配置文件加载API地址等配置信息
 */

class ConfigLoader {
    constructor() {
        this.config = null;
        this.loaded = false;
        this.loading = false;
        this.loadPromise = null;
    }

    /**
     * 加载配置文件
     */
    async loadConfig() {
        if (this.loaded) {
            return this.config;
        }

        if (this.loading) {
            return this.loadPromise;
        }

        this.loading = true;
        this.loadPromise = this._doLoadConfig();
        
        try {
            this.config = await this.loadPromise;
            this.loaded = true;
            console.log('✅ 配置加载成功:', this.config);
            return this.config;
        } catch (error) {
            console.warn('⚠️ 配置加载失败，使用默认配置:', error.message);
            this.config = this._getDefaultConfig();
            this.loaded = true;
            return this.config;
        } finally {
            this.loading = false;
        }
    }

    /**
     * 实际加载配置的方法
     */
    async _doLoadConfig() {
        const configPaths = [
            '/config.json',
            '/api/config.json',
            '/src/api/config.json'
        ];

        let lastError = null;

        for (const path of configPaths) {
            try {
                console.log(`🔍 尝试加载配置: ${path}`);
                const response = await fetch(path);
                
                if (response.ok) {
                    const config = await response.json();
                    console.log(`✅ 成功加载配置: ${path}`);
                    return this._processConfig(config);
                }
            } catch (error) {
                console.log(`❌ 加载配置失败 ${path}:`, error.message);
                lastError = error;
            }
        }

        throw lastError || new Error('所有配置路径都无法访问');
    }

    /**
     * 处理配置，确保格式正确
     */
    _processConfig(rawConfig) {
        const config = {
            apiServer: rawConfig.apiServer || {},
            endpoints: rawConfig.endpoints || {},
            frontend: rawConfig.frontend || {}
        };

        // 确保API服务器配置完整
        if (!config.apiServer.url && config.apiServer.host && config.apiServer.port) {
            const protocol = window.location.protocol;
            config.apiServer.url = `${protocol}//${config.apiServer.host}:${config.apiServer.port}`;
        }

        // 如果没有配置URL，使用当前主机
        if (!config.apiServer.url) {
            const currentHost = window.location.hostname;
            const protocol = window.location.protocol;
            const port = config.apiServer.port || 5001;
            config.apiServer.url = `${protocol}//${currentHost}:${port}`;
        }

        return config;
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        const currentHost = window.location.hostname;
        const protocol = window.location.protocol;
        const port = 5001;

        return {
            apiServer: {
                host: currentHost,
                port: port,
                url: `${protocol}//${currentHost}:${port}`
            },
            endpoints: {
                auth: '/api/auth',
                health: '/api/health',
                config: '/api/config.json',
                esHistory: '/api/es-history',
                satelliteHistory: '/api/satellite-history'
            },
            frontend: {
                defaultPort: 8080,
                fallbackHosts: ['localhost', '127.0.0.1']
            }
        };
    }

    /**
     * 获取API基础URL
     */
    async getApiBaseUrl() {
        const config = await this.loadConfig();
        return config.apiServer.url;
    }

    /**
     * 获取完整的API端点URL
     */
    async getApiUrl(endpoint = 'auth') {
        const config = await this.loadConfig();
        const baseUrl = config.apiServer.url;
        const endpointPath = config.endpoints[endpoint] || `/api/${endpoint}`;
        return `${baseUrl}${endpointPath}`;
    }

    /**
     * 获取健康检查URL
     */
    async getHealthUrl() {
        return this.getApiUrl('health');
    }

    /**
     * 获取认证URL
     */
    async getAuthUrl() {
        return this.getApiUrl('auth');
    }

    /**
     * 获取历史数据URL
     */
    async getHistoryUrl(type = 'satelliteHistory') {
        return this.getApiUrl(type);
    }

    /**
     * 检查API服务器是否可用
     */
    async checkApiHealth() {
        try {
            const healthUrl = await this.getHealthUrl();
            const response = await fetch(healthUrl, {
                method: 'GET',
                timeout: 5000
            });
            
            return {
                available: response.ok,
                status: response.status,
                url: healthUrl
            };
        } catch (error) {
            return {
                available: false,
                error: error.message,
                url: await this.getHealthUrl()
            };
        }
    }

    /**
     * 重新加载配置
     */
    async reloadConfig() {
        this.loaded = false;
        this.loading = false;
        this.loadPromise = null;
        this.config = null;
        return this.loadConfig();
    }
}

// 创建全局配置实例
window.configLoader = new ConfigLoader();

// 兼容性方法 - 供现有代码使用
window.getApiBaseUrl = async function() {
    return window.configLoader.getApiBaseUrl();
};

window.getApiUrl = async function(endpoint) {
    return window.configLoader.getApiUrl(endpoint);
};

// 导出配置加载器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ConfigLoader;
}

console.log('🔧 统一配置加载器已初始化');
