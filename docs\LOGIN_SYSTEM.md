# 太空物体模拟平台 - 登录系统文档

## 概述

本系统为太空物体模拟平台添加了完整的用户认证和管理功能，包括：

- 用户登录/注册
- 管理员审批机制
- 完整的管理后台
- 安全防护措施
- 登录失败保护

## 功能特性

### 🔐 安全特性

1. **密码安全**
   - 强密码策略（至少8位，包含大小写字母、数字和特殊字符）
   - 密码哈希存储（PBKDF2）
   - 首次登录强制修改密码

2. **登录保护**
   - 5次失败后锁定1分钟
   - IP地址限制（1小时内最多10次失败）
   - JWT令牌认证

3. **输入安全**
   - 用户输入清理和验证
   - XSS防护
   - CSRF保护

### 👥 用户管理

1. **注册流程**
   - 用户提交注册申请
   - 管理员审批
   - 邮箱验证

2. **角色权限**
   - 管理员：完全访问权限
   - 普通用户：基础功能访问

### 🛡️ 管理后台

- 用户管理（启用/禁用、角色变更、删除）
- 注册审批
- 登录日志查看
- 系统统计

## 快速开始

### 1. 安装依赖

```bash
npm install express-rate-limit
```

### 2. 启动系统

```bash
# 方式1：使用启动脚本
node scripts/start-with-auth.js

# 方式2：手动启动
npm run build
npx tsx src/api/server.ts &
npx serve ./dist -l 8080
```

### 3. 首次登录

1. 访问 http://localhost:8080
2. 系统会自动跳转到登录页面
3. 使用默认管理员账户：
   - 用户名：`admin`
   - 密码：`SpaceDefense2025!`
4. 首次登录后会要求修改密码

## 页面说明

### 登录页面 (`/login.html`)

- **功能**：用户登录和注册
- **特性**：
  - 登录失败限制
  - 密码强度检查
  - 响应式设计
  - 星空背景动画

### 管理后台 (`/admin.html`)

- **访问权限**：仅管理员
- **功能模块**：
  - 用户管理
  - 注册审批
  - 登录日志
  - 系统统计

### 主页面 (`/`)

- **访问控制**：需要登录
- **认证检查**：自动验证登录状态
- **密码策略**：强制修改默认密码

## API接口

### 认证相关

```
POST /api/auth/login          # 用户登录
POST /api/auth/register       # 注册申请
POST /api/auth/verify         # 验证令牌
POST /api/auth/change-password # 修改密码
```

### 管理功能（需要管理员权限）

```
GET  /api/auth/users                    # 获取用户列表
GET  /api/auth/pending-registrations    # 获取待审批注册
POST /api/auth/approve-registration/:id # 批准注册
POST /api/auth/reject-registration/:id  # 拒绝注册
POST /api/auth/toggle-user/:id          # 切换用户状态
POST /api/auth/update-role/:id          # 更新用户角色
DELETE /api/auth/users/:id              # 删除用户
GET  /api/auth/login-attempts           # 获取登录日志
```

## 数据存储

### 文件结构

```
data/
├── users.json           # 用户数据
├── registrations.json   # 注册申请
└── login-attempts.json  # 登录记录
```

### 用户数据结构

```json
{
  "id": "uuid",
  "username": "string",
  "password": "hashed_password",
  "role": "admin|user",
  "email": "string",
  "createdAt": "ISO_date",
  "lastLogin": "ISO_date",
  "isActive": "boolean",
  "failedLoginAttempts": "number",
  "lockedUntil": "ISO_date",
  "needsPasswordChange": "boolean"
}
```

## 安全配置

### 环境变量

创建 `.env` 文件：

```env
JWT_SECRET=your-super-secret-jwt-key
PORT=3001
NODE_ENV=production
LOGIN_LOCKOUT_TIME=1
MAX_LOGIN_ATTEMPTS=5
```

### 生产环境建议

1. **HTTPS配置**
   - 使用SSL证书
   - 强制HTTPS重定向

2. **数据库**
   - 替换文件存储为数据库
   - 定期备份

3. **监控**
   - 登录失败监控
   - 异常访问检测

## 故障排除

### 常见问题

1. **无法访问管理后台**
   - 检查用户角色是否为管理员
   - 确认登录状态

2. **登录失败**
   - 检查用户名密码
   - 确认账户未被锁定
   - 查看登录日志

3. **注册申请无响应**
   - 检查网络连接
   - 确认API服务器运行状态

### 日志查看

```bash
# 查看API服务器日志
tail -f logs/api.log

# 查看登录记录
cat data/login-attempts.json | jq '.'
```

## 安全检查

运行安全检查脚本：

```bash
node scripts/security-check.js
```

## 联系信息

- **公司**：北京星地探索科技有限公司
- **邮箱**：<EMAIL>
- **网站**：www.spacedefense.cn

---

**注意**：首次部署后请立即修改默认密码并配置适当的安全策略。
