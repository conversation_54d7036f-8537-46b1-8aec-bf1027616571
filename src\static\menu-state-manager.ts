/**
 * 菜单状态管理器 - 解决底部菜单状态同步问题
 * 创建时间: 2025-01-30
 */

export class MenuStateManager {
  private static instance: MenuStateManager;
  private activeMenus: Set<string> = new Set();
  private menuStates: Map<string, boolean> = new Map();
  private lastStateChange = 0;
  private readonly stateChangeDebounce = 100; // 100ms防抖

  static getInstance(): MenuStateManager {
    if (!MenuStateManager.instance) {
      MenuStateManager.instance = new MenuStateManager();
    }
    return MenuStateManager.instance;
  }

  /**
   * 设置菜单状态
   */
  setMenuState(menuId: string, isActive: boolean): void {
    const currentTime = Date.now();
    
    // 防抖：避免频繁状态变更
    if (currentTime - this.lastStateChange < this.stateChangeDebounce) {
      return;
    }
    
    this.lastStateChange = currentTime;
    
    if (isActive) {
      this.activeMenus.add(menuId);
      this.menuStates.set(menuId, true);
    } else {
      this.activeMenus.delete(menuId);
      this.menuStates.set(menuId, false);
    }
    
    // 同步DOM状态
    this.syncDOMState(menuId, isActive);
  }

  /**
   * 获取菜单状态
   */
  getMenuState(menuId: string): boolean {
    return this.menuStates.get(menuId) ?? false;
  }

  /**
   * 关闭所有菜单
   */
  closeAllMenus(): void {
    this.activeMenus.forEach(menuId => {
      this.setMenuState(menuId, false);
    });
    this.activeMenus.clear();
  }

  /**
   * 获取当前活跃的菜单
   */
  getActiveMenus(): string[] {
    return Array.from(this.activeMenus);
  }

  /**
   * 检查是否有活跃的菜单
   */
  hasActiveMenus(): boolean {
    return this.activeMenus.size > 0;
  }

  /**
   * 同步DOM状态
   */
  private syncDOMState(menuId: string, isActive: boolean): void {
    const menuElement = document.getElementById(menuId);
    if (menuElement) {
      if (isActive) {
        menuElement.classList.add('bmenu-item-selected');
        menuElement.classList.remove('bmenu-item-unselected');
      } else {
        menuElement.classList.remove('bmenu-item-selected');
        menuElement.classList.add('bmenu-item-unselected');
      }
    }
  }

  /**
   * 强制同步所有菜单状态
   */
  forceSyncAllStates(): void {
    this.menuStates.forEach((isActive, menuId) => {
      this.syncDOMState(menuId, isActive);
    });
  }

  /**
   * 重置所有状态
   */
  reset(): void {
    this.activeMenus.clear();
    this.menuStates.clear();
    this.lastStateChange = 0;
  }

  /**
   * 切换菜单状态（用于点击事件）
   * 🔥 简化逻辑：系统本身就是单菜单设计，不需要手动关闭其他菜单
   */
  toggleMenuState(menuId: string): boolean {
    const currentState = this.getMenuState(menuId);
    const newState = !currentState;

    // 🔥 直接切换状态，让系统的hideSideMenus机制处理其他菜单的关闭
    this.setMenuState(menuId, newState);
    return newState;
  }

  /**
   * 批量更新菜单状态
   */
  batchUpdateStates(updates: Array<{menuId: string, isActive: boolean}>): void {
    updates.forEach(({menuId, isActive}) => {
      if (isActive) {
        this.activeMenus.add(menuId);
        this.menuStates.set(menuId, true);
      } else {
        this.activeMenus.delete(menuId);
        this.menuStates.set(menuId, false);
      }
      this.syncDOMState(menuId, isActive);
    });
  }

  /**
   * 检查菜单冲突
   */
  checkConflicts(menuId: string): string[] {
    const conflicts: string[] = [];
    
    // 检查是否有其他活跃的菜单
    this.activeMenus.forEach(activeMenuId => {
      if (activeMenuId !== menuId) {
        conflicts.push(activeMenuId);
      }
    });
    
    return conflicts;
  }

  /**
   * 获取状态调试信息
   */
  getDebugInfo(): object {
    return {
      activeMenus: Array.from(this.activeMenus),
      menuStates: Object.fromEntries(this.menuStates),
      lastStateChange: this.lastStateChange,
      hasActiveMenus: this.hasActiveMenus()
    };
  }
}

// 导出单例实例
export const menuStateManager = MenuStateManager.getInstance();
