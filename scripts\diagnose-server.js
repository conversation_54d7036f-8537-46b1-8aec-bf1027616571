#!/usr/bin/env node

/**
 * 服务器诊断脚本
 * 检查API服务器的状态和配置
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🔍 API服务器诊断工具\n');

// 检查服务器文件是否存在
function checkServerFiles() {
  console.log('📁 检查服务器文件...');
  
  const requiredFiles = [
    'src/api/server.ts',
    'src/api/auth.routes.ts',
    'src/auth/auth.service.ts'
  ];
  
  let allFilesExist = true;
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - 文件不存在`);
      allFilesExist = false;
    }
  });
  
  return allFilesExist;
}

// 检查端口是否被占用
function checkPort(port) {
  return new Promise((resolve) => {
    const server = http.createServer();
    
    server.listen(port, () => {
      server.close(() => {
        resolve(false); // 端口可用
      });
    });
    
    server.on('error', () => {
      resolve(true); // 端口被占用
    });
  });
}

// 测试API端点
function testEndpoint(url) {
  return new Promise((resolve) => {
    const request = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          success: true,
          statusCode: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    });
    
    request.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });
    
    request.setTimeout(5000, () => {
      request.destroy();
      resolve({
        success: false,
        error: '请求超时'
      });
    });
  });
}

// 主诊断函数
async function diagnose() {
  // 1. 检查文件
  const filesOk = checkServerFiles();
  if (!filesOk) {
    console.log('\n❌ 缺少必要文件，请检查项目完整性');
    return;
  }
  
  console.log('\n🔌 检查端口状态...');
  
  // 2. 检查端口5001
  const port5001InUse = await checkPort(5001);
  if (port5001InUse) {
    console.log('✅ 端口5001被占用（服务器可能正在运行）');
  } else {
    console.log('❌ 端口5001未被占用（服务器未运行）');
    console.log('\n💡 请先启动API服务器：');
    console.log('   npm run start:api');
    console.log('   或');
    console.log('   npx tsx src/api/server.ts');
    return;
  }
  
  console.log('\n🌐 测试API端点...');
  
  // 3. 测试根路径
  console.log('测试 http://localhost:5001/');
  const rootTest = await testEndpoint('http://localhost:5001/');
  if (rootTest.success) {
    console.log(`✅ 根路径响应: ${rootTest.statusCode}`);
    console.log(`📄 响应内容: ${rootTest.data}`);
    console.log(`🏷️  Content-Type: ${rootTest.headers['content-type']}`);
  } else {
    console.log(`❌ 根路径测试失败: ${rootTest.error}`);
  }
  
  // 4. 测试健康检查端点
  console.log('\n测试 http://localhost:5001/api/health');
  const healthTest = await testEndpoint('http://localhost:5001/api/health');
  if (healthTest.success) {
    console.log(`✅ 健康检查响应: ${healthTest.statusCode}`);
    console.log(`📄 响应内容: ${healthTest.data}`);
  } else {
    console.log(`❌ 健康检查测试失败: ${healthTest.error}`);
  }
  
  // 5. 测试认证端点
  console.log('\n测试 http://localhost:5001/api/auth (POST请求会失败，但应该返回405或400)');
  const authTest = await testEndpoint('http://localhost:5001/api/auth');
  if (authTest.success) {
    console.log(`✅ 认证端点响应: ${authTest.statusCode}`);
    console.log(`📄 响应内容: ${authTest.data}`);
  } else {
    console.log(`❌ 认证端点测试失败: ${authTest.error}`);
  }
  
  console.log('\n📊 诊断完成！');
  
  // 6. 给出建议
  console.log('\n💡 建议：');
  if (rootTest.success && rootTest.data.includes('太空物体模拟平台API服务')) {
    console.log('✅ API服务器运行正常');
  } else if (rootTest.success && rootTest.data.includes('statusCode')) {
    console.log('⚠️  端口5001上运行的不是我们的API服务器');
    console.log('   可能是其他应用占用了端口，请检查：');
    console.log('   1. 是否有其他Node.js应用在运行');
    console.log('   2. 是否有其他服务占用5001端口');
    console.log('   3. 尝试更换端口或停止冲突的服务');
  } else {
    console.log('❌ API服务器可能存在问题');
    console.log('   请检查服务器日志或重新启动服务');
  }
}

// 运行诊断
diagnose().catch(console.error);
