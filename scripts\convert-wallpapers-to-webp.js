#!/usr/bin/env node

/**
 * 将壁纸图片从JPG格式转换为WebP格式的脚本
 * 
 * 使用方法：
 * 1. 安装依赖：npm install sharp
 * 2. 运行脚本：node scripts/convert-wallpapers-to-webp.js
 */

const fs = require('fs');
const path = require('path');

// 检查是否安装了sharp
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.error('错误：未找到sharp模块。请先安装：npm install sharp');
  process.exit(1);
}

const wallpaperDir = path.join(__dirname, '../public/img/wallpaper');
const outputDir = wallpaperDir; // 输出到同一目录

// 要转换的JPG文件列表
const jpgFiles = [
  'blue-marble.jpg',
  'cubesat.jpg',
  'Earth.jpg',
  'iss.jpg',
  'moon.jpg',
  'observatory.jpg',
  'rocket.jpg',
  'rocket2.jpg',
  'rocket3.jpg',
  'rocket4.jpg',
  'sat.jpg',
  'sat2.jpg',
  'telescope.jpg',
  'thule.jpg'
];

async function convertToWebP() {
  console.log('开始转换壁纸图片为WebP格式...');
  
  for (const jpgFile of jpgFiles) {
    const inputPath = path.join(wallpaperDir, jpgFile);
    const outputPath = path.join(outputDir, jpgFile.replace('.jpg', '.webp'));
    
    try {
      // 检查输入文件是否存在
      if (!fs.existsSync(inputPath)) {
        console.warn(`警告：文件不存在 ${inputPath}`);
        continue;
      }
      
      // 转换为WebP格式
      await sharp(inputPath)
        .webp({
          quality: 85, // 设置质量为85%，平衡文件大小和质量
          effort: 6    // 压缩努力程度（0-6，6为最高）
        })
        .toFile(outputPath);
      
      // 获取文件大小信息
      const originalSize = fs.statSync(inputPath).size;
      const webpSize = fs.statSync(outputPath).size;
      const savings = ((originalSize - webpSize) / originalSize * 100).toFixed(1);
      
      console.log(`✅ ${jpgFile} -> ${path.basename(outputPath)}`);
      console.log(`   原始大小: ${(originalSize / 1024).toFixed(1)} KB`);
      console.log(`   WebP大小: ${(webpSize / 1024).toFixed(1)} KB`);
      console.log(`   节省空间: ${savings}%\n`);
      
    } catch (error) {
      console.error(`❌ 转换失败 ${jpgFile}:`, error.message);
    }
  }
  
  console.log('转换完成！');
  console.log('\n下一步：');
  console.log('1. 检查生成的WebP文件');
  console.log('2. 运行更新代码脚本：node scripts/update-wallpaper-imports.js');
}

// 创建输出目录（如果不存在）
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 开始转换
convertToWebP().catch(console.error);
