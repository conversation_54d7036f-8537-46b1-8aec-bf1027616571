import { GetSatType, KeepTrackApiEvents, MenuMode, ToastMsgType } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import { hideLoading, showLoadingSticky } from '@app/lib/showLoading';
import { waitForCruncher } from '@app/lib/waitForCruncher';
import rocketLaunchPng from '@public/img/icons/rocket-launch.png';

import { SatMath } from '@app/static/sat-math';

import { launchSites } from '@app/catalogs/launch-sites';
import { t7e } from '@app/locales/keys';
import { CatalogManager } from '@app/singletons/catalog-manager';
import { errorManagerInstance } from '@app/singletons/errorManager';
import { OrbitFinder } from '@app/singletons/orbit-finder';
import { TimeManager } from '@app/singletons/time-manager';
import { PositionCruncherOutgoingMsg } from '@app/webworker/constants';
import { CruncerMessageTypes } from '@app/webworker/positionCruncher';
import { BaseObject, Degrees, DetailedSatellite, DetailedSatelliteParams, EciVec3, FormatTle, KilometersPerSecond, SatelliteRecord, Sgp4, TleLine1 } from 'ootk';
import { ClickDragOptions, KeepTrackPlugin } from '../KeepTrackPlugin';
import { SelectSatManager } from '../select-sat-manager/select-sat-manager';
import { SoundNames } from '../sounds/SoundNames';

export class NewLaunch extends KeepTrackPlugin {
  readonly id = 'NewLaunch';
  dependencies_ = [SelectSatManager.name];
  private readonly selectSatManager_: SelectSatManager;

  constructor() {
    super();
    const selectSatManagerInstance = keepTrackApi.getPlugin(SelectSatManager);

    if (!selectSatManagerInstance) {
      throw new Error('SelectSatManager not found');
    }
    this.selectSatManager_ = selectSatManagerInstance;
  }

  bottomIconCallback = () => {
    if (!this.isMenuButtonActive) {
      return;
    }
    if (!this.verifySatelliteSelected()) {
      return;
    }

    const sat = keepTrackApi.getCatalogManager().getObject(this.selectSatManager_.selectedSat, GetSatType.EXTRA_ONLY) as DetailedSatellite;

    (<HTMLInputElement>getEl('nl-scc')).value = sat.sccNum;
    (<HTMLInputElement>getEl('nl-inc')).value = sat.inclination.toFixed(4).padStart(8, '0');
  };

  menuMode: MenuMode[] = [MenuMode.BASIC, MenuMode.ADVANCED, MenuMode.ALL];

  bottomIconImg = rocketLaunchPng;
  isRequireSatelliteSelected = true;
  isIconDisabledOnLoad = true;
  isIconDisabled = true;
  sideMenuElementName: string = 'newLaunch-menu';
  sideMenuElementHtml: string = (() => {
    // Group launchSites by country
    const grouped: { [country: string]: { key: string; name: string, site: string }[] } = {};

    for (const [key, site] of Object.entries(launchSites)) {
      const country = site.country || 'Other';

      if (!grouped[country]) {
        grouped[country] = [];
      }
      grouped[country].push({ key, name: site.name, site: site.site ?? 'Unknown Site' });
    }

    // Sort countries alphabetically, and sites by name
    const countryKeys = Object.keys(grouped).sort();

    for (const country of countryKeys) {
      grouped[country].sort((a, b) => {
        const siteCompare = a.site.localeCompare(b.site);

        if (siteCompare !== 0) {
          return siteCompare;
        }

        return a.name.localeCompare(b.name);
      });
    }

    // Build the select options HTML
    const optionsHtml = countryKeys.map((country) =>
      `<optgroup label="${country}"> ${grouped[country]
        .map((site) => `<option value="${site.key}">${site.name}<br/> - ${site.site}</option>`).join('\n')}
      </optgroup>`,
    ).join('\n');

    return keepTrackApi.html`
      <div id="newLaunch-menu" class="side-menu-parent start-hidden text-select">
        <div id="newLaunch-content" class="side-menu">
          <div class="row">
            <h5 class="center-align">新建发射</h5>
            <form id="${this.sideMenuElementName}-form" class="col s12">
              <div class="input-field col s12">
                <input disabled value="00005" id="nl-scc" type="text">
                <label for="disabled" class="active">卫星SCC#</label>
              </div>
              <div class="input-field col s12">
                <input disabled value="50.00" id="nl-inc" type="text">
                <label for="disabled" class="active">倾角</label>
              </div>
              <div class="input-field col s12">
                <select value="50.00" id="nl-updown" type="text">
                  <option value="N">北</option>
                  <option value="S">南</option>
                </select>
                <label for="disabled">向北或者向南发射</label>
              </div>
              <div class="input-field col s12" id="nl-launch-menu">
                <select id="nl-facility">
                  ${optionsHtml}
                </select>
                <label>发射设施</label>
              </div>
              <div class="center-align">
                <button
                  id="${this.sideMenuElementName}-submit" class="btn btn-ui waves-effect waves-light" type="submit" name="action">创建发射
                  Nominal &#9658;
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    `;
  })();

  dragOptions: ClickDragOptions = {
    minWidth: 400,
    maxWidth: 600,
    isDraggable: true,
  };

  isDoingCalculations = false;
  submitCallback: () => void = () => {
    if (this.isDoingCalculations) {
      return;
    }
    this.isDoingCalculations = true;

    const timeManagerInstance = keepTrackApi.getTimeManager();
    const catalogManagerInstance = keepTrackApi.getCatalogManager();
    const uiManagerInstance = keepTrackApi.getUiManager();
    const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();

    showLoadingSticky();

    const sccNum = (<HTMLInputElement>getEl('nl-scc')).value;
    const inputSat = catalogManagerInstance.sccNum2Sat(parseInt(sccNum))!;
    let nominalSat: DetailedSatellite | null = null;
    let id = -1;

    // TODO: Next available analyst satellite should be a function in the catalog manager
    for (let nomninalNumber = 500; nomninalNumber < 2500; nomninalNumber++) {
      nominalSat = catalogManagerInstance.sccNum2Sat(CatalogManager.ANALYST_START_ID + nomninalNumber);

      if (nominalSat && !nominalSat?.active) {
        id = nominalSat.id;
        break;
      }
    }

    if (id === -1 || !nominalSat) {
      uiManagerInstance.toast('没有更多可用的标称卫星！', ToastMsgType.critical);
      this.isDoingCalculations = false;
      hideLoading();

      return;
    }

    const sat = this.createNominalSat_(inputSat, nominalSat.sccNum, id);

    if (!sat) {
      this.isDoingCalculations = false;
      hideLoading();

      return;
    }

    const upOrDown = <'N' | 'S'>(<HTMLInputElement>getEl('nl-updown')).value;
    const launchFac = (<HTMLInputElement>getEl('nl-facility')).value;
    let launchLat: Degrees | null = null;
    let launchLon: Degrees | null = null;

    const launchSite = catalogManagerInstance.launchSites[launchFac];

    launchLat = launchSite.lat;
    launchLon = launchSite.lon;

    if (launchLat === null || launchLon === null) {
      uiManagerInstance.toast(`未找到 ${launchFac} 发射场！`, ToastMsgType.critical);

      return;
    }

    if (launchLon > 180) {
      // if West not East
      launchLon = (launchLon - 360) as Degrees; // Convert from 0-360 to -180-180
    }

    /*
     * if (sat.inclination < launchLat) {
     *   uiManagerInstance.toast(`卫星倾角低于发射纬度！`, ToastMsgType.critical);
     *   return;
     * }
     * Set time to 0000z for relative time.
     */
    const today = new Date(); // Need to know today for offset calculation
    const quadZTime = new Date(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate(), 0, 0, 0); // New Date object of the future collision

    // Date object defaults to local time.
    quadZTime.setUTCHours(0); // Move to UTC Hour

    const cacheStaticOffset = timeManagerInstance.staticOffset; // Cache the current static offset

    timeManagerInstance.changeStaticOffset(quadZTime.getTime() - today.getTime()); // Find the offset from today

    colorSchemeManagerInstance.calculateColorBuffers(true);

    keepTrackApi.getMainCamera().isAutoPitchYawToTarget = false;

    const simulationTimeObj = timeManagerInstance.simulationTimeObj;

    const TLEs = new OrbitFinder(sat, launchLat, launchLon, upOrDown, simulationTimeObj).rotateOrbitToLatLon();

    const tle1 = TLEs[0];
    const tle2 = TLEs[1];

    if (tle1 === 'Error' || tle1.length !== 69 || tle2.length !== 69) {
      if (tle1 === 'Error') {
        uiManagerInstance.toast(`创建TLE失败: ${tle2}`, ToastMsgType.critical);
      } else if (tle1.length !== 69) {
        uiManagerInstance.toast(`创建的TLE1无效：长度不为 69- ${tle1}`, ToastMsgType.critical);
      } else if (tle2.length !== 69) {
        uiManagerInstance.toast(`创建的TLE2无效：长度不为 69 - ${tle2}`, ToastMsgType.critical);
      }

      // We have to change the time for the TLE creation, but it failed, so revert it.
      timeManagerInstance.changeStaticOffset(cacheStaticOffset);
      this.isDoingCalculations = false;
      hideLoading();

      return;
    }

    // Prevent caching of old TLEs
    sat.satrec = null as unknown as SatelliteRecord;

    let satrec: SatelliteRecord;

    try {
      satrec = Sgp4.createSatrec(tle1, tle2);
      sat.satrec = satrec;
    } catch (e) {
      errorManagerInstance.error(e, 'new-launch.ts', '创建卫星记录时出错！');

      return;
    }
    if (SatMath.altitudeCheck(satrec, simulationTimeObj) > 1) {
      catalogManagerInstance.satCruncher.postMessage({
        typ: CruncerMessageTypes.SAT_EDIT,
        id,
        active: true,
        tle1,
        tle2,
      });

      const orbitManagerInstance = keepTrackApi.getOrbitManager();

      if (id) {
        orbitManagerInstance.changeOrbitBufferData(id, tle1, tle2);
      }
    } else {
      uiManagerInstance.toast('高度测试失败-尝试不同的卫星！', ToastMsgType.critical);
    }

    waitForCruncher({
      cruncher: catalogManagerInstance.satCruncher,
      cb: () => {
        this.isDoingCalculations = false;
        hideLoading();

        // Deseletect the satellite
        keepTrackApi.getPlugin(SelectSatManager)?.selectSat(sat.id);

        uiManagerInstance.toast('启动标准创建！', ToastMsgType.standby);
        uiManagerInstance.searchManager.doSearch(sat.sccNum);

        uiManagerInstance.toast('现在的时间与发射时间有关。', ToastMsgType.standby);
        keepTrackApi.getSoundManager()?.play(SoundNames.LIFT_OFF);
      },
      validationFunc: (data: PositionCruncherOutgoingMsg) => typeof data.satPos !== 'undefined',
      error: () => {
        if (!this.isDoingCalculations) {
          // If we are not doing calculations, then it must have finished already.
          return;
        }

        this.isDoingCalculations = false;
        hideLoading();
        uiManagerInstance.toast('我多次尝试都未能满足要求！确定这还能发射吗？', ToastMsgType.critical);
      },
      skipNumber: 2,
      maxRetries: 50,
    });
  };

  addJs(): void {
    super.addJs();
    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {
        getEl(`${this.sideMenuElementName}-form`)?.addEventListener('change', () => {
          const sat = keepTrackApi.getCatalogManager().getObject(this.selectSatManager_.selectedSat, GetSatType.EXTRA_ONLY) as DetailedSatellite;

          if (!sat.isSatellite()) {
            return;
          }
          this.preValidate_(sat);
        });
      },
    );

    keepTrackApi.on(
      KeepTrackApiEvents.selectSatData,
      (obj: BaseObject) => {
        if (obj?.isSatellite()) {
          const sat = obj as DetailedSatellite;

          (<HTMLInputElement>getEl('nl-scc')).value = sat.sccNum;
          this.setBottomIconToEnabled();
          this.preValidate_(sat);
        } else {
          this.setBottomIconToDisabled();
        }
      },
    );
  }

  private preValidate_(sat: DetailedSatellite): void {
    // Get Current LaunchSiteOptionValue
    const launchSiteOptionValue = (<HTMLInputElement>getEl('nl-facility')).value;
    const lat = launchSites[launchSiteOptionValue].lat;
    let inc = sat.inclination;

    inc = inc > 90 ? ((180 - inc) as Degrees) : inc;

    const submitButtonDom = <HTMLButtonElement>getEl(`${this.sideMenuElementName}-submit`);

    if (inc < lat) {
      submitButtonDom.disabled = true;
      submitButtonDom.textContent = '倾角太低了！';
    } else {
      submitButtonDom.disabled = false;
      submitButtonDom.textContent = '创建标准发射 \u25B6';
    }
  }

  private createNominalSat_(inputParams: DetailedSatellite, scc: string, id: number): DetailedSatellite | null {
    const country = inputParams.country;
    const type = inputParams.type;
    const intl = `${inputParams.epochYear}69B`; // International designator

    // Create TLE from parameters
    const { tle1: tle1_, tle2 } = FormatTle.createTle({
      sat: inputParams,
      inc: inputParams.inclination,
      meanmo: inputParams.meanMotion,
      rasc: inputParams.rightAscension,
      argPe: inputParams.argOfPerigee,
      meana: inputParams.meanAnomaly,
      ecen: inputParams.eccentricity.toString().split('.')[1].padStart(7, '0'),
      epochyr: inputParams.epochYear.toString().padStart(2, '0'),
      epochday: inputParams.epochDay.toString().padStart(3, '0'),
      intl,
      scc,
    });

    // Check if TLE generation failed
    if (tle1_ === 'Error') {
      errorManagerInstance.error(
        new Error(tle2),
        'create-sat.ts',
        t7e('errorMsgs.CreateSat.errorCreatingSat'),
      );

      return null;
    }

    const currentEpoch = TimeManager.currentEpoch(keepTrackApi.getTimeManager().simulationTimeObj);

    const tle1 = (tle1_.substr(0, 18) + currentEpoch[0] + currentEpoch[1] + tle1_.substr(32)) as TleLine1;

    // Create satellite record from TLE
    let satrec: SatelliteRecord;

    try {
      satrec = Sgp4.createSatrec(tle1, tle2);
    } catch (e) {
      errorManagerInstance.error(e as Error, 'create-sat.ts', '创建卫星记录时出错！');

      return null;
    }

    // Validate altitude is reasonable
    if (SatMath.altitudeCheck(satrec, keepTrackApi.getTimeManager().simulationTimeObj) <= 1) {
      keepTrackApi.getUiManager().toast(
        '传播卫星失败。请尝试不同的参数，如果参数正确，请报告此问题。',
        ToastMsgType.caution,
        true,
      );

      return null;
    }

    // Propagate satellite to get position and velocity
    const spg4vec = Sgp4.propagate(satrec, 0);
    const pos = spg4vec.position as EciVec3;
    const vel = spg4vec.velocity as EciVec3<KilometersPerSecond>;

    // Create new satellite object
    const info: DetailedSatelliteParams = {
      id,
      type,
      country,
      tle1,
      tle2,
      name: 'New Launch Nominal',
    };

    const newSat = new DetailedSatellite({
      ...info,
      ...{
        position: pos,
        velocity: vel,
        source: 'User Created',
      },
    });

    newSat.active = true;

    const catalogManagerInstance = keepTrackApi.getCatalogManager();

    // Add to catalog
    catalogManagerInstance.objectCache[id] = newSat;

    // Update orbit buffer
    try {
      keepTrackApi.getOrbitManager().changeOrbitBufferData(id, tle1, tle2);
    } catch (e) {
      errorManagerInstance.error(e as Error, 'create-sat.ts', '更改轨道缓冲区数据失败');

      return null;
    }

    return newSat;
  }
}
