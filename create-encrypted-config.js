#!/usr/bin/env node

/**
 * 创建加密的ES配置文件
 * 使用方法: node create-encrypted-config.js
 */

const fs = require('fs');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function createEncryptedConfig() {
  console.log('=== 创建加密的ES配置文件 ===\n');
  
  try {
    const url = await question('ES服务器地址 (默认: http://localhost:9200): ') || 'http://localhost:9200';
    const username = await question('ES用户名: ');
    const password = await question('ES密码: ');
    const index = await question('ES索引名 (默认: orbital_tle): ') || 'orbital_tle';
    
    if (!username || !password) {
      console.error('用户名和密码不能为空！');
      process.exit(1);
    }
    
    // 创建配置对象
    const config = {
      elasticsearch: {
        url,
        username,
        password,
        index
      }
    };
    
    // 转换为JSON并加密（base64编码）
    const jsonConfig = JSON.stringify(config, null, 2);
    const encryptedConfig = Buffer.from(jsonConfig, 'utf8').toString('base64');
    
    // 保存加密文件
    fs.writeFileSync('es-config.enc', encryptedConfig);
    
    console.log('\n✅ 加密配置文件已创建: es-config.enc');
    console.log('⚠️  请确保将此文件添加到 .gitignore 中');
    console.log('⚠️  请妥善保管此文件，不要分享给他人');
    
    // 验证文件
    console.log('\n=== 验证配置 ===');
    const testContent = fs.readFileSync('es-config.enc', 'utf8');
    const decodedContent = Buffer.from(testContent, 'base64').toString('utf8');
    const testConfig = JSON.parse(decodedContent);
    console.log('✅ 配置文件验证成功');
    console.log(`   服务器: ${testConfig.elasticsearch.url}`);
    console.log(`   索引: ${testConfig.elasticsearch.index}`);
    console.log(`   用户名: ${testConfig.elasticsearch.username}`);
    console.log(`   密码: ${'*'.repeat(testConfig.elasticsearch.password.length)}`);
    
  } catch (error) {
    console.error('创建配置文件时出错:', error);
  } finally {
    rl.close();
  }
}

createEncryptedConfig(); 