/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * https://spacedefense
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { KeepTrackApiEvents, MenuMode, ToastMsgType } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import { shake } from '@app/lib/shake';
import { t7e } from '@app/locales/keys';
import { CameraType } from '@app/singletons/camera';
import viewInAirPng from '@public/img/icons/view-in-air.png';
import { DetailedSatellite } from 'ootk';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { SelectSatManager } from '../select-sat-manager/select-sat-manager';

export class SatelliteViewPlugin extends KeepTrackPlugin {
  readonly id = 'SatelliteViewPlugin';
  dependencies_ = [SelectSatManager.name];
  private readonly selectSatManager_: SelectSatManager;

  constructor() {
    super();
    this.selectSatManager_ = keepTrackApi.getPlugin(SelectSatManager) as unknown as SelectSatManager; // this will be validated in KeepTrackPlugin constructor
  }

  menuMode: MenuMode[] = [MenuMode.ALL];

  isRequireSatelliteSelected = true;
  bottomIconImg = viewInAirPng;
  isIconDisabledOnLoad = true;

  addJs(): void {
    super.addJs();

    keepTrackApi.on(
      KeepTrackApiEvents.selectSatData,
      (obj) => {
        if (obj instanceof DetailedSatellite) {
          this.setBottomIconToEnabled();
        } else {
          this.setBottomIconToDisabled();
        }
      },
    );
  }


  bottomIconCallback = () => {
    if (keepTrackApi.getMainCamera().cameraType === CameraType.SATELLITE) {
      const uiManagerInstance = keepTrackApi.getUiManager();

      uiManagerInstance.hideSideMenus();
      keepTrackApi.getMainCamera().cameraType = CameraType.FIXED_TO_SAT; // Back to normal Camera Mode
      getEl(this.bottomIconElementName)?.classList.remove('bmenu-item-selected');
    } else if (this.selectSatManager_.selectedSat !== -1) {
      keepTrackApi.getMainCamera().cameraType = CameraType.SATELLITE; // Activate Satellite Camera Mode
      getEl(this.bottomIconElementName)?.classList.add('bmenu-item-selected');
    } else {
      const uiManagerInstance = keepTrackApi.getUiManager();

      uiManagerInstance.toast(t7e('errorMsgs.SelectSatelliteFirst'), ToastMsgType.serious, true);
      shake(getEl(this.bottomIconElementName));
    }
  };
}
