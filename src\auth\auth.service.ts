import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';

interface User {
  id: string;
  username: string;
  password: string;
  role: 'admin' | 'user';
  email?: string;
  createdAt: string;
  lastLogin?: string;
  isActive: boolean;
  failedLoginAttempts: number;
  lockedUntil?: string;
  needsPasswordChange: boolean;
  expiresAt?: string; // 账号有效期
}

interface RegistrationRequest {
  id: string;
  username: string;
  password: string;
  email: string;
  requestedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  approvedAt?: string;
}

interface LoginAttempt {
  ip: string;
  username: string;
  timestamp: string;
  success: boolean;
}

export class AuthService {
  private readonly dataPath = path.join(__dirname, '../../data');
  private readonly usersFile = path.join(this.dataPath, 'users.json');
  private readonly registrationsFile = path.join(this.dataPath, 'registrations.json');
  private readonly loginAttemptsFile = path.join(this.dataPath, 'login-attempts.json');
  private readonly secretKey = process.env.JWT_SECRET || 'SpaceDefense2025!@#$%^&*()';

  public users: User[] = [];
  public registrationRequests: RegistrationRequest[] = [];
  public loginAttempts: LoginAttempt[] = [];

  constructor() {
    this.ensureDataDirectory();
    this.loadData();
    this.initializeDefaultAdmin();
  }

  private ensureDataDirectory() {
    if (!fs.existsSync(this.dataPath)) {
      fs.mkdirSync(this.dataPath, { recursive: true });
    }
  }

  private hashPassword(password: string): string {
    return crypto.pbkdf2Sync(password, this.secretKey, 10000, 64, 'sha512').toString('hex');
  }

  private verifyPassword(password: string, hash: string): boolean {
    const hashToVerify = crypto.pbkdf2Sync(password, this.secretKey, 10000, 64, 'sha512').toString('hex');
    return hashToVerify === hash;
  }

  private loadData() {
    try {
      if (fs.existsSync(this.usersFile)) {
        this.users = JSON.parse(fs.readFileSync(this.usersFile, 'utf8'));
      }
      if (fs.existsSync(this.registrationsFile)) {
        this.registrationRequests = JSON.parse(fs.readFileSync(this.registrationsFile, 'utf8'));
      }
      if (fs.existsSync(this.loginAttemptsFile)) {
        this.loginAttempts = JSON.parse(fs.readFileSync(this.loginAttemptsFile, 'utf8'));
      }
    } catch (error) {
      console.error('Error loading auth data:', error);
    }
  }

  private saveData() {
    try {
      fs.writeFileSync(this.usersFile, JSON.stringify(this.users, null, 2));
      fs.writeFileSync(this.registrationsFile, JSON.stringify(this.registrationRequests, null, 2));
      fs.writeFileSync(this.loginAttemptsFile, JSON.stringify(this.loginAttempts, null, 2));
    } catch (error) {
      console.error('Error saving auth data:', error);
    }
  }

  private initializeDefaultAdmin() {
    if (this.users.length === 0) {
      const adminUser: User = {
        id: uuidv4(),
        username: 'admin',
        password: this.hashPassword('SpaceDefense2025!'),
        role: 'admin',
        email: '<EMAIL>',
        createdAt: new Date().toISOString(),
        isActive: true,
        failedLoginAttempts: 0,
        needsPasswordChange: true
      };
      this.users.push(adminUser);
      this.saveData();
    }
  }

  private isAccountLocked(user: User): boolean {
    if (!user.lockedUntil) return false;
    const lockTime = new Date(user.lockedUntil);
    return lockTime > new Date();
  }

  private recordLoginAttempt(ip: string, username: string, success: boolean) {
    const attempt: LoginAttempt = {
      ip,
      username,
      timestamp: new Date().toISOString(),
      success
    };

    this.loginAttempts.push(attempt);

    // 只保留最近1000条记录
    if (this.loginAttempts.length > 1000) {
      this.loginAttempts = this.loginAttempts.slice(-1000);
    }

    this.saveData();
  }

  private getRecentFailedAttempts(ip: string): number {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    return this.loginAttempts.filter(attempt =>
      attempt.ip === ip &&
      !attempt.success &&
      new Date(attempt.timestamp) > oneHourAgo
    ).length;
  }

  login(username: string, password: string, ip: string = 'unknown'): { user?: any; token?: string; error?: string } {
    // 检查IP是否被临时锁定（1小时内超过10次失败）
    const recentFailures = this.getRecentFailedAttempts(ip);
    if (recentFailures >= 10) {
      this.recordLoginAttempt(ip, username, false);
      return { error: '该IP地址登录失败次数过多，请1小时后再试' };
    }

    const user = this.users.find(u => u.username === username);

    if (!user) {
      this.recordLoginAttempt(ip, username, false);
      return { error: '用户名或密码错误' };
    }

    if (!user.isActive) {
      this.recordLoginAttempt(ip, username, false);
      return { error: '账户已被禁用' };
    }

    if (this.isAccountLocked(user)) {
      this.recordLoginAttempt(ip, username, false);
      return { error: '账户已被锁定，请稍后再试' };
    }

    // 检查账号是否过期
    if (user.expiresAt && new Date(user.expiresAt) < new Date()) {
      this.recordLoginAttempt(ip, username, false);
      return { error: '账户已过期，请联系管理员' };
    }

    if (!this.verifyPassword(password, user.password)) {
      user.failedLoginAttempts++;

      // 5次失败后锁定1分钟
      if (user.failedLoginAttempts >= 5) {
        user.lockedUntil = new Date(Date.now() + 60 * 1000).toISOString();
        user.failedLoginAttempts = 0;
      }

      this.saveData();
      this.recordLoginAttempt(ip, username, false);
      return { error: '用户名或密码错误' };
    }

    // 登录成功
    user.failedLoginAttempts = 0;
    user.lockedUntil = undefined;
    user.lastLogin = new Date().toISOString();
    this.saveData();
    this.recordLoginAttempt(ip, username, true);

    // 生成JWT token
    const token = this.generateToken(user);

    return {
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        email: user.email,
        needsPasswordChange: user.needsPasswordChange
      },
      token
    };
  }

  private generateToken(user: User): string {
    const payload = {
      userId: user.id,
      username: user.username,
      role: user.role,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24小时过期
    };

    // 简单的JWT实现（生产环境应使用专业的JWT库）
    const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64');
    const payloadStr = Buffer.from(JSON.stringify(payload)).toString('base64');
    const signature = crypto.createHmac('sha256', this.secretKey).update(`${header}.${payloadStr}`).digest('base64');

    return `${header}.${payloadStr}.${signature}`;
  }

  verifyToken(token: string): { valid: boolean; user?: User; error?: string } {
    try {
      const [header, payload, signature] = token.split('.');

      // 验证签名
      const expectedSignature = crypto.createHmac('sha256', this.secretKey).update(`${header}.${payload}`).digest('base64');
      if (signature !== expectedSignature) {
        return { valid: false, error: 'Invalid token signature' };
      }

      const decodedPayload = JSON.parse(Buffer.from(payload, 'base64').toString());

      // 检查过期时间
      if (decodedPayload.exp < Math.floor(Date.now() / 1000)) {
        return { valid: false, error: 'Token expired' };
      }

      const user = this.users.find(u => u.id === decodedPayload.userId);
      if (!user || !user.isActive) {
        return { valid: false, error: 'User not found or inactive' };
      }

      // 检查账号是否过期
      if (user.expiresAt && new Date(user.expiresAt) < new Date()) {
        return { valid: false, error: 'Account expired' };
      }

      return { valid: true, user };
    } catch (error) {
      return { valid: false, error: 'Invalid token format' };
    }
  }

  changePassword(userId: string, oldPassword: string, newPassword: string): { success: boolean; error?: string } {
    const user = this.users.find(u => u.id === userId);
    if (!user) {
      return { success: false, error: '用户不存在' };
    }

    if (!this.verifyPassword(oldPassword, user.password)) {
      return { success: false, error: '原密码错误' };
    }

    // 密码强度检查
    if (!this.isPasswordStrong(newPassword)) {
      return { success: false, error: '密码强度不足：至少8位，包含大小写字母、数字和特殊字符' };
    }

    user.password = this.hashPassword(newPassword);
    user.needsPasswordChange = false;
    this.saveData();

    return { success: true };
  }

  private isPasswordStrong(password: string): boolean {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
  }

  requestRegistration(username: string, password: string, email: string): { success: boolean; error?: string } {
    // 输入验证
    if (!username || !password || !email) {
      return { success: false, error: '所有字段都是必填的' };
    }

    // 用户名格式检查
    if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
      return { success: false, error: '用户名只能包含字母、数字和下划线，长度3-20位' };
    }

    // 邮箱格式检查
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return { success: false, error: '邮箱格式不正确' };
    }

    // 密码强度检查
    if (!this.isPasswordStrong(password)) {
      return { success: false, error: '密码强度不足：至少8位，包含大小写字母、数字和特殊字符' };
    }

    // 检查用户名是否已存在
    if (this.users.find(u => u.username === username)) {
      return { success: false, error: '用户名已存在' };
    }

    // 检查邮箱是否已存在
    if (this.users.find(u => u.email === email)) {
      return { success: false, error: '邮箱已被使用' };
    }

    // 检查是否已有待审批的注册请求
    if (this.registrationRequests.find(r => r.username === username && r.status === 'pending')) {
      return { success: false, error: '该用户名已有待审批的注册请求' };
    }

    const request: RegistrationRequest = {
      id: uuidv4(),
      username,
      password: this.hashPassword(password),
      email,
      requestedAt: new Date().toISOString(),
      status: 'pending'
    };

    this.registrationRequests.push(request);
    this.saveData();

    return { success: true };
  }

  getPendingRegistrations(): RegistrationRequest[] {
    return this.registrationRequests.filter(r => r.status === 'pending');
  }

  approveRegistration(requestId: string, adminId: string): { success: boolean; error?: string } {
    const request = this.registrationRequests.find(r => r.id === requestId);
    if (!request) {
      return { success: false, error: '注册请求不存在' };
    }

    if (request.status !== 'pending') {
      return { success: false, error: '该请求已被处理' };
    }

    // 创建新用户
    const newUser: User = {
      id: uuidv4(),
      username: request.username,
      password: request.password,
      role: 'user',
      email: request.email,
      createdAt: new Date().toISOString(),
      isActive: true,
      failedLoginAttempts: 0,
      needsPasswordChange: false
    };

    this.users.push(newUser);

    // 更新请求状态
    request.status = 'approved';
    request.approvedBy = adminId;
    request.approvedAt = new Date().toISOString();

    this.saveData();
    return { success: true };
  }

  rejectRegistration(requestId: string, adminId: string): { success: boolean; error?: string } {
    const request = this.registrationRequests.find(r => r.id === requestId);
    if (!request) {
      return { success: false, error: '注册请求不存在' };
    }

    if (request.status !== 'pending') {
      return { success: false, error: '该请求已被处理' };
    }

    request.status = 'rejected';
    request.approvedBy = adminId;
    request.approvedAt = new Date().toISOString();

    this.saveData();
    return { success: true };
  }

  // 管理员功能
  getAllUsers(): User[] {
    return this.users.map(u => ({
      ...u,
      password: '[HIDDEN]' // 不返回密码
    })) as User[];
  }

  toggleUserStatus(userId: string): { success: boolean; error?: string } {
    const user = this.users.find(u => u.id === userId);
    if (!user) {
      return { success: false, error: '用户不存在' };
    }

    if (user.role === 'admin' && this.users.filter(u => u.role === 'admin' && u.isActive).length === 1) {
      return { success: false, error: '不能禁用最后一个管理员账户' };
    }

    user.isActive = !user.isActive;
    this.saveData();
    return { success: true };
  }

  deleteUser(userId: string): { success: boolean; error?: string } {
    const userIndex = this.users.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      return { success: false, error: '用户不存在' };
    }

    const user = this.users[userIndex];
    if (user.role === 'admin' && this.users.filter(u => u.role === 'admin').length === 1) {
      return { success: false, error: '不能删除最后一个管理员账户' };
    }

    this.users.splice(userIndex, 1);
    this.saveData();
    return { success: true };
  }

  updateUserRole(userId: string, newRole: 'admin' | 'user'): { success: boolean; error?: string } {
    const user = this.users.find(u => u.id === userId);
    if (!user) {
      return { success: false, error: '用户不存在' };
    }

    if (user.role === 'admin' && newRole === 'user' && this.users.filter(u => u.role === 'admin').length === 1) {
      return { success: false, error: '不能将最后一个管理员降级' };
    }

    user.role = newRole;
    this.saveData();
    return { success: true };
  }

  getLoginAttempts(limit: number = 100): LoginAttempt[] {
    return this.loginAttempts.slice(-limit).reverse();
  }

  // 安全功能
  sanitizeInput(input: string): string {
    return input.replace(/[<>\"'&]/g, (match) => {
      const map: { [key: string]: string } = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '&': '&amp;'
      };
      return map[match];
    });
  }

  validateCSRFToken(token: string, sessionToken: string): boolean {
    // 简单的CSRF保护
    const expectedToken = crypto.createHmac('sha256', this.secretKey).update(sessionToken).digest('hex');
    return token === expectedToken;
  }

  generateCSRFToken(sessionToken: string): string {
    return crypto.createHmac('sha256', this.secretKey).update(sessionToken).digest('hex');
  }

  // 直接创建普通用户（无需审批）
  createUser(username: string, email: string, password: string, role: 'user' | 'admin' = 'user', expiresAt?: string): { success: boolean; error?: string; user?: User } {
    // 验证输入
    if (!username || !email || !password) {
      return { success: false, error: '用户名、邮箱和密码不能为空' };
    }

    // 密码强度检查
    if (!this.isPasswordStrong(password)) {
      return { success: false, error: '密码强度不足：至少8位，包含大小写字母、数字和特殊字符' };
    }

    // 检查用户名是否已存在
    if (this.users.find(u => u.username === username)) {
      return { success: false, error: '用户名已存在' };
    }

    // 检查邮箱是否已存在
    if (this.users.find(u => u.email === email)) {
      return { success: false, error: '邮箱已被使用' };
    }

    // 创建新用户
    const hashedPassword = this.hashPassword(password);
    const newUser: User = {
      id: crypto.randomUUID(),
      username,
      email,
      password: hashedPassword,
      role,
      isActive: true,
      createdAt: new Date().toISOString(),
      needsPasswordChange: false,
      failedLoginAttempts: 0,
      expiresAt: expiresAt
    };

    this.users.push(newUser);
    this.saveData();

    // 记录操作日志
    this.recordLoginAttempt('unknown', username, true);

    return {
      success: true,
      user: {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email,
        role: newUser.role,
        isActive: newUser.isActive,
        createdAt: newUser.createdAt
      } as any
    };
  }

  // 设置用户有效期
  setUserExpiration(userId: string, expiresAt?: string): { success: boolean; error?: string } {
    const user = this.users.find(u => u.id === userId);
    if (!user) {
      return { success: false, error: '用户不存在' };
    }

    // 验证有效期格式
    if (expiresAt && isNaN(new Date(expiresAt).getTime())) {
      return { success: false, error: '有效期格式无效' };
    }

    user.expiresAt = expiresAt;
    this.saveData();

    return { success: true };
  }

  // 获取即将过期的用户（7天内过期）
  getExpiringUsers(): User[] {
    const sevenDaysFromNow = new Date();
    sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

    return this.users.filter(user => {
      if (!user.expiresAt) return false;
      const expirationDate = new Date(user.expiresAt);
      return expirationDate <= sevenDaysFromNow && expirationDate > new Date();
    });
  }

  // 获取已过期的用户
  getExpiredUsers(): User[] {
    const now = new Date();
    return this.users.filter(user => {
      if (!user.expiresAt) return false;
      return new Date(user.expiresAt) < now;
    });
  }
}

export const authService = new AuthService();