import { keep<PERSON><PERSON><PERSON><PERSON> } from '../keepTrackApi';
import { GroupData, GroupType, ObjectGroup } from './object-group';

/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * https://spacedefense
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

export class GroupsManager {
  groupList: Record<string, ObjectGroup<GroupType>> = {};
  selectedGroup: ObjectGroup<GroupType> | null = null;
  stopUpdatingInViewSoon: boolean;

  private changeGroup_(group: ObjectGroup<GroupType> | null): ObjectGroup<GroupType> | null {
    this.selectedGroup = group;

    return this.selectedGroup;
  }

  selectGroup(group: ObjectGroup<GroupType>): void {
    this.changeGroup_(group);
    group.updateOrbits();
    const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();

    colorSchemeManagerInstance.setToGroupColorScheme();
    // 强制重新计算颜色缓冲区以应用组颜色方案
    colorSchemeManagerInstance.calculateColorBuffers(true);

    this.stopUpdatingInViewSoon = false;
  }

  selectGroupNoOverlay(): void {
    settingsManager.isGroupOverlayDisabled = true;

    const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();

    colorSchemeManagerInstance.isUseGroupColorScheme = true;
    colorSchemeManagerInstance.calculateColorBuffers();
  }

  clearSelect(): void {
    this.changeGroup_(null);
    settingsManager.isGroupOverlayDisabled = false;
    this.stopUpdatingInViewSoon = true;
  }

  /**
   * Including the name parameter creates a cached version of the object
   *
   * Do not include a name if the group is temporary or will change
   */
  createGroup<T extends GroupType>(type: GroupType, data: GroupData[T] | null = null, name: string | null = null): ObjectGroup<T> {
    // Seee if this group already exists and return it
    if (name) {
      if (this.groupList[name]) {
        return this.groupList[name];
      }
    }

    const group = new ObjectGroup(type, data);

    // If the group was named, add it to the cache
    if (name) {
      this.groupList[name] = group;
    }

    return group;
  }

  init() {
    this.selectedGroup = null;
    this.stopUpdatingInViewSoon = false;
    this.groupList = {};
  }
}
