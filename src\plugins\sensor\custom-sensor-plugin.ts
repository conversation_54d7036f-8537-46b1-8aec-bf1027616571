import { KeepTrackApiEvents, MenuMode, ToastMsgType } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl, hideEl } from '@app/lib/get-el';
import { slideInRight } from '@app/lib/slide';
import { triggerSubmit } from '@app/lib/trigger-submit';
import { waitForCruncher } from '@app/lib/waitForCruncher';
import { errorManagerInstance } from '@app/singletons/errorManager';
import { PersistenceManager, StorageKey } from '@app/singletons/persistence-manager';
import { UiGeolocation } from '@app/static/ui-manager-geolocation';
import { PositionCruncherOutgoingMsg } from '@app/webworker/constants';
import { CruncerMessageTypes } from '@app/webworker/positionCruncher';
import bookmarkRemovePng from '@public/img/icons/bookmark-remove.png';
import sensorAddPng from '@public/img/icons/sensor-add.png';
import { saveAs } from 'file-saver';
import { Degrees, DetailedSensor, Kilometers, SpaceObjectType, ZoomValue } from 'ootk';
import { ClickDragOptions, KeepTrackPlugin, SideMenuSettingsOptions } from '../KeepTrackPlugin';
import { SensorFov } from '../sensor-fov/sensor-fov';
import { SensorSurvFence } from '../sensor-surv/sensor-surv-fence';
import { SoundNames } from '../sounds/SoundNames';
import { SensorInfoPlugin } from './sensor-info-plugin';
import { SensorListPlugin } from '../sensor-list/sensor-list';

export class CustomSensorPlugin extends KeepTrackPlugin {
  readonly id = 'CustomSensorPlugin';
  dependencies_ = [SensorListPlugin.name];

  // 自定义传感器注册表 - 统一管理所有自定义传感器
  private static customSensorRegistry: Map<string, DetailedSensor> = new Map();

  /**
   * 注册自定义传感器到注册表
   */
  static registerCustomSensor(sensor: DetailedSensor): void {
    if (sensor.objName) {
      if (this.customSensorRegistry.has(sensor.objName)) {
        console.warn('传感器已存在，将覆盖:', sensor.objName);
      }
      this.customSensorRegistry.set(sensor.objName, sensor);
    }
  }

  /**
   * 从注册表获取自定义传感器
   */
  static getCustomSensor(objName: string): DetailedSensor | undefined {
    return this.customSensorRegistry.get(objName);
  }

  /**
   * 从注册表删除自定义传感器
   */
  static unregisterCustomSensor(objName: string): boolean {
    return this.customSensorRegistry.delete(objName);
  }

  /**
   * 获取所有自定义传感器
   */
  static getAllCustomSensors(): DetailedSensor[] {
    return Array.from(this.customSensorRegistry.values());
  }

  /**
   * 调试方法：打印注册表状态
   */
  static debugRegistry(): void {
    // Registry debug info available for development
  }


  bottomIconCallback: () => void = () => {
    if (this.isMenuButtonActive) {
      const sensorManagerInstance = keepTrackApi.getSensorManager();

      if (sensorManagerInstance.isSensorSelected()) {
        (<HTMLInputElement>getEl('cs-replace')).style.display = '';
        CustomSensorPlugin.setInputValue_('cs-lat', sensorManagerInstance.currentSensors[0].lat.toString());
        CustomSensorPlugin.setInputValue_('cs-lon', sensorManagerInstance.currentSensors[0].lon.toString());
        CustomSensorPlugin.setInputValue_('cs-hei', sensorManagerInstance.currentSensors[0].alt.toString());
      } else {
        (<HTMLInputElement>getEl('cs-replace')).style.display = 'none';
      }

      // 移除特殊的标签位置修复，现在使用统一的CSS规则
    }
  };

  menuMode: MenuMode[] = [MenuMode.ADVANCED, MenuMode.ALL];

  bottomIconImg = sensorAddPng;

  sideMenuElementName: string = 'custom-sensor-menu';
  sideMenuElementHtml: string = keepTrackApi.html`
    <form id="customSensor">
      <div class="input-field col s12" data-position="top" data-delay="50" data-tooltip="传感器名字">
          <input id="cs-uiName" type="text" value="自定义传感器" />
          <label for="cs-uiName" class="active">传感器名字</label>
      </div>
      <div class="input-field col s12" data-position="top" data-delay="50" data-tooltip="十进制形式的纬度（例如：43.283）">
          <input id="cs-lat" type="text" value="0" />
          <label for="cs-lat" class="active">纬度</label>
      </div>
      <div class="input-field col s12" data-position="top" data-delay="50" data-tooltip="十进制经度（例如：-73.283）">
          <input id="cs-lon" type="text" value="0" />
          <label for="cs-lon" class="active">经度</label>
      </div>
      <div class="input-field col s12" data-position="top" data-delay="50" data-tooltip="海拔高度（公里）（例如：0.645）">
          <input id="cs-hei" type="text" value="0" />
          <label for="cs-hei" class="active">海拔高度(Km)</label>
      </div>
      <div class="input-field col s12">
          <select id="cs-type">
          <option value="Observer">观察者</option>
          <option value="Optical">光学</option>
          <option value="Phased Array Radar">相控阵雷达</option>
          <option value="Mechanical">机械</option>
          </select>
          <label>传感器类型</label>
      </div>
      <div class="col s12" style="margin-bottom: 30px; margin-top: 20px; border: none !important;">
        <div class="switch row" data-position="top" data-delay="50" data-tooltip="这个传感器是望远镜吗？">
            <label>
            <input id="cs-telescope" type="checkbox" />
            <span class="lever"></span>
            望远镜
            </label>
        </div>
      </div>
      <div id="cs-minaz-div" class="start-hidden input-field col s12" style="margin-top: 15px;" data-position="top" data-delay="50" data-tooltip="方位角（例如：50）">
          <input id="cs-minaz" type="text" value="0" />
          <label for="cs-minaz" class="active">最小方位角</label>
      </div>
      <div id="cs-maxaz-div" class="start-hidden input-field col s12" data-position="top" data-delay="50" data-tooltip="方位角（例如：120）">
          <input id="cs-maxaz" type="text" value="360" />
          <label for="cs-maxaz" class="active">最大方位角</label>
      </div>
      <div id="cs-minel-div" class="start-hidden input-field col s12" data-position="top" data-delay="50" data-tooltip="仰角（例如：10）">
          <input id="cs-minel" type="text" value="10" />
          <label for="cs-minel" class="active">最小仰角</label>
      </div>
      <div id="cs-maxel-div" class="start-hidden input-field col s12" data-position="top" data-delay="50" data-tooltip="仰角角度（例如：90）">
          <input id="cs-maxel" type="text" value="90" />
          <label for="cs-maxel" class="active">最大仰角</label>
      </div>
      <div id="cs-minrange-div" class="start-hidden input-field col s12" data-position="top" data-delay="50" data-tooltip="覆盖范围以公里为单位（例如：500）">
          <input id="cs-minrange" type="text" value="100" />
          <label for="cs-minrange" class="active">最小覆盖范围</label>
      </div>
      <div id="cs-maxrange-div" class="start-hidden input-field col s12" data-position="top" data-delay="50" data-tooltip="覆盖范围以公里为单位（例如：2000）">
          <input id="cs-maxrange" type="text" value="50000" />
          <label for="cs-maxrange" class="active">最大覆盖范围</label>
      </div>
    <div class="center-align">
        <button id="cs-replace" class="btn btn-ui waves-effect waves-light" name="action">更换传感器 &#9658;</button>
        <br />
        <br />
        <button id="cs-submit" class="btn btn-ui waves-effect waves-light" name="action">添加自定义传感器 &#9658;</button>
        <br />
        <br />
        <button id="cs-clear" class="btn btn-ui waves-effect waves-light" name="action">清除自定义传感器 &#9658;</button>
        <br />
        <br />
        <button id="cs-geolocation" class="btn btn-ui waves-effect waves-light" name="search">使用地理定位 &#9658;</button>
        <br />
        <br />
        <button id="cs-save" class="btn btn-ui waves-effect waves-light" name="action">保存传感器列表 &#9658;</button>
        <br />
        <br />
        <button id="cs-load" class="btn btn-ui waves-effect waves-light" name="action">加载传感器列表 &#9658;</button>
        <br />
        <br />
        <button id="cs-export" class="btn btn-ui waves-effect waves-light" name="action">将列表导出为文件 &#9658;</button>
        <br />
        <br />
        <input type="file" id="cs-import-file" accept=".json" style="display: none;" />
        <button id="cs-import" class="btn btn-ui waves-effect waves-light" name="action">从文件导入列表 &#9658;</button>
    </div>
    </form>
    `;
  sideMenuSecondaryHtml: string = keepTrackApi.html`
    <div class="row" style="margin: 0 10px;">
      <div id="custom-sensors-sensor-list">
      </div>
    </div>`;
  sideMenuSecondaryOptions: SideMenuSettingsOptions = {
    width: 450,
    leftOffset: null,
    zIndex: 3,
  };

  rmbL1ElementName = 'create-rmb';
  rmbL1Html = keepTrackApi.html`
  <li class="rmb-menu-item" id=${this.rmbL1ElementName}><a href="#">创建 &#x27A4;</a></li>`;

  isRmbOnEarth = true;
  isRmbOffEarth = false;
  isRmbOnSat = false;
  rmbMenuOrder = 10;

  rmbL2ElementName = 'create-rmb-menu';
  rmbL2Html = keepTrackApi.html`
  <ul class='dropdown-contents'>
    <li id="create-observer-rmb"><a href="#">在此创建观察者</a></li>
    <li id="create-sensor-rmb"><a href="#">在此创建传感器</a></li>
  </ul>`;

  rmbCallback: (targetId: string, clickedSat?: number) => void = (targetId: string) => {
    const sensorManagerInstance = keepTrackApi.getSensorManager();
    const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();
    const catalogManagerInstance = keepTrackApi.getCatalogManager();
    const mouseInputInstance = keepTrackApi.getInputManager().mouse;

    switch (targetId) {
      case 'create-observer-rmb':
        slideInRight(getEl('custom-sensor-menu'), 1000);
        this.setBottomIconToSelected();
        sensorManagerInstance.isCustomSensorMenuOpen = true;
        if (!(<HTMLInputElement>getEl('cs-telescope')).checked) {
          getEl('cs-telescope')?.click();
        }
        CustomSensorPlugin.setInputValue_('cs-uiName', 'Observer');
        CustomSensorPlugin.setInputValue_('cs-lat', mouseInputInstance.latLon.lat.toString());
        CustomSensorPlugin.setInputValue_('cs-lon', mouseInputInstance.latLon.lon.toString());
        CustomSensorPlugin.setInputValue_('cs-hei', '0');
        (<HTMLInputElement>getEl('cs-type')).value = 'Observer';
        triggerSubmit(<HTMLFormElement>getEl('customSensor'));
        catalogManagerInstance.satCruncher.postMessage({
          isSunlightView: true,
          typ: CruncerMessageTypes.SUNLIGHT_VIEW,
        });
        waitForCruncher({
          cruncher: catalogManagerInstance.satCruncher,
          cb: () => {
            colorSchemeManagerInstance.setColorScheme(colorSchemeManagerInstance.colorSchemeInstances.SunlightColorScheme, true);
          },
          validationFunc: (data: PositionCruncherOutgoingMsg) => data.satInSun,
        });
        break;
      case 'create-sensor-rmb':
        {
          slideInRight(getEl('custom-sensor-menu'), 1000);
          this.setBottomIconToSelected();
          sensorManagerInstance.isCustomSensorMenuOpen = true;
          if ((<HTMLInputElement>getEl('cs-telescope')).checked) {
            getEl('cs-telescope')!.click();
          }
          CustomSensorPlugin.setInputValue_('cs-uiName', 'Custom Sensor');
          CustomSensorPlugin.setInputValue_('cs-lat', mouseInputInstance.latLon.lat.toString());
          CustomSensorPlugin.setInputValue_('cs-lon', mouseInputInstance.latLon.lon.toString());
          CustomSensorPlugin.setInputValue_('cs-hei', '0');
          (<HTMLInputElement>getEl('cs-type')).value = 'Phased Array Radar';
          CustomSensorPlugin.setInputValue_('cs-minaz', '0');
          CustomSensorPlugin.setInputValue_('cs-maxaz', '360');
          CustomSensorPlugin.setInputValue_('cs-minel', '10');
          CustomSensorPlugin.setInputValue_('cs-maxel', '90');
          CustomSensorPlugin.setInputValue_('cs-minrange', '0');
          CustomSensorPlugin.setInputValue_('cs-maxrange', '5556');
          triggerSubmit(<HTMLFormElement>getEl('customSensor'));
          const defaultColorScheme = colorSchemeManagerInstance.colorSchemeInstances[settingsManager.defaultColorScheme] ??
            Object.values(colorSchemeManagerInstance.colorSchemeInstances)[0];

          colorSchemeManagerInstance.setColorScheme(defaultColorScheme, true);
          catalogManagerInstance.satCruncher.postMessage({
            isSunlightView: false,
            typ: CruncerMessageTypes.SUNLIGHT_VIEW,
          });
        }
        break;
      case 'colors-confidence-rmb':
      case 'colors-rcs-rmb':
      case 'colors-density-rmb':
      case 'colors-starlink-rmb':
      case 'colors-sunlight-rmb':
      case 'colors-country-rmb':
      case 'colors-velocity-rmb':
      case 'colors-default-rmb':
        break;
      default:
        // errorManagerInstance.info(`Unknown RMB target: ${targetId}`);
        break;
    }
  };

  dragOptions: ClickDragOptions = {
    minWidth: 350,
    isDraggable: true,
  };

  addHtml(): void {
    super.addHtml();

    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {
        CustomSensorPlugin.httpsCheck_();
        CustomSensorPlugin.addCustomSensorFormSubmitListener();
        CustomSensorPlugin.addTelescopeClickListener_();
        CustomSensorPlugin.addCustomSensorBtnCLickListener_();
        CustomSensorPlugin.addClearCustomSensorListener_();
        CustomSensorPlugin.addSaveLoadListeners_();
        CustomSensorPlugin.initializeTelescopeState_();
      },
    );

    // 在Cruncher准备好后自动加载保存的自定义传感器列表
    keepTrackApi.on(
      KeepTrackApiEvents.onCruncherReady,
      () => {
        CustomSensorPlugin.autoLoadCustomSensorList_();
      },
    );
  }

  addJs(): void {
    super.addJs();



    // 将静态方法暴露到全局，供其他插件使用
    (window as any).CustomSensorPlugin = {
      getCustomSensor: CustomSensorPlugin.getCustomSensor.bind(CustomSensorPlugin),
      getAllCustomSensors: CustomSensorPlugin.getAllCustomSensors.bind(CustomSensorPlugin),
      registerCustomSensor: CustomSensorPlugin.registerCustomSensor.bind(CustomSensorPlugin),
      unregisterCustomSensor: CustomSensorPlugin.unregisterCustomSensor.bind(CustomSensorPlugin),
      debugRegistry: CustomSensorPlugin.debugRegistry.bind(CustomSensorPlugin)
    };

    // 移除特殊的标签位置修复，使用统一的CSS规则
  }

  // 移除了所有特殊的标签位置修复方法，现在完全使用统一的CSS规则

  /**
   * 设置输入字段的值并确保标签正确定位
   */
  private static setInputValue_(inputId: string, value: string) {
    const input = getEl(inputId) as HTMLInputElement;
    const label = input?.nextElementSibling as HTMLLabelElement;

    if (input) {
      input.value = value;

      // 确保标签保持在正确位置
      if (label && value && value.trim() !== '') {
        label.classList.add('active');
      } else if (label) {
        label.classList.remove('active');
      }
    }
  }

  private static httpsCheck_() {
    if (location.protocol !== 'https:') {
      hideEl('cs-geolocation');
    } else {
      CustomSensorPlugin.addUseGeolocationListener_();
    }
  }

  private static addCustomSensorFormSubmitListener() {
    const customSensorEl = getEl('customSensor');
    if (customSensorEl) {
      customSensorEl.addEventListener('submit', (e: Event) => {
        // Prevent the form from submitting
        e.preventDefault();
        // Don't call the sensor creation function here - it's handled by button click
      });
    }
  }

  private static addUseGeolocationListener_() {
    const geolocationEl = getEl('cs-geolocation');
    if (geolocationEl) {
      geolocationEl.addEventListener('click', UiGeolocation.useCurrentGeolocationAsSensor);
      keepTrackApi.getSoundManager()?.play(SoundNames.CLICK);
    }
  }

  private static addClearCustomSensorListener_() {
    const clearEl = getEl('cs-clear');
    if (clearEl) {
      clearEl.addEventListener('click', () => {
        // 从注册表获取所有自定义传感器
        const customSensors = CustomSensorPlugin.getAllCustomSensors();
        const sensorManagerInstance = keepTrackApi.getSensorManager();

        // 移除所有自定义传感器
        customSensors.forEach(sensor => {
          sensorManagerInstance.removeSensor(sensor);
          if (sensor.objName) {
            CustomSensorPlugin.unregisterCustomSensor(sensor.objName);
          }
        });

        // 保存空的传感器列表
        CustomSensorPlugin.saveCustomSensorList_();

        keepTrackApi.getSoundManager()?.play(SoundNames.CLICK);
        CustomSensorPlugin.updateCustomSensorListDom();

        // 强制更新主传感器列表中的自定义传感器显示
        setTimeout(() => {
          try {
            SensorListPlugin.updateCustomSensorsList();
          } catch (error) {
            // 忽略错误
          }
        }, 50);

        keepTrackApi.getUiManager().toast('所有自定义传感器已清除', ToastMsgType.normal);
      });
    }
  }

  private static addCustomSensorBtnCLickListener_() {
    const submitEl = getEl('cs-submit');
    if (submitEl) {
      submitEl.addEventListener('click', () => {
        CustomSensorPlugin.processCustomSensorSubmit_();
        keepTrackApi.getSoundManager()?.play(SoundNames.CLICK);
      });
    }

    const replaceEl = getEl('cs-replace');
    if (replaceEl) {
      replaceEl.addEventListener('click', () => {
        CustomSensorPlugin.processCustomSensorSubmit_(true);
        keepTrackApi.getSoundManager()?.play(SoundNames.CLICK);
      });
    }
  }

  private static processCustomSensorSubmit_(isReplaceSensor = false) {
    keepTrackApi.getPlugin(SensorInfoPlugin)?.setBottomIconToUnselected();
    keepTrackApi.getPlugin(SensorFov)?.setBottomIconToUnselected();
    keepTrackApi.getPlugin(SensorSurvFence)?.setBottomIconToUnselected();
    keepTrackApi.getPluginByName('Planetarium')?.setBottomIconToUnselected();
    keepTrackApi.getPluginByName('Astronomy')?.setBottomIconToUnselected();

    (<HTMLInputElement>getEl('sensor-type')).value = (<HTMLInputElement>getEl('cs-type')).value.replace(/</gu, '&lt;').replace(/>/gu, '&gt;');
    const sensorInfoTitleEl = getEl('sensor-info-title');
    if (sensorInfoTitleEl) {
      sensorInfoTitleEl.innerHTML = 'Custom Sensor';
    }
    getEl('sensor-country')!.innerHTML = 'Custom Sensor';

    const uiName = (<HTMLInputElement>getEl('cs-uiName')).value;
    const lon = CustomSensorPlugin.str2Deg((<HTMLInputElement>getEl('cs-lon')).value);
    const lat = CustomSensorPlugin.str2Deg((<HTMLInputElement>getEl('cs-lat')).value);
    const alt = (<HTMLInputElement>getEl('cs-hei')).value;
    const sensorType = <'Observer' | 'Optical' | 'Mechanical' | 'Phased Array Radar'>(<HTMLInputElement>getEl('cs-type')).value;
    const minaz = (<HTMLInputElement>getEl('cs-minaz')).value;
    const maxaz = (<HTMLInputElement>getEl('cs-maxaz')).value;
    const minel = (<HTMLInputElement>getEl('cs-minel')).value;
    const maxel = (<HTMLInputElement>getEl('cs-maxel')).value;
    const minrange = (<HTMLInputElement>getEl('cs-minrange')).value;
    const maxrange = (<HTMLInputElement>getEl('cs-maxrange')).value;

    let type = SpaceObjectType.OBSERVER;

    switch (sensorType) {
      case 'Observer':
        type = SpaceObjectType.OBSERVER;
        break;
      case 'Optical':
        type = SpaceObjectType.OPTICAL;
        break;
      case 'Mechanical':
        type = SpaceObjectType.MECHANICAL;
        break;
      case 'Phased Array Radar':
        type = SpaceObjectType.PHASED_ARRAY_RADAR;
        break;
      default:
        errorManagerInstance.info(`Unknown sensor type: ${sensorType}`);
        type = SpaceObjectType.OBSERVER;
        break;
    }

    const randomUUID = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const objName = `Custom Sensor-${randomUUID}`;



    const newSensor = new DetailedSensor({
      lat,
      lon,
      alt: CustomSensorPlugin.str2Km(alt),
      minAz: CustomSensorPlugin.str2Deg(minaz),
      maxAz: CustomSensorPlugin.str2Deg(maxaz),
      minEl: CustomSensorPlugin.str2Deg(minel),
      maxEl: CustomSensorPlugin.str2Deg(maxel),
      minRng: CustomSensorPlugin.str2Km(minrange),
      maxRng: CustomSensorPlugin.str2Km(maxrange),
      type,
      name: uiName, // 使用用户输入的名称而不是固定的'Custom Sensor'
      uiName,
      system: 'Custom Sensor',
      country: 'Custom Sensor',
      objName,
      operator: 'Custom Sensor',
      zoom: CustomSensorPlugin.str2Km(maxrange) > 6000 ? ZoomValue.GEO : ZoomValue.LEO,
      volume: false,
    });

    // 注册到自定义传感器注册表
    CustomSensorPlugin.registerCustomSensor(newSensor);

    keepTrackApi.getSensorManager().addSecondarySensor(newSensor, isReplaceSensor);

    CustomSensorPlugin.updateCustomSensorListDom();

    // 强制更新主传感器列表中的自定义传感器显示
    setTimeout(() => {
      try {
        SensorListPlugin.updateCustomSensorsList();
      } catch (error) {
        // 忽略错误
      }
    }, 50);
  }

  private static updateCustomSensorListDom() {
    // 直接从注册表获取所有自定义传感器
    const sensors = CustomSensorPlugin.getAllCustomSensors();

    const sensorListHtml = sensors.map((sensor) => `
      <div class="row" style="height: 100%; display: flex; align-items: center; margin: 20px 0px;">
        <div class="col s10 m10 l10">
          <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px; color: #fff;"><strong>传感器名字:</strong> ${sensor.uiName}</div>
          <div style="font-size: 14px; margin-bottom: 4px;"><strong>传感器ID:</strong> ${sensor.objName}</div>
          <div style="font-size: 14px; margin-bottom: 4px;"><strong>纬度:</strong> ${sensor.lat.toFixed(0)}°</div>
          <div style="font-size: 14px; margin-bottom: 4px;"><strong>经度:</strong> ${sensor.lon.toFixed(0)}°</div>
          <div style="font-size: 14px; margin-bottom: 4px;"><strong>高度:</strong> ${sensor.alt.toFixed(0)} km</div>
          <div style="font-size: 14px; margin-bottom: 4px;"><strong>方位角:</strong> ${sensor.minAz.toFixed(0)}° - ${sensor.maxAz.toFixed(0)}°</div>
          <div style="font-size: 14px; margin-bottom: 4px;"><strong>仰角:</strong> ${sensor.minEl.toFixed(0)}° - ${sensor.maxEl.toFixed(0)}°</div>
          <div style="font-size: 14px; margin-bottom: 4px;"><strong>范围:</strong> ${sensor.minRng.toFixed(0)} km - ${sensor.maxRng.toFixed(0)} km</div>
        </div>
        <div class="col s2 m2 l2 center-align remove-icon" style="display: flex; align-items: center; height: 100%; justify-content: center;">
          <button class="btn-flat remove-sensor-btn" data-sensor-id="${sensor.objName}" style="padding: 5px; min-width: auto;">
            <img src="${bookmarkRemovePng}" style="width: 20px; height: 20px;">
          </button>
        </div>
      </div>
      <div class="divider"></div>
      `).join('');

    const sensorListEl = getEl('custom-sensors-sensor-list');
    if (sensorListEl) {
      sensorListEl.innerHTML = sensorListHtml;
    }

    // 确保事件监听器正确绑定
    CustomSensorPlugin.bindDeleteEvents();
  }

  /**
   * 绑定删除按钮事件
   */
  private static bindDeleteEvents() {
    const sensorList = getEl('custom-sensors-sensor-list');
    if (!sensorList) {
      return;
    }

    // 移除旧的事件监听器
    sensorList.removeEventListener('click', CustomSensorPlugin.handleDeleteClick);

    // 添加新的事件监听器
    sensorList.addEventListener('click', CustomSensorPlugin.handleDeleteClick);
  }

  private static handleDeleteClick = (e: Event) => {
    const target = e.target as HTMLElement;

    // 检查是否点击了删除按钮或其子元素
    const deleteButton = target.closest('.remove-sensor-btn') as HTMLElement;
    if (!deleteButton) {
      return;
    }

    e.preventDefault();
    e.stopPropagation();

    const sensorId = deleteButton.dataset.sensorId;
    if (!sensorId) {
      return;
    }

    // 从注册表查找传感器
    const sensor = CustomSensorPlugin.getCustomSensor(sensorId);
    if (!sensor) {
      keepTrackApi.getUiManager().toast('未找到要删除的传感器', ToastMsgType.caution);
      return;
    }

    // 从传感器管理器删除传感器
    const sensorManagerInstance = keepTrackApi.getSensorManager();
    sensorManagerInstance.removeSensor(sensor);

    // 从注册表删除传感器
    CustomSensorPlugin.unregisterCustomSensor(sensorId);

    // 保存并更新显示
    CustomSensorPlugin.saveCustomSensorList_();
    CustomSensorPlugin.updateCustomSensorListDom();

    // 更新主传感器列表
    setTimeout(() => {
      try {
        SensorListPlugin.updateCustomSensorsList();
      } catch (error) {
        // 忽略错误
      }
    }, 50);

    keepTrackApi.getUiManager().toast('传感器已删除', ToastMsgType.normal);
  };

  private static addTelescopeClickListener_() {
    const telescopeEl = getEl('cs-telescope');
    if (telescopeEl) {
      // 监听checkbox的change事件
      telescopeEl.addEventListener('change', () => {
        CustomSensorPlugin.updateTelescopeState_();
      });

      // 也监听点击事件作为备用
      telescopeEl.addEventListener('click', () => {
        // 延迟一点执行，确保checked状态已更新
        setTimeout(() => {
          CustomSensorPlugin.updateTelescopeState_();
        }, 10);
      });

      // 监听整个开关标签的点击
      const switchLabel = telescopeEl.closest('.switch')?.querySelector('label');
      if (switchLabel) {
        switchLabel.addEventListener('click', () => {
          // 延迟一点执行，确保checked状态已更新
          setTimeout(() => {
            CustomSensorPlugin.updateTelescopeState_();
          }, 10);
        });
      }
    }
  }

  /**
   * 初始化望远镜状态，确保子菜单正确显示
   */
  private static initializeTelescopeState_() {
    CustomSensorPlugin.updateTelescopeState_();
  }

  /**
   * 根据望远镜按钮状态更新子菜单显示
   */
  private static updateTelescopeState_() {
    const telescopeEl = <HTMLInputElement>getEl('cs-telescope');
    if (!telescopeEl) {
      return;
    }

    const isTelescopeChecked = telescopeEl.checked;

    if (isTelescopeChecked) {
      // 望远镜模式：隐藏所有子菜单
      getEl('cs-minaz-div')!.style.display = 'none';
      getEl('cs-maxaz-div')!.style.display = 'none';
      getEl('cs-minel-div')!.style.display = 'none';
      getEl('cs-maxel-div')!.style.display = 'none';
      getEl('cs-minrange-div')!.style.display = 'none';
      getEl('cs-maxrange-div')!.style.display = 'none';
      CustomSensorPlugin.setInputValue_('cs-minaz', '0');
      CustomSensorPlugin.setInputValue_('cs-maxaz', '360');
      CustomSensorPlugin.setInputValue_('cs-minel', '10');
      CustomSensorPlugin.setInputValue_('cs-maxel', '90');
      CustomSensorPlugin.setInputValue_('cs-minrange', '100');
      CustomSensorPlugin.setInputValue_('cs-maxrange', '1000000');
    } else {
      // 传感器模式：显示所有子菜单
      getEl('cs-minaz-div')!.style.display = 'block';
      getEl('cs-maxaz-div')!.style.display = 'block';
      getEl('cs-minel-div')!.style.display = 'block';
      getEl('cs-maxel-div')!.style.display = 'block';
      getEl('cs-minrange-div')!.style.display = 'block';
      getEl('cs-maxrange-div')!.style.display = 'block';

      const sensorManagerInstance = keepTrackApi.getSensorManager();

      if (sensorManagerInstance.isSensorSelected()) {
        CustomSensorPlugin.setInputValue_('cs-minaz', sensorManagerInstance.currentSensors[0].minAz.toString());
        CustomSensorPlugin.setInputValue_('cs-maxaz', sensorManagerInstance.currentSensors[0].maxAz.toString());
        CustomSensorPlugin.setInputValue_('cs-minel', sensorManagerInstance.currentSensors[0].minEl.toString());
        CustomSensorPlugin.setInputValue_('cs-maxel', sensorManagerInstance.currentSensors[0].maxEl.toString());
        CustomSensorPlugin.setInputValue_('cs-minrange', sensorManagerInstance.currentSensors[0].minRng.toString());
        CustomSensorPlugin.setInputValue_('cs-maxrange', sensorManagerInstance.currentSensors[0].maxRng.toString());
      }
    }
  }

  static str2Km(str: string): Kilometers {
    return <Kilometers>parseFloat(str);
  }

  static str2Deg(str: string): Degrees {
    return <Degrees>parseFloat(str);
  }

  /**
   * 添加保存和加载相关的事件监听器
   */
  private static addSaveLoadListeners_() {
    // 保存到本地存储
    const saveEl = getEl('cs-save');
    if (saveEl) {
      saveEl.addEventListener('click', () => {
        CustomSensorPlugin.saveCustomSensorList_();
        keepTrackApi.getSoundManager()?.play(SoundNames.CLICK);
      });
    }

    // 从本地存储加载
    const loadEl = getEl('cs-load');
    if (loadEl) {
      loadEl.addEventListener('click', () => {
        CustomSensorPlugin.loadCustomSensorList_();
        keepTrackApi.getSoundManager()?.play(SoundNames.CLICK);
      });
    }

    // 导出为文件
    const exportEl = getEl('cs-export');
    if (exportEl) {
      exportEl.addEventListener('click', () => {
        CustomSensorPlugin.exportCustomSensorList_();
        keepTrackApi.getSoundManager()?.play(SoundNames.CLICK);
      });
    }

    // 从文件导入
    const importEl = getEl('cs-import');
    if (importEl) {
      importEl.addEventListener('click', () => {
        const importFileEl = getEl('cs-import-file');
        if (importFileEl) {
          importFileEl.click();
        }
        keepTrackApi.getSoundManager()?.play(SoundNames.CLICK);
      });
    }

    // 文件选择事件
    const importFileEl = getEl('cs-import-file');
    if (importFileEl) {
      importFileEl.addEventListener('change', (e: Event) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          CustomSensorPlugin.importCustomSensorList_(file);
        }
      });
    }
  }

  /**
   * 保存自定义传感器列表到本地存储
   */
  private static saveCustomSensorList_() {
    // 直接从注册表获取所有自定义传感器
    const customSensors = CustomSensorPlugin.getAllCustomSensors();

    const sensorData = customSensors.map(sensor => ({
      objName: sensor.objName, // 保存唯一标识符
      uiName: sensor.uiName,
      lat: sensor.lat,
      lon: sensor.lon,
      alt: sensor.alt,
      minAz: sensor.minAz,
      maxAz: sensor.maxAz,
      minEl: sensor.minEl,
      maxEl: sensor.maxEl,
      minRng: sensor.minRng,
      maxRng: sensor.maxRng,
      type: sensor.type,
      name: sensor.name,
      system: sensor.system,
      country: sensor.country,
      operator: sensor.operator,
      zoom: sensor.zoom
    }));

    PersistenceManager.getInstance().saveItem(StorageKey.CUSTOM_SENSOR_LIST, JSON.stringify(sensorData));
    keepTrackApi.getUiManager().toast(`已保存 ${customSensors.length} 个自定义传感器`, ToastMsgType.normal);
  }

  /**
   * 从本地存储加载自定义传感器列表
   */
  private static loadCustomSensorList_() {
    const savedData = PersistenceManager.getInstance().getItem(StorageKey.CUSTOM_SENSOR_LIST);

    if (!savedData) {
      keepTrackApi.getUiManager().toast('没有找到保存的传感器列表', ToastMsgType.caution);
      return;
    }

    try {
      const sensorData = JSON.parse(savedData);
      CustomSensorPlugin.restoreCustomSensors_(sensorData);
    } catch (error) {
      errorManagerInstance.warn('传感器列表格式不正确');
    }
  }

  /**
   * 启动时自动加载自定义传感器列表（静默加载，不显示提示）
   */
  private static autoLoadCustomSensorList_() {
    const savedData = PersistenceManager.getInstance().getItem(StorageKey.CUSTOM_SENSOR_LIST);

    if (!savedData) {
      return; // 没有保存的数据，静默返回
    }

    try {
      const sensorData = JSON.parse(savedData);
      CustomSensorPlugin.restoreCustomSensors_(sensorData, true); // 传入true表示静默加载
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 导出自定义传感器列表为JSON文件
   */
  private static exportCustomSensorList_() {
    const sensorManagerInstance = keepTrackApi.getSensorManager();
    const allActiveSensors = sensorManagerInstance.getAllActiveSensors();

    const customSensors = allActiveSensors.filter(sensor =>
      sensor.objName?.startsWith('Custom Sensor')
    );

    if (customSensors.length === 0) {
      keepTrackApi.getUiManager().toast('没有自定义传感器可导出', ToastMsgType.caution);
      return;
    }

    const sensorData = customSensors.map(sensor => ({
      objName: sensor.objName, // 保存唯一标识符
      uiName: sensor.uiName,
      lat: sensor.lat,
      lon: sensor.lon,
      alt: sensor.alt,
      minAz: sensor.minAz,
      maxAz: sensor.maxAz,
      minEl: sensor.minEl,
      maxEl: sensor.maxEl,
      minRng: sensor.minRng,
      maxRng: sensor.maxRng,
      type: sensor.type,
      name: sensor.name,
      system: sensor.system,
      country: sensor.country,
      operator: sensor.operator,
      zoom: sensor.zoom
    }));

    const dataString = JSON.stringify(sensorData, null, 2);
    const blob = new Blob([dataString], { type: 'application/json;charset=utf-8' });
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    saveAs(blob, `custom-sensors-${timestamp}.json`);

    keepTrackApi.getUiManager().toast(`已导出 ${customSensors.length} 个自定义传感器`, ToastMsgType.normal);
  }

  /**
   * 从文件导入自定义传感器列表
   */
  private static importCustomSensorList_(file: File) {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const sensorData = JSON.parse(e.target?.result as string);
        CustomSensorPlugin.restoreCustomSensors_(sensorData);
      } catch (error) {
        errorManagerInstance.warn('传感器文件格式不正确');
      }
    };

    reader.readAsText(file);
  }

  /**
   * 恢复自定义传感器列表
   * @param sensorData 传感器数据数组
   * @param isSilent 是否静默加载（不显示提示消息）
   */
  private static restoreCustomSensors_(sensorData: any[], isSilent = false) {
    if (!Array.isArray(sensorData)) {
      errorManagerInstance.warn('传感器数据格式不正确');
      return;
    }

    // 清除现有的自定义传感器（保留其他类型的传感器）
    const sensorManagerInstance = keepTrackApi.getSensorManager();
    const customSensors = CustomSensorPlugin.getAllCustomSensors();

    // 移除所有自定义传感器
    customSensors.forEach(sensor => {
      sensorManagerInstance.removeSensor(sensor);
      if (sensor.objName) {
        CustomSensorPlugin.unregisterCustomSensor(sensor.objName);
      }
    });

    let restoredCount = 0;

    for (const data of sensorData) {
      try {
        // 使用保存的objName，如果没有则生成新的UUID（向后兼容）
        let objName = data.objName;
        if (!objName) {
          const randomUUID = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
          objName = `Custom Sensor-${randomUUID}`;
        }

        const sensor = new DetailedSensor({
          lat: data.lat,
          lon: data.lon,
          alt: data.alt,
          minAz: data.minAz,
          maxAz: data.maxAz,
          minEl: data.minEl,
          maxEl: data.maxEl,
          minRng: data.minRng,
          maxRng: data.maxRng,
          type: data.type,
          name: data.uiName || data.name || 'Custom Sensor', // 优先使用uiName作为name
          uiName: data.uiName,
          system: data.system || 'Custom Sensor',
          country: data.country || 'Custom Sensor',
          objName: objName, // 使用原始的objName或新生成的
          operator: data.operator || 'Custom Sensor',
          zoom: data.zoom,
        });

        // 注册到自定义传感器注册表
        CustomSensorPlugin.registerCustomSensor(sensor);

        keepTrackApi.getSensorManager().addSecondarySensor(sensor);
        restoredCount++;
      } catch (error) {
        // 跳过无效的传感器数据
      }
    }

    CustomSensorPlugin.updateCustomSensorListDom();

    // 确保删除按钮事件正确绑定
    setTimeout(() => {
      CustomSensorPlugin.bindDeleteEvents();
    }, 100);

    // 强制更新主传感器列表中的自定义传感器显示
    setTimeout(() => {
      try {
        SensorListPlugin.updateCustomSensorsList();
      } catch (error) {
        // 忽略错误
      }
    }, 50);

    if (!isSilent) {
      keepTrackApi.getUiManager().toast(`已恢复 ${restoredCount} 个自定义传感器`, ToastMsgType.normal);
    }
  }
}
