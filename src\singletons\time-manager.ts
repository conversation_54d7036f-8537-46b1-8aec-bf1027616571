import { KeepTrackApiEvents, ToastMsgType } from '@app/interfaces';
import { SatMath } from '@app/static/sat-math';
import { CruncerMessageTypes } from '@app/webworker/positionCruncher';
import { getDayOfYear, GreenwichMeanSiderealTime, Milliseconds } from 'ootk';
import { InputEventType, keepTrackApi } from '../keepTrackApi';
import { getEl } from '../lib/get-el';
import { DateTimeManager } from '../plugins/date-time-manager/date-time-manager';
import { errorManagerInstance } from './errorManager';

export class TimeManager {
  dateDOM = null;
  datetimeInputDOM = <HTMLInputElement>null;
  /**
   * The real time at the moment when dynamicOffset or propRate changes
   */
  dynamicOffsetEpoch = <number>null;
  private iText = <number>null;
  lastPropRate = <number>1;
  /**
   * Time in Milliseconds the last time sim time was updated
   */
  private lastTime = <Milliseconds>0;
  propFrozen = 0;
  propOffset = 0;
  /**
   * The rate of change applied to the dynamicOffset
   */
  propRate = <number>null;
  propRate0 = <number>null;
  /**
   * The time in the real world
   */
  realTime = <Milliseconds>0;
  selectedDate = new Date();
  /**
   * The time in the simulation
   *
   * simulationTime = realTime + staticOffset + dynamicOffset * propRate
   */
  simulationTimeObj = <Date>null;
  /**
   * The time offset ignoring propRate (ex. New Launch)
   */
  staticOffset = settingsManager.staticOffset ?? 0;
  private simulationTimeSerialized_ = <string>null;
  timeTextStr = <string>null;
  /**
   * Reusable empty text string to reduce garbage collection
   */
  private timeTextStrEmpty_ = <string>null;
  lastBoxUpdateTime = <Milliseconds>0;
  /**
   * dynamicOffset: The time offset that is impacted by propRate
   *
   * dynamicOffset = realTime - dynamicOffsetEpoch
   */
  private dynamicOffset_: number;
  isCreateClockDOMOnce_ = false;
  gmst: GreenwichMeanSiderealTime = 0 as GreenwichMeanSiderealTime;
  j: number;

  static currentEpoch(currentDate: Date): [string, string] {
    const currentDateObj = new Date(currentDate);
    const epochYear = currentDateObj.getUTCFullYear().toString().slice(2, 4);
    const epochDay = getDayOfYear(currentDateObj);
    const timeOfDay = (currentDateObj.getUTCHours() * 60 + currentDateObj.getUTCMinutes()) / 1440;
    const epochDayStr = (epochDay + timeOfDay).toFixed(8).padStart(12, '0');


    return [epochYear, epochDayStr];
  }

  // Propagation Time Functions
  calculateSimulationTime(newSimulationTime?: Date): Date {
    if (typeof newSimulationTime !== 'undefined' && newSimulationTime !== null) {
      this.simulationTimeObj.setTime(newSimulationTime.getTime());

      return this.simulationTimeObj;
    }

    if (this.propRate === 0) {
      const simulationTime = this.dynamicOffsetEpoch + this.staticOffset;

      this.simulationTimeObj.setTime(simulationTime);
    } else {
      this.realTime = <Milliseconds>Date.now();
      this.dynamicOffset_ = this.realTime - this.dynamicOffsetEpoch;
      const simulationTime = this.dynamicOffsetEpoch + this.staticOffset + this.dynamicOffset_ * this.propRate;

      this.simulationTimeObj.setTime(simulationTime);
    }

    return this.simulationTimeObj;
  }

  changePropRate(propRate: number) {
    if (this.propRate === propRate) {
      return;
    } // no change

    this.staticOffset = this.simulationTimeObj.getTime() - Date.now();
    // Changing propRate or dynamicOffsetEpoch before calculating the staticOffset will give incorrect results
    this.dynamicOffsetEpoch = Date.now();
    this.propRate = propRate;
    this.calculateSimulationTime();

    this.synchronize();

    const toggleTimeDOM = getEl('toggle-time-rmb');

    if (keepTrackApi.getTimeManager().propRate === 0) {
      toggleTimeDOM.childNodes[0].textContent = '开始时钟';
    } else {
      toggleTimeDOM.childNodes[0].textContent = '停止时钟';
    }

    // 更新非实时状态指示器
    this.updateNonRealtimeIndicator();

    const uiManagerInstance = keepTrackApi.getUiManager();

    if (!settingsManager.isAlwaysHidePropRate && this.propRate0 !== this.propRate) {
      if (this.propRate > 1.01 || this.propRate < 0.99) {
        if (this.propRate < 10) {
          uiManagerInstance.toast(`运行速度: ${this.propRate.toFixed(1)}x`, ToastMsgType.standby);
        }
        if (this.propRate >= 10 && this.propRate < 60) {
          uiManagerInstance.toast(`运行速度: ${this.propRate.toFixed(1)}x`, ToastMsgType.caution);
        }
        if (this.propRate >= 60) {
          uiManagerInstance.toast(`运行速度: ${this.propRate.toFixed(1)}x`, ToastMsgType.serious);
        }
      } else {
        uiManagerInstance.toast(`运行速度: ${this.propRate.toFixed(1)}x`, ToastMsgType.standby);
      }

      if (!settingsManager.disableUI) {
        const datetimeTextElement = getEl('datetime-text', true);

        if (!datetimeTextElement) {
          errorManagerInstance.debug('Datetime text element not found');

          return;
        }

        if (!this.isCreateClockDOMOnce_) {
          datetimeTextElement.innerText = this.timeTextStr;
          this.isCreateClockDOMOnce_ = true;
        } else {
          datetimeTextElement.childNodes[0].nodeValue = this.timeTextStr;
        }
      }
    }

    // 更新非实时状态指示器
    this.updateNonRealtimeIndicator();

    keepTrackApi.emit(KeepTrackApiEvents.propRateChanged, this.propRate);
  }

  static isLeapYear(dateIn: Date) {
    const year = dateIn.getUTCFullYear();

    if ((year & 3) !== 0) {
      return false;
    }

    return year % 100 !== 0 || year % 400 === 0;
  }

  changeStaticOffset(staticOffset: number) {
    this.dynamicOffsetEpoch = Date.now();
    this.staticOffset = staticOffset;
    this.calculateSimulationTime();
    this.synchronize();
    keepTrackApi.emit(KeepTrackApiEvents.staticOffsetChange, this.staticOffset);
    // synchronize方法中已经包含了updateNonRealtimeIndicator的调用
  }

  getOffsetTimeObj(offset: number) {
    // Make a time variable
    const now = new Date();
    // Set the time variable to the time in the future

    now.setTime(this.simulationTimeObj.getTime() + offset);

    return now;
  }

  getPropOffset(): number {
    if (!this.selectedDate) {
      return 0;
    }
    // Not using local scope caused time to drift backwards!

    return this.selectedDate.getTime() - Date.now();
  }

  init() {
    this.dynamicOffsetEpoch = Date.now();
    this.simulationTimeObj = new Date();

    this.timeTextStr = '';
    this.timeTextStrEmpty_ = '';

    this.propFrozen = Date.now(); // for when propRate 0
    this.realTime = <Milliseconds>this.propFrozen; // (initialized as Date.now)
    this.propRate = 1.0; // time rate multiplier for propagation

    // Initialize
    this.calculateSimulationTime();
    this.setSelectedDate(this.simulationTimeObj);
    this.initializeKeyboardBindings_();

    // 🔥 移除这里的初始化，改为在DateTimeManager中调用
    // 初始化非实时指示器状态将在DateTimeManager创建DOM后进行
  }

  update() {
    const { gmst, j } = SatMath.calculateTimeVariables(this.simulationTimeObj);

    this.gmst = gmst;
    this.j = j;
  }

  private initializeKeyboardBindings_() {
    keepTrackApi.on(InputEventType.KeyDown, (key: string, _code: string, isRepeat: boolean) => {
      if (key === 't' && !isRepeat) {
        keepTrackApi.getUiManager().toast('Time Set to Real Time', ToastMsgType.standby);
        this.changeStaticOffset(0); // Reset to Current Time
      }
    });

    keepTrackApi.on(InputEventType.KeyDown, (key: string, _code: string, isRepeat: boolean) => {
      if (key === ',' && !isRepeat) {
        this.calculateSimulationTime();
        let newPropRate = this.propRate;

        if (this.propRate < 0.001 && this.propRate > -0.001) {
          newPropRate = -0.001;
        }

        if (this.propRate < -1000) {
          newPropRate = -1000;
        }

        if (newPropRate < 0) {
          newPropRate = (this.propRate * 1.5);
        } else {
          newPropRate = ((this.propRate * 2) / 3);
        }

        const calendarInstance = keepTrackApi.getPlugin(DateTimeManager)?.calendar;

        if (calendarInstance) {
          calendarInstance.updatePropRate(newPropRate);
        } else {
          this.changePropRate(newPropRate);
        }
      }
    });

    keepTrackApi.on(InputEventType.KeyDown, (key: string, _code: string, isRepeat: boolean) => {
      if (key === '.' && !isRepeat) {
        this.calculateSimulationTime();
        let newPropRate = this.propRate;

        if (this.propRate < 0.001 && this.propRate > -0.001) {
          newPropRate = 0.001;
        }

        if (this.propRate > 1000) {
          newPropRate = 1000;
        }

        if (newPropRate > 0) {
          newPropRate = (this.propRate * 1.5);
        } else {
          newPropRate = ((this.propRate * 2) / 3);
        }

        const calendarInstance = keepTrackApi.getPlugin(DateTimeManager)?.calendar;

        if (calendarInstance) {
          calendarInstance.updatePropRate(newPropRate);
        } else {
          this.changePropRate(newPropRate);
        }
      }
    });

    keepTrackApi.on(InputEventType.KeyDown, (key: string, _code: string, isRepeat: boolean) => {
      if (key === '<' && !isRepeat) {
        this.calculateSimulationTime();
        this.changeStaticOffset(this.staticOffset - settingsManager.changeTimeWithKeyboardAmountBig);
      }
    });

    keepTrackApi.on(InputEventType.KeyDown, (key: string, _code: string, isRepeat: boolean) => {
      if (key === '>' && !isRepeat) {
        this.calculateSimulationTime();
        this.changeStaticOffset(this.staticOffset + settingsManager.changeTimeWithKeyboardAmountBig);
      }
    });

    keepTrackApi.on(InputEventType.KeyDown, (key: string, _code: string, isRepeat: boolean) => {
      if (key === '/' && !isRepeat) {
        let newPropRate: number;

        if (this.propRate === 1) {
          newPropRate = 0;
        } else {
          newPropRate = 1;
        }

        const calendarInstance = keepTrackApi.getPlugin(DateTimeManager)?.calendar;

        if (calendarInstance) {
          calendarInstance.updatePropRate(newPropRate);
        } else {
          this.changePropRate(newPropRate);
        }
        this.calculateSimulationTime();
      }
    });

    keepTrackApi.on(InputEventType.KeyDown, (_key: string, code: string, isRepeat: boolean) => {
      if (code === 'Equal' && !isRepeat) {
        this.calculateSimulationTime();
        this.changeStaticOffset(this.staticOffset + settingsManager.changeTimeWithKeyboardAmountSmall);
      }
    });

    keepTrackApi.on(InputEventType.KeyDown, (_key: string, code: string, isRepeat: boolean) => {
      if (code === 'Minus' && !isRepeat) {
        this.calculateSimulationTime();
        this.changeStaticOffset(this.staticOffset - settingsManager.changeTimeWithKeyboardAmountSmall);
      }
    });
  }

  setNow(realTime: Milliseconds) {
    this.realTime = realTime;
    this.lastTime = <Milliseconds>this.simulationTimeObj.getTime();

    // NOTE: This should be the only regular call to calculateSimulationTime!!
    this.calculateSimulationTime();
  }

  toggleTime() {
    if (this.propRate === 0) {
      this.changePropRate(this.lastPropRate);
    } else {
      this.lastPropRate = this.propRate;
      this.changePropRate(0);
    }

    const uiManagerInstance = keepTrackApi.getUiManager();

    if (this.propRate > 1.01 || this.propRate < 0.99) {
      if (this.propRate < 10) {
        uiManagerInstance.toast(`运行速度: ${this.propRate.toFixed(1)}x`, ToastMsgType.standby);
      }
      if (this.propRate >= 10 && this.propRate < 60) {
        uiManagerInstance.toast(`运行速度: ${this.propRate.toFixed(1)}x`, ToastMsgType.caution);
      }
      if (this.propRate >= 60) {
        uiManagerInstance.toast(`运行速度: ${this.propRate.toFixed(1)}x`, ToastMsgType.serious);
      }
    } else {
      uiManagerInstance.toast(`运行速度: ${this.propRate.toFixed(1)}x`, ToastMsgType.standby);
    }
  }

  setSelectedDate(selectedDate: Date) {
    this.selectedDate = selectedDate;

    // This function only applies when datetime plugin is enabled
    if (settingsManager.plugins.DateTimeManager) {
      if (this.lastTime - this.simulationTimeObj.getTime() < <Milliseconds>300) {
        this.simulationTimeSerialized_ = this.simulationTimeObj.toJSON();
        this.timeTextStr = this.timeTextStrEmpty_;
        for (this.iText = 11; this.iText < 20; this.iText++) {
          if (this.iText > 11) {
            this.timeTextStr += this.simulationTimeSerialized_[this.iText - 1];
          }
        }
        this.propRate0 = this.propRate;
      }

      // Avoid race condition
      if (!this.dateDOM) {
        try {
          this.dateDOM = getEl('datetime-text');
          if (!this.dateDOM) {
            return;
          }
        } catch {
          errorManagerInstance.debug('Date DOM not found');

          return;
        }
      }

      // textContent doesn't remove the Node! No unecessary DOM changes everytime time updates.
      this.dateDOM.textContent = this.timeTextStr;
    }

    // Passing datetimeInput eliminates needing jQuery in main module
    if (
      this.lastTime - this.simulationTimeObj.getTime() < 300 &&
      ((keepTrackApi.getPlugin(DateTimeManager))?.isEditTimeOpen || !settingsManager.cruncherReady || !keepTrackApi.getPlugin(DateTimeManager))
    ) {
      if (settingsManager.plugins.DateTimeManager) {
        if (!this.datetimeInputDOM) {
          this.datetimeInputDOM = <HTMLInputElement>getEl('datetime-input-tb', true);
        }
        if (!this.datetimeInputDOM) {
          this.datetimeInputDOM.value = `${this.selectedDate.toISOString().slice(0, 10)} ${this.selectedDate.toISOString().slice(11, 19)}`;
        }
      }
    }
  }

  synchronize() {
    const catalogManagerInstance = keepTrackApi.getCatalogManager();
    const orbitManagerInstance = keepTrackApi.getOrbitManager();

    keepTrackApi.emit(KeepTrackApiEvents.updateDateTime, new Date(this.dynamicOffsetEpoch + this.staticOffset));

    const message = {
      typ: CruncerMessageTypes.OFFSET,
      staticOffset: this.staticOffset,
      dynamicOffsetEpoch: this.dynamicOffsetEpoch,
      propRate: this.propRate,
    };

    catalogManagerInstance.satCruncher.postMessage(message);

    /*
     * OrbitWorker starts later than the satCruncher so it might not be
     * ready yet.
     */
    if (orbitManagerInstance.orbitWorker) {
      orbitManagerInstance.orbitWorker.postMessage(message);
    }

    // 更新非实时状态指示器
    this.updateNonRealtimeIndicator();
  }

  /**
   * 检测当前时间是否为非实时状态
   * @returns true如果是非实时状态，false如果是实时状态
   */
  isNonRealtime(): boolean {
    // 如果静态偏移不为0，说明时间被手动调整过
    if (Math.abs(this.staticOffset) > 1000) { // 允许1秒的误差
      return true;
    }

    // 如果传播速率不是1.0，说明时间速度被调整过
    if (Math.abs(this.propRate - 1.0) > 0.01) { // 允许0.01的误差
      return true;
    }

    return false;
  }

  /**
   * 更新非实时状态指示器的显示
   */
  updateNonRealtimeIndicator() {
    const indicator = document.getElementById('non-realtime-indicator');
    if (!indicator) {
      return;
    }

    if (this.isNonRealtime()) {
      indicator.classList.remove('start-hidden');
    } else {
      indicator.classList.add('start-hidden');
    }
  }

  /**
   * 初始化非实时指示器的点击事件
   */
  initializeNonRealtimeIndicatorClick() {
    const indicator = document.getElementById('non-realtime-indicator');
    if (!indicator) {
      return;
    }

    indicator.addEventListener('click', (event: Event) => {
      // 阻止事件冒泡，防止触发其他点击事件（如datetime-text的点击事件）
      event.stopPropagation();
      event.preventDefault();

      this.restoreToRealtime();
    });
  }

  /**
   * 还原到实时状态
   */
  restoreToRealtime() {
    console.log('🔧 [TIME-MANAGER] 开始还原到实时状态');
    console.log('🔍 [TIME-MANAGER] 当前状态:', {
      staticOffset: this.staticOffset,
      propRate: this.propRate,
      isNonRealtime: this.isNonRealtime()
    });

    // 重置静态偏移为0（当前时间）
    this.staticOffset = 0;
    this.dynamicOffsetEpoch = Date.now();

    // 重置传播速率为1.0（实时速度）
    this.propRate = 1.0;
    this.lastPropRate = 1.0;

    // 重新计算模拟时间
    this.calculateSimulationTime();

    // 同步时间
    this.synchronize();

    // 立即更新非实时状态指示器
    this.updateNonRealtimeIndicator();

    console.log('✅ [TIME-MANAGER] 实时状态还原完成');
    console.log('🔍 [TIME-MANAGER] 新状态:', {
      staticOffset: this.staticOffset,
      propRate: this.propRate,
      isNonRealtime: this.isNonRealtime()
    });

    // 关闭时间管理器窗口
    const dateTimeManager = keepTrackApi.getPlugin(DateTimeManager);
    if (dateTimeManager && dateTimeManager.isMenuButtonActive) {
      dateTimeManager.hideSideMenus();
    }

    // 显示提示信息
    const uiManagerInstance = keepTrackApi.getUiManager();
    uiManagerInstance.toast('时间已还原到实时状态', ToastMsgType.standby);
  }

  private isLeapYear(date: Date): boolean {
    const year = date.getUTCFullYear();

    if ((year & 3) !== 0) {
      return false;
    }

    return year % 100 !== 0 || year % 400 === 0;
  }

  getUTCDayOfYear(doy: Date) {
    const mn = doy.getUTCMonth();
    const dn = doy.getUTCDate();
    const dayCount = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334];
    let dayInYear = 365;
    let dayOfYear = dayCount[mn] + dn; // 移除 -1，使其返回基于1的索引

    if (mn > 1 && this.isLeapYear(doy)) {
      dayOfYear++;
      dayInYear++;
    }

    return dayOfYear;
  }

  getUTCDateFromDayOfYear(year: number, dayOfYear: number): Date {
    // dayOfYear 是基于1的索引 (1-365/366)
    if (dayOfYear < 1 || dayOfYear > 366) {
      throw new Error(`Invalid dayOfYear: ${dayOfYear}. Must be between 1 and 366.`);
    }

    const isLeapYear = this.isLeapYear(this.createUTCDate(year, 0, 1));
    const maxDays = isLeapYear ? 366 : 365;

    if (dayOfYear > maxDays) {
      throw new Error(`Invalid dayOfYear: ${dayOfYear}. Year ${year} has only ${maxDays} days.`);
    }

    const daysInMonth = [31, isLeapYear ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    let month = 0;
    let remainingDays = dayOfYear;

    // 找到正确的月份
    while (remainingDays > daysInMonth[month]) {
      remainingDays -= daysInMonth[month];
      month++;
    }

    // remainingDays 现在是该月的第几天（基于1的索引）
    return this.createUTCDate(year, month, remainingDays);
  }

  private createUTCDate(year: number, month?: number, day?: number, hours?: number, minutes?: number, seconds?: number): Date {
    const date = new Date(Date.UTC(year, month ?? 0, day ?? 1, hours ?? 0, minutes ?? 0, seconds ?? 0));

    return date;
  }
}
