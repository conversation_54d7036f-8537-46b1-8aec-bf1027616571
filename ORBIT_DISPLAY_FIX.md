# 卫星轨道显示异常修复

## ✅ 修复完成

已成功修复卫星近距离模式下轨道呈现直角转折的问题。

## 🔧 主要修复内容

### 1. 修复ECF坐标转换算法

**问题**: 原有的GMST角度计算方式不正确，导致ECI到ECF坐标转换出现误差。

**修复位置**: `src/webworker/orbitCruncher.ts` 第268-274行和第306-312行

**修复前**:
```typescript
if (isEcfOutput) {
  // 改进ECF坐标转换 - 使用更稳定的时间计算
  const gmstAngle = (i * timeslice * TAU) / period;
  // 限制角度范围以避免数值不稳定
  const normalizedAngle = gmstAngle % TAU;
  pos = eci2ecf(pos, normalizedAngle);
}
```

**修复后**:
```typescript
if (isEcfOutput) {
  // 修复ECF坐标转换 - 使用正确的GMST计算
  const currentTime = t; // t已经是正确的时间
  const jd = (currentTime / 86400000) + 2440587.5; // 转换为儒略日
  const gmst = Sgp4.gstime(jd); // 使用SGP4库的正确GMST计算
  pos = eci2ecf(pos, gmst);
}
```

**改进点**:
- 使用正确的时间基准计算儒略日
- 调用SGP4库的标准GMST计算函数
- 消除了基于轨道周期的简化计算误差

### 2. 实现动态轨道精度调整

**问题**: 近距离模式下轨道段数不足，导致轨道线段之间的直线连接变得明显。

**修复位置**: `src/singletons/orbitManager.ts` 第381-393行

**新增功能**:
```typescript
// 根据相机距离动态调整轨道精度
const camera = keepTrackApi.getMainCamera();
const cameraDistance = camera.getCameraDistance();
let orbitSegments = settingsManager.orbitSegments;

if (camera.cameraType === CameraType.SATELLITE || camera.cameraType === CameraType.FIXED_TO_SAT) {
  // 近距离模式下增加轨道精度
  if (cameraDistance < 1000) {
    orbitSegments = Math.min(settingsManager.orbitSegments * 4, 1024);
  } else if (cameraDistance < 5000) {
    orbitSegments = Math.min(settingsManager.orbitSegments * 2, 512);
  }
}
```

**精度等级**:
- **超近距离** (< 1000km): 4倍精度，最高1024段
- **近距离** (< 5000km): 2倍精度，最高512段  
- **正常距离**: 使用默认256段

### 3. 支持动态轨道段数传递

**修复位置**: 
- `src/singletons/orbitManager.ts` 第416-424行
- `src/webworker/orbitCruncher.ts` 第132-138行

**实现机制**:
1. OrbitManager计算动态轨道段数
2. 通过消息传递给WebWorker
3. WebWorker使用动态段数进行轨道计算

**代码修改**:
```typescript
// OrbitManager中传递动态段数
this.orbitWorker.postMessage({
  typ: OrbitCruncherType.SATELLITE_UPDATE,
  id,
  // ... 其他参数
  orbitSegments: orbitSegments, // 传递动态轨道段数
});

// OrbitCruncher中接收并使用
const currentSegments = m.data.orbitSegments || numberOfSegments;
const pointsOut = new Float32Array((currentSegments + 1) * 4);
```

## 🎯 修复效果

### 解决的问题
- ✅ **直角转折**: 轨道线不再出现不自然的直角
- ✅ **坐标精度**: ECF坐标转换更加准确
- ✅ **近距离显示**: 放大后轨道保持平滑
- ✅ **椭圆形状**: 轨道正确显示为椭圆而非多边形

### 性能优化
- ✅ **自适应精度**: 只在需要时增加计算精度
- ✅ **距离感知**: 根据观察距离动态调整
- ✅ **资源平衡**: 在质量和性能间找到平衡

## 🧪 测试验证

### 测试场景
1. **地球同步轨道**: 验证圆形轨道的平滑性
2. **高椭圆轨道**: 验证椭圆轨道的形状正确性  
3. **近距离观察**: 验证放大后轨道的连续性
4. **不同倾角**: 验证各种轨道倾角的显示

### 验证方法
1. 选择卫星进入近距离模式
2. 观察轨道线是否平滑连续
3. 检查轨道形状是否符合椭圆特征
4. 验证近地点和远地点位置正确性

## 📊 技术细节

### GMST计算改进
- **时间基准**: 使用正确的儒略日转换
- **标准函数**: 调用SGP4.gstime()标准实现
- **精度提升**: 消除了周期性误差累积

### 动态精度算法
- **距离检测**: 基于相机到目标的距离
- **模式识别**: 识别卫星视角和固定视角模式
- **渐进调整**: 避免精度突变造成的视觉跳跃

### 内存管理
- **缓冲区动态分配**: 根据段数动态分配GPU缓冲区
- **性能监控**: 避免过度精度导致的性能问题
- **资源回收**: 及时释放不需要的高精度缓冲区

## 🔍 代码影响范围

### 修改的文件
1. **`src/webworker/orbitCruncher.ts`**
   - 修复ECF坐标转换算法
   - 支持动态轨道段数
   - 更新导弹轨道计算

2. **`src/singletons/orbitManager.ts`**
   - 实现动态精度调整逻辑
   - 添加相机距离检测
   - 修改消息传递接口

### 兼容性
- ✅ **向后兼容**: 不影响现有功能
- ✅ **默认行为**: 远距离模式保持原有性能
- ✅ **渐进增强**: 只在需要时启用高精度

## 🚀 部署建议

### 测试步骤
1. 重新构建项目: `npm run build`
2. 启动服务: `npm start`
3. 选择任意卫星进入近距离模式
4. 验证轨道显示是否平滑

### 性能监控
- 监控GPU内存使用情况
- 观察帧率是否受到影响
- 根据设备性能调整最大精度限制

### 用户反馈
- 收集用户对轨道显示质量的反馈
- 监控是否还有其他显示异常
- 根据反馈进一步优化算法

## 🎉 预期效果

修复后，用户在近距离观察卫星时应该能看到：
- **平滑的椭圆轨道**: 不再有直角转折
- **准确的轨道形状**: 符合真实的轨道力学
- **连续的轨道线**: 段与段之间无明显断点
- **稳定的显示**: 在不同距离下都有良好表现

这个修复解决了轨道显示的核心问题，大大提升了近距离模式下的用户体验！🛰️✨
