# Keep Track 6 - Celestial Symphony

With the emphasis on sound enhancements, transition to a more modern codebase, and celestial-related features, we've named this version "Celestial Symphony". The name signifies harmony and unity in orbital tools and evokes a feeling of orchestration in the vastness of space, resonating with the newly added sound features and the immersive Milky Way textures.

## Software Release Documentation

In this software release, notable advancements include extensive updates on UI and functionality, a clear emphasis on performance optimization, migration from older libraries, and a concentrated effort towards debugging and enhancing reliability. The removal of jQuery across various functionalities underscores a drive towards a modern and streamlined codebase.

## Major Features

### UI & Aesthetics

- Introduced a new loading screen.
- Updated the about page.
- Optimized milky way textures for a more immersive experience.

### Functionality Enhancements

- Added new sensors to enhance tracking capabilities.
- Implemented additional TLE export options.
- Enhanced the sound effects for a more interactive experience, including a mute button for convenience.
- Improved the satellite search function on the control site.
- Implemented ECI, ECF, RIC, and other analytic plots, strengthening orbital analysis tools.
- Introduced an error reporting feature for more immediate feedback on issues.

### Code & Infrastructure Updates

- Migrated from satellite.js to ootk, illustrating a commitment to using updated and reliable orbital math libraries.
- Removed jQuery from various modules, emphasizing a move to a more modern and performant codebase.
- Added Google Cloud support, indicating a push towards scalable infrastructure.
- Optimized code for better performance, notably color calculation speed and caching of the satrec object.

### Bug Fixes

- Addressed several bugs related to user interface, TLE formatting, and satellite calculations.
- Enhanced the breakup logic and options for realism.
- Fixed issues related to ray casting on Earth.
- Updated and fixed issues with the next pass time, lookangles menu, countries filter, and other satellite-related features.

### Performance Improvements

- Reduced complexity in legend color code for better legibility.
- Optimized milky way textures for better visual appeal.
- Improved the loading times for a more responsive user experience.

### Documentation

- Regularly updated the changelog to reflect changes and new features.
