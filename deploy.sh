#!/bin/bash

# 部署脚本 - 上传并编译运行keeptrack.space项目

# 配置变量
REMOTE_HOST="your-server-ip"
REMOTE_USER="your-username"
REMOTE_PATH="/var/www/keeptrack.space"
LOCAL_PATH="."

echo "开始部署 keeptrack.space 项目..."

# 1. 创建远程目录
echo "创建远程目录..."
ssh $REMOTE_USER@$REMOTE_HOST "mkdir -p $REMOTE_PATH"

# 2. 上传项目文件
echo "上传项目文件..."
rsync -avz --exclude 'node_modules' --exclude '.git' --exclude 'dist' $LOCAL_PATH/ $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/

# 3. 连接到服务器并执行部署命令
echo "连接到服务器执行部署..."
ssh $REMOTE_USER@$REMOTE_HOST << 'EOF'
cd /var/www/keeptrack.space

# 安装依赖
echo "安装 Node.js 依赖..."
npm install

# 编译项目
echo "编译项目..."
npm run build

# 检查编译结果
if [ $? -eq 0 ]; then
    echo "编译成功！"
    
    # 重启服务（如果使用PM2）
    if command -v pm2 &> /dev/null; then
        echo "重启 PM2 服务..."
        pm2 restart keeptrack.space || pm2 start npm --name "keeptrack.space" -- start
    else
        echo "启动服务..."
        npm start &
    fi
    
    echo "部署完成！"
else
    echo "编译失败！"
    exit 1
fi
EOF

echo "部署脚本执行完成！" 