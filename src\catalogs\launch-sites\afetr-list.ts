export const AFETRList = {
  'AFETR-LC36A': {
    name: 'Launch Complex 36A',
    lat: 28.471375,
    lon: -80.537876,
    site: 'Cape Canaveral Space Force Station',
    country: 'United States',
    wikiUrl: 'https://en.wikipedia.org/wiki/Cape_Canaveral_Launch_Complex_36',
  },
  'AFETR-LC36B': {
    name: 'Launch Complex 36B',
    lat: 28.468341,
    lon: -80.54111,
    site: 'Cape Canaveral Space Force Station',
    country: 'United States',
    wikiUrl: 'https://en.wikipedia.org/wiki/Cape_Canaveral_Launch_Complex_36',
  },
  'AFETR-LC39A': {
    name: 'Launch Complex 39A',
    lat: 28.608331,
    lon: -80.604273,
    site: 'Kennedy Space Center',
    country: 'United States',
    wikiUrl: 'https://en.wikipedia.org/wiki/Kennedy_Space_Center_Launch_Complex_39A',
  },
  'AFETR-LC39B': {
    name: 'Launch Complex 39B',
    lat: 28.627204,
    lon: -80.621004,
    site: 'Kennedy Space Center',
    country: 'United States',
    wikiUrl: 'https://en.wikipedia.org/wiki/Kennedy_Space_Center_Launch_Complex_39B',
  },
  'AFETR-LC40': {
    name: 'Space Launch Complex 40',
    lat: 28.562001,
    lon: -80.577352,
    site: 'Cape Canaveral Space Force Station',
    country: 'United States',
    wikiUrl: 'https://en.wikipedia.org/wiki/Cape_Canaveral_Space_Launch_Complex_40',
  },
  'AFETR-LC41': {
    name: 'Space Launch Complex 41',
    lat: 28.583445,
    lon: -80.583032,
    site: 'Cape Canaveral Space Force Station',
    country: 'United States',
    wikiUrl: 'https://en.wikipedia.org/wiki/Cape_Canaveral_Space_Launch_Complex_41',
  },
  'AFETR-LC46': {
    name: 'Space Launch Complex 46',
    lat: 28.458333,
    lon: -80.528333,
    site: 'Cape Canaveral Space Force Station',
    country: 'United States',
    wikiUrl: 'https://en.wikipedia.org/wiki/Cape_Canaveral_Space_Launch_Complex_46',
  },
  ERAS: {
    name: 'Eastern Range Airspace',
    lat: 28.46,
    lon: 279.45,
    site: 'Cape Canaveral Space Force Station',
    country: 'United States',
    wikiUrl: 'https://en.wikipedia.org/wiki/Cape_Canaveral_Space_Force_Station',
  },
};
