# 账号有效期管理功能

## ✅ 功能概述

已成功为管理员界面添加了完整的账号有效期管理功能，管理员可以为每个账号设置有效期，过期后账号将不可用。

## 🔧 实现的功能

### 1. 后端功能
- ✅ 用户数据结构添加 `expiresAt` 字段
- ✅ 登录时检查账号是否过期
- ✅ Token验证时检查账号是否过期
- ✅ 创建用户时支持设置有效期
- ✅ 提供设置用户有效期的API
- ✅ 提供获取即将过期用户的API
- ✅ 提供获取已过期用户的API

### 2. 前端功能
- ✅ 创建用户表单添加有效期字段
- ✅ 用户列表显示有效期状态
- ✅ 过期和即将过期用户的视觉提示
- ✅ 设置有效期的对话框
- ✅ 批量管理有效期功能

## 🎯 功能特点

### 安全性
1. **登录拦截**: 过期账号无法登录，显示"账户已过期"错误
2. **Token验证**: 已登录的过期账号会被自动登出
3. **实时检查**: 每次API调用都会验证账号有效期

### 用户体验
1. **视觉提示**: 
   - 已过期账号：红色背景 + "已过期"标识
   - 即将过期账号（7天内）：橙色背景 + "X天后过期"提示
   - 正常账号：显示具体过期日期或"永不过期"

2. **便捷操作**:
   - 一键设置有效期
   - 支持永不过期设置
   - 批量查看过期状态

### 管理功能
1. **灵活设置**: 支持设置任意日期为有效期
2. **永不过期**: 留空有效期表示永不过期
3. **即时生效**: 设置后立即生效，无需重启

## 📋 API 接口

### 创建用户（支持有效期）
```
POST /api/auth/create-user
Body: {
  "username": "testuser",
  "email": "<EMAIL>", 
  "password": "password123",
  "role": "user",
  "expiresAt": "2024-12-31T23:59:59.999Z"  // 可选
}
```

### 设置用户有效期
```
POST /api/auth/set-user-expiration/:userId
Body: {
  "expiresAt": "2024-12-31T23:59:59.999Z"  // 留空表示永不过期
}
```

### 获取即将过期用户
```
GET /api/auth/expiring-users
返回: 7天内即将过期的用户列表
```

### 获取已过期用户
```
GET /api/auth/expired-users
返回: 已过期的用户列表
```

## 🧪 测试指南

### 1. 测试创建带有效期的用户
1. 登录管理后台
2. 点击"创建新用户"
3. 填写用户信息，设置有效期为明天
4. 创建成功后，在用户列表中查看有效期显示

### 2. 测试设置有效期
1. 在用户列表中点击"设置有效期"按钮
2. 选择新的有效期日期
3. 确认设置，查看列表更新

### 3. 测试过期账号登录
1. 创建一个已过期的测试账号（设置昨天的日期）
2. 尝试用该账号登录
3. 应该显示"账户已过期"错误

### 4. 测试已登录账号过期
1. 用正常账号登录
2. 在管理后台将该账号设置为已过期
3. 刷新页面或进行API操作，应该被自动登出

## 🎨 界面展示

### 用户列表界面
- **正常用户**: 显示具体过期日期
- **永不过期**: 显示"永不过期"
- **即将过期**: 橙色背景 + "X天后过期"
- **已过期**: 红色背景 + "已过期"

### 设置有效期对话框
- 用户名（只读）
- 有效期日期选择器
- 提示信息："留空表示永不过期"

## 🔧 技术实现

### 数据结构
```typescript
interface User {
  id: string;
  username: string;
  password: string;
  role: 'admin' | 'user';
  email?: string;
  createdAt: string;
  lastLogin?: string;
  isActive: boolean;
  failedLoginAttempts: number;
  lockedUntil?: string;
  needsPasswordChange: boolean;
  expiresAt?: string; // 新增：账号有效期
}
```

### 过期检查逻辑
```javascript
// 检查账号是否过期
if (user.expiresAt && new Date(user.expiresAt) < new Date()) {
  return { error: '账户已过期，请联系管理员' };
}
```

### 有效期显示逻辑
```javascript
if (user.expiresAt) {
  const expirationDate = new Date(user.expiresAt);
  const now = new Date();
  const daysUntilExpiration = Math.ceil((expirationDate - now) / (1000 * 60 * 60 * 24));
  
  if (expirationDate < now) {
    // 已过期
  } else if (daysUntilExpiration <= 7) {
    // 即将过期
  } else {
    // 正常
  }
}
```

## 📁 修改的文件

### 后端文件
1. `src/auth/auth.service.ts`
   - 添加 `expiresAt` 字段到User接口
   - 修改登录验证逻辑
   - 修改token验证逻辑
   - 添加设置有效期方法
   - 添加获取过期用户方法

2. `src/api/auth.routes.ts`
   - 修改创建用户路由支持有效期
   - 添加设置有效期路由
   - 添加获取过期用户路由

### 前端文件
1. `public/admin-simple.html`
   - 创建用户表单添加有效期字段
   - 用户列表添加有效期列
   - 添加设置有效期对话框
   - 修改displayUsers函数显示有效期
   - 添加设置有效期相关JavaScript函数
   - 添加过期用户样式

## 🎉 使用效果

1. **安全性提升**: 过期账号无法继续使用系统
2. **管理便捷**: 管理员可以灵活设置和管理账号有效期
3. **视觉清晰**: 不同状态的账号有明确的视觉区分
4. **操作简单**: 一键设置有效期，支持永不过期

所有功能已完成并可以立即使用！🚀
