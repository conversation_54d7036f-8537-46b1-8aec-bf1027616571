# 首页登录问题故障排除指南

## 🔍 问题诊断

### 当前问题
用户反馈首页无法登录，可能的原因包括：
1. API服务器未启动
2. 认证检查脚本问题
3. 网络连接问题
4. 浏览器缓存问题

## 🛠️ 诊断工具

### 1. 使用调试页面
访问: http://localhost:8080/index-debug.html

这个页面会自动检查：
- 页面状态
- 认证令牌状态
- API连接状态
- 用户认证状态

### 2. 检查服务器状态

#### 确保API服务器运行
```bash
# 启动API服务器
npm run start:api

# 检查端口3001是否被占用
netstat -an | findstr :3001
```

#### 确保Web服务器运行
```bash
# 启动Web服务器
npm start

# 检查端口8080是否被占用
netstat -an | findstr :8080
```

### 3. 浏览器调试

#### 打开开发者工具
1. 访问 http://localhost:8080
2. 按F12打开开发者工具
3. 查看Console标签页的错误信息
4. 查看Network标签页的网络请求

#### 检查本地存储
在Console中运行：
```javascript
// 检查认证令牌
console.log('Token:', localStorage.getItem('authToken'));

// 检查用户信息
console.log('User:', localStorage.getItem('user'));

// 清除认证数据（如果需要）
localStorage.removeItem('authToken');
localStorage.removeItem('user');
```

## 🔧 解决方案

### 方案1: 重新启动服务器
```bash
# 停止所有服务
Ctrl+C (在运行服务器的终端中)

# 重新启动API服务器
npm run start:api

# 重新启动Web服务器（新终端）
npm start
```

### 方案2: 清除浏览器缓存
1. 按Ctrl+Shift+Delete
2. 选择"所有时间"
3. 勾选"缓存的图片和文件"
4. 点击"清除数据"
5. 刷新页面

### 方案3: 手动登录测试
1. 访问: http://localhost:8080/index-debug.html
2. 点击"测试登录"按钮
3. 如果成功，点击"前往主页"
4. 如果失败，查看错误信息

### 方案4: 直接访问登录页面
1. 访问: http://localhost:8080/login.html
2. 使用默认账户登录:
   - 用户名: admin
   - 密码: SpaceDefense2025!
3. 登录成功后会自动跳转到主页

### 方案5: 检查API连接
在浏览器Console中运行：
```javascript
// 测试API连接
fetch('http://localhost:3001/api/auth/verify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ token: 'test' })
})
.then(response => {
    console.log('API响应状态:', response.status);
    return response.json();
})
.then(data => console.log('API响应数据:', data))
.catch(error => console.error('API连接错误:', error));
```

## 📋 常见错误及解决方法

### 错误1: "正在跳转到登录页面..."一直显示
**原因**: API服务器未启动或连接失败
**解决**: 
1. 检查API服务器是否运行在端口3001
2. 重新启动API服务器
3. 检查防火墙设置

### 错误2: 页面空白
**原因**: JavaScript错误或认证脚本问题
**解决**:
1. 查看浏览器控制台错误
2. 清除浏览器缓存
3. 使用调试页面测试

### 错误3: "Failed to fetch"
**原因**: 网络连接问题或CORS问题
**解决**:
1. 检查API服务器状态
2. 确认端口配置正确
3. 检查网络连接

### 错误4: "401 Unauthorized"
**原因**: 认证令牌无效或过期
**解决**:
1. 清除本地存储的认证数据
2. 重新登录
3. 检查令牌生成逻辑

### 错误5: "403 Forbidden"
**原因**: 权限不足
**解决**:
1. 确认使用管理员账户
2. 检查用户角色设置
3. 重新登录

## 🔄 完整重置流程

如果所有方法都无效，执行完整重置：

### 1. 停止所有服务
```bash
# 在所有运行服务器的终端中按Ctrl+C
```

### 2. 清除数据
```bash
# 删除数据目录（可选，会清除所有用户数据）
rm -rf data/
# 或在Windows中
rmdir /s data
```

### 3. 重新构建
```bash
npm run build
```

### 4. 重新启动
```bash
# 启动API服务器
npm run start:api

# 启动Web服务器（新终端）
npm start
```

### 5. 清除浏览器数据
1. 清除浏览器缓存
2. 清除本地存储
3. 关闭所有浏览器标签页
4. 重新打开浏览器

### 6. 测试登录
1. 访问: http://localhost:8080/index-debug.html
2. 执行所有测试
3. 如果成功，访问主页

## 📞 获取帮助

### 收集诊断信息
如果问题仍然存在，请收集以下信息：

1. **浏览器信息**:
   - 浏览器类型和版本
   - 操作系统版本

2. **错误信息**:
   - 浏览器控制台错误
   - 网络请求状态
   - 服务器日志

3. **环境信息**:
   - Node.js版本
   - npm版本
   - 端口占用情况

4. **测试结果**:
   - 调试页面测试结果
   - API连接测试结果
   - 手动登录测试结果

### 联系支持
- **邮箱**: <EMAIL>
- **提供**: 详细的错误描述和上述诊断信息

## 🎯 预防措施

### 1. 定期检查
- 定期重启服务器
- 清除浏览器缓存
- 检查端口占用

### 2. 监控日志
- 查看API服务器日志
- 监控错误频率
- 记录常见问题

### 3. 备份配置
- 备份用户数据
- 保存配置文件
- 记录环境设置

---

**注意**: 在生产环境中，建议使用更稳定的认证机制和错误处理策略。
