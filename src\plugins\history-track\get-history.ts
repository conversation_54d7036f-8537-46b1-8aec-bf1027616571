// 前端专用：通过后端 API 获取历史轨道数据

export interface HistoryRecord {
  norad_id: number;
  time: string;
  [key: string]: any;
}

async function getApiServerUrl() {
  // 使用配置加载器获取API地址
  if ((window as any).configLoader) {
    try {
      const apiBaseUrl = await (window as any).configLoader.getApiBaseUrl();
      console.log('从配置加载器获取API地址:', apiBaseUrl);
      return apiBaseUrl;
    } catch (error) {
      console.warn('无法从配置加载器获取API地址:', error);
    }
  }

  try {
    // 尝试从配置文件获取API地址
    const response = await fetch('/config.json');
    if (response.ok) {
      const config = await response.json();
      if (config.apiServer && config.apiServer.url) {
        console.log('从配置文件获取API地址:', config.apiServer.url);
        return config.apiServer.url;
      }
    }
  } catch (e) {
    console.warn('无法获取配置文件，使用默认地址:', e);
  }

  // 兜底：使用当前域名和默认端口
  const currentHost = window.location.hostname;
  const defaultUrl = `http://${currentHost}:5001`;
  console.log('使用默认API地址:', defaultUrl);
  return defaultUrl;
}

/**
 * 通过后端 API 获取历史轨道数据
 * @param noradIds NORAD 编号数组
 * @param startDate 开始日期（YYYY-MM-DD）
 * @param endDate 结束日期（YYYY-MM-DD）
 * @returns Promise<HistoryRecord[]>
 */
export async function getHistoryFromES(noradIds: number[], startDate: string, endDate: string): Promise<HistoryRecord[]> {
  const params = new URLSearchParams({
    norad_id: noradIds.join(','),
    start: startDate,
    end: endDate,
  });
  const apiBaseUrl = await getApiServerUrl();
  console.log('使用API地址:', apiBaseUrl);
  
  const response = await fetch(`${apiBaseUrl}/api/es-history?${params.toString()}`);
  if (!response.ok) {
    throw new Error(`ES API 查询失败: ${response.status} ${response.statusText}`);
  }
  const result = await response.json();
  if (!Array.isArray(result.data)) {
    throw new Error('ES API 返回数据格式错误');
  }
  return result.data;
} 