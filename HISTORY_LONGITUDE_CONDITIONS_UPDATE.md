# 历史经度功能条件更新

## ✅ 修改完成

已成功更新sat-info-box界面中"查看历史经度"功能的有效条件。

## 🔧 修改内容

### 1. 更新有效条件

**原有条件**:
- 倾角20度以内
- 轨道高度大于34000km

**新条件**:
- ✅ **倾角20度以内**
- ✅ **非JSC卫星名字开头**
- ✅ **近地点大于34000km**
- ✅ **远地点小于37000km**
- ✅ **必须有NORAD编号**

### 2. 统一错误提示

**所有不符合条件的情况都显示统一提示**:
```
"仅限非JSC名字的GEO卫星"
```

**触发统一提示的情况**:
- JSC名字开头的卫星
- 没有NORAD编号的卫星
- 倾角超过20度的卫星
- 近地点≤34000km的卫星
- 远地点≥37000km的卫星

## 📝 代码修改详情

### 修改文件: `src/plugins/select-sat-manager/sat-info-box.ts`

#### 1. 更新显示条件逻辑 (第650-665行)

**修改前**:
```typescript
// 根据轨道倾角和高度决定是否显示历史经度选项（倾角≤20度且轨道高度>34000km）
const historyLongitudeRow = getEl('sat-history-longitude-row');
if (historyLongitudeRow) {
  // 使用SatMath.getAlt()计算当前高度
  const gmst = keepTrackApi.getTimeManager().gmst;
  const altitude = SatMath.getAlt(sat.position, gmst);

  // 确保卫星数据已完全加载
  if (typeof sat.inclination === 'number' && !isNaN(sat.inclination) &&
      typeof altitude === 'number' && !isNaN(altitude)) {
    if (sat.inclination <= 20 && altitude > 34000) {
      historyLongitudeRow.style.display = 'flex';
    } else {
      historyLongitudeRow.style.display = 'none';
    }
  } else {
    historyLongitudeRow.style.display = 'none';
  }
}
```

**修改后**:
```typescript
// 根据新的条件决定是否显示历史经度选项（倾角≤20度、非JSC名字、近地点>34000km、远地点<37000km）
const historyLongitudeRow = getEl('sat-history-longitude-row');
if (historyLongitudeRow) {
  // 检查所有条件
  const hasNoradId = sat.sccNum && sat.sccNum.trim() !== '';
  const isNotJSC = !sat.name || !sat.name.toUpperCase().startsWith('JSC');
  const isValidInclination = typeof sat.inclination === 'number' && !isNaN(sat.inclination) && sat.inclination <= 20;
  const isValidPerigee = typeof sat.perigee === 'number' && !isNaN(sat.perigee) && sat.perigee > 34000;
  const isValidApogee = typeof sat.apogee === 'number' && !isNaN(sat.apogee) && sat.apogee < 37000;

  if (hasNoradId && isNotJSC && isValidInclination && isValidPerigee && isValidApogee) {
    historyLongitudeRow.style.display = 'flex';
  } else {
    historyLongitudeRow.style.display = 'none';
  }
}
```

#### 2. 更新点击事件逻辑 (第2066-2100行)

**修改前**:
```typescript
private openHistoryLongitude_() {
  keepTrackApi.getSoundManager().play(SoundNames.CLICK);

  // 获取当前选中的卫星
  const selectedSat = this.selectSatManager_.getSelectedSat();
  if (!selectedSat?.isSatellite()) {
    keepTrackApi.getUiManager().toast('请先选择一个卫星', ToastMsgType.caution, false);
    return;
  }

  const sat = selectedSat as DetailedSatellite;

  // 检查轨道倾角和高度条件
  if (sat.inclination > 20) {
    keepTrackApi.getUiManager().toast('历史经度功能仅适用于轨道倾角20度以内的卫星', ToastMsgType.caution, false);
    return;
  }

  // 使用SatMath.getAlt()计算当前高度
  const gmst = keepTrackApi.getTimeManager().gmst;
  const altitude = SatMath.getAlt(sat.position, gmst);
  if (altitude <= 34000) {
    keepTrackApi.getUiManager().toast('历史经度功能仅适用于轨道高度大于34000km的卫星', ToastMsgType.caution, false);
    return;
  }
  // ... 其余代码
}
```

**修改后**:
```typescript
private openHistoryLongitude_() {
  keepTrackApi.getSoundManager().play(SoundNames.CLICK);

  // 获取当前选中的卫星
  const selectedSat = this.selectSatManager_.getSelectedSat();
  if (!selectedSat?.isSatellite()) {
    keepTrackApi.getUiManager().toast('请先选择一个卫星', ToastMsgType.caution, false);
    return;
  }

  const sat = selectedSat as DetailedSatellite;

  // 检查是否有NORAD编号
  if (!sat.sccNum || sat.sccNum.trim() === '') {
    keepTrackApi.getUiManager().toast('仅限非JSC名字的GEO卫星', ToastMsgType.caution, false);
    return;
  }

  // 检查是否为JSC卫星（名字以JSC开头）
  if (sat.name && sat.name.toUpperCase().startsWith('JSC')) {
    keepTrackApi.getUiManager().toast('仅限非JSC名字的GEO卫星', ToastMsgType.caution, false);
    return;
  }

  // 检查轨道倾角条件
  if (sat.inclination > 20) {
    keepTrackApi.getUiManager().toast('仅限非JSC名字的GEO卫星', ToastMsgType.caution, false);
    return;
  }

  // 检查近地点和远地点条件
  if (sat.perigee <= 34000 || sat.apogee >= 37000) {
    keepTrackApi.getUiManager().toast('仅限非JSC名字的GEO卫星', ToastMsgType.caution, false);
    return;
  }
  // ... 其余代码
}
```

#### 3. 更新tooltip描述 (第1274-1275行)

**修改前**:
```html
data-tooltip="查看该卫星的历史经度变化数据（仅限轨道倾角20度以内且轨道高度大于34000km的卫星）"
```

**修改后**:
```html
data-tooltip="查看该卫星的历史经度变化数据（仅限非JSC名字的GEO卫星：倾角≤20度、近地点>34000km、远地点<37000km）"
```

## 🎯 功能特点

### 1. 智能显示控制
- 只有符合所有条件的卫星才会显示"历史经度"选项
- 不符合条件的卫星不会看到该选项，避免用户困惑

### 2. 统一用户体验
- 所有不符合条件的情况都显示相同的提示信息
- 提示信息简洁明了，直接说明适用范围

### 3. 严格条件检查
- **NORAD编号检查**: 确保卫星有有效的编号
- **JSC名字过滤**: 排除所有以"JSC"开头的卫星
- **轨道参数验证**: 检查倾角、近地点、远地点是否符合GEO卫星特征

### 4. 数据完整性验证
- 检查所有必要的轨道参数是否存在且有效
- 防止因数据缺失导致的错误判断

## 🧪 测试场景

### 符合条件的卫星
- 非JSC名字
- 有NORAD编号
- 倾角 ≤ 20度
- 近地点 > 34000km
- 远地点 < 37000km

**预期结果**: 显示"历史经度"选项，点击可正常打开

### 不符合条件的卫星

#### 1. JSC名字卫星
- 名字以"JSC"开头的卫星

**预期结果**: 不显示"历史经度"选项

#### 2. 无NORAD编号卫星
- sccNum为空或无效

**预期结果**: 不显示"历史经度"选项

#### 3. 高倾角卫星
- 倾角 > 20度

**预期结果**: 不显示"历史经度"选项

#### 4. 非GEO轨道卫星
- 近地点 ≤ 34000km 或 远地点 ≥ 37000km

**预期结果**: 不显示"历史经度"选项

### 强制点击测试
如果通过其他方式触发点击事件（如开发者工具），所有不符合条件的情况都会显示：
```
"仅限非JSC名字的GEO卫星"
```

## 🔍 技术细节

### 条件检查顺序
1. **NORAD编号检查** - 最基础的要求
2. **JSC名字检查** - 排除特定类型卫星
3. **轨道倾角检查** - GEO卫星特征
4. **近地点/远地点检查** - GEO轨道高度范围

### 数据类型安全
- 使用`typeof`检查确保数据类型正确
- 使用`!isNaN()`确保数值有效
- 使用`trim()`处理字符串空白

### 性能优化
- 条件检查按照最可能失败的顺序排列
- 早期返回避免不必要的计算

## 🎉 预期效果

修改后，用户将看到：
- **更精确的功能定位**: 明确针对非JSC的GEO卫星
- **更清晰的使用条件**: tooltip详细说明适用范围
- **更一致的用户体验**: 统一的错误提示信息
- **更可靠的功能过滤**: 严格的条件检查确保功能正确性

这个修改确保了"历史经度"功能只对真正的GEO卫星（排除JSC卫星）开放，提升了功能的专业性和准确性！🛰️✨
