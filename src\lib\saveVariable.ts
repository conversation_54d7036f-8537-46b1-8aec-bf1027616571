/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * saveVariable.ts exports two functions saveVariable and saveCsv that save a variable
 * and an array of objects as a text file and a CSV file respectively. The file also imports
 * saveAs function from file-saver library and errorManagerInstance singleton from
 * errorManager.ts file.
 * https://spacedefense
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { saveAs } from 'file-saver';
import Papa from 'papaparse';
import { errorManagerInstance } from '../singletons/errorManager';
import { isThisNode } from '../static/isThisNode';

/**
 * Saves a variable as a text file.
 * @param variable The variable to be saved as a text file.
 * @param filename The name of the text file to be saved. Defaults to 'variable.txt'.
 */
export const saveVariable = <T>(variable: T, filename?: string): void => {
  try {
    filename = filename || 'variable.txt';
    const variableStr = JSON.stringify(variable, getCircularReplacer());
    const blob = new Blob([variableStr], { type: 'text/plain;charset=utf-8' });

    if (!saveAs) {
      throw new Error('saveAs is unavailable!');
    }
    saveAs(blob, filename);
  } catch (e) {
    errorManagerInstance.error(e, 'saveVariable', 'Error in saving variable!');
  }
};

/**
 * Returns a replacer function that can be used with JSON.stringify to handle circular references.
 * @returns A replacer function that replaces circular references with null.
 */
export const getCircularReplacer = () => {
  const seen = new WeakSet();


  return (_key: string, value: object) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return null;
      }
      seen.add(value);
    }

    return value;
  };
};

/**
 * Saves an array of objects as a CSV file.
 * @param items The array of objects to be saved as CSV.
 * @param name The name of the CSV file to be saved. Defaults to 'data'.
 */
export const saveCsv = <T extends Record<string, unknown>>(items: Array<T>, name?: string): void => {
  try {
    const csv = Papa.unparse(items);
    const blob = new Blob([csv], { type: 'text/plain;charset=utf-8' });

    if (!saveAs) {
      throw new Error('saveAs is unavailable!');
    }
    name ??= 'data';
    saveAs(blob, `${name}.csv`);
  } catch (e) {
    if (!isThisNode()) {
      errorManagerInstance.error(e, 'saveVariable', 'Error in saving csv!');
    }
  }
};
