
/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * reports.ts is a plugin for generating quick reports in text format of various
 * satellite and sensor data
 *
 * http://www.spacesecure.cn
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { KeepTrackApiEvents, MenuMode } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import { errorManagerInstance } from '@app/singletons/errorManager';
import analysisPng from '@public/img/icons/reports.png';


import { t7e } from '@app/locales/keys';
import { BaseObject, DetailedSatellite, DetailedSensor, MILLISECONDS_PER_SECOND } from 'ootk';
import { ClickDragOptions, KeepTrackPlugin } from '../KeepTrackPlugin';
import { SelectSatManager } from '../select-sat-manager/select-sat-manager';

interface ReportData {
  filename: string;
  header: string;
  body: string;
  columns?: number;
  isHeaders?: boolean;
}

export class ReportsPlugin extends KeepTrackPlugin {
  readonly id = 'ReportsPlugin';
  dependencies_ = [SelectSatManager.name];
  private readonly selectSatManager_: SelectSatManager;

  constructor() {
    super();
    this.selectSatManager_ = keepTrackApi.getPlugin(SelectSatManager) as unknown as SelectSatManager; // this will be validated in KeepTrackPlugin constructor
  }

  isRequireSatelliteSelected = true;

  menuMode: MenuMode[] = [MenuMode.ANALYSIS, MenuMode.ALL];

  bottomIconImg = analysisPng;
  isIconDisabledOnLoad = true;
  isIconDisabled = true;
  sideMenuElementName: string = 'reports-menu';
  sideMenuElementHtml: string = keepTrackApi.html`
  <div id="reports-menu" class="side-menu-parent start-hidden text-select">
    <div id="reports-content" class="side-menu">
      <div class="row">
        <h5 class="center-align">报告</h5>
        <div class="divider"></div>
        <div class="center-align" style="display: flex; flex-direction: column; gap: 10px; margin-top: 10px; margin-left: 10px; margin-right: 10px;">
          <button
              id="aer-report-btn" class="btn btn-ui waves-effect waves-light" type="button" name="action">方位角仰角范围 &#9658;
          </button>
          <button
              id="lla-report-btn" class="btn btn-ui waves-effect waves-light" type="button" name="action">纬度 经度 高度 &#9658;
          </button>
          <button
              id="eci-report-btn" class="btn btn-ui waves-effect waves-light" type="button" name="action">地心惯性 &#9658;
          <button
              id="coes-report-btn" class="btn btn-ui waves-effect waves-light" type="button" name="action">经典轨道元素 &#9658;
          </button>
        </div>
      </div>
    </div>
  </div>
  `;

  dragOptions: ClickDragOptions = {
    isDraggable: false,
    minWidth: 320,
  };

  addJs(): void {
    super.addJs();
    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {
        getEl('aer-report-btn')!.addEventListener('click', () => this.generateAzElRng_());
        getEl('coes-report-btn')!.addEventListener('click', () => this.generateClasicalOrbElJ2000_());
        getEl('eci-report-btn')!.addEventListener('click', () => this.generateEci_());
        getEl('lla-report-btn')!.addEventListener('click', () => this.generateLla_());
      },
    );

    keepTrackApi.on(
      KeepTrackApiEvents.selectSatData,
      (obj: BaseObject) => {
        if (obj?.isSatellite()) {
          getEl(this.bottomIconElementName)?.classList.remove('bmenu-item-disabled');
          this.isIconDisabled = false;
        } else {
          getEl(this.bottomIconElementName)?.classList.add('bmenu-item-disabled');
          this.isIconDisabled = true;
        }
      },
    );
  }

  private generateAzElRng_() {
    const sat = this.getSat_();
    const sensor = this.getSensor_();

    if (!sat || !sensor) {
      return;
    }

    const header = `方位角仰角范围报告\n-------------------------------\n${this.createHeader_(sat, sensor)}`;
    let body = '时间 (UTC),方位角(°),仰角(°),范围(km)\n';
    const durationInSeconds = 72 * 60 * 60;
    let isInCoverage = false;
    let time = this.getStartTime_();

    for (let t = 0; t < durationInSeconds; t += 30) {
      time = new Date(time.getTime() + MILLISECONDS_PER_SECOND * 30);
      const rae = sensor.rae(sat, time);

      if (rae.el > 0) {
        isInCoverage = true;
        body += `${this.formatTime_(time)},${rae.az.toFixed(3)},${rae.el.toFixed(3)},${rae.rng.toFixed(3)}\n`;
      } else if (isInCoverage) {
        // If we were in coverage but now we are not, add a blank line to separate the passes
        body += '\n\n';
        isInCoverage = false;
      }
    }

    if (body === '时间 (UTC),方位角(°),仰角(°),范围(km)\n') {
      body += 'No passes found!';
    }

    this.writeReport_({
      filename: `aer-${sat.sccNum}`,
      header,
      body,
    });
  }

  private formatTime_(time: Date) {
    const timeStr = time.toISOString();
    const timeStrSplit = timeStr.split('T');
    const date = timeStrSplit[0];
    const timeSplit = timeStrSplit[1].split('.');
    const timeOut = timeSplit[0];

    return `${date} ${timeOut}`;
  }

  private generateLla_() {
    const sat = this.getSat_();

    if (!sat) {
      return;
    }

    const header = `纬度经度高度报告\n-------------------------------\n${this.createHeader_(sat)}`;
    let body = '时间(UTC),纬度(°),经度(°),高度(km)\n';
    const durationInSeconds = 72 * 60 * 60;
    let time = this.getStartTime_();

    for (let t = 0; t < durationInSeconds; t += 30) {
      time = new Date(time.getTime() + 30 * MILLISECONDS_PER_SECOND);
      const lla = sat.lla(time);

      body += `${this.formatTime_(time)},${lla.lat.toFixed(3)},${lla.lon.toFixed(3)},${lla.alt.toFixed(3)}\n`;
    }

    this.writeReport_({
      filename: `lla-${sat.sccNum}`,
      header,
      body,
    });
  }

  private generateEci_() {
    const sat = this.getSat_();

    if (!sat) {
      return;
    }

    const header = `地心惯性报告\n-------------------------------\n${this.createHeader_(sat)}`;
    let body = '时间(UTC),位置 X(km),位置 Y(km),位置 Z(km),速度 X(km/s),速度 Y(km/s),速度 Z(km/s)\n';
    const durationInSeconds = 72 * 60 * 60;
    let time = this.getStartTime_();

    for (let t = 0; t < durationInSeconds; t += 30) {
      time = new Date(time.getTime() + 30 * MILLISECONDS_PER_SECOND);
      const eci = sat.eci(time);

      body += `${this.formatTime_(time)},${eci.position.x.toFixed(3)},${eci.position.y.toFixed(3)},${eci.position.z.toFixed(3)},` +
        `${eci.velocity.x.toFixed(3)},${eci.velocity.y.toFixed(3)},${eci.velocity.z.toFixed(3)}\n`;
    }

    this.writeReport_({
      filename: `eci-${sat.sccNum}`,
      header,
      body,
      columns: 7,
      isHeaders: true,
    });
  }

  private createHeader_(sat: DetailedSatellite, sensor?: DetailedSensor) {
    const satData = '' +
      `日期: ${new Date().toISOString()}\n` +
      `卫星: ${sat.name}\n` +
      `NORAD编号: ${sat.sccNum}\n` +
      `备用ID: ${sat.altId || 'None'}\n` +
      `国际代码: ${sat.intlDes}\n\n`;
    const sensorData = '' +
      `传感器: ${sensor ? sensor.name : 'None'}\n` +
      `类型: ${sensor ? sensor.getTypeString() : 'None'}\n` +
      `纬度: ${sensor ? sensor.lat : 'None'}\n` +
      `经度: ${sensor ? sensor.lon : 'None'}\n` +
      `高度: ${sensor ? sensor.alt : 'None'}\n` +
      `最小方位角: ${sensor ? sensor.minAz : 'None'}\n` +
      `最大方位角: ${sensor ? sensor.maxAz : 'None'}\n` +
      `最小仰角: ${sensor ? sensor.minEl : 'None'}\n` +
      `最大仰角: ${sensor ? sensor.maxEl : 'None'}\n` +
      `最小范围: ${sensor ? sensor.minRng : 'None'}\n` +
      `最大范围: ${sensor ? sensor.maxRng : 'None'}\n\n`;


    return sensor ? `${satData}${sensorData}` : `${satData}`;
  }

  private generateClasicalOrbElJ2000_() {
    const sat = this.getSat_();

    if (!sat) {
      return;
    }

    const header = `经典轨道元素报告\n-------------------------------\n${this.createHeader_(sat)}`;
    const classicalEls = sat.toJ2000().toClassicalElements();
    const body = '' +
      `Epoch, ${classicalEls.epoch}\n` +
      `远地点, ${classicalEls.apogee.toFixed(3)} km\n` +
      `近地点, ${classicalEls.perigee.toFixed(3)} km\n` +
      `倾角, ${classicalEls.inclination.toFixed(3)}°\n` +
      `升交点赤经, ${classicalEls.rightAscensionDegrees.toFixed(3)}°\n` +
      `近地点幅角, ${classicalEls.argPerigeeDegrees.toFixed(3)}°\n` +
      `平近点角, ${classicalEls.trueAnomalyDegrees.toFixed(3)}°\n` +
      `偏心率, ${classicalEls.eccentricity.toFixed(3)}\n` +
      `周期, ${classicalEls.period.toFixed(3)} min\n` +
      `半长轴, ${classicalEls.semimajorAxis.toFixed(3)} km\n` +
      `平均运动, ${classicalEls.meanMotion.toFixed(3)} rev/day`;


    this.writeReport_({
      filename: `coes-${sat.sccNum}`,
      header,
      body,
      columns: 2,
      isHeaders: false,
    });
  }

  private writeReport_({ filename, header, body, columns = 4, isHeaders = true }: ReportData) {
    // Open a new window and write the report to it - the title of the window should be the satellite name
    const win = window.open('text/plain', filename);

    // Create an array that is columns long and fill it with 0s
    const colWidths = new Array(columns).fill(0);

    if (win) {
      const formattedReport = body
        .split('\n')
        .map((line) => line.split(','))
        .map((values, idx) => values.map((value, idx2) => {
          if (idx === 0) {
            if (idx2 === 0) {
              colWidths[idx2] = Math.max(new Date().toISOString().length + 5, value.trim().length + 5);
            } else {
              colWidths[idx2] = Math.max(10, value.trim().length + 5);
            }
          }

          return value.trim().padEnd(colWidths[idx2]);
        },
        ))
        .map((values, idx) => {
          const row = values.join('   ');

          if (idx === 0 && isHeaders) {
            // Add ---- under the entire header
            const header = values.join('   ');
            const headerUnderline = header.replace(/./gu, '-');


            return `${header}\n${headerUnderline}`;
          }


          return row;
        })
        .join('\n');

      // Create a download button at the top so you can download the report as a .txt file
      win.document.write(`<a href="data:text/plain;charset=utf-8,${encodeURIComponent(header + formattedReport)}" download="${filename}.txt">下载报告</a><br>`);

      win.document.write(`<plaintext>${header}${formattedReport}`);
      win.document.title = filename;
      win.history.replaceState(null, filename, `/${filename}.txt`);
    } else {
      // eslint-disable-next-line no-alert
      alert(t7e('errorMsgs.Reports.popupBlocker'));
    }
  }

  private getStartTime_() {
    const time = keepTrackApi.getTimeManager().getOffsetTimeObj(0);

    time.setMilliseconds(0);
    time.setSeconds(0);

    return time;
  }

  private getSat_(): DetailedSatellite | null {
    const sat = this.selectSatManager_.primarySatObj as DetailedSatellite;

    if (!sat) {
      errorManagerInstance.warn(t7e('errorMsgs.SelectSatelliteFirst'));

      return null;
    }

    if (!(sat instanceof DetailedSatellite)) {
      errorManagerInstance.warn(t7e('errorMsgs.SatelliteNotDetailedSatellite'));

      return null;
    }

    return sat;
  }

  private getSensor_(): DetailedSensor | null {
    const sensorManager = keepTrackApi.getSensorManager();

    if (!sensorManager.isSensorSelected()) {
      errorManagerInstance.warn(t7e('errorMsgs.SelectSensorFirst'));

      return null;
    }

    const sensor = sensorManager.currentSensors[0];

    return sensor;
  }
}
