import bodyParser from 'body-parser';
import express from 'express';
import authRoutes from './auth.routes';
import esHistoryRoutes from './es-history.routes';
import satelliteHistoryRoutes from './satellite-history.routes';
import os from 'os';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// ES模块中获取__dirname的方法
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 环境配置
const PORT = parseInt(process.env.PORT || process.env.API_PORT || '5001', 10);
const HOST = process.env.HOST || '0.0.0.0';
const NODE_ENV = process.env.NODE_ENV || 'development';

const configPath = path.join(__dirname, 'config.json');

function getServerIP() {
  const interfaces = os.networkInterfaces();
  console.log('检测到的网络接口:', Object.keys(interfaces));
  
  // 优先选择非回环的IPv4地址
  for (const name of Object.keys(interfaces)) {
    const ifaces = interfaces[name];
    if (ifaces) {
      for (const iface of ifaces) {
        if (iface.family === 'IPv4' && !iface.internal) {
          console.log(`选择网络接口 ${name}: ${iface.address}`);
          return iface.address;
        }
      }
    }
  }
  
  // 如果没有找到非回环地址，尝试获取第一个IPv4地址
  for (const name of Object.keys(interfaces)) {
    const ifaces = interfaces[name];
    if (ifaces) {
      for (const iface of ifaces) {
        if (iface.family === 'IPv4') {
          console.log(`回退到网络接口 ${name}: ${iface.address}`);
          return iface.address;
        }
      }
    }
  }
  
  console.log('未找到合适的IP地址，使用localhost');
  return 'localhost';
}

function updateConfigFile() {
  const ip = getServerIP();
  console.log(`服务器IP: ${ip}`);

  const config = {
    apiServer: {
      host: ip,
      port: PORT,
      url: `http://${ip}:${PORT}`,
      lastUpdated: new Date().toISOString()
    },
    endpoints: {
      auth: '/api/auth',
      health: '/api/health',
      config: '/api/config.json',
      esHistory: '/api/es-history',
      satelliteHistory: '/api/satellite-history'
    },
    frontend: {
      defaultPort: 8080,
      fallbackHosts: ['localhost', '127.0.0.1']
    }
  };

  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  console.log('配置文件已更新:', config);

  // 同时更新根目录的配置文件
  const rootConfigPath = path.join(__dirname, '..', '..', 'config.json');
  fs.writeFileSync(rootConfigPath, JSON.stringify(config, null, 2));
  console.log('根配置文件已更新');
}

updateConfigFile();

const app = express();

// 设置信任代理（用于部署在反向代理后面）
app.set('trust proxy', true);

app.use(bodyParser.json());

// 请求日志中间件（必须在路由之前）
app.use((req, _res, next) => {
  const timestamp = new Date().toISOString();
  const method = req.method;
  const url = req.url;
  const ip = req.ip || req.socket.remoteAddress || 'unknown';

  // 颜色代码
  const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    dim: '\x1b[2m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    gray: '\x1b[90m'
  };

  // 根据HTTP方法选择颜色
  let methodColor = colors.white;
  switch (method) {
    case 'GET':
      methodColor = colors.green;
      break;
    case 'POST':
      methodColor = colors.blue;
      break;
    case 'PUT':
      methodColor = colors.yellow;
      break;
    case 'DELETE':
      methodColor = colors.red;
      break;
    case 'PATCH':
      methodColor = colors.magenta;
      break;
    default:
      methodColor = colors.cyan;
  }

  // 格式化日志
  const formattedLog = [
    `${colors.gray}[${timestamp}]${colors.reset}`,
    `${methodColor}${colors.bright}${method.padEnd(6)}${colors.reset}`,
    `${colors.blue}${url}${colors.reset}`,
    `${colors.gray}from${colors.reset}`,
    `${colors.cyan}${ip}${colors.reset}`
  ].join(' ');

  console.log(formattedLog);
  next();
});

// 添加静态文件服务，让前端能访问配置文件
app.use(express.static(path.join(__dirname, '..', '..')));

// 增强CORS配置
app.use((req, res, next) => {
  const origin = req.headers.origin;

  // 允许的域名列表（可通过环境变量配置）
  const allowedOrigins = process.env.ALLOWED_ORIGINS
    ? process.env.ALLOWED_ORIGINS.split(',')
    : ['http://localhost:8080', 'http://127.0.0.1:8080'];

  // 开发环境允许所有来源，生产环境检查白名单
  if (NODE_ENV === 'development') {
    res.header('Access-Control-Allow-Origin', '*');
  } else if (origin && allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else if (!origin) {
    // 允许同源请求（没有Origin头的请求）
    res.header('Access-Control-Allow-Origin', '*');
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-CSRF-Token');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Expose-Headers', 'Authorization, X-CSRF-Token');

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    return res.status(200).json({});
  }

  return next();
});

// 集成认证路由
app.use('/api/auth', authRoutes);
// 集成ES历史轨道代理路由（支持GEO卫星经度查询）
app.use('/api/es-history', esHistoryRoutes);
// 集成新的标准化卫星历史数据查询API
app.use('/api/satellite-history', satelliteHistoryRoutes);

// 根路由 - API信息
app.get('/', (_req, res) => {
  res.json({
    name: '太空物体模拟平台API服务',
    version: '1.0.0',
    environment: NODE_ENV,
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/api/health',
      auth: {
        login: 'POST /api/auth/login',
        register: 'POST /api/auth/register',
        verify: 'POST /api/auth/verify',
        changePassword: 'POST /api/auth/change-password'
      },
      config: '/api/config.json',
      history: {
        es: '/api/es-history',
        satellite: '/api/satellite-history'
      }
    },
    status: 'running'
  });
});

app.get('/api/health', (_req, res) => {
  res.json({ status: 'ok' });
});

// 提供配置文件访问
app.get('/api/config.json', (_req, res) => {
  if (fs.existsSync(configPath)) {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    res.json(config);
  } else {
    res.status(404).json({ error: '配置文件不存在' });
  }
});

// 提供api-config.json访问
app.get('/api-config.json', (_req, res) => {
  if (fs.existsSync(configPath)) {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    res.json(config);
  } else {
    res.status(404).json({ error: '配置文件不存在' });
  }
});

// 404处理 - 必须在所有路由之后
app.use((req, res) => {
  const timestamp = new Date().toISOString();
  const ip = req.ip || req.socket.remoteAddress || 'unknown';

  // 颜色代码
  const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    cyan: '\x1b[36m',
    gray: '\x1b[90m'
  };

  console.warn(`${colors.gray}[${timestamp}]${colors.reset} ${colors.red}${colors.bright}404${colors.reset} - ${colors.yellow}未找到路径:${colors.reset} ${colors.red}${req.method}${colors.reset} ${colors.cyan}${req.url}${colors.reset} - ${colors.gray}${ip}${colors.reset}`);
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.url,
    method: req.method,
    timestamp: timestamp
  });
});

// 错误处理中间件 - 必须在最后
app.use((err: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  const timestamp = new Date().toISOString();
  const ip = req.ip || req.socket.remoteAddress || 'unknown';

  // 颜色代码
  const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    cyan: '\x1b[36m',
    gray: '\x1b[90m'
  };

  console.error(`${colors.gray}[${timestamp}]${colors.reset} ${colors.red}${colors.bright}ERROR${colors.reset} - ${colors.yellow}服务器错误${colors.reset} [${colors.red}${req.method}${colors.reset} ${colors.cyan}${req.url}${colors.reset}] - ${colors.gray}${ip}${colors.reset}:`);
  console.error(`${colors.red}${err.stack}${colors.reset}`);

  // 不在生产环境暴露错误堆栈
  const errorResponse = {
    error: 'Internal server error',
    timestamp: timestamp,
    path: req.url,
    method: req.method
  };

  if (NODE_ENV === 'development') {
    (errorResponse as any).stack = err.stack;
  }

  res.status(500).json(errorResponse);
});

// 启动服务器
const server = app.listen(PORT, HOST, () => {
  // 颜色代码
  const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    dim: '\x1b[2m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    gray: '\x1b[90m'
  };

  console.log(`
${colors.blue}${colors.bright}╔══════════════════════════════════════════════════════════════╗${colors.reset}
${colors.blue}${colors.bright}║                    🚀 API服务已启动                          ║${colors.reset}
${colors.blue}${colors.bright}╠══════════════════════════════════════════════════════════════╣${colors.reset}
${colors.blue}║${colors.reset} ${colors.yellow}环境:${colors.reset}        ${colors.green}${NODE_ENV}${colors.reset}                                    ${colors.blue}║${colors.reset}
${colors.blue}║${colors.reset} ${colors.yellow}本地地址:${colors.reset}    ${colors.cyan}http://localhost:${PORT}${colors.reset}                     ${colors.blue}║${colors.reset}
${colors.blue}║${colors.reset} ${colors.yellow}网络地址:${colors.reset}    ${colors.cyan}http://${HOST}:${PORT}${colors.reset}                      ${colors.blue}║${colors.reset}
${colors.blue}║${colors.reset} ${colors.yellow}启动时间:${colors.reset}    ${colors.gray}${new Date().toISOString()}${colors.reset}        ${colors.blue}║${colors.reset}
${colors.blue}║${colors.reset} ${colors.yellow}进程ID:${colors.reset}      ${colors.magenta}${process.pid}${colors.reset}                                      ${colors.blue}║${colors.reset}
${colors.blue}${colors.bright}╠══════════════════════════════════════════════════════════════╣${colors.reset}
${colors.blue}║${colors.reset} ${colors.bright}${colors.white}可用端点:${colors.reset}                                              ${colors.blue}║${colors.reset}
${colors.blue}║${colors.reset} ${colors.green}  GET${colors.reset}  ${colors.blue}/              ${colors.reset} - ${colors.gray}服务信息${colors.reset}                    ${colors.blue}║${colors.reset}
${colors.blue}║${colors.reset} ${colors.green}  GET${colors.reset}  ${colors.blue}/api/health    ${colors.reset} - ${colors.gray}健康检查${colors.reset}                    ${colors.blue}║${colors.reset}
${colors.blue}║${colors.reset} ${colors.blue}  POST${colors.reset} ${colors.blue}/api/auth/*    ${colors.reset} - ${colors.gray}认证相关${colors.reset}                    ${colors.blue}║${colors.reset}
${colors.blue}║${colors.reset} ${colors.green}  GET${colors.reset}  ${colors.blue}/api/config.json${colors.reset} - ${colors.gray}配置文件${colors.reset}                  ${colors.blue}║${colors.reset}
${colors.blue}║${colors.reset} ${colors.green}  GET${colors.reset}  ${colors.blue}/api/es-history${colors.reset} - ${colors.gray}ES历史数据${colors.reset}                 ${colors.blue}║${colors.reset}
${colors.blue}║${colors.reset} ${colors.green}  GET${colors.reset}  ${colors.blue}/api/satellite-history${colors.reset} - ${colors.gray}卫星历史数据${colors.reset}         ${colors.blue}║${colors.reset}
${colors.blue}${colors.bright}╠══════════════════════════════════════════════════════════════╣${colors.reset}
${colors.blue}║${colors.reset} ${colors.yellow}📊 日志记录已启用${colors.reset} - ${colors.gray}所有请求将被记录${colors.reset}                ${colors.blue}║${colors.reset}
${colors.blue}${colors.bright}╚══════════════════════════════════════════════════════════════╝${colors.reset}
  `);

  // 记录服务器启动成功
  const timestamp = new Date().toISOString();
  console.log(`${colors.gray}[${timestamp}]${colors.reset} ${colors.green}${colors.bright}SERVER_START${colors.reset} - ${colors.blue}服务器启动成功${colors.reset} - ${colors.magenta}PID:${process.pid}${colors.reset}`);
});

// 优雅关闭处理
process.on('SIGTERM', () => {
  const timestamp = new Date().toISOString();
  const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    gray: '\x1b[90m'
  };

  console.log(`${colors.gray}[${timestamp}]${colors.reset} ${colors.yellow}${colors.bright}SIGTERM${colors.reset} - ${colors.blue}收到SIGTERM信号，正在优雅关闭服务器...${colors.reset} ${colors.magenta}PID:${process.pid}${colors.reset}`);
  server.close(() => {
    const closeTimestamp = new Date().toISOString();
    console.log(`${colors.gray}[${closeTimestamp}]${colors.reset} ${colors.blue}${colors.bright}SHUTDOWN${colors.reset} - ${colors.blue}服务器已关闭${colors.reset} - ${colors.magenta}PID:${process.pid}${colors.reset}`);
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  const timestamp = new Date().toISOString();
  const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    gray: '\x1b[90m'
  };

  console.log(`${colors.gray}[${timestamp}]${colors.reset} ${colors.yellow}${colors.bright}SIGINT${colors.reset} - ${colors.blue}收到SIGINT信号，正在优雅关闭服务器...${colors.reset} ${colors.magenta}PID:${process.pid}${colors.reset}`);
  server.close(() => {
    const closeTimestamp = new Date().toISOString();
    console.log(`${colors.gray}[${closeTimestamp}]${colors.reset} ${colors.blue}${colors.bright}SHUTDOWN${colors.reset} - ${colors.blue}服务器已关闭${colors.reset} - ${colors.magenta}PID:${process.pid}${colors.reset}`);
    process.exit(0);
  });
});

export default app;