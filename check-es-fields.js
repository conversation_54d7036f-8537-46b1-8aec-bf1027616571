const { Client } = require('@elastic/elasticsearch');

// ES 配置
const esConfig = {
  url: "http://123.57.173.156:9200",
  username: "readonly_tle",
  password: "<PERSON><PERSON><PERSON>@readonly4tle",
  index: "orbital_tle"
};

async function checkESFields() {
  try {
    console.log('正在连接 Elasticsearch...');
    console.log('ES 地址:', esConfig.url);
    console.log('索引:', esConfig.index);
    
    const client = new Client({
      node: esConfig.url,
      auth: {
        username: esConfig.username,
        password: esConfig.password,
      },
      maxRetries: 3,
      requestTimeout: 10000,
    });

    // 1. 获取索引映射信息
    console.log('\n1. 获取索引映射信息...');
    try {
      const mapping = await client.indices.getMapping({
        index: esConfig.index
      });
      console.log('索引映射:', JSON.stringify(mapping, null, 2));
    } catch (error) {
      console.log('获取映射失败:', error.message);
    }

    // 2. 查询一条记录看字段结构
    console.log('\n2. 查询一条记录看字段结构...');
    const searchResult = await client.search({
      index: esConfig.index,
      size: 1,
      query: {
        match_all: {}
      }
    });
    
    if (searchResult.hits?.hits?.length > 0) {
      const firstRecord = searchResult.hits.hits[0]._source;
      console.log('第一条记录的字段:');
      console.log(JSON.stringify(firstRecord, null, 2));
      
      console.log('\n字段列表:');
      Object.keys(firstRecord).forEach(field => {
        console.log(`  - ${field}: ${typeof firstRecord[field]} = ${firstRecord[field]}`);
      });
    }

    // 3. 查找包含经度信息的记录
    console.log('\n3. 查找包含经度信息的记录...');
    
    // 尝试不同的可能字段名
    const possibleLongitudeFields = [
      'subsat_long', 'longitude', 'lon', 'lng', 'subsat_longitude',
      'subsatellite_longitude', 'geo_longitude', 'orbital_longitude'
    ];
    
    for (const field of possibleLongitudeFields) {
      try {
        const result = await client.search({
          index: esConfig.index,
          size: 1,
          query: {
            bool: {
              must: [
                { exists: { field: field } },
                { 
                  bool: { 
                    must_not: { 
                      term: { [field]: '' } 
                    } 
                  } 
                }
              ]
            }
          }
        });
        
        if (result.hits?.hits?.length > 0) {
          console.log(`✓ 找到经度字段: ${field}`);
          const record = result.hits.hits[0]._source;
          console.log(`  值: ${record[field]}`);
          console.log(`  完整记录:`, JSON.stringify(record, null, 2));
          break;
        }
      } catch (error) {
        // 字段不存在，继续尝试下一个
      }
    }

    // 4. 查找GEO卫星
    console.log('\n4. 查找GEO卫星...');
    
    const possibleTypeFields = [
      'object_type', 'type', 'satellite_type', 'orbit_type', 'classification'
    ];
    
    const possibleGeoValues = [
      'GEO', 'geo', 'Geo', 'GEOSTATIONARY', 'geostationary'
    ];
    
    for (const typeField of possibleTypeFields) {
      for (const geoValue of possibleGeoValues) {
        try {
          const result = await client.search({
            index: esConfig.index,
            size: 1,
            query: {
              term: { [typeField]: geoValue }
            }
          });
          
          if (result.hits?.hits?.length > 0) {
            console.log(`✓ 找到GEO卫星字段: ${typeField} = ${geoValue}`);
            const record = result.hits.hits[0]._source;
            console.log(`  记录:`, JSON.stringify(record, null, 2));
            return;
          }
        } catch (error) {
          // 字段不存在，继续尝试
        }
      }
    }
    
    console.log('未找到明确的GEO卫星标识字段');

    // 5. 统计总记录数
    console.log('\n5. 统计总记录数...');
    const countResult = await client.count({
      index: esConfig.index
    });
    console.log(`总记录数: ${countResult.count}`);

  } catch (error) {
    console.error('检查ES字段失败:', error.message);
    console.error('错误详情:', error);
  }
}

checkESFields();
