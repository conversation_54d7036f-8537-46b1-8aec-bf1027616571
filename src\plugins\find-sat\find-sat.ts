/* eslint-disable prefer-const */
/* eslint-disable complexity */
import { GetSatType, KeepTrackApiEvents, MenuMode, ToastMsgType } from '@app/interfaces';
import { getEl } from '@app/lib/get-el';
import { getUnique } from '@app/lib/get-unique';
import { hideLoading, showLoading } from '@app/lib/showLoading';
import { errorManagerInstance } from '@app/singletons/errorManager';
import findSatPng from '@public/img/icons/database-search.png';

import { countryCodeList, countryNameList } from '@app/catalogs/countries';
import { getChineseCountryName } from '@app/catalogs/countries-chinese';
import { CatalogExporter } from '@app/static/catalog-exporter';
import { BaseObject, Degrees, DetailedSatellite, Hours, Kilometers, Minutes, SpaceObjectType, eci2rae } from 'ootk';
import { keepTrackApi } from '../../keepTrackApi';
import { ClickDragOptions, KeepTrackPlugin } from '../KeepTrackPlugin';

export interface SearchSatParams {
  argPe: Degrees;
  argPeMarg: Degrees;
  az: Degrees;
  azMarg: Degrees;
  bus: string;
  countryCode: string;
  el: Degrees;
  elMarg: Degrees;
  inc: Degrees;
  incMarg: Degrees;
  objType: number;
  payload: string;
  period: Minutes;
  periodMarg: Minutes;
  tleAge: Hours;
  tleAgeMarg: Hours;
  raan: Degrees;
  raanMarg: Degrees;
  rcs: number;
  rcsMarg: number;
  rng: Kilometers;
  rngMarg: Kilometers;
  shape: string;
  source: string;
  perigee: Kilometers;
  perigeeMarg: Kilometers;
  apogee: Kilometers;
  apogeeMarg: Kilometers;
}

export class FindSatPlugin extends KeepTrackPlugin {
  readonly id = 'FindSatPlugin';
  dependencies_: string[];
  private lastResults_ = <DetailedSatellite[]>[];

  dragOptions: ClickDragOptions = {
    isDraggable: true,
    minWidth: 500,
    maxWidth: 700,
  };

  menuMode: MenuMode[] = [MenuMode.ADVANCED, MenuMode.ALL];

  sideMenuElementName: string = 'findByLooks-menu';
  sideMenuElementHtml: string = keepTrackApi.html`
  <div id="findByLooks-menu" class="side-menu-parent start-hidden text-select">
    <div id="findByLooks-content" class="side-menu">
      <div class="row">
        <h5 class="center-align">查找目标</h5>
        <form id="findByLooks-form">
          <div class="row">
            <div class="input-field col s12">
              <select value=0 id="fbl-type" type="text">
                <option value=0>全部</option>
                <option value=1>有效载荷</option>
                <option value=2>火箭体</option>
                <option value=3>碎片</option>
              </select>
              <label for="disabled">目标类型</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12">
              <select value=0 id="fbl-country" type="text">
                <option value='All'>全部</option>
              </select>
              <label for="disabled">国家</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12">
              <select value=0 id="fbl-bus" type="text">
                <option value='All'>全部</option>
              </select>
              <label for="disabled">卫星总线</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12">
              <select value=0 id="fbl-payload" type="text">
                <option value='All'>全部</option>
              </select>
              <label for="disabled">载荷</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12">
              <select value=0 id="fbl-shape" type="text">
                <option value='All'>全部</option>
              </select>
              <label for="disabled">形状</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12">
              <select value=0 id="fbl-source" type="text">
                <option value='All'>全部</option>
              </select>
              <label for="disabled">数据源</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12 m6 l6">
              <input placeholder="xxx.x" id="fbl-azimuth" type="text">
              <label for="fbl-azimuth" class="active">方位角(度)</label>
            </div>
            <div class="input-field col s12 m6 l6">
              <input placeholder="5" id="fbl-azimuth-margin" type="text">
              <label for="fbl-azimuth-margin "class="active">误差(度)</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12 m6 l6">
              <input placeholder="xx.xx" id="fbl-elevation" type="text">
              <label for="fbl-elevation "class="active">仰角(度)</label>
            </div>
            <div class="input-field col s12 m6 l6">
              <input placeholder="5" id="fbl-elevation-margin" type="text">
              <label for="fbl-elevation-margin "class="active">误差(度)</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12 m6 l6">
              <input placeholder="xxxx.x" id="fbl-range" type="text">
              <label for="fbl-range "class="active">范围(km)</label>
            </div>
            <div class="input-field col s12 m6 l6">
              <input placeholder="500" id="fbl-range-margin" type="text">
              <label for="fbl-range-margin "class="active">误差(km)</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12 m6 l6">
              <input placeholder="xx.x" id="fbl-inc" type="text">
              <label for="fbl-inc "class="active">倾角(度)</label>
            </div>
            <div class="input-field col s12 m6 l6">
              <input value="0.5" placeholder="0.5" id="fbl-inc-margin" type="text">
              <label for="fbl-inc-margin "class="active">误差 (度)</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12 m6 l6">
              <input placeholder="xx.x" id="fbl-period" type="text">
              <label for="fbl-period "class="active">周期(分钟)</label>
            </div>
            <div class="input-field col s12 m6 l6">
              <input value="10" placeholder="10" id="fbl-period-margin" type="text">
              <label for="fbl-period-margin "class="active">误差 (分钟)</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12 m6 l6">
              <input placeholder="xx.x" id="fbl-tleAge" type="text">
              <label for="fbl-tleAge "class="active">TLE寿命(小时)</label>
            </div>
            <div class="input-field col s12 m6 l6">
              <input value="1" placeholder="1" id="fbl-tleAge-margin" type="text">
              <label for="fbl-tleAge-margin "class="active">误差(小时)</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12 m6 l6">
              <input placeholder="xx.x" id="fbl-rcs" type="text">
              <!-- RCS in meters squared -->
              <label for="fbl-rcs "class="active">RCS面积(m<sup>2</sup>)</label>
            </div>
            <div class="input-field col s12 m6 l6">
              <input value="10" placeholder="10" id="fbl-rcs-margin" type="text">
              <label for="fbl-rcs-margin "class="active">误差(m<sup>2</sup>)</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12 m6 l6">
              <input placeholder="xx.x" id="fbl-raan" type="text">
              <label for="fbl-raan "class="active">升交点赤经(度)</label>
            </div>
            <div class="input-field col s12 m6 l6">
              <input value="0.5" placeholder="0.5" id="fbl-raan-margin" type="text">
              <label for="fbl-raan-margin "class="active">误差(度)</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12 m6 l6">
              <input placeholder="xx.x" id="fbl-argPe" type="text">
              <label for="fbl-argPe "class="active">近地点幅角(度)</label>
            </div>
            <div class="input-field col s12 m6 l6">
              <input value="0.5" placeholder="0.5" id="fbl-argPe-margin" type="text">
              <label for="fbl-argPe-margin "class="active">误差(度)</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12 m6 l6">
              <input placeholder="xxxxx.x" id="fbl-perigee" type="text">
              <label for="fbl-perigee "class="active">近地点高度(km)</label>
            </div>
            <div class="input-field col s12 m6 l6">
              <input value="100" placeholder="100" id="fbl-perigee-margin" type="text">
              <label for="fbl-perigee-margin "class="active">误差(km)</label>
            </div>
          </div>
          <div class="row">
            <div class="input-field col s12 m6 l6">
              <input placeholder="xxxxx.x" id="fbl-apogee" type="text">
              <label for="fbl-apogee "class="active">远地点高度(km)</label>
            </div>
            <div class="input-field col s12 m6 l6">
              <input value="100" placeholder="100" id="fbl-apogee-margin" type="text">
              <label for="fbl-apogee-margin "class="active">误差(km)</label>
            </div>
          </div>
          <div class="row">
          <center>
            <button id="findByLooks-submit" class="btn btn-ui waves-effect waves-light" type="submit"
              name="action">查找目标 &#9658;
              </button>
            </center>
          </div>
          <div class="row">
          <center>
          <button id="findByLooks-export" class="btn btn-ui waves-effect waves-light" type="button"
            name="action">导出数据 &#9658;
            </button>
          </center>
          </div>
        </form>
        <div class="row center-align" style="margin-top:20px;">
          <span id="fbl-error" class="menu-selectable"></span>
        </div>
      </div>
    </div>
  </div>`;

  bottomIconImg = findSatPng;
  private hasSearchBeenRun_ = false;

  addJs(): void {
    super.addJs();

    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, this.uiManagerFinal_.bind(this));
  }

  printLastResults() {
    errorManagerInstance.info(this.lastResults_.map((sat) => sat.name).join('\n'));
  }

  private uiManagerFinal_() {
    const satData = keepTrackApi.getCatalogManager().objectCache;

    getEl('fbl-error')!.addEventListener('click', () => {
      getEl('fbl-error')!.style.display = 'none';
    });

    getEl('findByLooks-form').addEventListener('submit', (e: Event) => {
      e.preventDefault();
      showLoading(() => {
        this.findByLooksSubmit_().then(() => {
          hideLoading();
        });
      });
    });

    getUnique(satData.filter((obj: BaseObject) => (obj as DetailedSatellite)?.bus).map((obj) => (obj as DetailedSatellite).bus))
      // Sort using lower case
      .sort((a, b) => (<string>a).toLowerCase().localeCompare((<string>b).toLowerCase()))
      .forEach((bus) => {
        getEl('fbl-bus').insertAdjacentHTML('beforeend', `<option value="${bus}">${bus}</option>`);
      });

    // 🔥🔥🔥 生成中文国家名称列表并排序
    const chineseCountryList = countryNameList.map((englishName: string) => ({
      englishName,
      chineseName: getChineseCountryName(englishName),
      countryCode: countryCodeList[englishName]
    })).sort((a, b) => a.chineseName.localeCompare(b.chineseName, 'zh-CN'));

    chineseCountryList.forEach((country) => {
      getEl('fbl-country').insertAdjacentHTML('beforeend', `<option value="${country.countryCode}" title="${country.englishName}">${country.chineseName}</option>`);
    });

    getUnique(satData.filter((obj: BaseObject) => (obj as DetailedSatellite)?.shape).map((obj) => (obj as DetailedSatellite).shape))
      // Sort using lower case
      .sort((a, b) => (<string>a).toLowerCase().localeCompare((<string>b).toLowerCase()))
      .forEach((shape) => {
        getEl('fbl-shape').insertAdjacentHTML('beforeend', `<option value="${shape}">${shape}</option>`);
      });

    getUnique(satData.filter((obj: BaseObject) => (obj as DetailedSatellite)?.source).map((obj) => (obj as DetailedSatellite).source))
      // Sort using lower case
      .sort((a, b) => (<string>a).toLowerCase().localeCompare((<string>b).toLowerCase()))
      .forEach((source) => {
        getEl('fbl-source').insertAdjacentHTML('beforeend', `<option value="${source}">${source}</option>`);
      });
    const payloadPartials = satData
      .filter((obj: BaseObject) => (obj as DetailedSatellite)?.payload)
      .map((obj) =>
        (obj as DetailedSatellite).payload
          .split(' ')[0]
          .split('-')[0]
          .replace(/[^a-zA-Z]/gu, ''),
      )
      .filter((obj) => obj.length >= 3);

    getUnique(payloadPartials)
      .sort((a, b) => (<string>a).toLowerCase().localeCompare((<string>b).toLowerCase()))
      .forEach((payload) => {
        if (payload === '') {
          return;
        }
        if (payload.length > 3) {
          getEl('fbl-payload').insertAdjacentHTML('beforeend', `<option value="${payload}">${payload}</option>`);
        }
      });

    // Export data
    getEl('findByLooks-export')?.addEventListener('click', () => {
      if (!this.hasSearchBeenRun_) {
        errorManagerInstance.warn('Try finding satellites first!');

        return;
      }
      CatalogExporter.exportTle2Csv(this.lastResults_);
    });
  }

  private findByLooksSubmit_(): Promise<void> {
    this.hasSearchBeenRun_ = true;

    return new Promise(() => {
      const uiManagerInstance = keepTrackApi.getUiManager();

      const az = parseFloat((<HTMLInputElement>getEl('fbl-azimuth')).value);
      const el = parseFloat((<HTMLInputElement>getEl('fbl-elevation')).value);
      const rng = parseFloat((<HTMLInputElement>getEl('fbl-range')).value);
      const inc = parseFloat((<HTMLInputElement>getEl('fbl-inc')).value);
      const period = parseFloat((<HTMLInputElement>getEl('fbl-period')).value);
      const tleAge = parseFloat((<HTMLInputElement>getEl('fbl-tleAge')).value);
      const rcs = parseFloat((<HTMLInputElement>getEl('fbl-rcs')).value);
      const azMarg = parseFloat((<HTMLInputElement>getEl('fbl-azimuth-margin')).value);
      const elMarg = parseFloat((<HTMLInputElement>getEl('fbl-elevation-margin')).value);
      const rngMarg = parseFloat((<HTMLInputElement>getEl('fbl-range-margin')).value);
      const incMarg = parseFloat((<HTMLInputElement>getEl('fbl-inc-margin')).value);
      const periodMarg = parseFloat((<HTMLInputElement>getEl('fbl-period-margin')).value);
      const tleAgeMarg = parseFloat((<HTMLInputElement>getEl('fbl-tleAge-margin')).value);
      const rcsMarg = parseFloat((<HTMLInputElement>getEl('fbl-rcs-margin')).value);
      const objType = parseInt((<HTMLInputElement>getEl('fbl-type')).value);
      const raan = parseFloat((<HTMLInputElement>getEl('fbl-raan')).value);
      const raanMarg = parseFloat((<HTMLInputElement>getEl('fbl-raan-margin')).value);
      const argPe = parseFloat((<HTMLInputElement>getEl('fbl-argPe')).value);
      const argPeMarg = parseFloat((<HTMLInputElement>getEl('fbl-argPe-margin')).value);
      const perigee = parseFloat((<HTMLInputElement>getEl('fbl-perigee')).value);
      const perigeeMarg = parseFloat((<HTMLInputElement>getEl('fbl-perigee-margin')).value);
      const apogee = parseFloat((<HTMLInputElement>getEl('fbl-apogee')).value);
      const apogeeMarg = parseFloat((<HTMLInputElement>getEl('fbl-apogee-margin')).value);
      const countryCode = (<HTMLInputElement>getEl('fbl-country')).value;
      const bus = (<HTMLInputElement>getEl('fbl-bus')).value;
      const payload = (<HTMLInputElement>getEl('fbl-payload')).value;
      const shape = (<HTMLInputElement>getEl('fbl-shape')).value;
      const source = (<HTMLInputElement>getEl('fbl-source')).value;

      (<HTMLInputElement>getEl('search')).value = ''; // Reset the search first
      try {
        const searchParams = {
          az,
          el,
          rng,
          inc,
          azMarg,
          elMarg,
          rngMarg,
          incMarg,
          period,
          periodMarg,
          tleAge,
          tleAgeMarg,
          rcs,
          rcsMarg,
          objType,
          raan,
          raanMarg,
          argPe,
          argPeMarg,
          perigee,
          perigeeMarg,
          apogee,
          apogeeMarg,
          countryCode,
          bus,
          payload,
          shape,
          source,
        };

        this.lastResults_ = FindSatPlugin.searchSats_(searchParams as SearchSatParams);
        if (this.lastResults_.length === 0) {
          uiManagerInstance.toast('未找到卫星', ToastMsgType.critical);
        }
      } catch (e) {
        if (e.message === 'No Search Criteria Entered') {
          uiManagerInstance.toast('未输入搜索条件', ToastMsgType.critical);
        }
      }
    });
  }

  private static checkAz_(posAll: DetailedSatellite[], min: number, max: number) {
    return posAll.filter((pos) => {
      if (!pos.isSatellite() && !pos.isMissile()) {
        return false;
      }

      const rae = eci2rae(
        keepTrackApi.getTimeManager().simulationTimeObj,
        keepTrackApi.getCatalogManager().getSat(pos.id, GetSatType.POSITION_ONLY).position,
        keepTrackApi.getSensorManager().currentSensors[0],
      );


      return rae.az >= min && rae.az <= max;
    });
  }

  private static checkEl_(posAll: DetailedSatellite[], min: number, max: number) {
    return posAll.filter((pos) => {
      if (!pos.isSatellite() && !pos.isMissile()) {
        return false;
      }

      const rae = eci2rae(
        keepTrackApi.getTimeManager().simulationTimeObj,
        keepTrackApi.getCatalogManager().getSat(pos.id, GetSatType.POSITION_ONLY).position,
        keepTrackApi.getSensorManager().currentSensors[0],
      );


      return rae.el >= min && rae.el <= max;
    });
  }

  private static checkInview_(posAll: DetailedSatellite[]) {
    const dotsManagerInstance = keepTrackApi.getDotsManager();


    return posAll.filter((pos) => dotsManagerInstance.inViewData[pos.id] === 1);
  }

  private static checkObjtype_(posAll: DetailedSatellite[], objtype: number) {
    return posAll.filter((pos) => pos.type === objtype);
  }

  private static checkRange_(posAll: DetailedSatellite[], min: number, max: number) {
    return posAll.filter((pos) => {
      if (!pos.isSatellite() && !pos.isMissile()) {
        return false;
      }

      const rae = eci2rae(
        keepTrackApi.getTimeManager().simulationTimeObj,
        keepTrackApi.getCatalogManager().getSat(pos.id, GetSatType.POSITION_ONLY).position,
        keepTrackApi.getSensorManager().currentSensors[0],
      );


      return rae.rng >= min && rae.rng <= max;
    });
  }

  private static limitPossibles_(possibles: DetailedSatellite[], limit: number): DetailedSatellite[] {
    const uiManagerInstance = keepTrackApi.getUiManager();

    if (possibles.length >= limit) {
      uiManagerInstance.toast(`数量太多，仅显示 ${limit} 搜索结果`, ToastMsgType.serious);
    }
    possibles = possibles.slice(0, limit);

    return possibles;
  }

  private static searchSats_(searchParams: SearchSatParams): DetailedSatellite[] {
    let {
      az,
      el,
      rng,
      countryCode,
      inc,
      azMarg,
      elMarg,
      rngMarg,
      incMarg,
      period,
      periodMarg,
      tleAge,
      tleAgeMarg,
      rcs,
      rcsMarg,
      objType,
      raan: rightAscension,
      raanMarg: rightAscensionMarg,
      argPe,
      argPeMarg,
      perigee,
      perigeeMarg,
      apogee,
      apogeeMarg,
      bus,
      shape,
      payload,
      source,
    } = searchParams;

    const isValidAz = !isNaN(az) && isFinite(az);
    const isValidEl = !isNaN(el) && isFinite(el);
    const isValidRange = !isNaN(rng) && isFinite(rng);
    const isValidInc = !isNaN(inc) && isFinite(inc);
    const isValidRaan = !isNaN(rightAscension) && isFinite(rightAscension);
    const isValidArgPe = !isNaN(argPe) && isFinite(argPe);
    const isValidPerigee = !isNaN(perigee) && isFinite(perigee);
    const isValidApogee = !isNaN(apogee) && isFinite(apogee);
    const isValidPeriod = !isNaN(period) && isFinite(period);
    const isValidTleAge = !isNaN(tleAge) && isFinite(tleAge);
    const isValidRcs = !isNaN(rcs) && isFinite(rcs);
    const isSpecificCountry = countryCode !== 'All';
    const isSpecificBus = bus !== 'All';
    const isSpecificShape = shape !== 'All';
    const isSpecificSource = source !== 'All';
    const isSpecificPayload = payload !== 'All';

    azMarg = !isNaN(azMarg) && isFinite(azMarg) ? azMarg : (5 as Degrees);
    elMarg = !isNaN(elMarg) && isFinite(elMarg) ? elMarg : (5 as Degrees);
    rngMarg = !isNaN(rngMarg) && isFinite(rngMarg) ? rngMarg : (200 as Kilometers);
    incMarg = !isNaN(incMarg) && isFinite(incMarg) ? incMarg : (1 as Degrees);
    periodMarg = !isNaN(periodMarg) && isFinite(periodMarg) ? periodMarg : (0.5 as Minutes);
    tleAgeMarg = !isNaN(tleAgeMarg) && isFinite(tleAgeMarg) ? tleAgeMarg : (1 as Hours);
    rcsMarg = !isNaN(rcsMarg) && isFinite(rcsMarg) ? rcsMarg : rcs / 10;
    rightAscensionMarg = !isNaN(rightAscensionMarg) && isFinite(rightAscensionMarg) ? rightAscensionMarg : (1 as Degrees);
    argPeMarg = !isNaN(argPeMarg) && isFinite(argPeMarg) ? argPeMarg : (1 as Degrees);
    perigeeMarg = !isNaN(perigeeMarg) && isFinite(perigeeMarg) ? perigeeMarg : (100 as Kilometers);
    apogeeMarg = !isNaN(apogeeMarg) && isFinite(apogeeMarg) ? apogeeMarg : (100 as Kilometers);

    if (
      !isValidEl &&
      !isValidRange &&
      !isValidAz &&
      !isValidInc &&
      !isValidPeriod &&
      !isValidTleAge &&
      !isValidRcs &&
      !isValidArgPe &&
      !isValidRaan &&
      !isValidPerigee &&
      !isValidApogee &&
      !isSpecificCountry &&
      !isSpecificBus &&
      !isSpecificShape &&
      !isSpecificSource &&
      !isSpecificPayload &&
      objType === 0
    ) {
      throw new Error('No Search Criteria Entered');
    }

    let res = keepTrackApi.getCatalogManager().getSats();

    res = !isValidInc && !isValidPeriod && !isValidTleAge && (isValidAz || isValidEl || isValidRange) ? FindSatPlugin.checkInview_(res) : res;

    // 根据用户偏好，搜索结果中排除碎片（除非明确选择碎片类型）
    if (objType !== 0) {
      res = FindSatPlugin.checkObjtype_(res, objType);
    } else {
      // 当选择"全部"时，排除碎片
      res = res.filter((sat) => {
        const isDebris = sat.type === SpaceObjectType.DEBRIS || (sat.type as any) === 3;
        return !isDebris;
      });
    }

    if (isValidAz) {
      res = FindSatPlugin.checkAz_(res, az - azMarg, az + azMarg);
    }
    if (isValidEl) {
      res = FindSatPlugin.checkEl_(res, el - elMarg, el + elMarg);
    }
    if (isValidRange) {
      res = FindSatPlugin.checkRange_(res, rng - rngMarg, rng + rngMarg);
    }
    if (isValidInc) {
      res = FindSatPlugin.checkInc_(res, (inc - incMarg) as Degrees, (inc + incMarg) as Degrees);
    }
    if (isValidRaan) {
      res = FindSatPlugin.checkRightAscension_(res, (rightAscension - rightAscensionMarg) as Degrees, (rightAscension + rightAscensionMarg) as Degrees);
    }
    if (isValidArgPe) {
      res = FindSatPlugin.checkArgPe_(res, (argPe - argPeMarg) as Degrees, (argPe + argPeMarg) as Degrees);
    }
    if (isValidPerigee) {
      res = FindSatPlugin.checkPerigee_(res, (perigee - perigeeMarg) as Kilometers, (perigee + perigeeMarg) as Kilometers);
    }
    if (isValidApogee) {
      res = FindSatPlugin.checkApogee_(res, (apogee - apogeeMarg) as Kilometers, (apogee + apogeeMarg) as Kilometers);
    }
    if (isValidPeriod) {
      res = FindSatPlugin.checkPeriod_(res, (period - periodMarg) as Minutes, (period + periodMarg) as Minutes);
    }
    if (isValidTleAge) {
      res = FindSatPlugin.checkTleAge_(res, (tleAge - tleAgeMarg) as Hours, (tleAge + tleAgeMarg) as Hours);
    }
    if (isValidRcs) {
      res = FindSatPlugin.checkRcs_(res, rcs - rcsMarg, rcs + rcsMarg);
    }
    if (countryCode !== 'All') {
      let country = countryCode.split('|');
      // Remove duplicates and undefined

      country = country.filter((item, index) => item && country.indexOf(item) === index);
      res = res.filter((obj: BaseObject) => country.includes((obj as DetailedSatellite).country));
    }
    if (bus !== 'All') {
      res = res.filter((obj: BaseObject) => (obj as DetailedSatellite).bus === bus);
    }
    if (shape !== 'All') {
      res = res.filter((obj: BaseObject) => (obj as DetailedSatellite).shape === shape);
    }
    if (source !== 'All') {
      res = res.filter((obj: BaseObject) => (obj as DetailedSatellite).source === source);
    }

    if (payload !== 'All') {
      res = res.filter(
        (obj: BaseObject) =>
          (obj as DetailedSatellite).payload
            ?.split(' ')[0]
            ?.split('-')[0]
            ?.replace(/[^a-zA-Z]/gu, '') === payload,
      );
    }

    res = FindSatPlugin.limitPossibles_(res, settingsManager.searchLimit);

    let result = '';

    res.forEach((obj: BaseObject, i: number) => {
      result += i < res.length - 1 ? `${(obj as DetailedSatellite).sccNum},` : `${(obj as DetailedSatellite).sccNum}`;
    });

    (<HTMLInputElement>getEl('search')).value = result;
    const uiManagerInstance = keepTrackApi.getUiManager();

    uiManagerInstance.doSearch((<HTMLInputElement>getEl('search')).value);

    return res;
  }

  private static checkArgPe_(possibles: DetailedSatellite[], min: Degrees, max: Degrees) {
    return possibles.filter((possible) => possible.argOfPerigee < max && possible.argOfPerigee > min);
  }

  private static checkInc_(possibles: DetailedSatellite[], min: Degrees, max: Degrees) {
    return possibles.filter((possible) => possible.inclination < max && possible.inclination > min);
  }

  private static checkPeriod_(possibles: DetailedSatellite[], minPeriod: Minutes, maxPeriod: Minutes) {
    return possibles.filter((possible) => possible.period > minPeriod && possible.period < maxPeriod);
  }

  private static checkTleAge_(possibles: DetailedSatellite[], minTleAge_: Hours, maxTleAge: Hours) {
    const minTleAge = minTleAge_ < 0 ? 0 : minTleAge_;
    const now = new Date();

    return possibles.filter((possible) => {
      const ageHours = possible.ageOfElset(now);


      return ageHours >= minTleAge && ageHours <= maxTleAge;
    });
  }

  private static checkRightAscension_(possibles: DetailedSatellite[], min: Degrees, max: Degrees) {
    return possibles.filter((possible) => possible.rightAscension < max && possible.rightAscension > min);
  }

  private static checkRcs_(possibles: DetailedSatellite[], minRcs: number, maxRcs: number) {
    return possibles.filter((possible) => possible.rcs > minRcs && possible.rcs < maxRcs);
  }

  private static checkPerigee_(possibles: DetailedSatellite[], minPerigee: Kilometers, maxPerigee: Kilometers) {
    return possibles.filter((possible) => {
      if (!possible.isSatellite() && !possible.isMissile()) {
        return false;
      }
      const perigee = possible.apogee - possible.eccentricity * possible.semiMajorAxis * 2;
      return perigee > minPerigee && perigee < maxPerigee;
    });
  }

  private static checkApogee_(possibles: DetailedSatellite[], minApogee: Kilometers, maxApogee: Kilometers) {
    return possibles.filter((possible) => {
      if (!possible.isSatellite() && !possible.isMissile()) {
        return false;
      }
      return possible.apogee > minApogee && possible.apogee < maxApogee;
    });
  }
}
