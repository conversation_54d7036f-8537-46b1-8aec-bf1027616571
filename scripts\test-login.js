#!/usr/bin/env node

/**
 * 登录测试脚本
 * 直接测试API服务器的登录功能
 */

const http = require('http');
const https = require('https');

console.log('🔐 登录功能测试工具\n');

// 测试配置
const config = {
    host: 'localhost',
    port: 5001,
    timeout: 5000,
    testCredentials: {
        username: 'admin',
        password: 'SpaceDefense2025!'
    }
};

// 发送HTTP请求
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const protocol = options.protocol === 'https:' ? https : http;
        
        const req = protocol.request(options, (res) => {
            let body = '';
            
            res.on('data', (chunk) => {
                body += chunk;
            });
            
            res.on('end', () => {
                try {
                    const jsonBody = body ? JSON.parse(body) : {};
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: jsonBody,
                        rawBody: body
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: { rawResponse: body },
                        rawBody: body
                    });
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.setTimeout(config.timeout, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        if (data) {
            req.write(data);
        }
        
        req.end();
    });
}

// 测试服务器连接
async function testConnection() {
    console.log('🔍 测试服务器连接...');
    
    try {
        const options = {
            hostname: config.host,
            port: config.port,
            path: '/',
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        };
        
        const response = await makeRequest(options);
        
        if (response.statusCode === 200) {
            console.log('✅ 服务器连接成功');
            console.log(`   状态码: ${response.statusCode}`);
            console.log(`   响应: ${JSON.stringify(response.body, null, 2)}`);
            return true;
        } else {
            console.log(`❌ 服务器响应异常: ${response.statusCode}`);
            console.log(`   响应: ${response.rawBody}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ 无法连接到服务器: ${error.message}`);
        return false;
    }
}

// 测试健康检查
async function testHealth() {
    console.log('\n🔍 测试健康检查...');
    
    try {
        const options = {
            hostname: config.host,
            port: config.port,
            path: '/api/health',
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        };
        
        const response = await makeRequest(options);
        
        if (response.statusCode === 200) {
            console.log('✅ 健康检查通过');
            console.log(`   响应: ${JSON.stringify(response.body, null, 2)}`);
            return true;
        } else {
            console.log(`❌ 健康检查失败: ${response.statusCode}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ 健康检查请求失败: ${error.message}`);
        return false;
    }
}

// 测试登录功能
async function testLogin() {
    console.log('\n🔍 测试登录功能...');
    
    try {
        const loginData = JSON.stringify(config.testCredentials);
        
        const options = {
            hostname: config.host,
            port: config.port,
            path: '/api/auth/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Content-Length': Buffer.byteLength(loginData)
            }
        };
        
        console.log(`   请求URL: http://${config.host}:${config.port}/api/auth/login`);
        console.log(`   请求数据: ${JSON.stringify({...config.testCredentials, password: '***'})}`);
        
        const response = await makeRequest(options, loginData);
        
        console.log(`   响应状态: ${response.statusCode}`);
        console.log(`   响应头: ${JSON.stringify(response.headers, null, 2)}`);
        console.log(`   响应体: ${JSON.stringify(response.body, null, 2)}`);
        
        if (response.statusCode === 200) {
            console.log('✅ 登录测试成功');
            
            if (response.body.token) {
                console.log(`   获得令牌: ${response.body.token.substring(0, 20)}...`);
            }
            
            return true;
        } else {
            console.log(`❌ 登录测试失败: ${response.statusCode}`);
            
            if (response.body.error) {
                console.log(`   错误信息: ${response.body.error}`);
            }
            
            return false;
        }
    } catch (error) {
        console.log(`❌ 登录请求失败: ${error.message}`);
        return false;
    }
}

// 测试CORS
async function testCORS() {
    console.log('\n🔍 测试CORS配置...');
    
    try {
        const options = {
            hostname: config.host,
            port: config.port,
            path: '/api/auth/login',
            method: 'OPTIONS',
            headers: {
                'Origin': 'http://localhost:8080',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            }
        };
        
        const response = await makeRequest(options);
        
        console.log(`   响应状态: ${response.statusCode}`);
        console.log(`   CORS头: ${JSON.stringify({
            'Access-Control-Allow-Origin': response.headers['access-control-allow-origin'],
            'Access-Control-Allow-Methods': response.headers['access-control-allow-methods'],
            'Access-Control-Allow-Headers': response.headers['access-control-allow-headers']
        }, null, 2)}`);
        
        if (response.headers['access-control-allow-origin']) {
            console.log('✅ CORS配置正常');
            return true;
        } else {
            console.log('⚠️ CORS配置可能有问题');
            return false;
        }
    } catch (error) {
        console.log(`❌ CORS测试失败: ${error.message}`);
        return false;
    }
}

// 主测试函数
async function runTests() {
    console.log(`📅 测试时间: ${new Date().toISOString()}`);
    console.log(`🎯 测试目标: http://${config.host}:${config.port}\n`);
    
    const results = {
        connection: await testConnection(),
        health: await testHealth(),
        cors: await testCORS(),
        login: await testLogin()
    };
    
    console.log('\n📊 测试结果汇总:');
    console.log('='.repeat(40));
    
    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅ 通过' : '❌ 失败';
        console.log(`   ${test.padEnd(12)}: ${status}`);
    });
    
    const passedCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`\n总计: ${passedCount}/${totalCount} 项测试通过`);
    
    if (passedCount === totalCount) {
        console.log('\n🎉 所有测试通过！API服务器工作正常。');
    } else {
        console.log('\n⚠️ 部分测试失败，请检查API服务器配置。');
        
        console.log('\n💡 故障排除建议:');
        if (!results.connection) {
            console.log('   - 确保API服务器已启动: npm run start:api');
            console.log('   - 检查端口5001是否被占用');
        }
        if (!results.health) {
            console.log('   - 检查健康检查端点配置');
        }
        if (!results.cors) {
            console.log('   - 检查CORS配置');
        }
        if (!results.login) {
            console.log('   - 检查认证路由和用户数据');
            console.log('   - 确认默认管理员账户存在');
        }
    }
    
    process.exit(passedCount === totalCount ? 0 : 1);
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log('登录测试工具 - 测试API服务器登录功能');
    console.log('');
    console.log('使用方法:');
    console.log('  node scripts/test-login.js [选项]');
    console.log('');
    console.log('选项:');
    console.log('  --host <host>     服务器地址 (默认: localhost)');
    console.log('  --port <port>     端口号 (默认: 5001)');
    console.log('  --username <user> 测试用户名 (默认: admin)');
    console.log('  --password <pass> 测试密码 (默认: SpaceDefense2025!)');
    console.log('');
    console.log('示例:');
    console.log('  node scripts/test-login.js');
    console.log('  node scripts/test-login.js --host ************* --port 5001');
    process.exit(0);
}

// 解析命令行参数
const hostIndex = process.argv.indexOf('--host');
if (hostIndex !== -1 && process.argv[hostIndex + 1]) {
    config.host = process.argv[hostIndex + 1];
}

const portIndex = process.argv.indexOf('--port');
if (portIndex !== -1 && process.argv[portIndex + 1]) {
    config.port = parseInt(process.argv[portIndex + 1]);
}

const usernameIndex = process.argv.indexOf('--username');
if (usernameIndex !== -1 && process.argv[usernameIndex + 1]) {
    config.testCredentials.username = process.argv[usernameIndex + 1];
}

const passwordIndex = process.argv.indexOf('--password');
if (passwordIndex !== -1 && process.argv[passwordIndex + 1]) {
    config.testCredentials.password = process.argv[passwordIndex + 1];
}

// 运行测试
runTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
});
