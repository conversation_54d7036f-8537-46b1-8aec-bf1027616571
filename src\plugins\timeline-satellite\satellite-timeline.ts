import { KeepTrackApiEvents, MenuMode, ToastMsgType } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import { errorManagerInstance } from '@app/singletons/errorManager';
import viewTimelinePng from '@public/img/icons/view_timeline2.png';

import { shake } from '@app/lib/shake';
import { SatMath } from '@app/static/sat-math';
import { BaseObject, Degrees, DetailedSatellite, DetailedSensor, Hours, Kilometers, MILLISECONDS_PER_SECOND, SatelliteRecord, Seconds } from 'ootk';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { SelectSatManager } from '../select-sat-manager/select-sat-manager';
import { WatchlistPlugin } from '../watchlist/watchlist';

interface Pass {
  start: Date;
  end: Date;
}

interface SatellitePasses {
  satellite: DetailedSatellite;
  passes: Pass[];
}

export class SatelliteTimeline extends KeepTrackPlugin {
  readonly id = 'SatelliteTimeline';
  dependencies_ = [SelectSatManager.name];
  private canvas_: HTMLCanvasElement;
  private ctx_: CanvasRenderingContext2D;
  private canvasStatic_: HTMLCanvasElement;
  private ctxStatic_: CanvasRenderingContext2D;
  private drawEvents_: { [key: string]: (mouseX: number, mouseY: number) => boolean } = {};
  private lengthOfLookAngles_ = 6 as Hours;
  private lengthOfBadPass_ = 120 as Seconds;
  private lengthOfAvgPass_ = 240 as Seconds;
  private angleCalculationInterval_ = <Seconds>30;

  isRequireSensorSelected = true;
  isIconDisabled = true;
  isIconDisabledOnLoad = true;

  menuMode: MenuMode[] = [MenuMode.ADVANCED, MenuMode.ALL];

  bottomIconImg = viewTimelinePng;
  bottomIconLabel = 'Satellite Timeline'; // 底部图标标签
  secondaryMenuIcon = 'settings'; // 设置菜单图标
  bottomIconCallback: () => void = () => {
    console.log('🔥 [SatelliteTimeline] bottomIconCallback 被调用');
    console.log('🔥 [SatelliteTimeline] isMenuButtonActive:', this.isMenuButtonActive);
    console.log('🔥 [SatelliteTimeline] sideMenuElementName:', this.sideMenuElementName);

    // 检查DOM元素是否存在
    const menuElement = document.getElementById(this.sideMenuElementName);
    console.log('🔥 [SatelliteTimeline] 菜单DOM元素:', menuElement);
    if (menuElement) {
      console.log('🔥 [SatelliteTimeline] 菜单元素类名:', menuElement.className);
      console.log('🔥 [SatelliteTimeline] 菜单元素样式:', menuElement.style.cssText);
      console.log('🔥 [SatelliteTimeline] 菜单元素计算样式 display:', window.getComputedStyle(menuElement).display);
      console.log('🔥 [SatelliteTimeline] 菜单元素计算样式 visibility:', window.getComputedStyle(menuElement).visibility);
    }

    if (!this.verifySensorSelected()) {
      console.log('🔥 [SatelliteTimeline] 传感器验证失败');
      return;
    }

    if (keepTrackApi.getPlugin(WatchlistPlugin).watchlistList.length === 0 && keepTrackApi.getPlugin(SelectSatManager).selectedSat === -1) {
      console.log('🔥 [SatelliteTimeline] 没有监视列表或选择的卫星');
      keepTrackApi.getUiManager().toast('将卫星添加到监视列表或选择卫星', ToastMsgType.caution);
      shake(getEl(this.bottomIconElementName));
      return;
    }

    console.log('🔥 [SatelliteTimeline] 所有验证通过，调整画布大小');
    // 调整画布大小
    this.resizeCanvas_();
  };

  sideMenuElementName = 'satellite-timeline-menu';
  sideMenuTitle = '卫星时间线';
  sideMenuElementHtml = keepTrackApi.html`
    <div class="row" style="margin: 80px 0 0 0;">
      <canvas id="satellite-timeline-canvas"></canvas>
      <canvas id="satellite-timeline-canvas-static" style="display: none;"></canvas>
    </div>`;
  sideMenuSecondaryHtml: string = keepTrackApi.html`
    <div class="row">
      <div class="input-field col s12">
        <input id="satellite-timeline-setting-total-length" value="${this.lengthOfLookAngles_.toString()}" type="text"
          style="text-align: center;"
        />
        <label for="satellite-timeline-setting-total-length" class="active">计算长度（小时）</label>
      </div>
    </div>
    <div class="row">
      <div class="input-field col s12">
        <input id="satellite-timeline-setting-interval" value="${this.angleCalculationInterval_.toString()}" type="text"
          style="text-align: center;"
        />
        <label for="satellite-timeline-setting-interval" class="active">计算间隔（秒）</label>
      </div>
    </div>
    <div class="row">
      <div class="input-field col s12">
        <input id="satellite-timeline-setting-bad-length" value="${this.lengthOfBadPass_.toString()}" type="text"
          style="text-align: center;"
        />
        <label for="satellite-timeline-setting-bad-length" class="active">不良经过时长（秒）</label>
      </div>
    </div>
    <div class="row">
      <div class="input-field col s12">
        <input id="satellite-timeline-setting-avg-length" value="${this.lengthOfAvgPass_.toString()}" type="text"
          style="text-align: center;"
        />
        <label for="satellite-timeline-setting-avg-length" class="active">平均经过长度（秒）</label>
      </div>
    </div>`;
  sideMenuSecondaryOptions = {
    width: 350,
    leftOffset: 0, // 设置在左侧显示
    zIndex: 100000, // 🔥 提高z-index确保在最前面显示
  };

  // 重写openSecondaryMenu方法，让设置菜单在左侧覆盖显示（参考传感器时间线）
  openSecondaryMenu() {
    // 如果已经打开，先关闭避免状态混乱
    if (this.isSideMenuSettingsOpen) {
      this.closeSecondaryMenu();
      return;
    }

    const secondaryMenuElement = getEl(`${this.sideMenuElementName}-secondary`);

    if (secondaryMenuElement) {
      this.isSideMenuSettingsOpen = true;

      // 清除可能存在的动画类和样式
      secondaryMenuElement.style.transition = 'none';
      secondaryMenuElement.style.transform = 'translateX(-100%)'; // 初始位置在屏幕外

      // 设置为左侧覆盖显示，确保可见
      secondaryMenuElement.style.position = 'fixed';
      secondaryMenuElement.style.top = '0';
      secondaryMenuElement.style.left = '0';
      secondaryMenuElement.style.width = '400px';
      secondaryMenuElement.style.height = '100vh';
      secondaryMenuElement.style.zIndex = '100000';
      secondaryMenuElement.style.display = 'block';
      secondaryMenuElement.classList.remove('start-hidden');

      // 使用requestAnimationFrame确保样式应用后再开始动画
      requestAnimationFrame(() => {
        secondaryMenuElement.style.transition = 'transform 0.3s ease-out';
        secondaryMenuElement.style.transform = 'translateX(0)';
      });
    }
  }

  // 重写closeSecondaryMenu方法（参考传感器时间线）
  closeSecondaryMenu() {
    // 如果已经关闭，避免重复操作
    if (!this.isSideMenuSettingsOpen) {
      return;
    }

    this.isSideMenuSettingsOpen = false;
    const secondaryButtonElement = getEl(`${this.sideMenuElementName}-secondary-btn`);
    const secondaryMenuElement = getEl(`${this.sideMenuElementName}-secondary`);

    if (secondaryButtonElement) {
      secondaryButtonElement.style.color = 'var(--color-dark-text-accent)';
    }

    if (secondaryMenuElement) {
      // 使用动画关闭
      secondaryMenuElement.style.transition = 'transform 0.3s ease-in';
      secondaryMenuElement.style.transform = 'translateX(-100%)';

      // 动画完成后隐藏元素
      setTimeout(() => {
        secondaryMenuElement.style.display = 'none';
        secondaryMenuElement.classList.add('start-hidden');
        // 清除所有自定义样式，恢复默认状态
        secondaryMenuElement.style.position = '';
        secondaryMenuElement.style.top = '';
        secondaryMenuElement.style.left = '';
        secondaryMenuElement.style.width = '';
        secondaryMenuElement.style.height = '';
        secondaryMenuElement.style.zIndex = '';
        secondaryMenuElement.style.transform = '';
        secondaryMenuElement.style.transition = '';
      }, 300);
    }
  }

  // 重写hideSideMenus方法，确保设置子窗口也会关闭
  hideSideMenus(): void {
    super.hideSideMenus();
    if (this.isSideMenuSettingsOpen) {
      this.closeSecondaryMenu();
    }
  }
  downloadIconCb = () => {
    const canvas = document.getElementById('satellite-timeline-canvas') as HTMLCanvasElement;
    const image = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream');
    const link = document.createElement('a');

    link.href = image;
    link.download = `sensor-${keepTrackApi.getSensorManager().getSensor().uiName}-timeline.png`;
    link.click();
  };

  addHtml(): void {
    // 调用基类方法来正确处理设置菜单
    super.addHtml();

    // 监听全局菜单关闭事件，确保设置子窗口也会关闭
    keepTrackApi.on(
      KeepTrackApiEvents.hideSideMenus,
      () => {
        if (this.isSideMenuSettingsOpen) {
          this.closeSecondaryMenu();
        }
      }
    );

    keepTrackApi.on(
      KeepTrackApiEvents.uiManagerFinal,
      () => {
        this.canvas_ = <HTMLCanvasElement>getEl('satellite-timeline-canvas');
        this.canvasStatic_ = <HTMLCanvasElement>getEl('satellite-timeline-canvas-static');
        this.ctx_ = this.canvas_.getContext('2d');
        this.ctxStatic_ = this.canvasStatic_.getContext('2d');

        const totalLengthEl = getEl('satellite-timeline-setting-total-length');
        if (totalLengthEl) {
          totalLengthEl.addEventListener('change', () => {
            this.lengthOfLookAngles_ = parseFloat((<HTMLInputElement>getEl('satellite-timeline-setting-total-length')).value) as Hours;
            this.ctxStatic_.reset();
            this.updateTimeline();
          });
        }

        const intervalEl = getEl('satellite-timeline-setting-interval');
        if (intervalEl) {
          intervalEl.addEventListener('change', () => {
            this.angleCalculationInterval_ = parseFloat((<HTMLInputElement>getEl('satellite-timeline-setting-interval')).value) as Seconds;
            this.ctxStatic_.reset();
            this.updateTimeline();
          });
        }

        const badLengthEl = getEl('satellite-timeline-setting-bad-length');
        if (badLengthEl) {
          badLengthEl.addEventListener('change', () => {
            this.lengthOfBadPass_ = parseFloat((<HTMLInputElement>getEl('satellite-timeline-setting-bad-length')).value) as Seconds;
            this.ctxStatic_.reset();
            this.updateTimeline();
          });
        }

        const avgLengthEl = getEl('satellite-timeline-setting-avg-length');
        if (avgLengthEl) {
          avgLengthEl.addEventListener('change', () => {
            this.lengthOfAvgPass_ = parseFloat((<HTMLInputElement>getEl('satellite-timeline-setting-avg-length')).value) as Seconds;
            this.ctxStatic_.reset();
            this.updateTimeline();
          });
        }
      },
    );
  }



  addJs(): void {
    super.addJs();



    // 添加按钮事件监听器 - 使用事件委托确保能工作
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;

      // 关闭按钮事件 - 现在使用基类生成的关闭按钮ID
      if (target && target.id === `${this.sideMenuElementName}-close-btn`) {
        e.preventDefault();
        e.stopPropagation();
        this.hideSideMenus();
        return; // 确保不会继续处理其他逻辑
      }

      // 检查是否点击在卫星时间线菜单外部，如果是则关闭设置菜单
      const satelliteTimelineMenu = getEl('satellite-timeline-menu');
      const secondaryMenu = getEl(`${this.sideMenuElementName}-secondary`);

      if (this.isSideMenuSettingsOpen && secondaryMenu && satelliteTimelineMenu) {
        // 检查点击是否在设置菜单或主菜单外部
        if (!secondaryMenu.contains(target) && !satelliteTimelineMenu.contains(target)) {
          this.closeSecondaryMenu();
        }
      }
    }, { capture: false }); // 确保不在捕获阶段处理

    keepTrackApi.on(
      KeepTrackApiEvents.selectSatData,
      (sat: BaseObject) => {
        if (!sat && keepTrackApi.getPlugin(WatchlistPlugin)?.watchlistList.length === 0) {
          this.setBottomIconToDisabled();
        } else if (this.verifySensorSelected(false)) {
          this.setBottomIconToEnabled();
        }

        if (this.isMenuButtonActive) {
          if (sat) {
            this.ctxStatic_.reset();
            this.updateTimeline();
            this.canvas_.style.display = 'block';
          }
        }
      },
    );
    keepTrackApi.on(KeepTrackApiEvents.onWatchlistUpdated, this.onWatchlistUpdated_.bind(this));
    keepTrackApi.on(KeepTrackApiEvents.resize, this.resizeCanvas_.bind(this));
  }

  private onWatchlistUpdated_(watchlistList: number[]) {
    if (watchlistList.length === 0 && keepTrackApi.getPlugin(SelectSatManager)?.selectedSat === -1) {
      this.setBottomIconToDisabled();
    } else if (this.verifySensorSelected(false)) {
      this.setBottomIconToEnabled();
    }
  }

  updateTimeline(): void {
    try {
      if (keepTrackApi.getSensorManager().isSensorSelected() === false) {
        return;
      }
      if (!this.isMenuButtonActive) {
        return;
      }

      const passes = this.calculatePasses_();

      this.drawTimeline_(passes);
    } catch (e) {
      errorManagerInstance.info(e);
    }
  }

  private calculatePasses_(): SatellitePasses[] {
    const satellitePasses: SatellitePasses[] = [];
    const sensor = keepTrackApi.getSensorManager().getSensor();
    const satellites = keepTrackApi.getPlugin(WatchlistPlugin).getSatellites().concat(keepTrackApi.getPlugin(SelectSatManager).selectedSat).filter((sat) => sat !== -1);

    for (const sat of satellites) {
      const satellite = keepTrackApi.getCatalogManager().getSat(sat);
      const sensorPass: SatellitePasses = {
        satellite,
        passes: [],
      };

      // Skip if satellite is above the max range of the sensor
      if (sensor.maxRng < satellite.perigee && (!sensor.maxRng2 || sensor.maxRng2 < satellite.perigee)) {
        continue;
      }

      // SensorManager.updateSensorUiStyling([sensor]);
      let offset = 0;

      const durationInSeconds = this.lengthOfLookAngles_ * 60 * 60;
      let isInView = false;
      let isEnterView = false;
      let isExitView = false;
      let startTime = null;


      for (let i = 0; i < durationInSeconds; i += this.angleCalculationInterval_) {
        // 5second Looks
        offset = i * 1000; // Offset in seconds (msec * 1000)
        const now = keepTrackApi.getTimeManager().getOffsetTimeObj(offset);
        const multiSitePass = SatelliteTimeline.propagateMultiSite(now, satellite.satrec, sensor);

        // Check if in FOV
        if (multiSitePass.time && !isInView) {
          startTime = new Date(multiSitePass.time);
          isInView = true;
          isEnterView = true;
        }

        if (!multiSitePass.time && isInView) {
          isExitView = true;
          isInView = false;
          // Jump 3/4th to the next orbit
          i += satellite.period * 60 * 0.75;
        }

        if ((isEnterView && isExitView) || (isEnterView && i === durationInSeconds - this.angleCalculationInterval_)) {
          sensorPass.passes.push({
            start: startTime,
            end: now,
          });
          isEnterView = false;
          isExitView = false;
        }
      }

      satellitePasses.push(sensorPass);
    }

    return satellitePasses;
  }

  static propagateMultiSite(now: Date, satrec: SatelliteRecord, sensor: DetailedSensor) {
    // Setup Realtime and Offset Time
    const aer = SatMath.getRae(now, satrec, sensor);

    if (SatMath.checkIsInView(sensor, aer)) {
      return {
        time: now,
        el: aer.el,
        az: aer.az,
        rng: aer.rng,
        objName: null,
      };
    }

    return {
      time: null,
      el: <Degrees>0,
      az: <Degrees>0,
      rng: <Kilometers>0,
      objName: null,
    };

  }

  private drawTimeline_(satellitePasses: SatellitePasses[]): void {
    // Clone the canvas element to remove all event listeners
    const oldCanvas = this.canvas_;
    const newCanvas = oldCanvas.cloneNode(true) as HTMLCanvasElement;

    oldCanvas.parentNode.replaceChild(newCanvas, oldCanvas);
    this.canvas_ = newCanvas;
    this.ctx_ = this.canvas_.getContext('2d');

    // Clear the events list
    this.drawEvents_ = {};

    const leftOffset = this.canvas_.width * 0.15;
    const topOffset = 0; // Canvas is already offset from the top
    const width = this.canvas_.width * 0.75;
    const height = this.canvas_.height * 0.85;
    const timeManager = keepTrackApi.getTimeManager();
    const startTime = timeManager.simulationTimeObj.getTime();
    const endTime = startTime + this.lengthOfLookAngles_ * 60 * 60 * 1000; // 24 hours from now

    // clear canvas
    this.ctx_.reset();

    this.ctx_.fillStyle = 'rgb(58, 58, 58)'; // #3a3a3a
    this.ctx_.fillRect(leftOffset, topOffset, width, height - 15);

    const yStep = height / (satellitePasses.length + 1);
    const xScale = (width) / (endTime - startTime);

    // Draw time axis
    this.ctx_.strokeStyle = 'rgb(255, 255, 255)';
    this.ctx_.lineWidth = 5; // Increase line width to make it thicker
    this.ctx_.beginPath();
    this.ctx_.moveTo(leftOffset, topOffset + height - 20);
    this.ctx_.lineTo(leftOffset + width, topOffset + height - 20);
    this.ctx_.stroke();

    // Draw hour markers
    for (let i = 0; i <= this.lengthOfLookAngles_; i++) {
      const x = leftOffset + ((i * 60 * 60 * 1000) * xScale);

      this.ctx_.lineWidth = 5; // Increase line width to make it thicker
      this.ctx_.beginPath();
      this.ctx_.moveTo(x, topOffset + height - 25);
      this.ctx_.lineTo(x, topOffset + height - 15);
      this.ctx_.strokeStyle = 'rgb(255, 255, 255)';
      this.ctx_.stroke();

      // Extend a thin line to the top of the canvas
      this.ctx_.lineWidth = 1;
      this.ctx_.beginPath();
      this.ctx_.moveTo(x, topOffset + height - 15);
      this.ctx_.lineTo(x, topOffset);
      this.ctx_.stroke();

      let hour = timeManager.simulationTimeObj.getUTCHours();

      hour = (hour + i) % 24;

      this.ctx_.font = '14px Consolas';
      this.ctx_.fillStyle = 'rgb(255, 255, 255)';
      this.ctx_.fillText(`${hour}h`, x - 10, topOffset + height);
    }

    // Draw passes for each sensor
    satellitePasses.forEach((satellitePass, index) => {
      const y = topOffset + (index + 1) * yStep;

      // Draw satellite name and NORAD ID
      this.ctx_.fillStyle = 'rgb(255, 255, 255)';
      this.ctx_.font = 'bold 16px Consolas'; // 加大字体并加粗
      const satelliteName = satellitePass.satellite.name || 'Unknown';
      const displayText = `${satelliteName} (${satellitePass.satellite.sccNum})`;
      this.ctx_.fillText(displayText, leftOffset - 150, y + 5);

      // Draw passes
      satellitePass.passes.forEach((pass) => {
        const passStart = pass.start.getTime();
        const passEnd = pass.end.getTime();
        const x1 = leftOffset + (passStart - startTime) * xScale;
        const x2 = leftOffset + (passEnd - startTime) * xScale;

        const passLength = (passEnd - passStart) / MILLISECONDS_PER_SECOND;

        if (passLength < this.lengthOfBadPass_) {
          this.ctx_.fillStyle = 'rgb(255, 42, 4)';
        } else if (passLength < this.lengthOfAvgPass_) {
          this.ctx_.fillStyle = 'rgb(252, 232, 58)';
        } else {
          this.ctx_.fillStyle = 'rgb(86, 240, 0)';
        }

        this.ctx_.fillRect(x1, y - 10, x2 - x1, 20);


        const drawEvent = (mouseX: number, mouseY: number): boolean => {
          if (mouseX >= x1 - 10 && mouseX <= x2 + 10 && mouseY >= y - 10 && mouseY <= y + 10) {
            const startTime = new Date(passStart).toISOString().slice(11, 19);
            const endTime = new Date(passEnd).toISOString().slice(11, 19);

            // Calculate width of box based on text
            const satelliteName = satellitePass.satellite.name || 'Unknown';
            const text = `${satelliteName} (${satellitePass.satellite.sccNum}): ${startTime} - ${endTime}`;

            this.ctx_.font = '14px Consolas';

            const boxWidth = this.ctx_.measureText(text).width;

            // Draw tooltip box (first box is bigger to make a white border)
            this.ctx_.fillStyle = 'rgb(255, 255, 255)';
            this.ctx_.fillRect(mouseX - boxWidth / 2 - 6, mouseY - 30, boxWidth + 12, 24);
            // Draw tooltip box (second box is smaller to create a border effect)
            this.ctx_.fillStyle = 'rgb(31, 51, 71)';
            this.ctx_.fillRect(mouseX - boxWidth / 2 - 3, mouseY - 27, boxWidth + 6, 18);

            // Draw tooltip text
            this.ctx_.fillStyle = 'rgb(255, 255, 255)';
            this.ctx_.fillText(text, mouseX - boxWidth / 2, mouseY - 15);

            // Make mouse cursor a pointer
            this.canvas_.style.cursor = 'pointer';

            return true;
          }

          return false;
        };

        this.drawEvents_[`${index}-${passStart}-${passEnd}`] = drawEvent;

        // Create an onclick event for each pass
        this.canvas_.addEventListener('click', (event) => {
          const rect = this.canvas_.getBoundingClientRect();
          const mouseX = event.clientX - rect.left;
          const mouseY = event.clientY - rect.top;

          // If the mouse is over a pass change the sensor
          if (drawEvent(mouseX, mouseY)) {
            const timeManagerInstance = keepTrackApi.getTimeManager();

            timeManagerInstance.changeStaticOffset(new Date(passStart).getTime() - timeManagerInstance.realTime);
            timeManagerInstance.calculateSimulationTime();
            keepTrackApi.getPlugin(SelectSatManager).selectSat(satellitePass.satellite.id);
          }
        });
      });

      // If no passes draw a light gray bar to indicate no passes
      if (satellitePass.passes.length === 0) {
        this.ctx_.fillStyle = 'rgba(200, 200, 200, 0.2)';
        this.ctx_.fillRect(leftOffset, y - 10, width, 20);

        const drawEvent = (mouseX: number, mouseY: number): boolean => {
          if (mouseX >= leftOffset && mouseX <= leftOffset + width && mouseY >= y - 10 && mouseY <= y + 10) {
            const satelliteName = satellitePass.satellite.name || 'Unknown';
            const text = `${satelliteName} (${satellitePass.satellite.sccNum}): No Passes`;

            this.ctx_.font = '14px Consolas';

            const boxWidth = this.ctx_.measureText(text).width;

            // Draw tooltip box (first box is bigger to make a white border)
            this.ctx_.fillStyle = 'rgb(255, 255, 255)';
            this.ctx_.fillRect(mouseX - boxWidth / 2 - 6, mouseY - 30, boxWidth + 12, 24);
            // Draw tooltip box (second box is smaller to create a border effect)
            this.ctx_.fillStyle = 'rgb(31, 51, 71)';
            this.ctx_.fillRect(mouseX - boxWidth / 2 - 3, mouseY - 27, boxWidth + 6, 18);

            // Draw tooltip text
            this.ctx_.fillStyle = 'rgb(255, 255, 255)';
            this.ctx_.fillText(text, mouseX - boxWidth / 2, mouseY - 15);

            // Make mouse cursor a pointer
            this.canvas_.style.cursor = 'pointer';

            return true;
          }

          return false;
        };

        this.drawEvents_[`${index}-${satellitePass.satellite.id}-no-passes`] = drawEvent;
      }
    });

    // Add one mousemove event
    this.canvas_.addEventListener('mousemove', (event) => {
      this.handleOnMouseMove_(event);
    });

    // Save initial state as staticCtx_ so we can redraw the static elements without clearing the canvas
    this.ctxStatic_ = this.canvasStatic_.getContext('2d');
    this.ctxStatic_.drawImage(this.canvas_, 0, 0);
  }

  private handleOnMouseMove_(event: MouseEvent): void {
    // clear canvas
    this.ctx_.reset();

    // Draw static elements
    this.ctx_.drawImage(this.canvasStatic_, 0, 0);

    const rect = this.canvas_.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;

    let isHoveringOverPass = false;

    for (const key in this.drawEvents_) {
      if (!this.drawEvents_[key]) {
        continue;
      }
      const success = this.drawEvents_[key](mouseX, mouseY);

      isHoveringOverPass = isHoveringOverPass || success;
    }

    if (!isHoveringOverPass) {
      this.canvas_.style.cursor = 'default';
    }
  }

  private resizeCanvas_(isForceWidescreen?: boolean): void {
    isForceWidescreen ??= false;
    const timelineMenuDOM = getEl('satellite-timeline-menu');

    // if the canvas is not visible, don't resize it
    if (timelineMenuDOM.style.display === 'none') {
      return;
    }

    if (isForceWidescreen || window.innerWidth > window.innerHeight) {
      timelineMenuDOM.style.width = `${window.innerWidth}px`;

      this.canvas_.width = window.innerWidth;
      this.canvas_.height = window.innerHeight;
    } else {
      settingsManager.mapWidth = settingsManager.mapHeight * 2;
      timelineMenuDOM.style.width = `${settingsManager.mapWidth}px`;

      this.canvas_.width = window.innerWidth;
      this.canvas_.style.width = `${window.innerWidth}px`;
      this.canvas_.height = window.innerHeight - 100;
      this.canvas_.style.height = `${window.innerHeight - 100}px`;
    }

    this.canvasStatic_.width = this.canvas_.width;
    this.canvasStatic_.height = this.canvas_.height;

    this.updateTimeline();
  }
}
