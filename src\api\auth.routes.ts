import { Router, Request, Response } from 'express';
// 尝试多种导入方式以确保兼容性
import { authService } from '../auth/auth.service';

// 简单的内存限流器替代express-rate-limit
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

const createRateLimit = (windowMs: number, max: number) => {
  return (req: Request, res: Response, next: any): void => {
    const key = req.ip || 'unknown';
    const now = Date.now();
    const windowStart = now - windowMs;

    let record = rateLimitStore.get(key);
    if (!record || record.resetTime < windowStart) {
      record = { count: 0, resetTime: now + windowMs };
      rateLimitStore.set(key, record);
    }

    record.count++;

    if (record.count > max) {
      res.status(429).json({ error: '请求过于频繁，请稍后再试' });
      return;
    }

    next();
  };
};

const router = Router();

// 登录限流：每15分钟最多5次尝试
const loginLimiter = createRateLimit(15 * 60 * 1000, 5);

// 中间件：验证token
const authenticateToken = (req: Request, res: Response, next: any): void => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    res.status(401).json({ error: '访问令牌缺失' });
    return;
  }

  const result = authService.verifyToken(token);
  if (!result.valid) {
    res.status(403).json({ error: result.error });
    return;
  }

  (req as any).user = result.user;
  next();
};

// 中间件：验证管理员权限
const requireAdmin = (req: Request, res: Response, next: any): void => {
  if ((req as any).user?.role !== 'admin') {
    res.status(403).json({ error: '需要管理员权限' });
    return;
  }
  next();
};

// 登录
router.post('/login', loginLimiter, (req, res) => {
  try {
    const { username, password } = req.body;
    const ip = req.ip || req.socket.remoteAddress || 'unknown';

    // 输入验证和清理
    if (!username || !password) {
      return res.status(400).json({ error: '用户名和密码不能为空' });
    }

    const cleanUsername = authService.sanitizeInput(username);
    const result = authService.login(cleanUsername, password, ip);

    if (result.error) {
      return res.status(401).json({ error: result.error });
    }

    return res.json({
      user: result.user,
      token: result.token,
      message: '登录成功'
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 注册请求
router.post('/register', (req, res) => {
  try {
    const { username, password, email } = req.body;

    // 输入清理
    const cleanUsername = authService.sanitizeInput(username);
    const cleanEmail = authService.sanitizeInput(email);

    const result = authService.requestRegistration(cleanUsername, password, cleanEmail);

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    return res.json({ message: '注册请求已提交，等待管理员审批' });
  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 验证token
router.post('/verify', (req, res) => {
  const { token } = req.body;

  if (!token) {
    return res.status(400).json({ error: 'Token缺失' });
  }

  const result = authService.verifyToken(token);

  if (!result.valid) {
    return res.status(401).json({ error: result.error });
  }

  return res.json({
    valid: true,
    user: {
      id: result.user!.id,
      username: result.user!.username,
      role: result.user!.role,
      needsPasswordChange: result.user!.needsPasswordChange
    }
  });
});

// 修改密码
router.post('/change-password', authenticateToken, (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;
    const userId = (req as any).user.id;

    const result = authService.changePassword(userId, oldPassword, newPassword);

    if (!result.success) {
      return res.status(400).json({ error: result.error });
    }

    return res.json({ message: '密码修改成功' });
  } catch (error) {
    console.error('Change password error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 获取所有用户
router.get('/users', authenticateToken, requireAdmin, (_req, res) => {
  try {
    const users = authService.getAllUsers();
    return res.json(users);
  } catch (error) {
    console.error('Get users error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 获取待审批注册
router.get('/pending-registrations', authenticateToken, requireAdmin, (_req, res) => {
  try {
    const requests = authService.getPendingRegistrations();
    return res.json(requests);
  } catch (error) {
    console.error('Get pending registrations error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 审批注册
router.post('/approve-registration/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const requestId = req.params.id;
    const adminId = (req as any).user.id;

    const result = authService.approveRegistration(requestId, adminId);

    if (!result.success) {
      return res.status(400).json({ error: result.error });
    }

    return res.json({ message: '注册请求已批准' });
  } catch (error) {
    console.error('Approve registration error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 拒绝注册
router.post('/reject-registration/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const requestId = req.params.id;
    const adminId = (req as any).user.id;

    const result = authService.rejectRegistration(requestId, adminId);

    if (!result.success) {
      return res.status(400).json({ error: result.error });
    }

    return res.json({ message: '注册请求已拒绝' });
  } catch (error) {
    console.error('Reject registration error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 切换用户状态
router.post('/toggle-user/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const userId = req.params.id;

    const result = authService.toggleUserStatus(userId);

    if (!result.success) {
      return res.status(400).json({ error: result.error });
    }

    return res.json({ message: '用户状态已更新' });
  } catch (error) {
    console.error('Toggle user status error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 删除用户
router.delete('/users/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const userId = req.params.id;

    const result = authService.deleteUser(userId);

    if (!result.success) {
      return res.status(400).json({ error: result.error });
    }

    return res.json({ message: '用户已删除' });
  } catch (error) {
    console.error('Delete user error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 更新用户角色
router.post('/update-role/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const userId = req.params.id;
    const { role } = req.body;

    if (!['admin', 'user'].includes(role)) {
      return res.status(400).json({ error: '无效的角色' });
    }

    const result = authService.updateUserRole(userId, role);

    if (!result.success) {
      return res.status(400).json({ error: result.error });
    }

    return res.json({ message: '用户角色已更新' });
  } catch (error) {
    console.error('Update user role error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 获取登录记录
router.get('/login-attempts', authenticateToken, requireAdmin, (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 100;
    const attempts = authService.getLoginAttempts(limit);
    return res.json(attempts);
  } catch (error) {
    console.error('Get login attempts error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 直接创建用户
router.post('/create-user', authenticateToken, requireAdmin, (req, res) => {
  try {
    const { username, email, password, role = 'user', expiresAt } = req.body;

    if (!username || !email || !password) {
      return res.status(400).json({ error: '用户名、邮箱和密码不能为空' });
    }

    // 验证有效期格式
    if (expiresAt && isNaN(new Date(expiresAt).getTime())) {
      return res.status(400).json({ error: '有效期格式无效' });
    }

    const result = authService.createUser(username, email, password, role, expiresAt);

    if (result.success) {
      return res.json({
        message: '用户创建成功',
        user: result.user
      });
    } else {
      return res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Create user error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 设置用户有效期
router.post('/set-user-expiration/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const userId = req.params.id;
    const { expiresAt } = req.body;

    const result = authService.setUserExpiration(userId, expiresAt);

    if (result.success) {
      return res.json({ message: '用户有效期设置成功' });
    } else {
      return res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Set user expiration error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 获取即将过期的用户
router.get('/expiring-users', authenticateToken, requireAdmin, (_req, res) => {
  try {
    const expiringUsers = authService.getExpiringUsers();
    return res.json(expiringUsers.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      expiresAt: user.expiresAt,
      createdAt: user.createdAt
    })));
  } catch (error) {
    console.error('Get expiring users error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 管理员路由 - 获取已过期的用户
router.get('/expired-users', authenticateToken, requireAdmin, (_req, res) => {
  try {
    const expiredUsers = authService.getExpiredUsers();
    return res.json(expiredUsers.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      expiresAt: user.expiresAt,
      createdAt: user.createdAt
    })));
  } catch (error) {
    console.error('Get expired users error:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

export default router;