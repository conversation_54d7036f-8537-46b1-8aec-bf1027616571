/**
 * This is the tsconfig extension that is used by the IDE.
 */
{
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    "noEmit": true,
    "strictNullChecks": true,
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.js",
    "src/**/*.jsx",
    "src/**/*.png",
    "src/**/*.mp3",
    "src/**/*.jpg",
    "test/**/*.ts",
    "test/**/*.tsx",
    "test/**/*.js",
    "test/**/*.jsx",
    "test/**/*.png",
    "test/**/*.mp3",
    "test/**/*.jpg",
    "public/serviceWorker.js",
    "public/settings/settingsOverride.js",
    "public/settings/settingsOverrideEmbed.js",
    "src/globabl.d.ts"
  ],
  "exclude": [
    "node_modules/**/*",
    "dist/**/*",
    "src/admin/**/*",
    "embed/**/*",
    "lib/**/*",
    "coverage/**/*",
    "tsconfig.json",
    "src/tools/**/*",
    "src/plugins/startAnalytics.js"
  ],
  "filesGlob": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.png",
    "src/**/*.jpg",
    "!**/node_modules/**"
  ]
}