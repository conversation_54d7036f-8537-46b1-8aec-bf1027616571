# Keep Track 9.1 - Kepler

## Software Release Documentation

Version 9.1 builds upon the foundation laid in previous releases, focusing on enhancing data visualization, analytical capabilities, and overall system performance. This update introduces significant improvements to user experience and orbital analysis tools.

## Major Features

### Data Visualization & Analysis

- Introduced polar plots for advanced orbital analysis
- Implemented new quick reports for faster data access and interpretation
- Enhanced the best pass report with sun angle information for improved solar analysis
- Added AER (Azimuth, Elevation, Range) report for comprehensive positional data

### User Interface Enhancements

- Implemented a save button functionality for polar plots, allowing data persistence
- Added inFOV color to group color scheme, improving visibility of objects within the field of view
- Introduced resize cursor icon on draggable menus for better usability

### Performance Optimizations

- Reduced the performance impact of godrays effect
- Optimized color buffer updates to improve rendering speed
- Enhanced reference orbit updating mechanism for more efficient calculations

### Functionality Improvements

- Improved sun angle calculation in the best pass report for more accurate solar positioning
- Expanded compatibility across different platforms and browsers

### Architecture & Infrastructure

- Migrated to eslint for more consistent code formatting and improved code quality
- Updated database files for more accurate and up-to-date satellite information
- Upgraded to the latest version of OOTK (Orbital Object ToolKit) for enhanced orbital calculations

## Bug Fixes

- Resolved issue with godrays breaking after screen resize
- Fixed logic errors in waitForCruncher function
- Addressed conflicts between custom sensors and normal sensors
- Corrected reference orbit update issues
- Fixed sun/moon line visualization in sensor info for custom sensors

## Documentation

- Updated user guides to reflect new analytical tools and visualization features
- Enhanced troubleshooting documentation for common issues and edge cases
