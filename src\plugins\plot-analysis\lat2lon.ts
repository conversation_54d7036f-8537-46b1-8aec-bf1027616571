import { EChartsData, GetSatType } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl } from '@app/lib/get-el';
import { SatMathApi } from '@app/singletons/sat-math-api';
import scatterPlot4Png from '@public/img/icons/scatter-plot4.png';
import * as echarts from 'echarts';
import 'echarts-gl';
import { Degrees, DetailedSatellite, SpaceObjectType } from 'ootk';
import { KeepTrackPlugin } from '../KeepTrackPlugin';
import { SelectSatManager } from '../select-sat-manager/select-sat-manager';

export class Lat2LonPlots extends KeepTrackPlugin {
  readonly id = 'Lat2LonPlots';
  dependencies_: string[] = [SelectSatManager.name];
  private readonly selectSatManager_: SelectSatManager;

  private static readonly maxEccentricity_ = 0.1;
  private static readonly minSatellitePeriod_ = 1240;
  private static readonly maxSatellitePeriod_ = 1640;
  private static readonly maxInclination_ = 17;

  constructor() {
    super();
    this.selectSatManager_ = keepTrackApi.getPlugin(SelectSatManager) as unknown as SelectSatManager; // this will be validated in KeepTrackPlugin constructor
  }


  bottomIconImg = scatterPlot4Png;
  bottomIconElementName = 'menu-lat2lon-plots';
  bottomIconCallback = () => {
    const chartDom = getEl(this.plotCanvasId)!;

    this.createPlot(Lat2LonPlots.getPlotData(), chartDom);
  };

  plotCanvasId = 'plot-analysis-chart-lat2lon';
  chart: echarts.ECharts;

  sideMenuElementName = 'lat2lon-plots-menu';
  sideMenuElementHtml: string = keepTrackApi.html`
  <div id="lat2lon-plots-menu" class="side-menu-parent start-hidden text-select" style="width: 100vw; min-width: 100vw; max-width: 100vw;">
    <!-- 右上角关闭按钮 -->
    <div id="lat2lon-close-btn" style="position: fixed; top: 60px; right: 20px; z-index: 99999; width: 50px; height: 50px; background: rgba(220, 53, 69, 0.8); border: none; border-radius: 6px; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 32px; color: #fff; font-weight: bold; user-select: none;" title="关闭">×</div>
    <div id="plot-analysis-content" class="side-menu" style="width: 100vw; height: 100vh; padding: 20px;">
      <h5 class="center-align" style="color: white; margin-bottom: 20px;">纬度-经度图</h5>
      <div id="${this.plotCanvasId}" style="width: 100%; height: calc(100vh - 100px);"></div>
    </div>
  </div>`;

  addHtml(): void {
    super.addHtml();
  }

  addJs(): void {
    super.addJs();

    // 添加关闭按钮事件监听器 - 使用事件委托确保能工作
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target && target.id === 'lat2lon-close-btn') {
        console.log('纬度-经度图关闭按钮被点击了！');
        e.preventDefault();
        e.stopPropagation();

        // 隐藏菜单
        const menu = document.getElementById('lat2lon-plots-menu');
        if (menu) {
          menu.classList.add('start-hidden');
          menu.style.display = 'none';
          console.log('纬度-经度图菜单已隐藏');
        }

        // 取消底部图标选中状态
        const bottomIcon = document.getElementById(this.bottomIconElementName);
        if (bottomIcon) {
          bottomIcon.classList.remove('bmenu-item-selected');
          console.log('纬度-经度图底部图标选中状态已取消');
        }

        // 重置插件状态
        this.isMenuButtonActive = false;
        console.log('纬度-经度图插件状态已重置');
      }
    });
  }


  createPlot(data: EChartsData, chartDom: HTMLElement) {
    // Dont Load Anything if the Chart is Closed
    if (!this.isMenuButtonActive) {
      return;
    }

    // Delete any old charts and start fresh
    if (!this.chart) {
      // Setup Configuration
      this.chart = echarts.init(chartDom);
      this.chart.on('click', (event) => {
        if ((event.data as unknown as { id: number })?.id > -1) {
          this.selectSatManager_.selectSat((event.data as unknown as { id: number })?.id);
        }
      });
    }

    // Setup Chart
    this.chart.setOption({
      legend: {
        show: true,
        textStyle: {
          color: '#fff',
        },
      },
      tooltip: {
        formatter: (params) => {
          const data = params.value;
          const color = params.color;
          const name = params.name;

          return `
            <div style="display: flex; flex-direction: column; align-items: flex-start;">
              <div style="display: flex; flex-direction: row; flex-wrap: nowrap; justify-content: space-between; align-items: flex-end;">
                <div style="width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-bottom: 5px;"></div>
                <div style="font-weight: bold;"> ${name}</div>
              </div>
              <div><bold>Latitude:</bold> ${data[1].toFixed(3)}°</div>
              <div><bold>Longitude:</bold> ${data[0].toFixed(3)}°</div>
              <div><bold>Time from now:</bold> ${data[2].toFixed(3)} min</div>
            </div>
          `;
        },
      },
      xAxis: {
        name: '经度(°)',
        type: 'value',
        position: 'bottom',
      },
      yAxis: {
        name: '纬度(°)',
        type: 'value',
        position: 'left',
      },
      zAxis: {
        name: '平均运动',
        type: 'value',
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: -180,
          end: 180,
        },
        {
          type: 'slider',
          show: true,
          yAxisIndex: [0],
          left: '93%',
          start: -50,
          end: 50,
        },
        {
          type: 'inside',
          xAxisIndex: [0],
          start: -180,
          end: 180,
        },
        {
          type: 'inside',
          yAxisIndex: [0],
          start: -50,
          end: 50,
        },
      ],
      series: data.map((item) => ({
        type: 'line',
        name: item.country,
        data: item.data?.map((dataPoint: [number, number, (number | undefined)?]) => ({
          name: item.name,
          id: item.satId,
          value: [dataPoint[2] ?? 0, dataPoint[1], dataPoint[0]],
        })),
        /*
         * symbolSize: 8,
         * itemStyle: {
         * borderWidth: 1,
         * borderColor: 'rgba(255,255,255,0.8)',
         * },
         */
        emphasis: {
          itemStyle: {
            color: '#fff',
          },
        },
      })),
    });
  }

  static getPlotData(): EChartsData {
    const data = [] as EChartsData;

    keepTrackApi.getCatalogManager().objectCache.forEach((obj) => {
      if (obj.type !== SpaceObjectType.PAYLOAD) {
        return;
      }
      let sat = obj as DetailedSatellite;

      // Only GEO objects
      if (sat.eccentricity > Lat2LonPlots.maxEccentricity_) {
        return;
      }
      if (sat.period < Lat2LonPlots.minSatellitePeriod_) {
        return;
      }
      if (sat.period > Lat2LonPlots.maxSatellitePeriod_) {
        return;
      }
      if (sat.inclination > Lat2LonPlots.maxInclination_) {
        return;
      }

      // Compute LLA for each object
      sat = keepTrackApi.getCatalogManager().getObject(sat.id, GetSatType.POSITION_ONLY) as DetailedSatellite;
      const plotPoints = SatMathApi.getLlaOfCurrentOrbit(sat, 24);
      const plotData: [number, Degrees, Degrees][] = [];

      const now = keepTrackApi.getTimeManager().simulationTimeObj;

      plotPoints.forEach((point) => {
        const pointTime = (point.time - now.getTime()) / 1000 / 60;

        if (pointTime > 1440 || pointTime < 0) {
          return;
        }
        plotData.push([pointTime, point.lat, point.lon]);
      });
      let country = '';

      switch (sat.country) {
        case 'US':
          country = '美国';
          break;
        case 'F':
          country = '法国';
          break;
        case 'RU':
        case 'USSR':
          country = '俄罗斯';
          break;
        case 'CN':
          country = '中国';
          break;
        case 'IN':
          country = '印度';
          break;
        case 'J':
          country = '日本';
          break;
        default:
          country = '其它';
          break;
      }
      data.push({
        name: sat.name,
        satId: sat.id,
        country,
        data: plotData,
      });
    });

    return data;
  }
}

export const Lat2LonPlotsPlugin = new Lat2LonPlots();
