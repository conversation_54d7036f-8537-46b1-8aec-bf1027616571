import { KeepTrackApiEvents, MenuMode } from '@app/interfaces';
import { keepTrackApi } from '@app/keepTrackApi';
import { getEl, showEl } from '@app/lib/get-el';
import { SoundNames } from '@app/plugins/sounds/SoundNames';
import { errorManagerInstance } from '@app/singletons/errorManager';
import barChart4BarsPng from '@public/img/icons/bar-chart-4-bars.png';
import developerModePng from '@public/img/icons/developer-mode.png';
import localCafePng from '@public/img/icons/local-cafe.png';
import sciencePng from '@public/img/icons/science.png';
import settingsPng from '@public/img/icons/settings.png';

// 🔥 性能监控工具
class PerformanceMonitor {
  private static measurements = new Map<string, number>();

  static start(name: string): void {
    this.measurements.set(name, performance.now());
  }

  static end(name: string): number {
    const startTime = this.measurements.get(name);
    if (startTime) {
      const duration = performance.now() - startTime;
      this.measurements.delete(name);
      if (duration > 16) { // 超过一帧时间则警告
        console.warn(`🔥 Performance: ${name} took ${duration.toFixed(2)}ms (>16ms)`);
      }
      return duration;
    }
    return 0;
  }
}

// 🔥 布局缓存管理器 - 优化重复的样式计算
class LayoutCache {
  private static cache = new Map<string, {value: number, timestamp: number}>();
  private static readonly CACHE_DURATION = 100; // 100ms缓存时间

  static get(key: string): number | null {
    const cached = this.cache.get(key);
    const now = performance.now();

    if (cached && now - cached.timestamp < this.CACHE_DURATION) {
      return cached.value;
    }
    return null;
  }

  static set(key: string, value: number): void {
    this.cache.set(key, {value, timestamp: performance.now()});
  }

  static clear(): void {
    this.cache.clear();
  }

  // 批量获取布局信息，减少重排次数
  static getBatchMeasurements(): {bottomHeight: number, navFooterBottom: number, searchBoxHeight: number} {
    PerformanceMonitor.start('getBatchMeasurements');

    const bottomContainer = getEl('bottom-icons-container');
    const navFooter = getEl('nav-footer');
    const satInfoBox = getEl('sat-infobox');

    // 批量读取DOM属性，减少重排
    const measurements = {
      bottomHeight: bottomContainer?.offsetHeight ?? 0,
      navFooterBottom: navFooter ? parseInt(getComputedStyle(navFooter).bottom.replace('px', '')) : 0,
      searchBoxHeight: satInfoBox?.getBoundingClientRect().height ?? 0
    };

    // 缓存结果
    this.set('bottomHeight', measurements.bottomHeight);
    this.set('navFooterBottom', measurements.navFooterBottom);
    this.set('searchBoxHeight', measurements.searchBoxHeight);

    PerformanceMonitor.end('getBatchMeasurements');
    return measurements;
  }
}

export class BottomMenu {
  static readonly basicMenuId = 'menu-filter-basic';
  static readonly advancedMenuId = 'menu-filter-advanced';
  static readonly analysisMenuId = 'menu-filter-analysis';
  static readonly experimentalMenuId = 'menu-filter-experimental';
  static readonly settingsMenuId = 'menu-filter-settings';
  static readonly allMenuId = 'menu-filter-all';







  static init() {
    // 强制启用底部菜单，忽略设置
    keepTrackApi.on(KeepTrackApiEvents.uiManagerInit, () => BottomMenu.createBottomMenu());
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, () => BottomMenu.addBottomMenuFilterButtons());
    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, () => BottomMenu.updateBottomMenuVisibility_());
  }

  // 🔥 优化ResizeObserver，减少不必要的触发
  private static setupResizeObserver() {
    if (BottomMenu.resizeObserver) {
      return;
    }

    const bottomContainer = getEl('bottom-icons-container');
    if (!bottomContainer) {
      return;
    }

    let resizeTimeout: number | null = null;
    let lastHeight = 0;

    BottomMenu.resizeObserver = new ResizeObserver((entries) => {
      // 🔥 防抖：避免频繁触发
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }

      resizeTimeout = setTimeout(() => {
        for (const entry of entries) {
          if (entry.target.id === 'bottom-icons-container') {
            const height = Math.round(entry.contentRect.height);

            // 🔥 只有高度真正改变时才更新
            if (height !== lastHeight) {
              lastHeight = height;
              document.documentElement.style.setProperty('--bottom-menu-top', `${height}px`);
            }
          }
        }
      }, 32) as any; // 增加防抖时间到32ms，减少触发频率
    });

    BottomMenu.resizeObserver.observe(bottomContainer);
  }

  // 🔥 重置缓存和状态的方法
  static resetCache() {
    BottomMenu.cachedElements.clear();
    LayoutCache.clear(); // 🔥 清理布局缓存
    BottomMenu.isStylesApplied = false;



    // 🔥 清理动画帧
    if (BottomMenu.updateAnimationFrame) {
      cancelAnimationFrame(BottomMenu.updateAnimationFrame);
      BottomMenu.updateAnimationFrame = null;
    }

    // 🔥 清理ResizeObserver
    if (BottomMenu.resizeObserver) {
      BottomMenu.resizeObserver.disconnect();
      BottomMenu.resizeObserver = null;
    }


  }
  static createBottomMenu(): void {
    // 修复DOM结构：直接在已存在的nav-footer中添加内容，而不是创建新的nav-footer
    const navFooter = getEl('nav-footer');
    if (!navFooter) {
      return;
    }
    // 添加底部菜单内容到现有的nav-footer中
    const bottomMenuContent = keepTrackApi.html`
      <div id="bottom-icons-container">
        <div id="bottom-icons-filter">
          <div id="${BottomMenu.allMenuId}" class="bmenu-filter-item">
            <div class="bmenu-filter-item-inner">
              <img alt="All Plugins" src="" delayedsrc="${developerModePng}" />
            </div>
            <span class="bmenu-filter-title">所有菜单</span>
          </div>
          <div id="${BottomMenu.basicMenuId}" class="bmenu-filter-item bmenu-item-selected">
            <div class="bmenu-filter-item-inner">
              <img alt="Basic Menu" src="" delayedsrc="${localCafePng}" />
            </div>
            <span class="bmenu-filter-title">基础菜单</span>
          </div>
          <div id="${BottomMenu.advancedMenuId}" class="bmenu-filter-item">
            <div class="bmenu-filter-item-inner">
              <img alt="Advanced Menu" src="" delayedsrc="${developerModePng}" />
            </div>
            <span class="bmenu-filter-title">高级菜单</span>
          </div>
          <div id="${BottomMenu.analysisMenuId}" class="bmenu-filter-item">
            <div class="bmenu-filter-item-inner">
              <img alt="Analysis Menu" src="" delayedsrc="${barChart4BarsPng}" />
            </div>
            <span class="bmenu-filter-title">分析菜单</span>
          </div>
          <div id="${BottomMenu.settingsMenuId}" class="bmenu-filter-item">
            <div class="bmenu-filter-item-inner">
              <img alt="Settings Menu" src="" delayedsrc="${settingsPng}" />
            </div>
            <span class="bmenu-filter-title">设置菜单</span>
          </div>
          <div id="${BottomMenu.experimentalMenuId}" class="bmenu-filter-item">
            <div class="bmenu-filter-item-inner">
              <img alt="Experimental Menu" src="" delayedsrc="${sciencePng}" />
            </div>
            <span class="bmenu-filter-title">实验菜单</span>
          </div>

        </div>
        <div id="bottom-icons"></div>
      </div>
    `;

    // 将内容插入到现有的nav-footer中
    navFooter.insertAdjacentHTML('beforeend', bottomMenuContent);

    // 🔥 恢复原始滑动系统，但默认设置为显示状态
    navFooter.classList.remove('footer-slide-down');
    navFooter.classList.add('footer-slide-up');
    navFooter.style.setProperty('visibility', 'visible', 'important');
    navFooter.style.setProperty('display', 'block', 'important');

    // 🔥 优化：立即执行，避免不必要的定时器
    BottomMenu.forceTransparentBackgrounds();
    BottomMenu.forceGridLayout();
    BottomMenu.isStylesApplied = true; // 🔥 标记样式已应用

    // 🔥 设置ResizeObserver监听底部菜单高度变化
    BottomMenu.setupResizeObserver();

  }

  // 🔥 缓存DOM元素，避免重复查询
  private static cachedElements: Map<string, HTMLElement> = new Map();
  private static isStylesApplied = false;



  private static getCachedElement(id: string): HTMLElement | null {
    if (!BottomMenu.cachedElements.has(id)) {
      const element = getEl(id);
      if (element) {
        BottomMenu.cachedElements.set(id, element);
      }
    }
    return BottomMenu.cachedElements.get(id) || null;
  }





  private static forceTransparentBackgrounds() {
    // 🔥 只在第一次或强制刷新时应用样式
    if (BottomMenu.isStylesApplied) return;

    try {
      const elementIds = [
        'bottom-icons',
        'bottom-icons-filter',
        'bottom-icons-container',
        'nav-footer'
      ];

      // 🔥 批量设置样式，减少重排
      const styleUpdates: Array<{element: HTMLElement, styles: Record<string, string>}> = [];

      elementIds.forEach(id => {
        const element = BottomMenu.getCachedElement(id);
        if (element) {
          styleUpdates.push({
            element,
            styles: {
              'background': 'transparent',
              'background-color': 'transparent',
              'background-image': 'none',
              'border': 'none',
              'outline': 'none',
              'box-shadow': 'none',
              'backdrop-filter': 'blur(15px)',
              '-webkit-backdrop-filter': 'blur(15px)',
              'pointer-events': 'auto',
              'visibility': 'visible',
              'opacity': '1'
            }
          });

          // 🔥 优化：简化拖动区域处理，减少DOM查询和样式设置
          if (id === 'bottom-icons-container') {
            // 只查找已知的拖动元素类，避免复杂的属性选择器
            const dragElements = element.querySelectorAll('.drag-resize-handle');
            dragElements.forEach(dragEl => {
              const dragElement = dragEl as HTMLElement;
              // 只设置必要的功能样式，视觉样式由CSS处理
              dragElement.style.setProperty('pointer-events', 'auto', 'important');
              dragElement.style.setProperty('cursor', 'n-resize', 'important');
            });
          }

          // 🔥 特殊处理：强制设置bottom-icons为grid布局 - 调整列宽
          if (id === 'bottom-icons') {
            element.style.setProperty('display', 'grid', 'important');
            element.style.setProperty('grid-template-columns', 'repeat(auto-fill, 80px)', 'important');
            element.style.setProperty('justify-content', 'center', 'important');
            element.style.setProperty('column-gap', '2px', 'important');

          } else {
            element.style.setProperty('display', 'block', 'important'); // 强制显示
          }

          // 特别处理nav-footer，确保显示状态
          if (id === 'nav-footer') {
            element.classList.remove('footer-slide-down');
            element.classList.add('footer-slide-up');
          }

        } else {
          // 元素不存在
        }
      });

      // 透明效果现在由CSS统一处理，移除JavaScript设置


    } catch (error) {
      console.warn('强制透明背景设置出错:', error);
    }
  }

  private static forceGridLayout() {
    // 🔥 只在第一次设置grid布局
    if (BottomMenu.isStylesApplied) return;

    try {
      const bottomIcons = BottomMenu.getCachedElement('bottom-icons');
      if (bottomIcons) {
        // 🔥 批量设置grid样式
        const gridStyles = {
          'display': 'grid',
          'grid-template-columns': 'repeat(auto-fill, 80px)',
          'justify-content': 'center',
          'width': 'calc(100% - 185px)',
          'float': 'right',
          'row-gap': '2px',
          'column-gap': '2px',
          'padding': '5px'
        };

        Object.entries(gridStyles).forEach(([property, value]) => {
          bottomIcons.style.setProperty(property, value, 'important');
        });
      }
    } catch (error) {
      console.warn('强制grid布局设置出错:', error);
    }
  }

  // 🔥 使用requestAnimationFrame优化动画性能
  private static updateAnimationFrame: number | null = null;
  private static resizeObserver: ResizeObserver | null = null;

  private static updateBottomMenuVisibility_() {
    // 🔥 简化逻辑，移除复杂的缓存和防抖
    const navFooter = getEl('nav-footer');
    const bottomContainer = getEl('bottom-icons-container');

    // 强制显示底部菜单
    if (navFooter) {
      showEl('nav-footer');
      navFooter.style.setProperty('display', 'block', 'important');
      navFooter.style.setProperty('visibility', 'visible', 'important');
    }

    // 设置底部菜单高度
    if (bottomContainer) {
      const bottomHeight = bottomContainer.offsetHeight;
      document.documentElement.style.setProperty('--bottom-menu-top', `${bottomHeight}px`);
    }

    // 只在首次初始化时设置样式
    if (!BottomMenu.isStylesApplied) {
      BottomMenu.forceTransparentBackgrounds();
      BottomMenu.forceGridLayout();
      BottomMenu.isStylesApplied = true;
    }
  }



  private static deselectAllBottomMenuFilterButtons_() {
    const menuIds = [
      BottomMenu.basicMenuId, BottomMenu.advancedMenuId, BottomMenu.analysisMenuId,
      BottomMenu.experimentalMenuId, BottomMenu.settingsMenuId, BottomMenu.allMenuId,
    ];

    const menuElements = menuIds.map((id) => getEl(id));

    if (menuElements.every((el) => el !== null)) {
      menuElements.forEach((el) => el.classList.remove('bmenu-item-selected'));
    } else {
      errorManagerInstance.warn('Failed to find all bottom menu filter buttons');
    }
  }

  private static onBottomMenuFilterClick_(menuButtonDom: HTMLElement, menuMode: MenuMode) {
    keepTrackApi.getSoundManager()?.play(SoundNames.MENU_BUTTON);
    settingsManager.activeMenuMode = menuMode;
    BottomMenu.deselectAllBottomMenuFilterButtons_();
    menuButtonDom.classList.add('bmenu-item-selected');
    keepTrackApi.emit(KeepTrackApiEvents.bottomMenuModeChange);
  }

  // 🔥 防止重复添加事件监听器
  private static isEventListenersAdded = false;

  static addBottomMenuFilterButtons() {
    // 🔥 防止重复添加事件监听器
    if (BottomMenu.isEventListenersAdded) return;

    const menuElements = [
      { element: getEl(BottomMenu.basicMenuId), mode: MenuMode.BASIC },
      { element: getEl(BottomMenu.advancedMenuId), mode: MenuMode.ADVANCED },
      { element: getEl(BottomMenu.analysisMenuId), mode: MenuMode.ANALYSIS },
      { element: getEl(BottomMenu.experimentalMenuId), mode: MenuMode.EXPERIMENTAL },
      { element: getEl(BottomMenu.settingsMenuId), mode: MenuMode.SETTINGS },
      { element: getEl(BottomMenu.allMenuId), mode: MenuMode.ALL }
    ];

    // 🔥 检查所有元素是否存在
    const allElementsExist = menuElements.every(item => item.element !== null);

    if (allElementsExist) {
      // 🔥 批量添加事件监听器
      menuElements.forEach(({ element, mode }) => {
        element!.addEventListener('click', () => BottomMenu.onBottomMenuFilterClick_(element!, mode), { once: false });
      });

      BottomMenu.isEventListenersAdded = true;
      keepTrackApi.emit(KeepTrackApiEvents.bottomMenuModeChange);
    } else {
      errorManagerInstance.warn('Failed to find all bottom menu filter buttons');
    }

    const wheel = (dom: EventTarget, deltaY: number) => {
      const domEl = dom as HTMLElement;
      const step = 0.15;
      const pos = domEl.scrollTop;
      const nextPos = pos + step * deltaY;

      domEl.scrollTop = nextPos;
    };

    ['bottom-icons', 'bottom-icons-filter'].forEach((divIdWithScroll) => {

      getEl(divIdWithScroll)!.addEventListener(
        'wheel',
        (event: WheelEvent) => {
          event.preventDefault(); // Prevent default scroll behavior
          if (event.currentTarget) {
            wheel(event.currentTarget, event.deltaY);
          }
        },
        { passive: false }, // Must be false to allow preventDefault()
      );
    });
  }


}
