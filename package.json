{"name": "太空物体模拟平台", "version": "1.0", "description": "专为非工程师设计的复杂天体动力学模拟平台，使学习轨道力学和卫星操作变得更简单。", "author": "北京星地探索科技有限公司", "contributors": [{"name": "", "url": ""}, {"name": "", "url": ""}, {"name": ""}], "license": "AGPL-3.0", "homepage": "https://spacedefense", "bugs": {"url": "www.spacedefense.cn/issues", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "main": "lib/index.js", "module": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib", "lib/workers"], "scripts": {"build": "npx tsx ./build/build-manager.ts production", "build:dev": "npx tsx ./build/build-manager.ts development", "build:watch": "npx --max-old-space-size=8192 tsx ./build/build-manager.ts development --watch", "changelog": "auto-changelog --tag-prefix v --commit-limit false -t ./changelog.hbs -o ./docs/CHANGELOG.md", "cypress:open": "cypress open", "generate-t7e-keys": "npx tsx ./scripts/generate-translation-keys.ts && npx eslint --fix ./scripts/generate-translation-keys.ts", "lcov": "node ./scripts/openLcov.mjs", "lint": "eslint . --ignore-path .gitignore --ext js,ts,tsx", "createtest": "npx tsx ./scripts/create-test-file.ts", "start": "npx serve ./dist -l 8080", "start:api": "npx tsx src/api/server.ts", "start:auth": "node scripts/start-with-auth.js", "test:api": "node scripts/test-api.js", "diagnose:server": "node scripts/diagnose-server.js", "check:modules": "node scripts/check-module-resolution.js", "migrate:port": "node scripts/migrate-port.js", "validate:config": "node scripts/validate-config.js", "test:login": "node scripts/test-login.js", "fix:ports": "node scripts/fix-hardcoded-ports.js", "start:api:safe": "bash scripts/start-api-server.sh", "security-check": "node scripts/security-check.js", "start:ci": "npx serve ./dist -l 8080", "start:ci2": "http-server --entry-file=dist/index.html", "test": "jest --coverage", "test:unit": "jest --coverage", "test:e2e-ci": "cypress run", "test:watch": "jest --coverage --watchAll", "prepublishOnly": "npm run build:lib", "version:major": "npm version major && npm run changelog", "version:minor": "npm version minor && npm run changelog", "version:patch": "npm version patch && npm run changelog", "husky": "husky install"}, "chromium-args": "--enable-threaded-compositing", "nyc": {"report-dir": "coverage/integration", "reporter": ["text", "json", "lcov", "html"], "all": true, "include": ["src/**/*.js"], "exclude": ["**/*.test.js", "**/test.js", "**/*.stories.js", "**/stories.js"]}, "directories": {"app": "src"}, "repository": {"type": "git", "url": "git+www.spacedefense.cn.git"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/plugin-transform-modules-commonjs": "^7.23.0", "@babel/preset-env": "^7.25.3", "@babel/preset-typescript": "^7.23.0", "@rspack/cli": "^1.3.8", "@rspack/core": "^1.3.8", "@testing-library/dom": "^9.3.3", "@testing-library/jest-dom": "^6.1.3", "@testing-library/user-event": "^14.5.2", "@types/agent-base": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.5", "@types/materialize-css": "^1.0.11", "@types/node": "^20.7.0", "@types/numeric": "^1.2.2", "@types/papaparse": "^5.3.14", "@types/webgl2": "^0.0.11", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/typescript-estree": "^8.29.0", "auto-changelog": "^2.4.0", "babel-loader": "^9.1.3", "babel-plugin-istanbul": "^6.1.1", "child_process": "^1.0.2", "clean-terminal-webpack-plugin": "^3.0.0", "codecov": "^3.8.3", "coverage-istanbul-loader": "^3.0.5", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "cypress": "^13.6.1", "download-file": "^0.1.5", "eslint": "^8.50.0", "eslint-plugin-jest": "^27.4.0", "http-server": "^14.1.1", "husky": "^8.0.1", "ignore-loader": "^0.1.2", "imports-loader": "^4.0.1", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "jsdom": "^22.1.0", "jsdom-worker": "^0.3.0", "mini-css-extract-plugin": "^2.7.6", "mkdirp": "^3.0.1", "opener": "^1.5.2", "papaparse": "^5.4.1", "path-to-regexp": "^8.1.0", "prettier-plugin-organize-imports": "^3.2.3", "retire": "^4.3.1", "serve": "^10.0.2", "source-map-loader": "^4.0.2", "style-loader": "^3.3.4", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "ts-node": "^10.9.2", "tsx": "^4.19.0", "typescript": "^5.3.3", "webgl-lint": "^1.11.3", "webgl-mock": "^0.1.7", "webpackbar": "^7.0.0", "worker-loader": "^3.0.8"}, "dependencies": {"@analytics/google-analytics": "^1.0.7", "@elastic/elasticsearch": "^8.15.0", "@materializecss/materialize": "^1.2.2", "@types/body-parser": "^1.19.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "analytics": "^0.8.14", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "draggabilly": "^3.0.0", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "eruda": "^2.5.0", "express": "^5.1.0", "file-saver": "^2.0.5", "flag-icons": "^7.2.3", "gl-matrix": "^3.4.3", "gremlins.js": "^2.2.0", "i18next": "^23.14.0", "i18next-browser-languagedetector": "^8.0.0", "material-icons": "^1.13.12", "new-github-issue-url": "^1.0.0", "node-fetch": "^2.7.0", "numeric": "^1.2.6", "ootk": "^5.1.0", "openmeteo": "^1.2.0", "resizable": "^1.2.1", "uuid": "^11.1.0", "webgl-obj-loader": "^2.0.8"}}