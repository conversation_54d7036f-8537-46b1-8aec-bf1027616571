@echo off
setlocal

echo 🚀 启动太空物体模拟平台API服务器
echo ==================================

REM 检查当前目录
if not exist "package.json" (
    echo ❌ 错误: 请在项目根目录中运行此脚本
    pause
    exit /b 1
)

REM 检查必要文件
echo 📋 检查必要文件...
if not exist "src\api\server.ts" (
    echo ❌ 缺少文件: src\api\server.ts
    pause
    exit /b 1
) else (
    echo ✅ src\api\server.ts
)

if not exist "src\api\auth.routes.ts" (
    echo ❌ 缺少文件: src\api\auth.routes.ts
    pause
    exit /b 1
) else (
    echo ✅ src\api\auth.routes.ts
)

if not exist "src\auth\auth.service.ts" (
    echo ❌ 缺少文件: src\auth\auth.service.ts
    pause
    exit /b 1
) else (
    echo ✅ src\auth\auth.service.ts
)

REM 检查Node.js版本
echo.
echo 🔍 检查Node.js版本...
node --version

REM 检查npm依赖
echo.
echo 📦 检查依赖...
if not exist "node_modules" (
    echo ⚠️  node_modules不存在，正在安装依赖...
    npm install
)

REM 设置环境变量
if "%NODE_ENV%"=="" set NODE_ENV=production
if "%PORT%"=="" set PORT=5001
if "%HOST%"=="" set HOST=0.0.0.0

echo.
echo 🔧 环境配置:
echo   NODE_ENV: %NODE_ENV%
echo   PORT: %PORT%
echo   HOST: %HOST%

REM 创建数据目录
echo.
echo 📁 创建数据目录...
if not exist "data" mkdir data
if not exist "logs" mkdir logs

REM 启动服务器
echo.
echo 🚀 启动API服务器...
echo 访问地址: http://localhost:%PORT%
echo 按 Ctrl+C 停止服务器
echo.

REM 使用tsx运行TypeScript文件
npx tsx src/api/server.ts

pause
