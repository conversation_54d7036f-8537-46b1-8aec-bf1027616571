<!doctype html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>太空物体模拟平台 - 管理后台</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #1a1a2e;
      color: #fff;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #0f3460;
      padding: 20px;
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .admin-panel {
      display: grid;
      grid-template-columns: 250px 1fr;
      gap: 20px;
    }
    .sidebar {
      background-color: #16213e;
      padding: 20px;
      border-radius: 5px;
    }
    .content {
      background-color: #16213e;
      padding: 20px;
      border-radius: 5px;
    }
    .menu-item {
      padding: 10px;
      margin-bottom: 10px;
      cursor: pointer;
      border-radius: 5px;
    }
    .menu-item:hover {
      background-color: #0f3460;
    }
    .menu-item.active {
      background-color: #0f3460;
      font-weight: bold;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #0f3460;
    }
    th {
      background-color: #0f3460;
    }
    .btn {
      padding: 8px 16px;
      background-color: #0f3460;
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .btn:hover {
      background-color: #1a4b8c;
    }
  </style>
</head>
<body>
  <header>
    <h1>太空物体模拟平台 - 管理后台</h1>
    <button id="logout-btn" class="btn">退出登录</button>
  </header>
  
  <div class="container">
    <div class="admin-panel">
      <div class="sidebar">
        <div class="menu-item active" data-section="dashboard">控制面板</div>
        <div class="menu-item" data-section="users">用户管理</div>
        <div class="menu-item" data-section="settings">系统设置</div>
        <div class="menu-item" data-section="analytics">数据分析</div>
      </div>
      
      <div class="content">
        <div id="dashboard-section">
          <h2>欢迎来到管理后台</h2>
          <p>这里是太空物体模拟平台的管理控制中心，您可以通过左侧菜单导航到不同功能模块。</p>
          
          <div style="margin-top: 30px;">
            <h3>系统概览</h3>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-top: 20px;">
              <div style="background-color: #0f3460; padding: 20px; border-radius: 5px;">
                <h4>用户总数</h4>
                <p id="total-users" style="font-size: 24px; margin: 10px 0 0;">0</p>
              </div>
              <div style="background-color: #0f3460; padding: 20px; border-radius: 5px;">
                <h4>活跃用户</h4>
                <p id="active-users" style="font-size: 24px; margin: 10px 0 0;">0</p>
              </div>
              <div style="background-color: #0f3460; padding: 20px; border-radius: 5px;">
                <h4>模拟次数</h4>
                <p id="simulations" style="font-size: 24px; margin: 10px 0 0;">0</p>
              </div>
              <div style="background-color: #0f3460; padding: 20px; border-radius: 5px;">
                <h4>系统状态</h4>
                <p id="system-status" style="font-size: 24px; margin: 10px 0 0; color: #4CAF50;">正常</p>
              </div>
            </div>
          </div>
        </div>
        
        <div id="users-section" style="display: none;">
          <h2>用户管理</h2>
          <div style="margin-bottom: 20px;">
            <button id="add-user-btn" class="btn">添加用户</button>
          </div>
          <table>
            <thead>
              <tr>
                <th>ID</th>
                <th>用户名</th>
                <th>角色</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="users-table">
              <!-- 用户数据将通过JavaScript动态加载 -->
            </tbody>
          </table>
        </div>
        
        <div id="settings-section" style="display: none;">
          <h2>系统设置</h2>
          <form id="settings-form">
            <div style="margin-bottom: 20px;">
              <label>系统名称</label>
              <input type="text" id="system-name" style="width: 100%; padding: 8px; background: #1a1a2e; border: 1px solid #0f3460; color: #fff; margin-top: 5px;">
            </div>
            <div style="margin-bottom: 20px;">
              <label>默认语言</label>
              <select id="default-language" style="width: 100%; padding: 8px; background: #1a1a2e; border: 1px solid #0f3460; color: #fff; margin-top: 5px;">
                <option value="zh">中文</option>
                <option value="en">English</option>
              </select>
            </div>
            <button type="submit" class="btn">保存设置</button>
          </form>
        </div>
        
        <div id="analytics-section" style="display: none;">
          <h2>数据分析</h2>
          <div style="background-color: #0f3460; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
            <canvas id="analytics-chart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 检查登录状态
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user || user.role !== 'admin') {
      window.location.href = '/';
    }

    // 菜单切换
    document.querySelectorAll('.menu-item').forEach(item => {
      item.addEventListener('click', () => {
        document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
        item.classList.add('active');
        
        document.querySelectorAll('[id$="-section"]').forEach(section => {
          section.style.display = 'none';
        });
        
        document.getElementById(`${item.dataset.section}-section`).style.display = 'block';
      });
    });

    // 退出登录
    document.getElementById('logout-btn').addEventListener('click', () => {
      localStorage.removeItem('user');
      window.location.href = '/';
    });

    // 加载用户数据
    async function loadUsers() {
      try {
        // 使用配置加载器获取API地址
        const apiBaseUrl = await window.configLoader.getApiBaseUrl();
        const response = await fetch(`${apiBaseUrl}/api/auth/users`);
        const users = await response.json();
        
        const tableBody = document.getElementById('users-table');
        tableBody.innerHTML = '';
        
        users.forEach(user => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.username}</td>
            <td>${user.role}</td>
            <td>
              <button class="btn edit-btn" data-id="${user.id}">编辑</button>
              <button class="btn delete-btn" data-id="${user.id}">删除</button>
            </td>
          `;
          tableBody.appendChild(row);
        });
      } catch (err) {
        console.error('加载用户数据失败:', err);
      }
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', () => {
      loadUsers();
    });
  </script>

  <!-- 引入统一配置加载器 -->
  <script src="js/config-loader.js"></script>
</body>
</html>