## Security Policy

### Reporting Security Vulnerabilities

We take the security of our project seriously. If you believe you've found a security vulnerability in our project, we encourage you to let us know right away. We will investigate all legitimate reports and do our best to quickly fix the problem.

#### Guidelines for Reporting

1. **Private Disclosure**: Please do not report security vulnerabilities through public GitHub issues. Instead, use the `Report a Vulnerability` option listed under the Issues tab.

2. **Provide Details**: Please provide as much detail as possible, including:
   - Steps to reproduce the vulnerability.
   - Potential impact if exploited.
   - Any potential solutions or mitigations, if known.

3. **Stay Updated**: After reporting, please monitor your email for any additional questions or updates on the resolution.

#### Our Commitment

1. **Confirmation**: We will acknowledge receipt of your vulnerability report.
   
2. **Investigation**: We will investigate the report and determine the risk associated with the reported vulnerability.

3. **Notification**: We will provide an estimated time for the vulnerability fix and notify you when the issue is resolved.

We appreciate responsible disclosure and will acknowledge your contribution once the vulnerability is confirmed and resolved.
