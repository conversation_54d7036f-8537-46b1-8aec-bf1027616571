@media (min-width: 1024px) and (max-width: 1279px) {
  :root {
    --nav-bar-height: 35px;
  }
}

@media (min-width: 1024px) {
  #sensor-selected-container {
    width: 100%;
    padding: 0px 10px;
  }

  #fullscreen-icon {
    display: none;
  }

  #menu-launches,
  #menu-record,
  #menu-color-scheme,
  #social,
  #fastCompSettings {
    display: block;
  }

  .top-menu-icons>a {
    padding: 0px 10px;
  }

  .search-icon-search-on {
    transition: 1s;
  }

  .top-menu-icons img {
    width: 25px;
    height: 25px;
  }

  #search-close {
    padding: 0px 6.25%;
    font-size: 24px;
  }

  #search-results {
    overflow-x: hidden;
    overflow-y: auto;
    display: none;
    position: absolute;
    right: 0px;
    width: 355px;
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    z-index: 1002;
    top: var(--top-menu-height);
    height: auto !important;
    max-height: calc(100vh - var(--top-menu-height) - var(--bottom-menu-height)) !important;
    min-height: auto !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    padding-top: 0px;
  }

  .share-icons {
    position: absolute;
    left: 0px;
    z-index: 1;
    width: 50px;
    height: 50px;
    padding: 9px;
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }

  .share-up {
    transition: 1s;
    top: 0px !important;
  }

  #time-machine-menu {
    padding-left: 35px;
    color: white;
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    top: 50px;
    right: 0px;
    z-index: 100;
    position: absolute;
    overflow: auto;
    width: 100%;
    border: none !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }

  #obfit-menu {
    width: 500px;
  }

  /* ::-webkit-scrollbar {
    display: none;
  } */

  .search-slide-down {
    width: 355px;
    /* This should match the #search-results */
  }

  .search-slide-up {
    width: 0px;
  }

  #legend-hover-menu {
    top: var(--top-menu-height);
    padding: 5px;
  }

  .Square-Box {
    width: calc(25px / var(--system-scale-factor, 1)) !important;
    height: calc(25px / var(--system-scale-factor, 1)) !important;
    /* 保留原有的颜色和样式属性 */
    border-width: calc(2px / var(--system-scale-factor, 1)) !important;
    border-style: solid !important;
    border-radius: calc(12px / var(--system-scale-factor, 1)) !important;
    margin-right: calc(15px / var(--system-scale-factor, 1)) !important;
    cursor: pointer !important;
    box-shadow: 0 0px calc(4px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.2),
                0 0px calc(6px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.19) !important;
  }

  #sat-infobox {
    /* 🔥 移除强制位置设置，允许用户拖动 */
    position: absolute !important;
    width: 237px; /* 355px * 2/3 ≈ 237px */
    max-height: 60%;
  }

  .satinfo-fixed:after {
    content: '';
    height: calc(var(--bottom-menu-height) + 15px);
    display: block;
  }

  .sat-info-value {
    float: right;
    width: 220px;
    padding: 0px 25px;
    text-align: center;
  }

  .truncate-search {
    width: 200px;
  }
}