
export type PluginConfiguration = {
  enabled: boolean;
  menuMode?: number[];
  order?: number;
};

export type KeepTrackPluginsConfiguration = {
  DebugMenuPlugin?: PluginConfiguration;
  SensorListPlugin?: PluginConfiguration;
  SensorInfoPlugin?: PluginConfiguration;
  CustomSensorPlugin?: PluginConfiguration;
  LookAnglesPlugin?: PluginConfiguration;
  MultiSiteLookAnglesPlugin?: PluginConfiguration;
  FilterMenuPlugin?: PluginConfiguration;
  transponderChannelData?: PluginConfiguration;
  VideoDirectorPlugin?: PluginConfiguration;
  DebrisScreening?: PluginConfiguration;
  SatInfoBox?: PluginConfiguration;
  FindSatPlugin?: PluginConfiguration;
  SatelliteFov?: PluginConfiguration;
  NightToggle?: PluginConfiguration;
  CountriesMenu?: PluginConfiguration;
  ScreenRecorder?: PluginConfiguration;
  AboutMenuPlugin?: PluginConfiguration;
  SettingsMenuPlugin?: PluginConfiguration;
  SoundManager?: PluginConfiguration;
  AnalysisMenu?: PluginConfiguration;
  Astronomy?: PluginConfiguration;
  Breakup?: PluginConfiguration;
  catalogLoader?: PluginConfiguration;
  ClassificationBar?: PluginConfiguration;
  Collisions?: PluginConfiguration;
  TrackingImpactPredict?: PluginConfiguration;
  ColorMenu?: PluginConfiguration;
  SatConstellations?: PluginConfiguration;
  countriesMenu?: PluginConfiguration;
  DateTimeManager?: PluginConfiguration;
  DopsPlugin?: PluginConfiguration;
  EditSat?: PluginConfiguration;
  GamepadPlugin?: PluginConfiguration;
  InitialOrbitDeterminationPlugin?: PluginConfiguration;
  LaunchCalendar?: PluginConfiguration;
  MissilePlugin?: PluginConfiguration;
  NewLaunch?: PluginConfiguration;
  NextLaunchesPlugin?: PluginConfiguration;
  OrbitReferences?: PluginConfiguration;
  SatellitePhotos?: PluginConfiguration;
  Planetarium?: PluginConfiguration;
  EciPlot?: PluginConfiguration;
  EcfPlot?: PluginConfiguration;
  RicPlot?: PluginConfiguration;
  Time2LonPlots?: PluginConfiguration;
  Lat2LonPlots?: PluginConfiguration;
  Inc2AltPlots?: PluginConfiguration;
  Inc2LonPlots?: PluginConfiguration;
  SatChangesPlugin?: PluginConfiguration;
  SatelliteViewPlugin?: PluginConfiguration;
  Screenshot?: PluginConfiguration;
  SensorFov?: PluginConfiguration;
  SensorSurvFence?: PluginConfiguration;
  ShortTermFences?: PluginConfiguration;
  SocialMedia?: PluginConfiguration;
  sounds?: PluginConfiguration;
  StereoMap?: PluginConfiguration;
  TimeMachine?: PluginConfiguration;
  TopMenu?: PluginConfiguration;
  updateSelectBox?: PluginConfiguration;
  WatchlistPlugin?: PluginConfiguration;
  WatchlistOverlay?: PluginConfiguration;
  ReportsPlugin?: PluginConfiguration;
  PolarPlotPlugin?: PluginConfiguration;
  GraphicsMenuPlugin?: PluginConfiguration;
  SensorTimeline?: PluginConfiguration;
  SatelliteTimeline?: PluginConfiguration;
  Calculator?: PluginConfiguration;
  CreateSat?: PluginConfiguration;
  ProximityOps?: PluginConfiguration;
  EarthPresetsPlugin?: PluginConfiguration;
  DrawLinesPlugin?: PluginConfiguration;
  ViewInfoRmbPlugin?: PluginConfiguration;
  HistoryTrackPlugin?: PluginConfiguration;
  GeoLongitudeHistoryPlugin?: PluginConfiguration;
};
