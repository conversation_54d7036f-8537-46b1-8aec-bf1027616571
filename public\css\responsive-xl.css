@media (min-width: 1280px) and (max-width: 1535px) {
  :root {
    --nav-bar-height: 30px;
  }
}

@media (min-width: 1280px) {
  #datetime-input {
    position: absolute;
    bottom: 10px;
    display: none;
  }

  #datetime-input-tb {
    color: white;
    font-family: 'Open Sans', sans-serif;
    padding: 10px 10px 11px 10px;
    /* Moves it below screen */
    height: 0px !important;
    margin: 0 0 55px 0 !important;
  }

  #datetime-input-tb:focus,
  input:focus {
    outline: none;
  }

  #datetime-title {
    position: absolute;
    left: 20px;
    bottom: 20px;
    width: 220px;
    height: 30px;
    background: transparent;
    overflow: hidden;
    text-align: center;
    font-weight: bold;
    font-size: 12px;
  }

  #sensor-selected-container {
    padding: 0px 10px 0px 10px;
    border-width: 0px 0px 0px 1px;
    border-style: solid;
    border-color: var(--color-dark-text-accent);
  }

  #share-icon {
    display: none;
  }

  #search-holder {
    right: 0px;
    top: 0px;
    display: inline-flex;
  }

  #search {
    height: 20px;
    margin: 0;
    padding-top: 5px;
  }

  #ui-datepicker-div {
    position: relative !important;
    border-radius: 0px;
  }

  #time-machine-menu {
    padding-left: 35px;
    color: white;
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    width: 300px;
    top: 25px;
    right: 0px;
    z-index: 100;
    position: absolute;
    overflow: auto;
    border: none !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }

  #obfit-menu {
    width: 500px;
  }

  #legend-hover-menu {
    padding-left: 5px;
    color: white;
    background: var(--color-dark-background);
    width: 300px;
    top: var(--top-menu-height);
    right: 0px;
    z-index: 100;
    position: absolute;
    overflow: auto;
    border-width: 0px 0px 5px 5px;
    border-style: solid;
    border-color: var(--color-dark-border);
  }

  .side-menu {
    position: absolute;
    background: var(--color-dark-background);
    color: white;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    z-index: 10;
    /* border-width: 0px 5px 0px 0px; */
    border-color: var(--color-dark-border);
    border-style: solid;
  }

  #about-menu {
    width: 375px;
  }

  #multi-site-look-angles-menu {
    width: 450px;
  }

  #findByLooks .row {
    margin: 0px;
  }

  #findByLooks .row div input {
    height: 2.5rem;
    margin: 0px;
  }

  #dops-menu {
    width: 400px;
  }

  #menu-satellite-collision,
  #FindSatPlugin-bottom-icon {
    display: inline;
  }

  #footer-toggle-wrapper {
    width: 50%;
    left: 25%;
    z-index: 1;
    position: relative;
  }

  .footer-slide-down {
    bottom: calc(-1 * var(--bottom-menu-height) + 5px) !important;
    height: var(--bottom-menu-height) !important;
    top: auto !important;
  }

  .footer-slide-trans {
    transition: 1s;
  }

  .footer-slide-up {
    bottom: 0px !important;
    top: auto !important;
  }

  footer {
    margin-top: 0px !important;
    position: fixed !important;
    bottom: 0px !important;
    /* 🔥 移除固定高度，允许拖动调整 */
    width: 100%;
    z-index: 100 !important;
  }

  #social {
    display: block;
    margin-top: -75px;
    margin-right: 90px;
  }

  #menu-space-stations,
  #menu-launches {
    display: inline;
  }

  #fastCompSettings {
    display: block;
  }

  #mobile-warning {
    display: none;
  }
}