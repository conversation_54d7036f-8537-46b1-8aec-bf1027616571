@echo off
echo 开始部署 keeptrack.space 项目...

REM 配置变量
set REMOTE_HOST=your-server-ip
set REMOTE_USER=your-username
set REMOTE_PATH=/var/www/keeptrack.space

echo 创建远程目录...
ssh %REMOTE_USER%@%REMOTE_HOST% "mkdir -p %REMOTE_PATH%"

echo 上传项目文件...
rsync -avz --exclude node_modules --exclude .git --exclude dist ./ %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/

echo 连接到服务器执行部署...
ssh %REMOTE_USER%@%REMOTE_HOST% "cd %REMOTE_PATH% && npm install && npm run build && pm2 restart keeptrack.space || pm2 start npm --name keeptrack.space -- start"

echo 部署完成！
pause 