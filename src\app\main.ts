import { AuthManager } from '../auth/auth';

class KeepTrack {
  private isAuthenticated = false;

  async init() {
    if (!await this.checkAuth()) {
      this.showLoginModal();
      return;
    }

    console.log("3D场景初始化完成");
    this.setupOrbitControls();
    this.loadSatelliteData();
  }

  private async checkAuth() {
    this.isAuthenticated = await AuthManager.checkAuth();
    return this.isAuthenticated;
  }

  private showLoginModal() {
    const modal = document.createElement('div');
    modal.innerHTML = `
      <div style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.7);z-index:9999;display:flex;justify-content:center;align-items:center;">
        <div style="background:#222;padding:2rem;border-radius:8px;width:300px;">
          <h2 style="color:#fff;margin-top:0;">登录</h2>
          <form id="loginForm">
            <input type="text" placeholder="用户名" required style="width:100%;padding:8px;margin-bottom:1rem;">
            <input type="password" placeholder="密码" required style="width:100%;padding:8px;margin-bottom:1rem;">
            <button type="submit" style="width:100%;padding:8px;background:#4CAF50;color:white;border:none;border-radius:4px;">登录</button>
          </form>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    document.getElementById('loginForm')?.addEventListener('submit', async (e) => {
      e.preventDefault();
      const form = e.target as HTMLFormElement;
      const username = form[0].value;
      const password = form[1].value;
      
      try {
        await AuthManager.login(username, password);
        modal.remove();
        this.init();
      } catch (error) {
        alert((error as Error).message);
      }
    });
  }

  private setupOrbitControls() {
    console.log("轨道控制已启用");
  }

  private loadSatelliteData() {
    fetch('/data/satellites.json')
      .then(res => res.json())
      .then(data => console.log(`加载了${data.length}个卫星数据`));
  }
}

// 初始化应用
const app = new KeepTrack();
app.init();