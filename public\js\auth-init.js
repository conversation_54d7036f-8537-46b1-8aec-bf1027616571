document.addEventListener('DOMContentLoaded', () => {
    const auth = new AuthManager();

    if (!auth.checkAuth()) {
        const loginForm = document.createElement('div');
        loginForm.id = 'login-form';
        loginForm.innerHTML = `
            <div style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.8);display:flex;justify-content:center;align-items:center;z-index:9999">
                <div style="background:#222;padding:2rem;border-radius:8px;width:300px">
                    <h2 style="color:#fff;margin-bottom:1rem">太空物体模拟平台登录</h2>
                    <input id="username" type="text" placeholder="用户名" style="width:100%;padding:0.5rem;margin-bottom:1rem">
                    <input id="password" type="password" placeholder="密码" style="width:100%;padding:0.5rem;margin-bottom:1rem">
                    <button id="login-btn" style="width:100%;padding:0.5rem;background:#4CAF50;color:white;border:none;border-radius:4px">登录</button>
                </div>
            </div>
        `;

        document.body.appendChild(loginForm);

        document.getElementById('login-btn').addEventListener('click', async () => {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (await auth.login(username, password)) {
                loginForm.remove();
                // 加载主应用脚本
                const mainScript = document.createElement('script');
                mainScript.src = 'js/main.js';
                document.body.appendChild(mainScript);
            } else {
                alert('登录失败，请检查用户名和密码');
            }
        });
    } else {
        // 已认证，加载主应用脚本
        const mainScript = document.createElement('script');
        mainScript.src = 'js/main.js';
        document.body.appendChild(mainScript);
    }
});