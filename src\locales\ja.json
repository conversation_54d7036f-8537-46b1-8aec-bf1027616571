{"time": {"days": {"0": "日曜日", "1": "月曜日", "2": "火曜日", "3": "水曜日", "4": "木曜日", "5": "金曜日", "6": "土曜日"}, "days-short": {"0": "日", "1": "月", "2": "火", "3": "水", "4": "木", "5": "金", "6": "土"}, "months": {"1": "1月", "2": "2月", "3": "3月", "4": "4月", "5": "5月", "6": "6月", "7": "7月", "8": "8月", "9": "9月", "10": "10月", "11": "11月", "12": "12月"}, "calendar": {"time": "時間", "hour": "時", "minute": "分", "second": "秒", "now": "現在", "done": "完了", "pause": "一時停止", "propagation": "伝播"}}, "errorMsgs": {"catalogNotFullyInitialized": "カタログはまだ完全に初期化されていません。数秒待ってから再試行してください。", "sensorGroupsApiEmpty": "APIにセンサーグループが見つかりません。組み込みのセンサーグループデータベースに戻ります。", "SelectSensorFirst": "最初にセンサーを選択してください！", "SelectSatelliteFirst": "最初に衛星を選択してください！", "SelectSecondarySatellite": "最初に二次衛星を選択してください！", "SatelliteNotDetailedSatellite": "衛星は詳細な衛星ではありません！", "SensorNotFound": "センサーが見つかりません！", "Scene": {"disablingGodrays": "コンピューターの処理が遅くなっています！ゴッドレイを無効にします。", "disablingAurora": "コンピューターの処理が遅くなっています！オーロラを無効にします。", "disablingAtmosphere": "コンピューターの処理が遅くなっています！大気を無効にします。", "disablingMoon": "コンピューターの処理が遅くなっています！月を無効にします。", "disablingMilkyWay": "コンピューターの処理が遅くなっています！天の川を無効にします。", "disablingSun": "コンピューターの処理が遅くなっています！太陽を無効にします。"}, "Breakup": {"CannotCreateBreakupForNonCircularOrbits": "非円形軌道では分裂を作成できません。修正中です。", "CannotCalcDirectionOfSatellite": "衛星の方向を計算できません。後でやり直してください。", "ErrorCreatingBreakup": "分裂の作成中にエラーが発生しました！", "InvalidStartNum": "無効な衛星開始番号です！デフォルトで90000に設定します！", "BreakupGeneratorFailed": "分裂ジェネレーターが失敗しました！", "SatelliteNotFound": "衛星が見つかりません！"}, "Collisions": {"noCollisionsData": "衝突データが見つかりません！", "errorProcessingCollisions": "SOCRATESデータの処理中にエラーが発生しました！"}, "EditSat": {"errorReadingFile": "ファイル読み込み中にエラーが発生しました！", "satelliteNotFound": "衛星が見つかりません！"}, "CreateSat": {"errorCreatingSat": "衛星の作成中にエラーが発生しました！"}, "Reports": {"popupBlocker": "レポートを表示するには、このサイトのポップアップを許可してください。"}, "SensorManager": {"errorUpdatingUi": "センサーUIスタイリングの更新中にエラーが発生しました。"}}, "hoverManager": {"launched": "打ち上げ済み", "launchedPlanned": "打ち上げ：予定", "launchedUnknown": "打ち上げ：不明"}, "loadingScreenMsgs": {"math": "計算中...", "science": "科学を探索中...", "science2": "科学を発見...", "dots": "宇宙に点を描画中...", "satIntel": "衛星情報を統合中...", "painting": "地球を描画中...", "coloring": "線の内側を着色中...", "elsets": "GPを探索中...", "models": "3Dモデルを構築中...", "cunningPlan": "狡猾な計画を立て中...", "copyrightNotice": "KeepTrack™およびspacedefense™はKruczek Labs LLCの商標です。<br>このインスタンスはGNU AGPL v3.0の下でライセンスされています。帰属表示、ソースコードへのアクセス、およびこの通知は可視のままにしてください。<br>商用ライセンスは付与されておらず、権利者への補償もありません。<br>無断使用、リブランディング、帰属表示の削除は、商標およびオープンソースライセンスの条件に違反する可能性があります。<br>© 2025 Kruczek Labs LLC. 無断転載を禁じます。完全な条件はLICENSEをご覧ください。", "copyrightNoticeMobile": "KeepTrack™およびspacedefense™はKruczek Labs LLCの商標です。<br>このインスタンスはGNU AGPL v3.0の下でライセンスされています。帰属表示、ソースコードへのアクセス、およびこの通知は可視のままにしてください。商用ライセンスは付与されていません。権利者への補償もありません。無断使用、リブランディング、帰属表示の削除は、商標およびオープンソースライセンスの条件に違反する可能性があります。<br>© 2025 Kruczek Labs LLC. 無断転載を禁じます。完全な条件はLICENSEをご覧ください。"}, "splashScreens": {"1": "衛星モデルは実際よりも大きく表示されています。他はすべて実寸です。", "2": "'L'キーを押して衛星軌道の表示/非表示を切り替えます。", "3": "'P'キーを押して現在の衛星の極座標プロットを開きます（最初にセンサーを選択する必要があります！）。", "4": "'T'キーを押してシミュレーション時間をリセットします。", "5": "'>'または'='キーを押してシミュレーション時間を進めます。", "6": "'<'または')'キーを押してシミュレーション時間を戻します。", "7": "'/'キーを押すとシミュレーション速度を1xと0xの間で切り替えます。", "8": "'V'キーを押して表示モードを変更します。", "9": "Shift+F1を押すといつでもヘルプメニューを開くことができます。", "10": "'R'キーを押して自動回転を有効にします。", "11": "衛星が選択されている時にShift + Cを押すと、視認コーンの表示/非表示を切り替えます。", "12": "'Home'キーでカメラを現在のセンサーに向けて回転させます。", "13": "'`'（チルダ）キーでカメラをデフォルトビューにリセットします。", "14": "'M'キーで現在の衛星の2Dマップビューを表示します。", "15": "下部ツールバーにある設定メニューには、体験をカスタマイズするための多くのオプションがあります。", "16": "多くのメニューには、歯車アイコンをクリックしてアクセスできる追加設定があります。", "17": "ウォッチリストに衛星を追加すると、現在のセンサーの上空にある時に通知を受け取ることができます。", "18": "地球儀上で右クリックするとコンテキストメニューが開き、さらにオプションが表示されます。", "19": "'+'または'-'キーでズームイン・アウトします。", "20": "'F11'キーを押して全画面モードのオン/オフを切り替えます。", "21": "右上の検索バーで衛星名またはNORAD IDで検索できます。", "22": "衛星を選択して下部メニューの'新規打ち上げ'ボタンをクリックすると、新しい打ち上げ名目を作成できます。", "23": "'N'キーを押して夜間表示を切り替えます。", "24": "'I'キーを押して衛星に関する文脈情報の表示/非表示を切り替えます。", "25": "'B'キーを押してメニューの表示/非表示を切り替えます。", "26": "Shift + ';'キーを押してシミュレーション速度を上げます。", "27": "','キーを押してシミュレーション速度を下げます。", "28": "オブジェクトをセカンダリに設定すると、プライマリオブジェクトとの相対距離を確認できます。", "29": "'['キーでオブジェクトをプライマリ/セカンダリ間で切り替えます。"}, "plugins": {"SensorListPlugin": {"bottomIconLabel": "センサー", "title": "センサーリストメニュー", "helpBody": "センサーメニューでは、計算やその他のメニュー機能で使用するセンサーを選択できます。センサーは主にサポートするネットワークに基づいてグループ化されています。メニューの左側にセンサー名、右側にはその所有国/組織が表示されています。<br><br>「すべての...センサー」オプションを選択すると、そのグループ内のすべてのセンサーが選択されます。これはネットワークカバレッジの視覚化に役立ちますが、現在はすべての計算に機能するわけではありません。ネットワークの視線角度を計算しようとする場合は、マルチサイト視線角度ツールを使用するか、ネットワーク内の個々のセンサーごとに視線角度を使用するのがベストです。<br><br>このリストのセンサーには、機械式レーダーやフェーズドアレイレーダーに加えて、光学センサーが含まれています：<ul style=\"margin-left: 40px;\"><li>フェーズドアレイレーダーは通常、低軌道（LEO）に限定されています。</li><li>機械式レーダーはLEOと静止軌道（GEO）の両方に使用できます。</li><li>光学センサーは通常GEOに使用されますが、LEOにも使用できます。</li><li>光学センサーは晴れた夜間の観測に限定されますが、レーダーは昼夜問わず使用できます。</li></ul><br>センサー情報は公開データに基づいており、センサー情報メニューで確認できます。追加センサーに関する公開データや既存のセンサー情報の修正がある場合は、<a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>までご連絡ください。"}, "SensorInfoPlugin": {"bottomIconLabel": "センサー情報", "title": "センサー情報メニュー", "helpBody": "センサー情報では、現在選択されているセンサーに関する情報を提供します。この情報は公開データに基づいており、常に100％正確であるとは限りません。追加センサーに関する公開データや既存のセンサー情報の修正がある場合は、<a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>までご連絡ください。<br><br>提供される情報には以下が含まれます：<ul style=\"margin-left: 40px;\"><li>センサー名</li><li>センサー所有者</li><li>センサータイプ</li><li>センサー視野</li></ul><br>さらに、このメニューからセンサーから太陽や月への線を素早く作成できます。"}, "CustomSensorPlugin": {"bottomIconLabel": "カスタムセンサー", "title": "カスタムセンサーメニュー", "helpBody": "これにより、計算やその他のメニュー機能で使用するカスタムセンサーを作成できます。これは完全にオリジナルのセンサーでも、既存のセンサーの変更でもかまいません。<br><br>センサーの緯度、経度、高度を設定した後、センサーの視野を設定できます。望遠鏡を選択すると、360度の視野と10度の仰角マスク、無制限の範囲を持つ視野が作成されます。望遠鏡オプションの選択を解除すると、視野を手動で設定できます。<br><br>既存のセンサーを編集しようとしている場合は、センサーリストから選択すると、カスタムセンサーが選択したセンサーの情報で更新されます。"}, "LookAnglesPlugin": {"bottomIconLabel": "視線角度", "title": "視線角度メニュー", "helpBody": "視線角度メニューでは、センサーと衛星間の距離、方位角、仰角を計算できます。メニューを使用する前に、衛星とセンサーを最初に選択する必要があります。<br><br>「上昇と下降時間のみを切り替え」は、衛星の上昇と下降時間のみを計算します。これは、衛星がセンサーから視認できる時間を素早く判断するのに役立ちます。<br><br>検索範囲は、長さと間隔のオプションを変更することで修正できます。"}, "MultiSiteLookAnglesPlugin": {"bottomIconLabel": "マルチサイト視線", "title": "マルチサイト視線角度メニュー", "helpBody": "マルチサイト視線角度メニューでは、衛星と複数のセンサー間の距離、方位角、仰角を計算できます。メニューを使用する前に、衛星を最初に選択する必要があります。<br><br>デフォルトでは、メニューは宇宙監視ネットワーク内のすべてのセンサーの視線角度を計算します。追加のセンサーの視線角度を計算したい場合は、メニューの下部でCSVファイルをエクスポートできます。CSVファイルにはすべてのセンサーの視線角度が含まれています。<br><br>テーブルの行をクリックすると、そのセンサーが選択され、シミュレーション時間がその視線角度の時間に変更されます。"}, "SensorTimeline": {"bottomIconLabel": "センサータイムライン", "title": "センサータイムラインメニュー", "helpBody": "センサータイムラインメニューは、複数のセンサーが1つの衛星の視認性を持つ時間を示します。タイムラインは通過の品質を示すためにカラーコードされています。赤は悪い通過、黄色は平均的な通過、緑は良い通過を示します。通過をクリックすると、センサーと時間がその通過に変更されます。<br><br>タイムラインは、シミュレーションの開始時間と終了時間を変更することで修正できます。さらに、タイムラインは長さと間隔のオプションを変更することでも修正できます。"}, "ProximityOps": {"bottomIconLabel": "ランデブー・近接運用", "title": "ランデブー・近接運用（RPO）", "titleSecondary": "ランデブー・近接運用リスト", "helpBody": "衛星間の今後の接近を検索します", "noradId": "NORAD ID", "maxDistThreshold": "最大距離閾値（km）", "maxRelativeVelocity": "最大相対速度（km/s）", "searchDuration": "検索期間（時間）", "geoText": "静止軌道", "leoText": "低軌道", "orbitType": "軌道タイプ", "geoAllVsAll": "静止軌道 全て対全て", "geoAllVsAllTooltip": "全ての静止軌道衛星間でRPOを検索", "comparePayloadsOnly": "ペイロードのみ比較", "comparePayloadsOnlyTooltip": "ペイロード間のみRPOを検索", "ignoreVimpelRso": "Vimpel RSOを無視", "ignoreVimpelRsoTooltip": "Vimpelカタログ由来のRSOを無視"}, "SatelliteTimeline": {"bottomIconLabel": "衛星タイムライン", "title": "衛星タイムラインメニュー", "helpBody": "衛星タイムラインメニューは、1つのセンサーが複数の衛星の視認性を持つ時間を示します。タイムラインは通過の品質を示すためにカラーコードされています。赤は悪い通過、黄色は平均的な通過、緑は良い通過を示します。通過をクリックすると、衛星と時間がその通過に変更されます。<br><br>タイムラインは、シミュレーションの開始時間と終了時間を変更することで修正できます。さらに、タイムラインは長さと間隔のオプションを変更することでも修正できます。"}, "WatchlistPlugin": {"bottomIconLabel": "ウォッチリスト", "title": "ウォッチリストメニュー", "helpBody": "ウォッチリストメニューでは、追跡する優先衛星のリストを作成できます。これにより、最も関心のある衛星をすぐに取得できます。リストはブラウザのローカルストレージに保存され、次回サイトを訪問したときにも利用できます。<br><br>ウォッチリスト上の衛星が選択されたセンサーの視野に入ると、通知が表示され、センサーから衛星への線が描画され、衛星の番号が地球儀上に表示されます。<br><br>オーバーレイ機能はウォッチリストが入力されていることに依存しています。"}, "WatchlistOverlay": {"bottomIconLabel": "オーバーレイ", "title": "オーバーレイメニュー", "helpBody": "<p>ウォッチリストオーバーレイは、ウォッチリスト内の各衛星の次の通過時間を表示します。オーバーレイは10秒ごとに更新されます。</p><p>オーバーレイは次の通過までの時間を示すためにカラーコードされています。色は以下の通りです：</p><ul><li>黄色 - 視界内</li><li>青色 - 次の通過までの時間が現在時刻から30分以内または現在時刻の10分前</li><li>白色 - 上記の要件に適合しない将来の通過</li></ul><p>オーバーレイ内の衛星をクリックすると、地図がその衛星に中心を合わせます。</p>"}, "ReportsPlugin": {"bottomIconLabel": "レポート", "title": "レポートメニュー", "helpBody": "レポートメニューは、表示しているデータを分析し理解するのに役立つツールの集まりです。"}, "PolarPlotPlugin": {"bottomIconLabel": "極座標プロット", "title": "極座標プロットメニュー", "helpBody": "極座標プロットメニューは、衛星の方位角と仰角の時間経過に伴う2D極座標プロットを生成するために使用されます。"}, "NextLaunchesPlugin": {"bottomIconLabel": "次の打ち上げ", "title": "次の打ち上げメニュー", "helpBody": "次の打ち上げメニューは<a href=\"https://thespacedevs.com/\" target=\"_blank\">The Space Devs</a>からデータを取得し、今後の打ち上げを表示します。"}, "FindSatPlugin": {"bottomIconLabel": "衛星を検索", "title": "衛星検索メニュー", "helpBody": "衛星検索メニューは、軌道パラメータまたは衛星特性によって衛星を見つけるために使用されます。<br><br>ほとんどのパラメータでは、左側に目標値を入力し、右側に誤差範囲を入力します。例えば、51〜52度の傾斜角を持つすべての衛星を見つけたい場合は、左のボックスに51.5、右のボックスに0.5を入力できます。検索は、それらの傾斜角内のすべての衛星を見つけて検索バーに表示します。"}, "ShortTermFences": {"bottomIconLabel": "短期フェンス", "title": "短期フェンス（STF）メニュー", "helpBody": "短期フェンス（STF）メニューは、センサー検索ボックスを視覚化するために使用されます。<br><br>これは、検索ボックス機能を持つセンサーを所有/操作していない限り、あまり役に立たない可能性があります。"}, "Collisions": {"bottomIconLabel": "衝突", "title": "衝突メニュー", "helpBody": "衝突メニューは、衝突確率が高い衛星を表示します。<br><br>行をクリックすると、衝突に関与する2つの衛星が選択され、時間が衝突時間に変更されます。"}, "TrackingImpactPredict": {"bottomIconLabel": "再突入予測", "title": "追跡と影響予測メニュー", "helpBody": "追跡と影響予測（TIP）メニューは、衛星の最新の追跡と影響予測メッセージを表示します。テーブルには以下の列が表示されています：<br><br><b>NORAD</b>：衛星のNORADカタログID。<br><br><b>減衰日</b>：衛星の予測減衰日。<br><br><b>緯度</b>：減衰時の衛星の緯度。<br><br><b>経度</b>：減衰時の衛星の経度。<br><br><b>ウィンドウ（分）</b>：予測の時間ウィンドウ（分単位）。<br><br><b>次のレポート（時間）</b>：次のレポートまでの時間（時間単位）。<br><br><b>再突入角度（度）</b>：衛星の再突入角度（度単位）。<br><br><b>RCS（m²）</b>：衛星のレーダー断面積（平方メートル単位）。<br><br><b>GP年齢（時間）</b>：最新の軌道要素セットの年齢（時間単位）。<br><br><b>乾燥質量（kg）</b>：衛星の乾燥質量（キログラム単位）。<br><br><b>体積（m³）</b>：衛星の推定体積（立方メートル単位）。<br><br>"}, "Breakup": {"bottomIconLabel": "分裂を作成", "title": "分裂メニュー", "helpBody": "分裂メニューは、衛星の分裂をシミュレートするためのツールです。<br><br>衛星の軌道を複製して修正することで、衛星の分裂をモデル化できます。衛星を選択してメニューを開いた後、ユーザーは以下を選択できます：<ul style=\"margin-left: 40px;\"><li>傾斜角の変動</li><li>昇交点赤経の変動</li><li>周期の変動</li><li>分裂片の数</li></ul>変動が大きいほど、シミュレートされた分裂の広がりが大きくなります。デフォルトの変動は、合理的な広がりを持つ分裂をシミュレートするのに十分です。"}, "DebrisScreening": {"bottomIconLabel": "デブリスクリーニング", "title": "デブリスクリーニングメニュー", "helpBody": "デブリスクリーニングメニューは、衛星によって潜在的に見えるデブリ物体のリストを生成するために使用されます。リストは、デブリ物体の軌道パラメータを計算し、それらを衛星の軌道パラメータと比較することによって生成されます。ユーザーはTLEまたはSGP4プロパゲータのいずれかを使用してリストを生成することを選択できます。また、ユーザーはデブリ物体のサイズとデブリ物体の等級によってリストをフィルタリングすることもできます。さらに、ユーザーはデブリ物体のサイズとデブリ物体の等級によってリストをフィルタリングすることもできます。また、ユーザーはTLEまたはSGP4プロパゲータのいずれかを使用してリストを生成することも選択できます。さらに、ユーザーはデブリ物体のサイズとデブリ物体の等級によってリストをフィルタリングすることもできます。"}, "TransponderChannelData": {"bottomIconLabel": "トランスポンダーとチャンネル情報", "title": "衛星トランスポンダーとチャンネル情報", "helpBody": "<p>この表には、テレビ、ラジオ、その他の通信サービスを含む衛星チャンネルの技術的詳細が含まれています：</p><ul><li><strong>衛星：</strong>チャンネルを放送している衛星の名前</li><li><strong>TVチャンネル：</strong>テレビ/ラジオチャンネルまたは通信サービスの名前</li><li><strong>ビーム：</strong>衛星ビームの方向（例：西半球）</li><li><strong>周波数：</strong>トランスポンダーの周波数と極性（例：3840 V）</li><li><strong>システム：</strong>放送システム（例：DVB-S2 8PSK）</li><li><strong>SRFEC：</strong>シンボルレートとFEC比率（例：30000 3/4）</li><li><strong>ビデオ：</strong>ビデオ/データ圧縮形式（例：MPEG-4/HD）</li><li><strong>言語：</strong>利用可能な音声/通信言語（例：英語、中国語）</li><li><strong>暗号化：</strong>使用されている暗号化システム（例：PowerVu）</li></ul><p>この情報は、衛星通信の専門家、技術者、愛好家、および衛星受信機器の設定やトラブルシューティングを行う人々に役立ちます。</p>"}, "EditSat": {"bottomIconLabel": "衛星を編集", "title": "衛星編集メニュー", "helpBody": "衛星編集メニューは、衛星データを編集するために使用されます。<br><br><ul><li>衛星SCC# - 米国宇宙軍によって各衛星に割り当てられた固有の番号。</li><li>エポック年 - 衛星の最後の軌道更新の年。</li><li>エポック日 - 衛星の最後の軌道更新の年間日数。</li><li>傾斜角 - 衛星の軌道面と赤道面との角度。</li><li>昇交点赤経 - 昇交点と最後の軌道更新時の衛星位置との角度。</li><li>離心率 - 衛星の軌道が完全な円からどれだけ逸脱しているかの量。</li><li>近地点引数 - 昇交点と衛星の地球に最も近い点との角度。</li><li>平均近点角 - 最後の軌道更新時の衛星位置と衛星の地球に最も近い点との角度。</li><li>平均運動 - 衛星の平均近点角が変化する率。</li></ul>"}, "NewLaunch": {"bottomIconLabel": "新規打ち上げ", "title": "新規打ち上げメニュー", "helpBody": "新規打ち上げメニューは、類似のパラメータを持つ既存の衛星を修正することにより、仮想的な軌道打ち上げを生成するために使用されます。<br><br>衛星を選択した後、打ち上げ場所と北/南方位角を選択できます。選択した衛星は、打ち上げサイトに合わせて修正されます。時計は00:00:00に変更され、打ち上げ後の相対時間を表します。これは、打ち上げ時間に対するセンサーカバレッジを計算するのに役立ちます。他の軌道物体との関係は正しくありません。"}, "MissilePlugin": {"bottomIconLabel": "ミサイル", "title": "ミサイルメニュー", "helpBody": "ミサイルメニューは、国家間の仮想的なミサイル発射を生成するために使用されます。<br><br>潜水艦発射ミサイルを使用する場合、発射地点はカスタムの緯度と経度です。陸上発射ミサイルを使用する場合、発射地点はオープンソースの報告に基づいた固定の緯度と経度になります。<br><br>カスタムミサイルに加えて、数百のミサイルを含むいくつかの事前定義されたシナリオも利用可能です。<br><br>すべてのミサイル発射は仮想的なものであり、実際の世界のイベントを表すものではありません。発射軌道はすべて同じ弾道モデルに基づいていますが、異なる最小および最大範囲を使用しています。"}, "StereoMap": {"bottomIconLabel": "ステレオマップ", "title": "ステレオグラフィックマップメニュー", "helpBody": "ステレオグラフィックマップメニューは、衛星の地表軌跡をステレオ投影法で視覚化するために使用されます。<br/><br/>地表軌跡上のある地点をクリックすると、衛星がその地点に到達する時間にシミュレーション時間を変更できます。<br/><br/>黄色の点は衛星がセンサーから視認できる時を示します。赤い点は衛星がセンサーから視認できない時を示します。衛星に最も近い点が現在の時間です。"}, "SensorFov": {"bottomIconLabel": "センサー視野"}, "SensorSurvFence": {"bottomIconLabel": "センサーフェンス"}, "SatelliteViewPlugin": {"bottomIconLabel": "衛星ビュー"}, "SatelliteFov": {"bottomIconLabel": "衛星視野", "title": "衛星視野メニュー", "helpBody": "衛星視野プラグインでは、衛星の視野を制御できます。"}, "Planetarium": {"bottomIconLabel": "プラネタリウムビュー"}, "NightToggle": {"bottomIconLabel": "夜間表示切替"}, "SatConstellations": {"bottomIconLabel": "コンステレーション", "title": "コンステレーションメニュー", "helpBody": "コンステレーションメニューでは、衛星のグループを表示できます。<br><br>一部のコンステレーションでは、コンステレーション内の衛星間の概念的なアップリンク/ダウンリンクやクロスリンクが描画されます。"}, "CountriesMenu": {"bottomIconLabel": "国別", "title": "国別メニュー", "helpBody": "国別メニューでは、衛星を原産国によってフィルタリングできます。"}, "ColorMenu": {"bottomIconLabel": "カラースキーム", "title": "カラースキームメニュー", "helpBody": "カラーメニューでは、オブジェクトの描画に使用するカラーテーマを変更できます。<br><br>様々なテーマでは、オブジェクトの軌道、特性、または太陽や地球との関係に基づいて色を変更できます。"}, "Screenshot": {"bottomIconLabel": "写真を撮る"}, "LaunchCalendar": {"bottomIconLabel": "打ち上げカレンダー"}, "TimeMachine": {"bottomIconLabel": "タイムマシン"}, "SatellitePhotos": {"bottomIconLabel": "衛星写真", "title": "衛星写真メニュー", "helpBody": "衛星写真メニューは、特定の衛星からのライブ写真を表示するために使用されます。<br><br>注意 - 画像APIの変更により、KeepTrackで間違った衛星が選択される場合があります。"}, "ScreenRecorder": {"bottomIconLabel": "動画を録画"}, "Astronomy": {"bottomIconLabel": "天文学"}, "Calculator": {"bottomIconLabel": "座標系変換", "title": "座標系変換メニュー", "helpBody": "座標系変換メニューは、異なる座標系間で変換するために使用されます。<br><br>このメニューでは、以下の座標系間で変換できます：<ul style=\"margin-left: 40px;\"><li>ECI - 地球中心慣性座標系</li><li>ECEF - 地球中心地球固定座標系</li><li>測地座標系</li><li>観測点中心座標系</li></ul>"}, "AnalysisMenu": {"bottomIconLabel": "分析", "title": "分析メニュー", "helpBody": "分析メニューでは、現在のビューのデータを分析するためのツールが提供されています。ツールには以下があります：<ul style=\"margin-left: 40px;\"><li>公式TLEのエクスポート - 実際の2行軌道要素をエクスポート</li><li>3LESのエクスポート - 3行軌道要素をエクスポート</li><li>KeepTrack TLEのエクスポート - 分析用を含むすべてのKeepTrack 2行軌道要素をエクスポート</li><li>KeepTrack 3LESのエクスポート - 分析用を含むすべてのKeepTrack 3行軌道要素をエクスポート</li><li>近接オブジェクトを検索 - 互いに近接しているオブジェクトを検索</li><li>再突入を検索 - 大気圏に再突入する可能性が高いオブジェクトを検索</li><li>最適な通過 - 現在選択されているセンサーに基づいて衛星の最適な通過を検索</li></ul>"}, "SettingsMenuPlugin": {"bottomIconLabel": "設定", "title": "設定メニュー", "helpBody": "設定メニューでは、アプリケーションを構成できます。"}, "VideoDirectorPlugin": {"bottomIconLabel": "ビデオディレクター", "title": "ビデオディレクターメニュー", "helpBody": "ビデオディレクターメニューは、ビデオを作成するためにシーン内のカメラやオブジェクトを操作するために使用されます。"}, "CreateSat": {"bottomIconLabel": "衛星を作成", "title": "衛星を作成", "helpBody": "衛星作成メニューは、ケプラー要素から衛星を作成するために使用されます。"}, "DopsPlugin": {"bottomIconLabel": "DOPを表示", "title": "精度低下率メニュー", "helpBody": "精度低下率（DOP）メニューは、衛星とセンサーのDOP値を計算するために使用されます。<br><br>DOP値は以下の通りです：<ul style=\"margin-left: 40px;\"><li>GDOP - 幾何学的精度低下率</li><li>PDOP - 位置精度低下率</li><li>HDOP - 水平精度低下率</li><li>VDOP - 垂直精度低下率</li><li>TDOP - 時間精度低下率</li><li>NDOP - 衛星数精度低下率</li></ul>"}, "EciPlot": {"bottomIconLabel": "ECIプロット", "title": "ECIプロットメニュー", "helpBody": "ECIプロットメニューは、衛星の位置を地球中心慣性（ECI）座標系でプロットするために使用されます。"}, "EcfPlot": {"bottomIconLabel": "ECFプロット", "title": "ECFプロットメニュー", "helpBody": "ECFプロットメニューは、衛星の位置を地球中心地球固定（ECF）座標系でプロットするために使用されます。"}, "RicPlot": {"bottomIconLabel": "RICプロット", "title": "RICプロットメニュー", "helpBody": "RICプロットメニューは、衛星の位置を動径方向、軌道方向、法線方向（RIC）座標系でプロットするために使用されます。"}, "Time2LonPlots": {"bottomIconLabel": "ウォーターフォールプロット", "title": "ウォーターフォールメニュー", "helpBody": "時間-経度（ウォーターフォール）プロットメニューは、衛星の経度を時間に対してプロットするために使用されます。"}, "Lat2LonPlots": {"bottomIconLabel": "緯度-経度プロット", "title": "緯度-経度プロットメニュー", "helpBody": "緯度-経度プロットメニューは、GEO帯における緯度と経度のプロットに使用されます。"}, "Inc2AltPlots": {"bottomIconLabel": "傾斜角-高度プロット", "title": "傾斜角-高度プロットメニュー", "helpBody": "傾斜角-高度プロットメニューは、衛星の傾斜角を高度に対してプロットするために使用されます。"}, "Inc2LonPlots": {"bottomIconLabel": "傾斜角-経度プロット", "title": "傾斜角-経度プロットメニュー", "helpBody": "傾斜角-経度プロットメニューは、GEO帯における傾斜角と経度のプロットに使用されます。"}, "GraphicsMenuPlugin": {"bottomIconLabel": "グラフィックメニュー", "title": "グラフィックメニュー", "helpBody": "グラフィックメニューは、アプリケーションのグラフィック設定を変更するために使用されます。"}}}