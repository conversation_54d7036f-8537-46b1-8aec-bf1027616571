const API_BASE_URL = 'http://localhost:5001';

export const authService = {
  async login(username: string, password: string) {
    const response = await fetch(`${API_BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
    });

    if (!response.ok) {
      throw new Error('登录失败，请检查用户名和密码');
    }

    const data = await response.json();
    return data;
  },

  async checkAuth(token: string) {
    const response = await fetch(`${API_BASE_URL}/validate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    return response.ok;
  }
};