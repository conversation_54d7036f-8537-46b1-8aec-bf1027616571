#!/usr/bin/env node

/**
 * 认证系统测试脚本
 */

const fetch = require('node-fetch');

class AuthTester {
    constructor() {
        this.baseUrl = 'http://localhost:3001/api/auth';
        this.testResults = [];
    }

    async runTests() {
        console.log('🧪 开始测试认证系统...\n');

        try {
            await this.testLogin();
            await this.testRegistration();
            await this.testTokenVerification();
            await this.testPasswordChange();
            await this.testAdminFunctions();
        } catch (error) {
            console.error('❌ 测试过程中发生错误:', error);
        }

        this.printResults();
    }

    async testLogin() {
        console.log('🔐 测试登录功能...');

        // 测试正确登录
        try {
            const response = await fetch(`${this.baseUrl}/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    username: 'admin',
                    password: 'SpaceDefense2025!'
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.adminToken = data.token;
                this.addResult('✅ 管理员登录', '成功');
            } else {
                this.addResult('❌ 管理员登录', '失败');
            }
        } catch (error) {
            this.addResult('❌ 管理员登录', `错误: ${error.message}`);
        }

        // 测试错误登录
        try {
            const response = await fetch(`${this.baseUrl}/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    username: 'admin',
                    password: 'wrongpassword'
                })
            });

            if (!response.ok) {
                this.addResult('✅ 错误密码拒绝', '成功');
            } else {
                this.addResult('❌ 错误密码拒绝', '失败 - 应该被拒绝');
            }
        } catch (error) {
            this.addResult('❌ 错误密码测试', `错误: ${error.message}`);
        }
    }

    async testRegistration() {
        console.log('📝 测试注册功能...');

        try {
            const response = await fetch(`${this.baseUrl}/register`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    username: 'testuser',
                    email: '<EMAIL>',
                    password: 'TestPassword123!'
                })
            });

            if (response.ok) {
                this.addResult('✅ 用户注册', '成功');
            } else {
                const data = await response.json();
                this.addResult('❌ 用户注册', `失败: ${data.error}`);
            }
        } catch (error) {
            this.addResult('❌ 用户注册', `错误: ${error.message}`);
        }

        // 测试重复注册
        try {
            const response = await fetch(`${this.baseUrl}/register`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    username: 'testuser',
                    email: '<EMAIL>',
                    password: 'TestPassword123!'
                })
            });

            if (!response.ok) {
                this.addResult('✅ 重复用户名拒绝', '成功');
            } else {
                this.addResult('❌ 重复用户名拒绝', '失败 - 应该被拒绝');
            }
        } catch (error) {
            this.addResult('❌ 重复用户名测试', `错误: ${error.message}`);
        }
    }

    async testTokenVerification() {
        console.log('🎫 测试令牌验证...');

        if (!this.adminToken) {
            this.addResult('❌ 令牌验证', '跳过 - 没有有效令牌');
            return;
        }

        try {
            const response = await fetch(`${this.baseUrl}/verify`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ token: this.adminToken })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.valid) {
                    this.addResult('✅ 有效令牌验证', '成功');
                } else {
                    this.addResult('❌ 有效令牌验证', '失败 - 令牌被认为无效');
                }
            } else {
                this.addResult('❌ 有效令牌验证', '失败');
            }
        } catch (error) {
            this.addResult('❌ 有效令牌验证', `错误: ${error.message}`);
        }

        // 测试无效令牌
        try {
            const response = await fetch(`${this.baseUrl}/verify`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ token: 'invalid-token' })
            });

            if (!response.ok) {
                this.addResult('✅ 无效令牌拒绝', '成功');
            } else {
                const data = await response.json();
                if (!data.valid) {
                    this.addResult('✅ 无效令牌拒绝', '成功');
                } else {
                    this.addResult('❌ 无效令牌拒绝', '失败 - 无效令牌被接受');
                }
            }
        } catch (error) {
            this.addResult('❌ 无效令牌测试', `错误: ${error.message}`);
        }
    }

    async testPasswordChange() {
        console.log('🔑 测试密码修改...');

        if (!this.adminToken) {
            this.addResult('❌ 密码修改', '跳过 - 没有有效令牌');
            return;
        }

        try {
            const response = await fetch(`${this.baseUrl}/change-password`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.adminToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    oldPassword: 'wrongpassword',
                    newPassword: 'NewPassword123!'
                })
            });

            if (!response.ok) {
                this.addResult('✅ 错误旧密码拒绝', '成功');
            } else {
                this.addResult('❌ 错误旧密码拒绝', '失败 - 应该被拒绝');
            }
        } catch (error) {
            this.addResult('❌ 错误旧密码测试', `错误: ${error.message}`);
        }
    }

    async testAdminFunctions() {
        console.log('👑 测试管理员功能...');

        if (!this.adminToken) {
            this.addResult('❌ 管理员功能', '跳过 - 没有有效令牌');
            return;
        }

        // 测试获取用户列表
        try {
            const response = await fetch(`${this.baseUrl}/users`, {
                headers: { 'Authorization': `Bearer ${this.adminToken}` }
            });

            if (response.ok) {
                const users = await response.json();
                this.addResult('✅ 获取用户列表', `成功 - 找到 ${users.length} 个用户`);
            } else {
                this.addResult('❌ 获取用户列表', '失败');
            }
        } catch (error) {
            this.addResult('❌ 获取用户列表', `错误: ${error.message}`);
        }

        // 测试获取待审批注册
        try {
            const response = await fetch(`${this.baseUrl}/pending-registrations`, {
                headers: { 'Authorization': `Bearer ${this.adminToken}` }
            });

            if (response.ok) {
                const registrations = await response.json();
                this.addResult('✅ 获取待审批注册', `成功 - 找到 ${registrations.length} 个申请`);
            } else {
                this.addResult('❌ 获取待审批注册', '失败');
            }
        } catch (error) {
            this.addResult('❌ 获取待审批注册', `错误: ${error.message}`);
        }

        // 测试获取登录日志
        try {
            const response = await fetch(`${this.baseUrl}/login-attempts`, {
                headers: { 'Authorization': `Bearer ${this.adminToken}` }
            });

            if (response.ok) {
                const logs = await response.json();
                this.addResult('✅ 获取登录日志', `成功 - 找到 ${logs.length} 条记录`);
            } else {
                this.addResult('❌ 获取登录日志', '失败');
            }
        } catch (error) {
            this.addResult('❌ 获取登录日志', `错误: ${error.message}`);
        }
    }

    addResult(test, result) {
        this.testResults.push({ test, result });
        console.log(`  ${test}: ${result}`);
    }

    printResults() {
        console.log('\n📊 测试结果汇总:');
        console.log('='.repeat(50));

        const passed = this.testResults.filter(r => r.test.startsWith('✅')).length;
        const failed = this.testResults.filter(r => r.test.startsWith('❌')).length;
        const total = this.testResults.length;

        console.log(`总测试数: ${total}`);
        console.log(`通过: ${passed}`);
        console.log(`失败: ${failed}`);
        console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);

        if (failed === 0) {
            console.log('\n🎉 所有测试通过！认证系统工作正常。');
        } else {
            console.log('\n⚠️  部分测试失败，请检查系统配置。');
        }

        console.log('\n💡 提示:');
        console.log('- 确保API服务器正在运行 (npm run start:api)');
        console.log('- 检查端口3001是否可用');
        console.log('- 确认数据目录存在且可写');
    }
}

// 运行测试
if (require.main === module) {
    const tester = new AuthTester();
    tester.runTests().catch(console.error);
}

module.exports = AuthTester;
