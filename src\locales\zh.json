{"time": {"days": {"0": "星期日", "1": "星期一", "2": "星期二", "3": "星期三", "4": "星期四", "5": "星期五", "6": "星期六"}, "days-short": {"0": "周日", "1": "周一", "2": "周二", "3": "周三", "4": "周四", "5": "周五", "6": "周六"}, "months": {"1": "一月", "2": "二月", "3": "三月", "4": "四月", "5": "五月", "6": "六月", "7": "七月", "8": "八月", "9": "九月", "10": "十月", "11": "十一月", "12": "十二月"}, "calendar": {"time": "时间", "hour": "小时", "minute": "分钟", "second": "秒", "now": "实时", "done": "完成", "pause": "暂停", "propagation": "速度"}}, "errorMsgs": {"catalogNotFullyInitialized": "目录尚未完全初始化。请等待几秒钟再试。", "sensorGroupsApiEmpty": "API中未找到传感器组，恢复使用内置的传感器组数据库。", "SelectSensorFirst": "请先选择一个传感器！", "SelectSatelliteFirst": "请先选择一个卫星！", "SelectSecondarySatellite": "请先选择一个从卫星！", "SatelliteNotDetailedSatellite": "卫星不是详细卫星类型！", "SensorNotFound": "未找到传感器！", "Scene": {"disablingGodrays": "您的计算机运行困难！正在禁用上帝光线。", "disablingAurora": "您的计算机运行困难！正在禁用极光。", "disablingAtmosphere": "您的计算机运行困难！正在禁用大气层。", "disablingMoon": "您的计算机运行困难！正在禁用月球。", "disablingMilkyWay": "您的计算机运行困难！正在禁用银河系。", "disablingSun": "您的计算机运行困难！正在禁用太阳。"}, "Breakup": {"CannotCreateBreakupForNonCircularOrbits": "无法为非圆形轨道创建解体。正在修复中。", "CannotCalcDirectionOfSatellite": "无法计算卫星方向。请稍后再试。", "ErrorCreatingBreakup": "创建解体时出错！", "InvalidStartNum": "无效的卫星起始编号！默认为90000！", "BreakupGeneratorFailed": "解体生成器失败！", "SatelliteNotFound": "未找到卫星！"}, "Collisions": {"noCollisionsData": "未找到碰撞数据！", "errorProcessingCollisions": "处理SOCRATES数据时出错！"}, "EditSat": {"errorReadingFile": "读取文件时出错！", "satelliteNotFound": "未找到卫星！"}, "CreateSat": {"errorCreatingSat": "创建卫星时出错！"}, "Reports": {"popupBlocker": "请允许此网站的弹出窗口以查看报告。"}, "SensorManager": {"errorUpdatingUi": "更新传感器UI样式时出错。"}}, "hoverManager": {"launched": "COSPAR", "launchedPlanned": "发射：计划中", "launchedUnknown": "发射：未知"}, "loadingScreenMsgs": {"math": "正在进行数学计算...", "science": "正在定位算法...", "science2": "已找到算法...", "dots": "正在绘制太空点...", "satIntel": "正在整合卫星数据...", "painting": "正在绘制地球...", "coloring": "正在进行线内着色...", "elsets": "正在定位轨道参数...", "models": "正在构建3D模型...", "cunningPlan": "正在完成任务...", "copyrightNotice": "《太空物体模拟平台》为北京星地科技有限公司产品<br> 未经许可请勿使用和复制，有问题请联系：<EMAIL>", "copyrightNoticeMobile": " "}, "splashScreens": {"1": "卫星模型看起来比实际大小要大，其他一切都是按比例显示的", "2": "按\"L\"键切换卫星轨道显示/隐藏", "3": "按\"P\"键打开当前卫星的极地图（需要先选择一个传感器！）", "4": "按\"T\"键重置模拟时间", "5": "按\">\"或\"=\"键推进模拟时间", "6": "按\"<\"或\">\"键回退模拟时间", "7": "按\"/\"键在1x和0x之间切换模拟速度", "8": "按\"V\"键更改视图模式", "9": "随时按\"Shift+F1\"打开帮助菜单", "10": "按\"R\"键启用自动旋转", "11": "选中卫星后按\"Shift+C\"键切换其可见锥显示/隐藏", "12": "\"Home\"键将使摄像机旋转到当前传感器", "13": "\"`\"（波浪号）键将重置摄像机到默认视图", "14": "按\"M\"键显示当前卫星的2D地图视图", "15": "底部工具栏中的设置菜单包含许多自定义体验的选项", "16": "许多菜单有额外的设置，可以通过点击齿轮图标访问", "17": "将卫星添加到监视列表，当它们在当前传感器上方时收到通知", "18": "右键点击地球打开更多选项的上下文菜单", "19": "按\"+\"或\"-\"键放大和缩小", "20": "按\"F11\"切换全屏模式", "21": "您可以在右上角的搜索栏中按名称或NORAD ID搜索卫星", "22": "通过选择一个卫星并点击底部菜单中的\"新发射\"按钮，可以创建一个新的发射模型", "23": "按\"N\"键切换夜间模式", "24": "按\"I\"键隐藏/显示卫星的上下文信息", "25": "按\"B\"键隐藏/显示菜单", "26": "按Shift+\";\"键加快模拟速度", "27": "按\",\"键降低模拟速度", "28": "将一个物体设为从对象，可以看到它与主要对象的相对距离", "29": "用\"[\"键在主要/从对象之间切换"}, "plugins": {"SensorListPlugin": {"bottomIconLabel": "传感器", "title": "传感器列表菜单", "helpBody": "传感器菜单允许您选择用于计算和其他菜单功能的传感器。传感器根据其主要支持的网络分组。菜单左侧显示传感器名称，右侧显示所属国家/组织。<br><br>选择“全部...传感器”选项将选择该组中的所有传感器。这对于可视化网络覆盖范围很有用，但目前并非所有计算都支持。如果您想为网络计算观测角度，建议使用多站点观测角工具，或分别为网络中的每个传感器计算观测角。<br><br>此列表中的传感器包括机械和相控阵雷达，以及光学传感器：<ul style=\"margin-left: 40px;\"> <li>相控阵雷达通常仅限于近地轨道（LEO）。</li> <li>机械雷达可用于LEO和地球同步轨道（GEO）。</li> <li>光学传感器通常用于GEO，但也可用于LEO。</li> <li>光学传感器仅限于晴朗夜晚观测，而雷达可用于昼夜。</li> </ul><br>传感器信息基于公开数据，可在传感器信息菜单中验证。如果您有更多公开传感器数据或修正建议，请联系<a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>。"}, "SensorInfoPlugin": {"bottomIconLabel": "传感器信息", "title": "传感器信息菜单", "helpBody": "传感器信息提供当前选中传感器的相关信息。信息基于公开数据，可能并非100%准确。如果您有更多公开传感器数据或修正建议，请联系<a href=\"mailto:admin@spacedefense\">admin@spacedefense</a>。<br><br>提供的信息包括：<ul style=\"margin-left: 40px;\"> <li>传感器名称</li> <li>传感器所属</li> <li>传感器类型</li> <li>传感器视场</li> </ul><br>此外，可从此菜单快速创建传感器到太阳或月亮的连线。"}, "CustomSensorPlugin": {"bottomIconLabel": "自定义传感器", "title": "自定义传感器菜单", "helpBody": "此功能允许您创建用于计算和其他菜单功能的自定义传感器。可以是全新传感器，也可以基于现有传感器修改。<br><br>设置传感器的纬度、经度和高度后，可设置视场。选择望远镜选项将创建360度视场，仰角遮罩为10度，范围无限。取消选择望远镜选项可手动设置视场。<br><br>如需编辑现有传感器，可先在传感器列表中选择，之后自定义传感器将更新为所选传感器信息。"}, "LookAnglesPlugin": {"bottomIconLabel": "观测角", "title": "观测角菜单", "helpBody": "观测角菜单允许您计算传感器与卫星之间的距离、方位角和仰角。使用前需先选择卫星和传感器。<br><br>仅计算升起和落下时间的切换功能可快速判断卫星何时可被传感器观测。搜索范围可通过更改时长和间隔选项调整。"}, "MultiSiteLookAnglesPlugin": {"bottomIconLabel": "多站观测", "title": "多站观测角菜单", "helpBody": "多站观测角菜单允许您计算一颗卫星与多个传感器之间的距离、方位角和仰角。使用前需先选择卫星。<br><br>默认情况下，菜单会计算空间监视网络中所有传感器的观测角。如需计算其他传感器的观测角，可在菜单底部导出csv文件，文件将包含所有传感器的观测角。<br><br>点击表格中的一行将选中该传感器并将仿真时间切换到该观测角时间点。"}, "SensorTimeline": {"bottomIconLabel": "传感器时间线", "title": "传感器时间线菜单", "helpBody": "传感器时间线菜单显示多个传感器对一颗卫星的可见性。时间线按颜色区分观测质量：红色为差，黄色为一般，绿色为好。点击某次观测可切换到对应传感器和时间。<br><br>可通过更改仿真的起止时间，以及长度和间隔选项来调整时间线。"}, "ProximityOps": {"bottomIconLabel": "交会与近距离", "title": "交会与近距离操作（RPO）", "titleSecondary": "交会与近距离操作列表", "helpBody": "查找卫星之间即将发生的接近事件", "noradId": "NORAD编号", "maxDistThreshold": "最大距离阈值（公里）", "maxRelativeVelocity": "最大相对速度（公里/秒）", "searchDuration": "搜索时长（小时）", "geoText": "地球同步轨道", "leoText": "近地轨道", "orbitType": "轨道类型", "geoAllVsAll": "地球同步轨道全部对全部", "geoAllVsAllTooltip": "搜索所有地球同步轨道卫星之间的RPO", "comparePayloadsOnly": "仅比较有效载荷", "comparePayloadsOnlyTooltip": "仅搜索有效载荷之间的RPO", "ignoreVimpelRso": "忽略Vimpel公司提供的RSO驻留空间物体信息", "ignoreVimpelRsoTooltip": "忽略来自Vimpel目录的RSO"}, "SatelliteTimeline": {"bottomIconLabel": "卫星时间线", "title": "卫星时间线菜单", "helpBody": "卫星时间线菜单显示单个传感器对一组卫星的可见性。时间线按颜色区分通过质量：红色为差，黄色为一般，绿色为好。点击某次通过可切换到对应卫星和时间。<br><br>可通过更改仿真的起止时间，以及长度和间隔选项来调整时间线。"}, "WatchlistPlugin": {"bottomIconLabel": "关注列表", "title": "关注列表菜单", "helpBody": "关注列表菜单允许您创建优先跟踪的卫星列表，便于快速检索感兴趣的卫星。该列表保存在浏览器本地存储，下次访问网站时仍可用。<br><br>当关注列表中的卫星进入所选传感器视场时，会显示通知，从传感器到卫星会绘制一条连线，并在地球上显示卫星编号。<br><br>叠加功能依赖于关注列表已被填充。"}, "WatchlistOverlay": {"bottomIconLabel": "叠加", "title": "叠加菜单", "helpBody": "<p>关注列表叠加显示关注卫星的下次通过时间，每10秒更新一次。</p><p>叠加颜色表示距离下次通过的时间：</p><ul><li>黄色 - 可见中</li><li>蓝色 - 距当前时间30分钟内或提前10分钟的通过</li><li>白色 - 其他未来通过</li></ul><p>点击叠加中的卫星会将地图中心定位到该卫星。</p>"}, "ReportsPlugin": {"bottomIconLabel": "报告", "title": "报告菜单", "helpBody": "报告菜单包含多种工具，帮助您分析和理解当前查看的数据。"}, "PolarPlotPlugin": {"bottomIconLabel": "极坐标图", "title": "极坐标图菜单", "helpBody": "极坐标图菜单用于生成卫星方位角和仰角随时间变化的二维极坐标图。"}, "NextLaunchesPlugin": {"bottomIconLabel": "未来发射", "title": "未来发射菜单", "helpBody": "未来发射菜单从<a href=\"https://thespacedevs.com/\" target=\"_blank\">The Space Devs</a>获取即将进行的发射信息。"}, "FindSatPlugin": {"bottomIconLabel": "查找目标", "title": "查找目标菜单", "helpBody": "查找目标菜单用于按轨道参数或卫星特性查找太空目标。<br><br>大多数参数输入左侧为目标值，右侧为误差范围。例如，查找51-52度倾角的卫星，可在左侧输入51.5，右侧输入0.5，搜索结果会显示所有在该范围内的卫星。"}, "ShortTermFences": {"bottomIconLabel": "机动传感器", "title": "机动传感器（STF）菜单", "helpBody": "机动搜索传感器（STF）菜单用于可视化传感器搜索区域。<br><br>除非您拥有/操作带有搜索框功能的传感器，否则该功能可能用处不大。"}, "Collisions": {"bottomIconLabel": "碰撞预测", "title": "碰撞预测菜单", "helpBody": "碰撞预测菜单显示高碰撞概率的卫星。<br><br>点击某一行会选中涉及碰撞的两颗卫星，并将时间切换到碰撞发生时刻。"}, "TrackingImpactPredict": {"bottomIconLabel": "再入预测", "title": "跟踪与再入预测菜单", "helpBody": "跟踪与再入预测（TIP）菜单显示卫星的最新跟踪与再入预测信息。表格包含以下列：<br><br><b>NORAD</b>：卫星的NORAD目录编号。<br><br><b>衰减日期</b>：预测的卫星衰减日期。<br><br><b>纬度</b>：卫星衰减时的纬度。<br><br><b>经度</b>：卫星衰减时的经度。<br><br><b>窗口（分钟）</b>：预测时间窗口（分钟）。<br><br><b>下次报告（小时）</b>：距离下次报告的时间（小时）。<br><br><b>再入角（度）</b>：卫星再入角度（度）。<br><br><b>雷达截面积（平方米）</b>：卫星的雷达截面积。<br><br><b>GP数据龄（小时）</b>：最新轨道参数集的时效（小时）。<br><br><b>干质量（千克）</b>：卫星干质量。<br><br><b>体积（立方米）</b>：卫星估算体积。"}, "Breakup": {"bottomIconLabel": "创建解体", "title": "解体菜单", "helpBody": "解体菜单用于模拟卫星解体。<br><br>通过复制并修改卫星轨道，可对卫星解体进行建模。选择卫星并打开菜单后，可设置：<ul style=\"margin-left: 40px;\"> <li>倾角变化</li> <li>升交点赤经变化</li> <li>周期变化</li> <li>解体碎片数量</li> </ul>变化越大，模拟解体的碎片分布越广。默认参数足以模拟合理的解体分布。"}, "DebrisScreening": {"bottomIconLabel": "碎片筛查", "title": "碎片筛查菜单", "helpBody": "碎片筛查菜单用于生成可能被卫星观测到的碎片对象列表。通过计算碎片的轨道参数并与卫星轨道参数比较生成列表。用户可选择使用TLE或SGP4预报器生成列表，也可按碎片大小和星等进行筛选。"}, "TransponderChannelData": {"bottomIconLabel": "转发器与频道信息", "title": "卫星转发器与频道信息", "helpBody": "<p>本表包含卫星频道的技术细节，包括电视、广播及其他通信服务：</p><ul><li><strong>卫星：</strong> 播出频道的卫星名称</li><li><strong>频道：</strong> 电视/广播频道或通信服务名称</li><li><strong>波束：</strong> 卫星波束方向（如西半球）</li><li><strong>频率：</strong> 转发器频率及极化（如3840 V）</li><li><strong>系统：</strong> 播出系统（如DVB-S2 8PSK）</li><li><strong>符号率/FEC：</strong> 符号率及FEC比（如30000 3/4）</li><li><strong>视频：</strong> 视频/数据压缩格式（如MPEG-4/HD）</li><li><strong>语言：</strong> 可用音频/通信语言（如英语、中文）</li><li><strong>加密：</strong> 使用的加密系统（如PowerVu）</li></ul><p>这些信息适用于卫星通信专业人士、技术人员、爱好者及任何需要设置或排查卫星接收设备的人。</p>"}, "EditSat": {"bottomIconLabel": "编辑卫星", "title": "编辑卫星菜单", "helpBody": "编辑卫星菜单用于编辑卫星数据。<br><br><ul><li>卫星SCC# - 美国太空军分配的唯一编号</li><li>历元年份 - 卫星最后一次轨道更新的年份</li><li>历元日 - 卫星最后一次轨道更新的儒略日</li><li>倾角 - 卫星轨道面与赤道面的夹角</li><li>升交点赤经 - 升交点与卫星最后一次轨道更新位置的夹角</li><li>偏心率 - 卫星轨道偏离圆形的程度</li><li>近地点幅角 - 升交点与卫星近地点的夹角</li><li>平近点角 - 卫星当前位置与近地点的夹角</li><li>平均运动 - 卫星平近点角变化速率</li></ul>"}, "NewLaunch": {"bottomIconLabel": "新建发射", "title": "新建发射菜单", "helpBody": "新建发射菜单用于通过修改具有相似参数的现有卫星，生成假想轨道发射。<br><br>选择卫星后，可选择发射地点和南/北方位角。所选卫星将被调整以与发射场对齐，时间将重置为00:00:00，表示发射后的相对时间。这有助于计算发射时刻的传感器覆盖。与其他轨道物体的关系可能不准确。"}, "MissilePlugin": {"bottomIconLabel": "导弹模拟", "title": "导弹模拟菜单", "helpBody": "导弹模拟菜单用于生成国家间的假想导弹发射。<br><br>使用潜射导弹时，发射点为自定义经纬度；使用陆基导弹时，发射点为公开报道的固定经纬度。<br><br>除自定义导弹外，还提供若干涉及数百枚导弹的预设场景。<br><br>所有导弹发射均为假想，不代表真实世界事件。发射轨迹均基于同一弹道模型，但最小和最大射程不同。"}, "StereoMap": {"bottomIconLabel": "平面地图", "title": "平面地图菜单", "helpBody": "平面地图菜单用于以立体投影方式可视化卫星地面轨迹。<br/><br/>可点击轨迹上的任意点，将仿真时间切换到卫星到达该点的时刻。<br/><br/>黄色点表示卫星在传感器视场内，红色点表示不在视场内，距离卫星最近的点为当前时间。"}, "SensorFov": {"bottomIconLabel": "传感器视场"}, "SensorSurvFence": {"bottomIconLabel": "传感器搜索框"}, "SatelliteViewPlugin": {"bottomIconLabel": "卫星视图"}, "SatelliteFov": {"bottomIconLabel": "卫星视场", "title": "卫星视场菜单", "helpBody": "卫星视场插件允许您控制卫星的视场。"}, "Planetarium": {"bottomIconLabel": "天文馆视图"}, "NightToggle": {"bottomIconLabel": "夜间切换"}, "SatConstellations": {"bottomIconLabel": "星座", "title": "星座菜单", "helpBody": "星座菜单允许您查看卫星群。<br><br>对于某些星座，将在星座内的卫星之间绘制假想的上/下行链路和/或星间链路。"}, "CountriesMenu": {"bottomIconLabel": "国家", "title": "国家菜单", "helpBody": "国家菜单允许您按原籍国筛选卫星。"}, "ColorMenu": {"bottomIconLabel": "配色方案", "title": "配色方案菜单", "helpBody": "配色菜单用于更改渲染对象时使用的配色主题。<br><br>不同主题可根据对象轨道、特性或与太阳/地球的关系改变颜色。"}, "Screenshot": {"bottomIconLabel": "拍照"}, "LaunchCalendar": {"bottomIconLabel": "发射日历"}, "TimeMachine": {"bottomIconLabel": "时光穿梭"}, "SatellitePhotos": {"bottomIconLabel": "卫星照片", "title": "卫星照片菜单", "helpBody": "卫星照片菜单用于显示部分卫星的实时照片。<br><br>注意：图像API的更改可能导致选择了错误的卫星。"}, "ScreenRecorder": {"bottomIconLabel": "录制视频"}, "Astronomy": {"bottomIconLabel": "天文"}, "Calculator": {"bottomIconLabel": "参考系转换", "title": "参考系转换菜单", "helpBody": "参考系转换菜单用于在不同参考系之间转换。<br><br>菜单允许您在以下参考系之间转换：<ul style=\"margin-left: 40px;\"><li>ECI - 地心惯性系</li><li>ECEF - 地心地固系</li><li>大地坐标系</li><li>地方坐标系</li></ul>"}, "AnalysisMenu": {"bottomIconLabel": "分析", "title": "分析菜单", "helpBody": "分析菜单提供多种工具，帮助您分析当前视图中的数据。工具包括：<ul style=\"margin-left: 40px;\"><li>导出官方TLE - 导出真实的两行元数据。</li><li>导出3LES - 导出三行元数据。</li><li>导出该系统TLE - 导出该系统所有两行元数据（含分析员数据）。</li><li>导出该系统 3LES - 导出所有该系统三行元数据（含分析员数据）。</li><li>查找近距离物体 - 查找彼此接近的物体。</li><li>查找再入 - 查找可能再入大气层的物体。</li><li>最佳过境 - 基于当前选中传感器查找卫星的最佳过境。</li></ul>"}, "SettingsMenuPlugin": {"bottomIconLabel": "设置", "title": "设置菜单", "helpBody": "设置菜单允许您配置应用程序。"}, "VideoDirectorPlugin": {"bottomIconLabel": "视频编排", "title": "视频编排菜单", "helpBody": "视频编排菜单用于操作场景中的摄像机和对象以创建视频。"}, "CreateSat": {"bottomIconLabel": "创建卫星", "title": "创建卫星", "helpBody": "创建卫星菜单用于根据开普勒元素创建卫星。"}, "DopsPlugin": {"bottomIconLabel": "查看DOP", "title": "精度因子菜单", "helpBody": "精度因子（DOP）菜单用于计算卫星和传感器的DOP值。<br><br>DOP值包括：<ul style=\"margin-left: 40px;\"><li>GDOP - 几何精度因子</li><li>PDOP - 位置精度因子</li><li>HDOP - 水平精度因子</li><li>VDOP - 垂直精度因子</li><li>TDOP - 时间精度因子</li><li>NDOP - 卫星数量精度因子</li></ul>"}, "EciPlot": {"bottomIconLabel": "ECI绘图", "title": "ECI绘图菜单", "helpBody": "ECI绘图菜单用于绘制卫星在地心惯性系（ECI）中的位置。"}, "EcfPlot": {"bottomIconLabel": "ECF绘图", "title": "ECF绘图菜单", "helpBody": "ECF绘图菜单用于绘制卫星在地心地固系（ECF）中的位置。"}, "RicPlot": {"bottomIconLabel": "RIC绘图", "title": "RIC绘图菜单", "helpBody": "RIC绘图菜单用于绘制卫星在径向、轨道内和轨道横向（RIC）参考系中的位置。"}, "Time2LonPlots": {"bottomIconLabel": "时间经度图", "title": "瀑布菜单", "helpBody": "经度随时间（瀑布）绘图菜单用于绘制卫星经度随时间的变化。"}, "Lat2LonPlots": {"bottomIconLabel": "纬度-经度图", "title": "纬度-经度绘图菜单", "helpBody": "纬度-经度绘图菜单用于绘制GEO带内的纬度与经度关系。"}, "Inc2AltPlots": {"bottomIconLabel": "倾角-高度图", "title": "倾角-高度绘图菜单", "helpBody": "倾角-高度绘图菜单用于绘制卫星倾角与高度的关系。"}, "Inc2LonPlots": {"bottomIconLabel": "GEO倾角-经度图图", "title": "GEO倾角-经度图绘图菜单", "helpBody": "GEO倾角-经度图绘图菜单用于绘制GEO带内的倾角与经度关系。"}, "GraphicsMenuPlugin": {"bottomIconLabel": "图形菜单", "title": "图形菜单", "helpBody": "图形菜单用于更改应用程序的图形设置。"}, "GeoLongitudeHistoryPlugin": {"bottomIconLabel": "GEO经度历史", "title": "GEO卫星经度历史", "helpBody": "GEO卫星经度历史插件用于查看和分析地球同步轨道卫星的经度变化历史数据。"}}}