// src/scripts/utils/versionManager.ts
import { BuildError, ConsoleStyles, ErrorCodes, logWithStyle } from './build-error';
import { FileSystemManager } from './filesystem-manager';

/**
 * Manages version information for the build
 */
export class VersionManager {
  private readonly fileManager: FileSystemManager;

  constructor(fileManager: FileSystemManager) {
    this.fileManager = fileManager;
  }

  /**
   * Generates a version constants file from package.json
   * @param packageJsonPath Path to package.json
   * @param outputPath Path to the output version file
   */
  public generateVersionFile(packageJsonPath: string, outputPath: string): void {
    try {
      logWithStyle('Updating version information', ConsoleStyles.INFO);

      // Read version from package.json
      const version = this.readVersionFromPackageJson(packageJsonPath);

      // Generate version file
      this.writeVersionFile(version, outputPath);

      // Update version in README.md
      this.updateReadmeVersion(version);

      logWithStyle(`Version ${version} has been set`, ConsoleStyles.SUCCESS);
    } catch (error) {
      if (error instanceof BuildError) {
        throw error;
      }
      throw new BuildError(
        `Failed to generate version file: ${error instanceof Error ? error.message : String(error)}`,
        ErrorCodes.FILE_OPERATION,
      );
    }
  }

  /**
   * Reads version from package.json file
   * @param packageJsonPath Path to package.json
   * @returns The version string
   */
  private readVersionFromPackageJson(packageJsonPath: string): string {
    try {
      const packageJsonContent = this.fileManager.readFile(packageJsonPath);
      const packageJson = JSON.parse(packageJsonContent);

      if (!packageJson.version) {
        throw new BuildError(
          'Package.json does not contain a version property',
          ErrorCodes.FILE_OPERATION,
        );
      }

      return packageJson.version;
    } catch (error) {
      if (error instanceof BuildError) {
        throw error;
      }
      if (error instanceof SyntaxError) {
        throw new BuildError(
          `package.json contains invalid JSON: ${error.message}`,
          ErrorCodes.FILE_OPERATION,
        );
      }
      throw new BuildError(
        `Failed to read version from package.json: ${error instanceof Error ? error.message : String(error)}`,
        ErrorCodes.FILE_OPERATION,
      );
    }
  }

  /**
   * Writes the version to a JavaScript file
   * @param version The version string
   * @param outputPath Path to the output file
   */
  private writeVersionFile(version: string, outputPath: string): void {
    const content = this.generateVersionFileContent(version);

    this.fileManager.writeFile(outputPath, content);
    logWithStyle(`Version file written to ${outputPath}`, ConsoleStyles.SUCCESS);
  }

  /**
   * Updates the version badge in README.md
   * @param version The version string
   */
  private updateReadmeVersion(version: string): void {
    const readmePath = 'README.md';
    const readmeContent = this.fileManager.readFile(readmePath);

    // Update version badge in README.md
    const updatedReadme = readmeContent.replace(
      /!\[Latest Version\]\(https:\/\/img\.shields\.io\/badge\/version-[^-]+-/gu,
      `![Latest Version](https://img.shields.io/badge/version-${version}-`,
    );

    this.fileManager.writeFile(readmePath, updatedReadme);
    logWithStyle(`Updated version in README.md to ${version}`, ConsoleStyles.SUCCESS);
  }

  /**
   * Generates the content for the version file
   * @param version The version string
   * @returns The file content
   */
  private generateVersionFileContent(version: string): string {
    return `
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/**
 * The current application version
 */
export const VERSION = '${version}';
`;
  }
}
