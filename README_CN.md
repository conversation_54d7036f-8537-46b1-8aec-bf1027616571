# 太空物体模拟平台 (Space Object Simulation Platform)

[![License](https://img.shields.io/badge/license-AGPL--3.0-blue.svg)](LICENSE)
[![Version](https://img.shields.io/badge/version-1.0-green.svg)](package.json)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)]()

<img src="./public/img/logo.png" width='100%' alt="太空物体模拟平台" align="center">

## 📋 目录

- [项目概述](#项目概述)
- [适用人群](#适用人群)
- [核心特性](#核心特性)
- [系统架构](#系统架构)
- [快速开始](#快速开始)
- [功能模块](#功能模块)
- [插件系统](#插件系统)
- [文档资源](#文档资源)
- [贡献指南](#贡献指南)
- [许可证](#许可证)

## 🚀 项目概述

太空物体模拟平台是一个专为非工程师设计的复杂天体动力学模拟平台，旨在使学习轨道力学和卫星操作变得更简单。该平台基于WebGL技术构建，能够实时模拟250万个太空碎片，并在60fps下流畅运行。

### ✨ 核心特性

- **🎯 高性能渲染**：基于WebGL2的自定义着色器，支持实时渲染大量太空物体
- **⚡ 轻量级架构**：核心应用仅7MB，2秒内完成加载
- **🌐 跨平台兼容**：支持所有现代浏览器，无需安装额外软件
- **🔧 模块化设计**：基于插件系统的可扩展架构
- **📡 实时数据**：支持TLE数据更新和实时轨道计算
- **📊 丰富分析**：轨道分析、碰撞检测、传感器管理等专业功能

## 👥 适用人群

太空物体模拟平台广泛应用于操作中心、教室和科普活动中。以下是不同群体的使用场景：

### 🏢 操作中心
- 当卫星通过传感器时获得弹窗提醒
- 在几秒内找到传感器可以观测到优先卫星的时间
- 轻松模拟新发射任务进行任务规划

### 🎓 教育培训
- 通过动手交互学习，使学习更加有趣
- 使用Xbox控制器进行课堂演示
- 体验AAA级视频游戏品质的图形效果

### 👥 公众科普
- 免费在您的网站上嵌入太空模拟平台
- 在发射前展示您的卫星设计在轨道中的样子
- 解释空间可持续性和太空碎片问题

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                      │
├─────────────────────────────────────────────────────────────┤
│  顶部菜单  │  侧边栏  │  底部工具栏  │  信息面板  │  控制面板  │
├─────────────────────────────────────────────────────────────┤
│                    插件系统 (Plugin System)                   │
├─────────────────────────────────────────────────────────────┤
│ 传感器管理 │ 轨道分析 │ 时间控制 │ 数据可视化 │ 搜索过滤 │
├─────────────────────────────────────────────────────────────┤
│                    核心引擎 (Core Engine)                     │
├─────────────────────────────────────────────────────────────┤
│  渲染引擎  │  场景管理  │  相机控制  │  输入处理  │  时间管理  │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data Layer)                       │
├─────────────────────────────────────────────────────────────┤
│  目录管理  │  轨道计算  │  TLE数据  │  传感器数据 │  配置管理  │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 📋 系统要求

- **浏览器**：Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **硬件**：支持WebGL2的显卡
- **内存**：建议4GB以上RAM
- **网络**：用于TLE数据更新的互联网连接

### 💻 安装部署

#### 开发环境

```bash
# 克隆项目
git clone https://github.com/your-repo/space-simulation-platform.git
cd space-simulation-platform

# 安装依赖
npm install

# 启动开发服务器
npm run build:watch
npm start
```

#### 生产环境

```bash
# 构建生产版本
npm run build

# 启动服务器
npm run start:ci
```

### 🎮 基本操作

#### 相机控制
- **旋转**：鼠标左键拖拽
- **缩放**：鼠标滚轮
- **平移**：鼠标中键拖拽
- **重置**：双击空白区域

#### 物体选择
- **单击选择**：点击卫星或碎片
- **搜索选择**：使用顶部搜索框
- **列表选择**：通过各种插件列表

#### 时间控制
- **播放/暂停**：空格键
- **加速/减速**：+/- 键
- **时间跳转**：点击时间显示区域

## 🔧 功能模块

### 📡 卫星跟踪与显示
- **实时位置计算**：使用SGP4/SDP4算法进行精确轨道传播
- **轨道路径显示**：显示历史和预测轨道
- **多种颜色编码**：按国家、类型、高度等分类显示
- **信息标签**：显示卫星名称和关键参数

### 🎯 传感器管理
- **多类型支持**：雷达、光学、射频等传感器类型
- **3D视场显示**：立体显示传感器覆盖范围
- **可见性分析**：计算目标可见时间窗口
- **多传感器协同**：支持多站联合观测

### 📊 轨道分析
- **轨道参数计算**：开普勒轨道根数和状态向量
- **轨道比较分析**：多个轨道的对比研究
- **机动检测**：自动识别轨道变化
- **碰撞预测**：潜在碰撞风险评估

### ⏰ 时间管理
- **仿真时间控制**：可控制的虚拟时间系统
- **历史回放**：查看过去的轨道状态
- **未来预测**：轨道状态的前向计算
- **多种时间倍率**：1x到10000x的时间加速

### 📈 历史轨道追踪
- **多参数显示**：同时显示轨道高度、倾角、偏心率等
- **时间范围选择**：支持自定义时间范围查询
- **多卫星对比**：可同时显示多个卫星的参数对比
- **交互式图表**：支持缩放、平移、参数切换等操作
- **动态Y轴**：根据选中参数自动调整坐标范围

## 🔌 插件系统

### 核心插件分类

#### 📊 数据管理插件
- **SelectSatManager**：卫星选择和信息显示
- **FilterMenu**：数据过滤和搜索
- **Countries**：按国家分类显示
- **CatalogManager**：目录数据管理

#### 🔬 分析工具插件
- **Analysis**：轨道分析工具
- **Collisions**：碰撞检测分析
- **HistoryTrack**：历史轨道追踪
- **DebrisScreening**：碎片筛选分析
- **ProximityOps**：近距离操作分析

#### 📡 传感器插件
- **SensorList**：传感器列表管理
- **SensorFov**：传感器视场显示
- **LookAngles**：观测角度计算
- **SensorTimeline**：传感器时间线

#### 🎨 可视化插件
- **ColorsMenu**：颜色方案管理
- **OrbitReferences**：参考轨道显示
- **SatelliteView**：卫星视角模式
- **StereoMap**：立体地图显示

#### 🛠️ 工具插件
- **Calculator**：轨道计算器
- **Screenshot**：屏幕截图工具
- **Reports**：报告生成器
- **TimeMachine**：时间控制面板
- **Settings**：系统设置面板

### 插件开发示例

```typescript
export class CustomPlugin extends KeepTrackPlugin {
  id = 'CustomPlugin';
  
  init(): void {
    this.addHtml();  // 添加UI元素
    this.addJs();    // 绑定事件处理
  }
  
  addHtml(): void {
    // 插件UI代码
  }
  
  addJs(): void {
    // 插件逻辑代码
  }
}
```

## 📚 文档资源

- [📖 详细用户手册](docs/用户手册.md) - 完整的功能介绍和使用指南
- [🔧 API文档](docs/api.md) - 开发者API参考
- [🔌 插件开发指南](docs/plugin-development.md) - 自定义插件开发
- [🏗️ 架构设计文档](docs/architecture.md) - 系统架构详解

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 AGPL-3.0 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- **公司**：北京星地探索科技有限公司
- **邮箱**：<EMAIL>
- **网站**：www.spacedefense.cn
- **问题反馈**：www.spacedefense.cn/issues

## 🙏 致谢

感谢以下开源项目的支持：
- [OOTK](https://github.com/thkruz/ootk) - 轨道工具包
- [WebGL](https://www.khronos.org/webgl/) - 图形渲染技术
- [Materialize](https://materializecss.com/) - UI框架
- [ECharts](https://echarts.apache.org/) - 图表库

---

**版本**：v1.0  
**最后更新**：2024年12月  
**维护者**：北京星地探索科技有限公司
