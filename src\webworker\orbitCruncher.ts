import { ObjD<PERSON><PERSON>son } from '@app/singletons/orbitManager';
import { DEG2RAD, Degrees, EciVec3, Kilometers, SatelliteRecord, Sgp4, eci2ecf } from 'ootk';
import { RADIUS_OF_EARTH } from '../lib/constants';
import { jday } from '../lib/transforms';
import { OrbitCruncherCachedObject } from './constants';
import { propTime, setupTimeVariables } from './positionCruncher/calculations';

let dynamicOffsetEpoch: number;
let staticOffset = 0;
let propRate = 1.0;

export enum OrbitCruncherType {
  INIT,
  UPDATE,
  CHANGE_ORBIT_TYPE,
  MISSILE_UPDATE,
  SATELLITE_UPDATE,
  SETTINGS_UPDATE,
}

export enum OrbitDrawTypes {
  ORBIT,
  TRAIL,
}

/** CONSTANTS */

const objCache = [] as OrbitCruncherCachedObject[];
let numberOfSegments: number;
let orbitType = OrbitDrawTypes.ORBIT;
let orbitFadeFactor = 1.0;
let numberOfOrbitsToDraw = 1;

// Handles Incomming Messages to sat-cruncher from main thread
try {
  onmessage = (m) => onmessageProcessing(m);
} catch (e) {
  // If Jest isn't running then throw the error
  if (!process) {
    throw e;
  }
}

export const onmessageProcessing = (m: {
  data: {
    typ: OrbitCruncherType;
    id: number;
    // Init Only
    objData?: string;
    numSegs: number;
    orbitFadeFactor?: number;
    numberOfOrbitsToDraw?: number;
    // Change Orbit Type Only
    orbitType?: OrbitDrawTypes.ORBIT;
    // Satellite Update Only
    tle1?: string;
    tle2?: string;
    // Missile Update Only
    latList: Degrees[];
    lonList: Degrees[];
    altList: Kilometers[];
    // Both Updates
    dynamicOffsetEpoch?: number;
    staticOffset?: number;
    propRate?: number;
    isEcfOutput?: boolean;
    orbitSegments?: number;
  };
}) => {
  switch (m.data.typ) {
    case OrbitCruncherType.INIT:
      orbitFadeFactor = m.data.orbitFadeFactor ?? 1.0;
      numberOfOrbitsToDraw = m.data.numberOfOrbitsToDraw ?? 1;
      numberOfSegments = m.data.numSegs;
      break;
    case OrbitCruncherType.SATELLITE_UPDATE:
      // If new orbit
      if (m.data.tle1) {
        objCache[m.data.id].satrec = Sgp4.createSatrec(m.data.tle1, m.data.tle2);
      }
      break;
    case OrbitCruncherType.MISSILE_UPDATE:
      // If new orbit
      if (m.data.latList) {
        objCache[m.data.id].latList = m.data.latList;
        objCache[m.data.id].lonList = m.data.lonList;
        objCache[m.data.id].altList = m.data.altList;
      }
      // Don't Add Anything Else
      break;
    case OrbitCruncherType.SETTINGS_UPDATE:
      numberOfOrbitsToDraw = m.data.numberOfOrbitsToDraw ?? numberOfOrbitsToDraw;
      break;
    case OrbitCruncherType.CHANGE_ORBIT_TYPE:
      orbitType = m.data.orbitType;

      return;
    default:
      return;
  }

  if (m.data.typ === OrbitCruncherType.INIT) {
    const objData = JSON.parse(m.data.objData) as ObjDataJson[];
    const sLen = objData.length - 1;
    let i = -1;

    while (i < sLen) {
      i++;
      if (objData[i].missile) {
        objCache[i] = objData[i];
      } else if (objData[i].ignore) {
        objCache[i] = { ignore: true };
      } else if (objData[i].tle1) {
        objCache[i] = {
          satrec: Sgp4.createSatrec(objData[i].tle1, objData[i].tle2),
        };
      } else {
        throw new Error('Invalid Object Data');
      }
    }
  }

  if (m.data.typ === OrbitCruncherType.SATELLITE_UPDATE || m.data.typ === OrbitCruncherType.MISSILE_UPDATE) {
    /*
     * TODO: figure out how to calculate the orbit points on constant
     * position slices, not timeslices (ugly perigees on HEOs)
     */

    dynamicOffsetEpoch = m.data.dynamicOffsetEpoch;
    staticOffset = m.data.staticOffset;
    propRate = m.data.propRate;

    const id = m.data.id;
    let isEcfOutput = m.data.isEcfOutput || false;
    // 使用动态轨道段数，如果没有传递则使用默认值
    const currentSegments = m.data.orbitSegments || numberOfSegments;
    const pointsOut = new Float32Array((currentSegments + 1) * 4);

    const len = currentSegments + 1;
    let i = 0;
    // Calculate Missile Orbits

    if (objCache[id].missile) {
      while (i < len) {
        const missile = objCache[id];

        if (missile.latList?.length === 0) {
          pointsOut[i * 4] = 0;
          pointsOut[i * 4 + 1] = 0;
          pointsOut[i * 4 + 2] = 0;
          pointsOut[i * 4 + 3] = 0;
          i++;
        } else {
          drawMissileSegment_(missile, i, pointsOut, len, currentSegments);
          i++;
        }
      }
    } else {
      // 使用与positionCruncher完全相同的时间计算逻辑
      const { j: nowJ } = setupTimeVariables(dynamicOffsetEpoch, staticOffset, propRate, false, []);
      const satrec = objCache[id].satrec as SatelliteRecord;
      const now = (nowJ - satrec.jdsatepoch) * 1440.0; // in minutes

      // Calculate Satellite Orbits
      const period = (2 * Math.PI) / satrec.no; // convert rads/min to min

      // 安全检查：确保周期和段数有效
      if (!isFinite(period) || period <= 0 || numberOfSegments <= 0) {
        // 如果周期无效，清空轨道数据
        for (let j = 0; j < len; j++) {
          pointsOut[j * 4] = 0;
          pointsOut[j * 4 + 1] = 0;
          pointsOut[j * 4 + 2] = 0;
          pointsOut[j * 4 + 3] = 0;
        }
        postMessageProcessing({ pointsOut, satId: id });
        return;
      }

      let timeslice = period / currentSegments;

      // ECF轨道显示逻辑 - 严格限制只有GEO轨道才显示ECF
      if (isEcfOutput) {
        // 🔥 调整后的GEO轨道检测条件
        const isGeoOrbit = period > 1420 && period < 1500 && // 轨道周期：1420-1500分钟
                          satrec.ecco < 0.1 && // 偏心率小于0.1
                          satrec.inclo < (20 * Math.PI / 180); // 倾角小于20度

        if (isGeoOrbit) {
          timeslice *= numberOfOrbitsToDraw;
        } else {
          // 对于非GEO轨道，禁用ECF输出
          isEcfOutput = false;
        }
      }

      if (orbitType === OrbitDrawTypes.ORBIT) {
        while (i < len) {
          drawOrbitSegment_(now, i, timeslice, id, isEcfOutput, pointsOut, len);
          i++;
        }
      } else if (orbitType === OrbitDrawTypes.TRAIL) {
        while (i < len) {
          drawOrbitSegmentTrail_(now, i, timeslice, id, isEcfOutput, pointsOut, len);
          i++;
        }
      }
    }

    postMessageProcessing({ pointsOut, satId: id, actuallyUsedEcf: isEcfOutput });
  }
};

interface OrbitCruncherMessageWorker {
  pointsOut: Float32Array;
  satId: number;
  actuallyUsedEcf?: boolean;
}
export const postMessageProcessing = ({ pointsOut, satId, actuallyUsedEcf }: OrbitCruncherMessageWorker) => {
  try {
    // TODO: Explore SharedArrayBuffer Options
    postMessage({
      pointsOut,
      satId,
      actuallyUsedEcf,
    } as OrbitCruncherMessageWorker);
  } catch (e) {
    // If Jest isn't running then throw the error
    if (!process) {
      throw e;
    }
  }
};

const drawMissileSegment_ = (missile: OrbitCruncherCachedObject, i: number, pointsOut: Float32Array, len: number, segments: number) => {
  const x = Math.round(missile.altList.length * (i / segments));

  const missileTime = propTime(dynamicOffsetEpoch, staticOffset, propRate);
  const j =
    jday(
      missileTime.getUTCFullYear(),
      missileTime.getUTCMonth() + 1, // Note, this function requires months in range 1-12.
      missileTime.getUTCDate(),
      missileTime.getUTCHours(),
      missileTime.getUTCMinutes(),
      missileTime.getUTCSeconds(),
    ) +
    missileTime.getUTCMilliseconds() * 1.15741e-8; // days per millisecond
  const gmst = Sgp4.gstime(j);

  const cosLat = Math.cos(missile.latList[x] * DEG2RAD);
  const sinLat = Math.sin(missile.latList[x] * DEG2RAD);
  const cosLon = Math.cos(missile.lonList[x] * DEG2RAD + gmst);
  const sinLon = Math.sin(missile.lonList[x] * DEG2RAD + gmst);

  pointsOut[i * 4] = (RADIUS_OF_EARTH + missile.altList[x]) * cosLat * cosLon;
  pointsOut[i * 4 + 1] = (RADIUS_OF_EARTH + missile.altList[x]) * cosLat * sinLon;
  pointsOut[i * 4 + 2] = (RADIUS_OF_EARTH + missile.altList[x]) * sinLat;
  pointsOut[i * 4 + 3] = Math.min(orbitFadeFactor * (len / (i + 1)), 1.0);
};

const drawOrbitSegmentTrail_ = (now: number, i: number, timeslice: number, id: number, isEcfOutput: boolean, pointsOut: Float32Array, len: number) => {
  // 轨道起点（i=0）对应当前时间，向未来绘制轨道
  const t = now + i * timeslice;
  const sv = Sgp4.propagate(objCache[id].satrec, t);

  if (!sv) {
    pointsOut[i * 4] = 0;
    pointsOut[i * 4 + 1] = 0;
    pointsOut[i * 4 + 2] = 0;
    pointsOut[i * 4 + 3] = 0;

    return;
  }

  let pos = sv.position as EciVec3;

  if (isEcfOutput) {
    // ECF坐标转换 - 每个轨道点使用对应时间的GMST
    const satrec = objCache[id].satrec as SatelliteRecord;
    const jd = satrec.jdsatepoch + (t / 1440.0); // t是相对于TLE历元的时间（分钟）
    const gmst = Sgp4.gstime(jd);
    pos = eci2ecf(pos, gmst);
  }

  // 检查位置是否有效，避免NaN或无穷大值
  if (isNaN(pos.x) || isNaN(pos.y) || isNaN(pos.z) || !isFinite(pos.x) || !isFinite(pos.y) || !isFinite(pos.z)) {
    pointsOut[i * 4] = 0;
    pointsOut[i * 4 + 1] = 0;
    pointsOut[i * 4 + 2] = 0;
    pointsOut[i * 4 + 3] = 0;
    return;
  }

  pointsOut[i * 4] = pos.x;
  pointsOut[i * 4 + 1] = pos.y;
  pointsOut[i * 4 + 2] = pos.z;
  pointsOut[i * 4 + 3] = i < len / 40 ? Math.min(orbitFadeFactor * (len / 40 / (2 * (i + 1))), 1.0) : 0.0;
};

const drawOrbitSegment_ = (now: number, i: number, timeslice: number, id: number, isEcfOutput: boolean, pointsOut: Float32Array, len: number) => {
  // 轨道计算：从当前时间开始绘制
  const t = now + i * timeslice;
  const sv = Sgp4.propagate(objCache[id].satrec, t);

  if (!sv) {
    pointsOut[i * 4] = 0;
    pointsOut[i * 4 + 1] = 0;
    pointsOut[i * 4 + 2] = 0;
    pointsOut[i * 4 + 3] = 0;

    return;
  }

  let pos = sv.position as EciVec3;

  if (isEcfOutput) {
    // ECF坐标转换 - 每个轨道点使用对应时间的GMST
    const satrec = objCache[id].satrec as SatelliteRecord;
    const jd = satrec.jdsatepoch + (t / 1440.0); // t是相对于TLE历元的时间（分钟）
    const gmst = Sgp4.gstime(jd);
    pos = eci2ecf(pos, gmst);
  }

  // 检查位置是否有效，避免NaN或无穷大值
  if (isNaN(pos.x) || isNaN(pos.y) || isNaN(pos.z) || !isFinite(pos.x) || !isFinite(pos.y) || !isFinite(pos.z)) {
    pointsOut[i * 4] = 0;
    pointsOut[i * 4 + 1] = 0;
    pointsOut[i * 4 + 2] = 0;
    pointsOut[i * 4 + 3] = 0;
    return;
  }

  pointsOut[i * 4] = pos.x;
  pointsOut[i * 4 + 1] = pos.y;
  pointsOut[i * 4 + 2] = pos.z;
  pointsOut[i * 4 + 3] = Math.min(orbitFadeFactor * (len / (i + 1)), 1.0);
};
