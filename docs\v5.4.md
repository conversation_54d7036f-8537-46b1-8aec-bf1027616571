# Keep Track 5.4 - Orion Overhaul

The name "Orion Overhaul" is a blend of "Orion", a prominent constellation in the night sky, and "Overhaul", suggesting significant changes or improvements. The name aims to highlight the significant enhancements and changes made in this version.

## Software Release Documentation

This software release offers enhanced satellite search capabilities, improved software quality through code refactoring and increased testing, as well as new visual and analysis tools.

## Major Features

### UI & Aesthetics

- New search filters and functionality added for satellite data.
- Improved visuals for orbit frames with the addition of RIC and ECF plots.
- Enhanced user feedback systems for several satellite tools.

### Functionality Enhancements

- Added ability to search for stars and use the "panToStar" function.
- Integrated satellite search on control site click.
- Introduced the scenario-creator scaffold.
- Enhanced breakup logic for more realistic simulations.
- Modified breakup options for realistic interpretations.

## Minor Features

### Bug Fixes

- Addressed various issues, including time not moving at the correct speed, ray casting on earth functionality, SOCRATES, country filters, and next pass time not updating.
- Fixed minor issues in menus such as STF, trend analysis, and lookangles.
- Addressed visual bugs, including missing top menu icons, image not found errors, and moon position discrepancies.

### Code & Infrastructure Updates

- Massive refactoring for improved code quality, including shifting from camelCase files to hyphenated naming for better git detection.
- Addressed several SonarQube findings for better code quality.
- Upgraded various dependencies and integrated new tools into the development pipeline, such as Cypress for testing and improved GitHub actions.
- Transitioned several parts of the codebase to TypeScript for stronger typing and better developer experience.

### Documentation

- Updated changelogs, readmes, and other documentation to reflect the changes in this release and improve developer contributions.
