import { ClickDragOptions } from '@app/plugins/KeepTrackPlugin';

// 使用全局拖动状态，避免循环依赖
declare global {
  interface Window {
    isDragging?: boolean;
  }
}

export const clickAndDragWidth = (el: HTMLElement | null, options: ClickDragOptions = {
  isDraggable: true,
}): HTMLDivElement | null => {
  if (!el) {
    return null;
  }

  // 🔥 获取缩放因子来调整窗口尺寸
  const scaleFactorStr = document.documentElement.style.getPropertyValue('--system-scale-factor') || '1';
  const scaleFactor = parseFloat(scaleFactorStr);

  const minWidth = Math.round((options.minWidth ?? 280) * scaleFactor);
  const maxWidth = Math.round((options.maxWidth ?? 450) * scaleFactor);

  let width = el.style.width ? parseInt(el.style.width) : el.clientWidth;

  width = width < minWidth ? minWidth : width;
  width = width > maxWidth ? maxWidth : width;
  el.style.width = `${width}px`;
  el.style.display = 'block';

  window.isDragging = false;

  if (options.isDraggable) {
    // create new element on right edge
    const edgeEl = createElWidth_(el);

    addEventsWidth_(edgeEl, el, width, minWidth, maxWidth, options);

    return edgeEl;
  }

  return null;
};

export const clickAndDragHeight = (el: HTMLElement, maxHeight?: number, callback?: () => void): void => {
  if (!el) {
    return;
  }

  window.isDragging = false;

  // create new element on right edge
  const edgeEl = createElHeight_(el);

  addEventsHeight_(edgeEl, el, callback, maxHeight);
};

const addEventsWidth_ = (edgeEl: HTMLDivElement, el: HTMLElement, width: number, minWidth: number, maxWidth: number, options: ClickDragOptions) => {
  const { attachedElement, leftOffset } = options;
  let startX: number;
  let startWidth: number;

  edgeEl.addEventListener('mousedown', (e: MouseEvent) => {
    Object.assign(edgeEl.style, {
      width: '100vw',
      height: '100vh',
      position: 'fixed',
    } as CSSStyleDeclaration);
    edgeEl.style.right = '';

    startX = e.clientX;
    startWidth = el.clientWidth;
    window.isDragging = true;
  });
  edgeEl.addEventListener('mouseup', () => {
    window.isDragging = false;
    Object.assign(edgeEl.style, {
      height: '100%',
      width: '8px',
      right: '0px',
      position: 'absolute',
    } as CSSStyleDeclaration);

    if (options.callback) {
      options.callback();
    }
  });
  edgeEl.addEventListener('mousemove', (e: MouseEvent) => {
    if (window.isDragging) {
      requestAnimationFrame(() => {
        width = startWidth + e.clientX - startX;
        width = width < minWidth ? minWidth : width;
        width = width > maxWidth ? maxWidth : width;
        el.style.width = `${width}px`;

        if (attachedElement && !leftOffset) {
          attachedElement.style.left = `${el.getBoundingClientRect().right}px`;
        }
      });
    }
  });
};

const createElWidth_ = (el: HTMLElement) => {
  const edgeEl = document.createElement('div');

  edgeEl.style.position = 'relative';
  edgeEl.style.height = '100%';
  edgeEl.style.width = '8px';
  edgeEl.style.right = '0px';
  edgeEl.style.cursor = 'w-resize';
  edgeEl.style.zIndex = '9999';
  edgeEl.style.marginLeft = 'auto';
  edgeEl.style.cursor = 'w-resize';

  el.appendChild(edgeEl);

  return edgeEl;
};

const addEventsHeight_ = (edgeEl: HTMLDivElement, el: HTMLElement, callback?: () => void, maxHeight?: number) => {
  let startY: number;
  let startHeight: number;
  let height: number;

  // 🔥 将mousemove和mouseup事件绑定到document，确保全局捕获
  const handleMouseMove = (e: MouseEvent) => {
    if (window.isDragging) {
      requestAnimationFrame(() => {
        height = startHeight - (e.clientY - startY);
        height = maxHeight ? Math.min(height, maxHeight) : height;
        height = height < 0 ? 0 : height;

        // 🔥 使用setProperty with important来强制覆盖所有CSS规则
        el.style.setProperty('height', `${height}px`, 'important');

        // 🔥 同时更新内部容器的高度，避免空隙
        if (el.id === 'nav-footer') {
          const bottomIconsContainer = el.querySelector('#bottom-icons-container') as HTMLElement;
          const bottomIcons = el.querySelector('#bottom-icons') as HTMLElement;

          if (bottomIconsContainer) {
            bottomIconsContainer.style.setProperty('height', `${height}px`, 'important');
          }
          if (bottomIcons) {
            bottomIcons.style.setProperty('max-height', `${height - 20}px`, 'important'); // 减去padding
          }
        }


      });
    }
  };

  const handleMouseUp = () => {
    if (window.isDragging) {
      window.isDragging = false;

      // 恢复拖动手柄原始样式
      Object.assign(edgeEl.style, {
        width: '100%',
        height: '4px',
        position: 'absolute',
      } as CSSStyleDeclaration);

      // 移除全局事件监听器
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      if (callback) {
        callback();
      }
    }
  };

  edgeEl.addEventListener('mousedown', (e: MouseEvent) => {
    // 只在拖动区域内才处理事件
    if (e.target === edgeEl && e.currentTarget === edgeEl) {
      // 阻止事件传播，但只针对拖动区域
      e.stopPropagation();
      e.preventDefault();

      // 🔥 不再修改拖动手柄的尺寸，保持原样
      startY = e.clientY;
      startHeight = el.clientHeight;
      window.isDragging = true;

      // 🔥 添加全局事件监听器
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
  });
  // 🔥 移除旧的事件监听器，现在使用全局事件监听器
};

const createElHeight_ = (el: HTMLElement) => {
  const edgeEl = document.createElement('div');

  edgeEl.style.position = 'absolute';
  edgeEl.style.width = '100%';
  edgeEl.style.height = '4px'; /* 增加高度便于拖动 */
  edgeEl.style.top = '0px';
  edgeEl.style.cursor = 'n-resize';
  edgeEl.style.zIndex = '1001'; /* 提高z-index确保可拖动 */
  edgeEl.style.marginBottom = 'auto';
  edgeEl.style.marginLeft = 'auto';
  edgeEl.style.marginRight = 'auto';
  edgeEl.style.background = 'transparent'; /* 透明背景，避免视觉干扰 */
  edgeEl.style.border = 'none'; /* 移除边框，避免粗阴影 */
  edgeEl.style.pointerEvents = 'auto'; /* 确保拖动区域可以接收鼠标事件 */

  // 添加类名以便识别
  edgeEl.className = 'drag-resize-handle';

  el.appendChild(edgeEl);

  return edgeEl;
};

// 全局事件监听器已完全移除，避免初始化问题
