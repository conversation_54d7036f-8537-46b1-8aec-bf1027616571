import { t7e } from '@app/locales/keys';
import { errorManagerInstance } from '@app/singletons/errorManager';

export interface SensorGroup {
  name: string;
  header: string;
  topLink: {
    name: string;
    badge: string;
  }
  list: string[];
}

export const fetchSensorGroups = async (): Promise<SensorGroup[]> => {
  let sensorGroupsApi = await fetch('https://api.spacedefense/v1/sensor-groups').then((response) => response.json());

  if (sensorGroupsApi.length === 0) {
    errorManagerInstance.warn(t7e('errorMsgs.sensorGroupsApiEmpty'));
    sensorGroupsApi = sensorGroups;
  }

  return sensorGroupsApi;
};

/**
 * @deprecated Migrate to fetchSensorGroups
 */
export const sensorGroups: SensorGroup[] = [
  {
    name: 'prc',
    header: '中国传感器',
    topLink: {
      name: '所有中国传感器',
      badge: 'CHINA',
    },
    list: ['SHD', 'HEI', 'ZHE', 'XIN', 'PMO'],
  },
  {
    name: 'ssn',
    header: '空间监视网络传感器',
    topLink: {
      name: '所有空间监视网络传感器',
      badge: 'COALITION',
    },
    list: [
      'EGLAFB', 'KWAJSPF', 'GEODDSDGC', 'GEODDSMAU', 'GEODDSSOC',
      'KWAJALT', 'KWAJMMW', 'KWAJALC', 'KWAJTDX', 'MITMIL', 'RAFASC',
      'GLBII', 'HOLCBAND', 'HOLSST',
    ],
  },
  {
    name: 'mw',
    header: '美国导弹预警传感器',
    topLink: {
      name: '所有导弹预警传感器',
      badge: 'NORAD',
    },
    list: ['BLEAFB', 'CODSFS', 'CAVSFS', 'CLRSFS', 'RAFFYL', 'PITSB'],
  },
  {
    name: 'md',
    header: '美国导弹防御局传感器',
    topLink: {
      name: '所有导弹防御传感器',
      badge: 'MDA',
    },
    list: ['COBRADANE', 'HARTPY', 'QTRTPY', 'KURTPY', 'SHATPY', 'KCSTPY', 'SBXRDR'],
  },
  {
    name: 'OWL-Net',
    header: 'OWL-Net传感器网络',
    topLink: {
      name: '所有OWL-Net传感器',
      badge: 'OWL-Net',
    },
    list: ['OWLKorea', 'OWLMongolia', 'OWLMorocco', 'OWLIsrael', 'OWLUSA'],
  },
  {
    name: 'leolabs',
    header: 'LeoLabs传感器',
    topLink: {
      name: '所有LeoLabs传感器',
      badge: 'LEOLABS',
    },
    list: ['LEOCRSR', 'LEOAZORES', 'LEOKSR', 'LEOPFISR', 'LEOMSR'],
  },
  {
    name: 'esoc',
    header: '欧洲空间操作中心传感器',
    topLink: {
      name: '所有ESOC传感器',
      badge: 'ESA',
    },
    list: [
      'GRV', 'TIR', 'GES', 'NRC', 'PDM', 'TRO', 'SDT', 'ZimLAT',
      'ZimSMART', 'Tromso', 'Kiruna', 'Sodankyla', 'Svalbard',
    ],
  },
  {
    name: 'rus',
    header: '俄罗斯传感器',
    topLink: {
      name: '所有俄罗斯传感器',
      badge: 'RUSSIA',
    },
    list: [
      'OLED', 'OLEV', 'PEC', 'MISD', 'MISV', 'LEKV', 'ARMV', 'KALV',
      'BARV', 'YENV', 'ORSV', 'STO', 'NAK',
    ],
  },

  {
    name: 'other',
    header: '其他传感器',
    topLink: {
      name: '其他传感器',
      badge: 'OTHER',
    },
    list: ['ROC', 'MLS', 'PO', 'LSO', 'MAY'],
  },
  /*
   * {
   *   name: 'us',
   *   title: 'United States',
   *   list: [
   *     'CODSFS', 'BLEAFB', 'CAVSFS', 'CLRSFS', 'EGLAFB', 'RAFFYL',
   *     'PITSB', 'MITMIL', 'KWAJALT', 'RAFASC', 'COBRADANE',
   *   ],
   * },
   */
];
