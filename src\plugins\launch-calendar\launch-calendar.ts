/**
 * /////////////////////////////////////////////////////////////////////////////
 *
 * launch-calendar.ts is a plugin for viewing the launch calendar on Gunter's Space Page.
 *
 * https://spacedefense
 *
 * @Copyright 北京星地探索科技有限公司
 *
 
 *
 * /////////////////////////////////////////////////////////////////////////////
 */

import { openColorbox } from '@app/lib/colorbox';
import { getEl } from '@app/lib/get-el';

import { KeepTrackPlugin } from '../KeepTrackPlugin';

import { MenuMode } from '@app/interfaces';
import calendarPng from '@public/img/icons/calendar.png';

export class LaunchCalendar extends KeepTrackPlugin {
  readonly id = 'LaunchCalendar';
  dependencies_ = [];
  bottomIconImg = calendarPng;
  isForceHideSideMenus = true;

  menuMode: MenuMode[] = [MenuMode.ADVANCED, MenuMode.ALL];

  bottomIconCallback = () => {
    if (this.isMenuButtonActive) {
      settingsManager.isPreventColorboxClose = true;
      setTimeout(() => {
        settingsManager.isPreventColorboxClose = false;
      }, 2000);
      const year = new Date().getFullYear();

      openColorbox(`https://space.skyrocket.de/doc_chr/lau${year}.htm`, {
        callback: this.closeColorbox_.bind(this),
      });
    }
  };

  private closeColorbox_() {
    if (this.isMenuButtonActive) {
      this.isMenuButtonActive = false;
      getEl(this.bottomIconElementName)?.classList.remove('bmenu-item-selected');
    }
  }
}

