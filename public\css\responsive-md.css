@media (min-width: 768px) {
  #sat-infobox {
    width: calc(50% / var(--system-scale-factor, 1)) !important;
    max-width: calc(500px / var(--system-scale-factor, 1)) !important;
    /* 🔥 移除强制位置设置，让Draggabilly控制位置 */
    border: calc(5px / var(--system-scale-factor, 1)) solid var(--color-dark-border);
  }

  #copyright-notice {
    font-size: calc(12px / var(--system-scale-factor, 1));
  }

  .search-slide-down {
    width: calc(250px / var(--system-scale-factor, 1));
  }

  #ui-datepicker-div {
    top: var(--top-menu-height) !important;
    border: none !important;
    left: 0px !important;
    border-radius: 0px;
    bottom: unset;
    width: fit-content;
    font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  }

  .ui-timepicker-div dl {
    text-align: center !important;
  }

  .ui-datepicker td a {
    padding: calc(0.2em / var(--system-scale-factor, 1)) !important;
    font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  }

  .ui-datepicker .ui-datepicker-buttonpane button {
    padding: calc(0.2em / var(--system-scale-factor, 1)) calc(0.6em / var(--system-scale-factor, 1)) calc(0.3em / var(--system-scale-factor, 1)) calc(0.6em / var(--system-scale-factor, 1)) !important;
    font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  }
}