// 简化版管理后台脚本，用于调试
console.log('Admin script loaded');

class SimpleAdminManager {
    constructor() {
        console.log('SimpleAdminManager constructor called');
        this.apiBaseUrl = this.getApiBaseUrl();
        this.token = localStorage.getItem('authToken');
        console.log('Token:', this.token ? 'Found' : 'Not found');
        this.init();
    }

    getApiBaseUrl() {
        const currentHost = window.location.hostname;
        const protocol = window.location.protocol;
        const port = window.location.hostname === 'localhost' ? '5001' : (protocol === 'https:' ? '443' : '5001');
        const url = `${protocol}//${currentHost}:${port}/api/auth`;
        console.log('API Base URL:', url);
        return url;
    }

    async init() {
        console.log('Initializing admin manager...');
        
        if (!this.token) {
            console.log('No token found, redirecting to login');
            alert('未找到认证令牌，将跳转到登录页面');
            window.location.href = '/login.html';
            return;
        }

        try {
            console.log('Verifying admin privileges...');
            const isAdmin = await this.verifyAdmin();
            
            if (!isAdmin) {
                console.log('Not admin, redirecting to login');
                alert('需要管理员权限');
                window.location.href = '/login.html';
                return;
            }

            console.log('Admin verified, loading data...');
            this.showLoading(true);
            await this.loadData();
            this.showLoading(false);
            console.log('Data loaded successfully');
            
        } catch (error) {
            console.error('Initialization error:', error);
            alert('初始化失败：' + error.message);
        }
    }

    async verifyAdmin() {
        try {
            console.log('Sending verify request...');
            const response = await fetch(`${this.apiBaseUrl}/verify`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ token: this.token })
            });

            console.log('Verify response status:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('Verify response data:', data);
                
                if (data.user && data.user.role === 'admin') {
                    const userElement = document.getElementById('currentUser');
                    if (userElement) {
                        userElement.textContent = data.user.username;
                    }
                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error('Token verification failed:', error);
            return false;
        }
    }

    async loadData() {
        console.log('Loading admin data...');
        try {
            await Promise.all([
                this.loadUsers(),
                this.loadStats()
            ]);
        } catch (error) {
            console.error('Error loading data:', error);
            this.showError('加载数据失败：' + error.message);
        }
    }

    async loadUsers() {
        console.log('Loading users...');
        try {
            const response = await fetch(`${this.apiBaseUrl}/users`, {
                headers: { 'Authorization': `Bearer ${this.token}` }
            });

            console.log('Users response status:', response.status);

            if (response.ok) {
                const users = await response.json();
                console.log('Users loaded:', users.length);
                this.displayUsers(users);
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('Failed to load users:', error);
            this.showError('加载用户数据失败');
        }
    }

    displayUsers(users) {
        const tableBody = document.getElementById('users-table');
        if (!tableBody) {
            console.error('Users table not found');
            return;
        }

        tableBody.innerHTML = '';
        
        users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.id.substring(0, 8)}...</td>
                <td>${user.username}</td>
                <td>${user.email || '-'}</td>
                <td><span class="badge ${user.role}">${user.role === 'admin' ? '管理员' : '普通用户'}</span></td>
                <td><span class="badge ${user.isActive ? 'active' : 'inactive'}">${user.isActive ? '活跃' : '禁用'}</span></td>
                <td>${new Date(user.createdAt).toLocaleString()}</td>
                <td>${user.lastLogin ? new Date(user.lastLogin).toLocaleString() : '-'}</td>
                <td>
                    <button class="btn ${user.isActive ? 'danger' : 'success'}" onclick="toggleUser('${user.id}')">
                        ${user.isActive ? '禁用' : '启用'}
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });
    }

    async loadStats() {
        console.log('Loading stats...');
        try {
            const response = await fetch(`${this.apiBaseUrl}/users`, {
                headers: { 'Authorization': `Bearer ${this.token}` }
            });

            if (response.ok) {
                const users = await response.json();
                const activeUsers = users.filter(u => u.isActive).length;
                
                document.getElementById('totalUsers').textContent = users.length;
                document.getElementById('activeUsers').textContent = activeUsers;
                document.getElementById('pendingRegistrations').textContent = '0';
                document.getElementById('todayLogins').textContent = '0';
                
                console.log('Stats updated');
            }
        } catch (error) {
            console.error('Failed to load stats:', error);
        }
    }

    showError(message) {
        console.error('Error:', message);
        const errorDiv = document.getElementById('errorMessage');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
    }

    showSuccess(message) {
        console.log('Success:', message);
        const successDiv = document.getElementById('successMessage');
        if (successDiv) {
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 5000);
        }
    }

    showLoading(show) {
        const loadingDiv = document.getElementById('mainLoading');
        if (loadingDiv) {
            loadingDiv.style.display = show ? 'block' : 'none';
        }
    }
}

// 全局函数
function switchTab(tabName) {
    console.log('Switching to tab:', tabName);
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => tab.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));
    
    const tabIndex = tabName === 'users' ? 0 : tabName === 'registrations' ? 1 : 2;
    if (tabs[tabIndex]) tabs[tabIndex].classList.add('active');
    
    const tabContent = document.getElementById(`${tabName}-tab`);
    if (tabContent) tabContent.classList.add('active');
}

function filterUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const rows = document.querySelectorAll('#users-table tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
}

function logout() {
    console.log('Logging out...');
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    window.location.href = '/login.html';
}

function toggleUser(userId) {
    console.log('Toggle user:', userId);
    alert('用户切换功能暂未实现');
}

// 初始化
let adminManager;
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing admin manager...');
    adminManager = new SimpleAdminManager();
});

console.log('Admin script setup complete');
