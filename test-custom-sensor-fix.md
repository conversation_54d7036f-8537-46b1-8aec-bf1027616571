# 自定义传感器修复测试

## 修复的问题

### 1. 只显示1个传感器的问题
**原因**: `updateCustomSensorListDom()` 只获取primary sensor，而不是所有自定义传感器
**修复**: 使用 `getAllActiveSensors().filter()` 获取所有自定义传感器

### 2. 点击自定义传感器出错的问题  
**原因**: 点击处理中对所有传感器都调用 `clearSecondarySensors()`，但自定义传感器可能在secondary sensors中
**修复**: 对自定义传感器不调用 `clearSecondarySensors()`

### 3. 恢复传感器时清除所有secondary sensors的问题
**原因**: `restoreCustomSensors_()` 调用 `clearSecondarySensors()` 清除所有secondary sensors
**修复**: 只删除自定义传感器，保留其他secondary sensors

## 测试步骤

1. 添加2个自定义传感器
2. 检查传感器列表是否显示2个传感器
3. 点击自定义传感器，检查是否正常工作
4. 保存传感器列表
5. 重新加载页面，检查是否自动恢复2个传感器
6. 测试导出功能

## 预期结果

- 传感器列表正确显示所有自定义传感器
- 点击自定义传感器不会出错
- 导出功能正常工作
- 自动加载功能正常工作
