{"time": {"days": {"0": "일요일", "1": "월요일", "2": "화요일", "3": "수요일", "4": "목요일", "5": "금요일", "6": "토요일"}, "days-short": {"0": "일", "1": "월", "2": "화", "3": "수", "4": "목", "5": "금", "6": "토"}, "months": {"1": "1월", "2": "2월", "3": "3월", "4": "4월", "5": "5월", "6": "6월", "7": "7월", "8": "8월", "9": "9월", "10": "10월", "11": "11월", "12": "12월"}, "calendar": {"time": "시간", "hour": "시", "minute": "분", "second": "초", "now": "지금", "done": "완료", "pause": "일시정지", "propagation": "전파"}}, "errorMsgs": {"catalogNotFullyInitialized": "카탈로그가 아직 완전히 초기화되지 않았습니다. 몇 초 후에 다시 시도해 주세요.", "sensorGroupsApiEmpty": "API에서 센서 그룹을 찾을 수 없습니다. 기본 센서 그룹 데이터베이스로 되돌립니다.", "SelectSensorFirst": "먼저 센서를 선택하세요!", "SelectSatelliteFirst": "먼저 위성을 선택하세요!", "SelectSecondarySatellite": "먼저 보조 위성을 선택하세요!", "SatelliteNotDetailedSatellite": "위성이 DetailedSatellite가 아닙니다!", "SensorNotFound": "센서를 찾을 수 없습니다!", "Scene": {"disablingGodrays": "컴퓨터 성능이 부족합니다! 광선 효과를 비활성화합니다.", "disablingAurora": "컴퓨터 성능이 부족합니다! 오로라를 비활성화합니다.", "disablingAtmosphere": "컴퓨터 성능이 부족합니다! 대기를 비활성화합니다.", "disablingMoon": "컴퓨터 성능이 부족합니다! 달을 비활성화합니다.", "disablingMilkyWay": "컴퓨터 성능이 부족합니다! 은하수를 비활성화합니다.", "disablingSun": "컴퓨터 성능이 부족합니다! 태양을 비활성화합니다."}, "Breakup": {"SatelliteNotFound": "위성을 찾을 수 없습니다!", "CannotCreateBreakupForNonCircularOrbits": "비원형 궤도에 대한 파편화를 생성할 수 없습니다. 수정 중입니다.", "CannotCalcDirectionOfSatellite": "위성의 방향을 계산할 수 없습니다. 나중에 다시 시도하세요.", "ErrorCreatingBreakup": "파편화 생성 오류!", "InvalidStartNum": "잘못된 위성 시작 번호입니다! 기본값 90000으로 설정합니다!", "BreakupGeneratorFailed": "파편화 생성기 실패!"}, "Collisions": {"noCollisionsData": "충돌 데이터를 찾을 수 없습니다!", "errorProcessingCollisions": "SOCRATES 데이터 처리 중 오류 발생!"}, "EditSat": {"errorReadingFile": "파일 읽기 중 오류 발생!", "satelliteNotFound": "위성 {{sccNum}}을(를) 찾을 수 없습니다!"}, "CreateSat": {"errorCreatingSat": "위성 생성 중 오류 발생!"}, "Reports": {"popupBlocker": "보고서를 보려면 이 사이트의 팝업을 허용해 주세요."}, "SensorManager": {"errorUpdatingUi": "센서 UI 스타일 업데이트 중 오류 발생."}}, "hoverManager": {"launched": "발사됨", "launchedPlanned": "발사 예정", "launchedUnknown": "발사: 알 수 없음"}, "loadingScreenMsgs": {"math": "수학 시도 중...", "science": "과학 찾는 중...", "science2": "과학 발견...", "dots": "우주에 점 그리는 중...", "satIntel": "위성 정보 통합 중...", "painting": "지구 그리는 중...", "coloring": "선 안에 색칠하는 중...", "elsets": "GP 찾는 중...", "models": "3D 모델 구축 중...", "cunningPlan": "교활한 계획 세우는 중...", "copyrightNotice": "KeepTrack™ 및 spacedefense™는 Kruczek Labs LLC의 상표입니다.<br>이 인스턴스는 GNU AGPL v3.0에 따라 라이선스가 부여되었습니다. 저작자 표시, 소스 접근 및 이 공지는 반드시 표시되어야 합니다.<br>상업적 라이선스가 부여되지 않았으며, 권리자에게 보상이 제공되지 않았습니다.<br>무단 사용, 리브랜딩 또는 저작자 표시 제거는 상표 및 오픈 소스 라이선스 조건을 위반할 수 있습니다.<br>© 2025 Kruczek Labs LLC. 모든 권리 보유. 전체 조건은 LICENSE를 참조하세요.", "copyrightNoticeMobile": "KeepTrack™ 및 spacedefense™는 Kruczek Labs LLC의 상표입니다.<br>이 인스턴스는 GNU AGPL v3.0에 따라 라이선스가 부여되었습니다. 저작자 표시, 소스 접근 및 이 공지는 반드시 표시되어야 합니다. 상업적 라이선스가 부여되지 않았으며, 권리자에게 보상이 제공되지 않았습니다. 무단 사용, 리브랜딩 또는 저작자 표시 제거는 상표 및 오픈 소스 라이선스 조건을 위반할 수 있습니다.<br>© 2025 Kruczek Labs LLC. 모든 권리 보유. 전체 조건은 LICENSE를 참조하세요."}, "splashScreens": {"1": "위성 모델은 실제보다 크게 표시됩니다. 다른 모든 것은 실제 크기입니다.", "2": "'L' 키를 눌러 위성 궤도를 켜고/끌 수 있습니다.", "3": "'P' 키를 눌러 현재 위성의 극좌표 플롯을 열 수 있습니다 (먼저 센서를 선택해야 합니다!).", "4": "'T' 키를 눌러 시뮬레이션 시간을 재설정합니다.", "5": "'>' 또는 '=' 키를 눌러 시뮬레이션 시간을 앞으로 이동합니다.", "6": "'<' 또는 ')' 키를 눌러 시뮬레이션 시간을 뒤로 이동합니다.", "7": "'/' 키를 눌러 시뮬레이션 속도를 1x와 0x 사이에서 전환합니다.", "8": "'V' 키를 눌러 보기 모드를 변경합니다.", "9": "Shift+F1을 눌러 언제든지 도움말 메뉴를 엽니다.", "10": "'R' 키를 눌러 자동 회전을 활성화합니다.", "11": "위성이 선택된 상태에서 Shift + C를 누르면 가시성 원뿔을 켜고/끌 수 있습니다.", "12": "'Home' 키는 카메라를 현재 센서로 회전시킵니다.", "13": "'`' (틸드) 키는 카메라를 기본 보기로 재설정합니다.", "14": "'M' 키는 현재 위성의 2D 지도 보기를 표시합니다.", "15": "하단 도구 모음에 있는 설정 메뉴에는 경험을 사용자 지정하는 많은 옵션이 있습니다.", "16": "많은 메뉴에는 기어 아이콘을 클릭하여 접근할 수 있는 추가 설정이 있습니다.", "17": "관심 목록에 위성을 추가하여 현재 센서 위에 있을 때 알림을 받을 수 있습니다.", "18": "지구본을 오른쪽 클릭하여 더 많은 옵션이 있는 컨텍스트 메뉴를 엽니다.", "19": "'+' 또는 '-' 키를 눌러 확대/축소합니다.", "20": "'F11'을 눌러 전체 화면 모드를 켜고/끕니다.", "21": "오른쪽 상단의 검색 창에서 이름이나 NORAD ID로 위성을 검색할 수 있습니다.", "22": "위성을 선택하고 하단 메뉴에서 '새 발사' 버튼을 클릭하여 새 발사 명목을 생성할 수 있습니다.", "23": "'N' 키를 눌러 밤을 켜고/끕니다.", "24": "'I' 키를 눌러 위성에 대한 문맥 정보를 숨기거나/표시합니다.", "25": "'B' 키를 눌러 메뉴를 숨기거나/표시합니다.", "26": "Shift + ';' 키를 눌러 시뮬레이션 속도를 높입니다.", "27": "',' 키를 눌러 시뮬레이션 속도를 낮춥니다.", "28": "객체를 보조로 설정하여 주 객체와의 상대적 거리를 확인합니다.", "29": "'[' 키로 객체를 주/보조 사이에서 전환합니다."}, "plugins": {"SensorListPlugin": {"bottomIconLabel": "센서", "title": "센서 목록 메뉴", "helpBody": "센서 메뉴를 사용하면 계산 및 기타 메뉴 기능에 사용할 센서를 선택할 수 있습니다. 센서는 주로 지원하는 네트워크를 기반으로 그룹화됩니다. 메뉴 왼쪽에는 센서 이름이, 오른쪽에는 소유 국가/조직이 표시됩니다. <br><br> '모든...센서' 옵션을 선택하면 해당 그룹의 모든 센서가 선택됩니다. 이는 네트워크 커버리지를 시각화하는 데 유용하지만, 현재 모든 계산에 작동하지 않습니다. 네트워크에 대한 관측각을 계산하려면 다중 사이트 관측각 도구를 사용하거나 네트워크에 있는 각 센서에 대한 관측각을 사용하는 것이 좋습니다. <br><br> 이 목록의 센서에는 기계식 및 위상 배열 레이더, 광학 센서가 포함됩니다: <ul style='margin-left: 40px;'> <li> 위상 배열 레이더는 일반적으로 저궤도(LEO)로 제한됩니다. </li> <li> 기계식 레이더는 저궤도와 정지궤도(GEO) 모두에 사용할 수 있습니다. </li> <li> 광학 센서는 일반적으로 정지궤도에 사용되지만 저궤도에도 사용할 수 있습니다. </li> <li> 광학 센서는 맑은 밤에만 관측이 가능하지만, 레이더는 주야간 모두 사용할 수 있습니다. </li> </ul> <br> 센서 정보는 공개적으로 사용 가능한 데이터를 기반으로 하며 센서 정보 메뉴에서 확인할 수 있습니다. 추가 센서에 대한 공개 데이터나 기존 센서 정보에 대한 수정 사항이 있으면 <a href='mailto@spacedefense'>admin@spacedefense</a>로 연락해 주세요."}, "SensorInfoPlugin": {"bottomIconLabel": "센서 정보", "title": "센서 정보 메뉴", "helpBody": "센서 정보는 현재 선택된 센서에 대한 정보를 제공합니다. 이 정보는 공개적으로 사용 가능한 데이터를 기반으로 하며 항상 100% 정확하지 않을 수 있습니다. 추가 센서에 대한 공개 데이터나 기존 센서 정보에 대한 수정 사항이 있으면 <a href='mailto@spacedefense'>admin@spacedefense</a>로 연락해 주세요. <br><br> 제공되는 정보는 다음과 같습니다: <ul style='margin-left: 40px;'> <li> 센서 이름 </li> <li> 센서 소유자 </li> <li> 센서 유형 </li> <li> 센서 시야각 </li> </ul> <br> 또한, 이 메뉴에서 센서에서 태양이나 달까지의 선을 빠르게 생성할 수 있습니다."}, "CustomSensorPlugin": {"bottomIconLabel": "커스텀 센서", "title": "커스텀 센서 메뉴", "helpBody": "이 메뉴를 사용하면 계산 및 기타 메뉴 기능에 사용할 커스텀 센서를 생성할 수 있습니다. 완전히 새로운 센서를 만들거나 기존 센서를 수정할 수 있습니다. <br><br> 센서의 위도, 경도 및 고도를 설정한 후 센서의 시야각을 설정할 수 있습니다. 망원경을 선택하면 360도 시야각, 10도 고도 마스크 및 무제한 범위로 생성됩니다. 망원경 옵션을 선택 해제하면 시야각을 수동으로 설정할 수 있습니다. <br><br> 기존 센서를 편집하려면 먼저 센서 목록에서 선택하면 커스텀 센서가 선택한 센서의 정보로 업데이트됩니다."}, "LookAnglesPlugin": {"bottomIconLabel": "관측각", "title": "관측각 메뉴", "helpBody": "관측각 메뉴를 사용하면 센서와 위성 사이의 거리, 방위각 및 고도각을 계산할 수 있습니다. 메뉴를 사용하기 전에 먼저 위성과 센서를 선택해야 합니다. <br><br> '상승 및 하강 시간만 전환'은 위성의 상승 및 하강 시간만 계산합니다. 이는 위성이 센서에 언제 보일지 빠르게 결정하는 데 유용합니다. <br><br> 검색 범위는 길이 및 간격 옵션을 변경하여 수정할 수 있습니다."}, "MultiSiteLookAnglesPlugin": {"bottomIconLabel": "다중 사이트 관측각", "title": "다중 사이트 관측각 메뉴", "helpBody": "다중 사이트 관측각 메뉴를 사용하면 위성과 여러 센서 사이의 거리, 방위각 및 고도각을 계산할 수 있습니다. 메뉴를 사용하기 전에 먼저 위성을 선택해야 합니다. <br><br> 기본적으로 메뉴는 우주 감시 네트워크의 모든 센서에 대한 관측각을 계산합니다. 추가 센서에 대한 관측각을 계산하려면 메뉴 하단에서 csv 파일을 내보낼 수 있습니다. csv 파일에는 모든 센서에 대한 관측각이 포함됩니다. <br><br> 표에서 행을 클릭하면 센서가 선택되고 시뮬레이션 시간이 관측각 시간으로 변경됩니다."}, "SensorTimeline": {"bottomIconLabel": "센서 타임라인", "title": "센서 타임라인 메뉴", "helpBody": "센서 타임라인 메뉴는 센서 목록이 하나의 위성을 볼 수 있는 시간을 보여줍니다. 타임라인은 패스의 품질을 나타내기 위해 색상으로 구분됩니다. 빨간색은 나쁜 패스, 노란색은 평균 패스, 녹색은 좋은 패스를 나타냅니다. 패스를 클릭하면 센서와 시간이 해당 패스로 변경됩니다. <br><br> 타임라인은 시뮬레이션의 시작 및 종료 시간을 변경하여 수정할 수 있습니다. 또한 길이 및 간격 옵션을 변경하여 타임라인을 수정할 수 있습니다."}, "ProximityOps": {"bottomIconLabel": "란데부 및 근접 조작", "title": "란데부 및 근접 조작(RPO)", "titleSecondary": "란데부 및 근접 조작 목록", "helpBody": "위성 간의 다가오는 접근을 찾습니다.", "noradId": "NORAD ID", "maxDistThreshold": "최대 거리 임계값 (km)", "maxRelativeVelocity": "최대 상대 속도 (km/s)", "searchDuration": "검색 기간 (시간)", "geoText": "정지궤도", "leoText": "저지구궤도", "orbitType": "궤도 유형", "geoAllVsAll": "정지궤도 전체 대 전체", "geoAllVsAllTooltip": "모든 정지궤도 위성 간의 RPO를 검색합니다.", "comparePayloadsOnly": "페이로드만 비교", "comparePayloadsOnlyTooltip": "페이로드 간의 RPO만 검색합니다.", "ignoreVimpelRso": "Vimpel RSO 무시", "ignoreVimpelRsoTooltip": "Vimpel 카탈로그에서 가져온 RSO를 무시합니다."}, "SatelliteTimeline": {"bottomIconLabel": "위성 타임라인", "title": "위성 타임라인 메뉴", "helpBody": "위성 타임라인 메뉴는 단일 센서가 위성 목록을 볼 수 있는 시간을 보여줍니다. 타임라인은 패스의 품질을 나타내기 위해 색상으로 구분됩니다. 빨간색은 나쁜 패스, 노란색은 평균 패스, 녹색은 좋은 패스를 나타냅니다. 패스를 클릭하면 위성과 시간이 해당 패스로 변경됩니다. <br><br> 타임라인은 시뮬레이션의 시작 및 종료 시간을 변경하여 수정할 수 있습니다. 또한 길이 및 간격 옵션을 변경하여 타임라인을 수정할 수 있습니다."}, "WatchlistPlugin": {"bottomIconLabel": "관심 목록", "title": "관심 목록 메뉴", "helpBody": "관심 목록 메뉴를 사용하면 추적할 우선 순위 위성 목록을 만들 수 있습니다. 이를 통해 가장 관심 있는 위성을 빠르게 검색할 수 있습니다. 이 목록은 브라우저의 로컬 스토리지에 저장되며 다음에 사이트를 방문할 때도 사용할 수 있습니다. <br><br> 관심 목록의 위성이 선택된 센서의 시야에 들어오면 알림이 표시되고, 센서에서 위성까지 선이 그려지며, 위성 번호가 지구본에 표시됩니다. <br><br> 오버레이 기능은 관심 목록이 채워져 있어야 작동합니다."}, "WatchlistOverlay": {"bottomIconLabel": "오버레이", "title": "오버레이 메뉴", "helpBody": "<p> 관심 목록 오버레이는 관심 목록에 있는 각 위성의 다음 패스 시간을 보여줍니다. 오버레이는 10초마다 업데이트됩니다. </p> <p> 오버레이는 다음 패스 시간을 나타내기 위해 색상 코드로 구분됩니다. 색상은 다음과 같습니다: </p> <ul> <li>노란색 - 보이는 중</li> <li>파란색 - 다음 패스 시간이 현재 시간 후 최대 30분 또는 현재 시간 전 10분</li> <li>흰색 - 위의 요구 사항에 맞지 않는 모든 미래 패스</li> </ul> <p> 오버레이에서 위성을 클릭하면 지도가 해당 위성을 중심으로 이동합니다. </p>"}, "ReportsPlugin": {"bottomIconLabel": "보고서", "title": "보고서 메뉴", "helpBody": "보고서 메뉴는 보고 있는 데이터를 분석하고 이해하는 데 도움이 되는 도구 모음입니다."}, "PolarPlotPlugin": {"bottomIconLabel": "극좌표 플롯", "title": "극좌표 플롯 메뉴", "helpBody": "극좌표 플롯 메뉴는 시간에 따른 위성의 방위각과 고도각의 2D 극좌표 플롯을 생성하는 데 사용됩니다."}, "NextLaunchesPlugin": {"bottomIconLabel": "다음 발사", "title": "다음 발사 메뉴", "helpBody": "다음 발사 메뉴는 <a href='https://thespacedevs.com/' target='_blank'>The Space Devs</a>에서 데이터를 가져와 예정된 발사를 표시합니다."}, "FindSatPlugin": {"bottomIconLabel": "위성 찾기", "title": "위성 찾기 메뉴", "helpBody": "위성 찾기 메뉴는 궤도 매개변수 또는 위성 특성으로 위성을 찾는 데 사용됩니다. <br><br> 대부분의 매개변수의 경우 왼쪽에 목표 값을 입력하고 오른쪽에 오차 범위를 입력합니다. 예를 들어, 51-52도 경사각의 모든 위성을 찾으려면 왼쪽 상자에 51.5를 입력하고 오른쪽 상자에 0.5를 입력할 수 있습니다. 그러면 검색은 해당 경사각 내의 모든 위성을 찾아 검색 창에 표시합니다."}, "ShortTermFences": {"bottomIconLabel": "단기 펜스", "title": "단기 펜스(STF) 메뉴", "helpBody": "단기 펜스(STF) 메뉴는 센서 검색 상자를 시각화하는 데 사용됩니다. <br><br> 검색 상자 기능이 있는 센서를 소유/운영하지 않는 한 이 기능은 크게 도움이 되지 않을 수 있습니다."}, "Collisions": {"bottomIconLabel": "충돌", "title": "충돌 메뉴", "helpBody": "충돌 메뉴는 충돌 가능성이 높은 위성을 보여줍니다. <br><br> 행을 클릭하면 충돌과 관련된 두 위성이 선택되고 시간이 충돌 시간으로 변경됩니다."}, "TrackingImpactPredict": {"bottomIconLabel": "재진입 예측", "title": "추적 및 충격 예측 메뉴", "helpBody": "추적 및 충격 예측(TIP) 메뉴는 위성에 대한 최신 추적 및 충격 예측 메시지를 표시합니다. 표에는 다음 열이 표시됩니다:<br><br> <b>NORAD</b>: 위성의 NORAD 카탈로그 ID.<br><br> <b>Decay Date</b>: 위성의 예측 소멸 날짜.<br><br> <b>Latitude</b>: 소멸 시 위성의 위도.<br><br> <b>Longitude</b>: 소멸 시 위성의 경도.<br><br> <b>Window (min)</b>: 예측의 시간 창(분).<br><br> <b>Next Report (hrs)</b>: 다음 보고서까지의 시간(시간).<br><br> <b>Reentry Angle (deg)</b>: 위성의 재진입 각도(도).<br><br> <b>RCS (m²)</b>: 위성의 레이더 단면적(제곱미터).<br><br> <b>GP Age (hrs)</b>: 최신 요소 세트의 나이(시간).<br><br> <b>Dry Mass (kg)</b>: 위성의 건조 질량(킬로그램).<br><br> <b>Volume (m³)</b>: 위성의 추정 부피(입방미터).<br><br>"}, "Breakup": {"bottomIconLabel": "파편화 생성", "title": "파편화 메뉴", "helpBody": "파편화 메뉴는 위성의 파편화를 시뮬레이션하는 도구입니다. <br><br> 위성의 궤도를 복제하고 수정하여 위성의 파편화를 모델링할 수 있습니다. 위성을 선택하고 메뉴를 연 후 사용자는 다음을 선택할 수 있습니다: <ul style='margin-left: 40px;'> <li>경사각 변화</li> <li>RAAN 변화</li> <li>주기 변화</li> <li>파편화 조각 수</li> </ul> 변화가 클수록 시뮬레이션된 파편화의 분산이 커집니다. 기본 변화는 합리적인 분산으로 파편화를 시뮬레이션하기에 충분합니다."}, "DebrisScreening": {"bottomIconLabel": "파편 스크리닝", "title": "파편 스크리닝 메뉴", "helpBody": "파편 스크리닝 메뉴는 위성에서 볼 수 있는 잠재적 파편 물체 목록을 생성하는 데 사용됩니다. 이 목록은 파편 물체의 궤도 매개변수를 계산하고 위성의 궤도 매개변수와 비교하여 생성됩니다. 사용자는 TLE 또는 SGP4 전파기를 사용하여 목록을 생성할 수 있습니다. 사용자는 또한 파편 물체의 크기와 밝기에 따라 목록을 필터링할 수 있습니다. 사용자는 또한 TLE 또는 SGP4 전파기를 사용하여 목록을 생성할 수 있습니다. 사용자는 또한 파편 물체의 크기와 밝기에 따라 목록을 필터링할 수 있습니다."}, "TransponderChannelData": {"bottomIconLabel": "트랜스폰더 및 채널 정보", "title": "위성 트랜스폰더 및 채널 정보", "helpBody": "<p>이 표는 TV, 라디오 및 기타 통신 서비스를 포함한 위성 채널의 기술 세부 정보를 포함합니다:</p><ul><li><strong>위성:</strong> 채널을 방송하는 위성의 이름</li><li><strong>TV채널:</strong> TV/라디오 채널 또는 통신 서비스의 이름</li><li><strong>빔:</strong> 위성 빔 방향 (예: 서반구)</li><li><strong>주파수:</strong> 트랜스폰더 주파수 및 편파 (예: 3840 V)</li><li><strong>시스템:</strong> 방송 시스템 (예: DVB-S2 8PSK)</li><li><strong>SRFEC:</strong> 심볼 속도 및 FEC 비율 (예: 30000 3/4)</li><li><strong>비디오:</strong> 비디오/데이터 압축 형식 (예: MPEG-4/HD)</li><li><strong>언어:</strong> 사용 가능한 오디오/통신 언어 (예: 영어, 중국어)</li><li><strong>암호화:</strong> 사용된 암호화 시스템 (예: PowerVu)</li></ul><p>이 정보는 위성 통신 전문가, 기술자, 애호가 및 위성 수신 장비를 설정하거나 문제를 해결하는 모든 사람에게 유용합니다.</p>"}, "EditSat": {"bottomIconLabel": "위성 편집", "title": "위성 편집 메뉴", "helpBody": "위성 편집 메뉴는 위성 데이터를 편집하는 데 사용됩니다. <br><br> <ul> <li> 위성 SCC# - 미 우주군에서 각 위성에 할당한 고유 번호입니다. </li> <li> 에포크 연도 - 위성의 마지막 궤도 업데이트 연도입니다. </li> <li> 에포크 일 - 위성의 마지막 궤도 업데이트 날짜입니다. </li> <li> 경사각 - 위성의 궤도 평면과 적도 평면 사이의 각도입니다. </li> <li> 적경 - 상승 노드와 위성의 마지막 궤도 업데이트 시 위치 사이의 각도입니다. </li> <li> 이심률 - 위성의 궤도가 완벽한 원에서 얼마나 벗어나는지를 나타냅니다. </li> <li> 근지점 인수 - 상승 노드와 지구에 가장 가까운 위성 지점 사이의 각도입니다. </li> <li> 평균 근점 이각 - 위성의 마지막 궤도 업데이트 시 위치와 지구에 가장 가까운 위성 지점 사이의 각도입니다. </li> <li> 평균 운동 - 위성의 평균 근점 이각이 변하는 속도입니다. </li> </ul>"}, "NewLaunch": {"bottomIconLabel": "새 발사", "title": "새 발사 메뉴", "helpBody": "새 발사 메뉴는 유사한 매개변수를 가진 기존 위성을 수정하여 개념적 궤도 발사를 생성하는 데 사용됩니다. <br><br> 위성을 선택한 후, 발사 위치와 북/남 방위각을 선택할 수 있습니다. 선택한 위성은 발사 지점에 맞게 수정됩니다. 시계는 발사 후 상대 시간을 나타내기 위해 00:00:00으로 변경됩니다. 이는 발사 시간에 대한 센서 커버리지를 계산하는 데 도움이 될 수 있습니다. 다른 궤도 물체와의 관계는 정확하지 않을 것입니다."}, "MissilePlugin": {"bottomIconLabel": "미사일", "title": "미사일 메뉴", "helpBody": "미사일 메뉴는 국가 간 개념적 미사일 발사를 생성하는 데 사용됩니다. <br><br> 잠수함 발사 미사일을 사용할 때 발사 지점은 사용자 지정 위도와 경도입니다. 지상 기반 미사일을 사용할 때는 공개 소스 보고에 기반한 고정 위도와 경도가 발사 지점입니다. <br><br> 사용자 지정 미사일 외에도, 수백 개의 미사일이 포함된 몇 가지 사전 정의된 시나리오를 사용할 수 있습니다. <br><br> 모든 미사일 발사는 개념적이며 실제 세계 이벤트를 나타내지 않습니다. 발사 궤적은 모두 동일한 탄도 모델을 기반으로 하지만 최소 및 최대 범위가 다릅니다."}, "StereoMap": {"bottomIconLabel": "입체 지도", "title": "입체 지도 메뉴", "helpBody": "입체 지도 메뉴는 입체 투영법에서 위성 지상 궤적을 시각화하는 데 사용됩니다. <br/><br/> 지상 궤적을 따라 한 지점을 클릭하여 위성이 해당 지점에 도달할 때의 시뮬레이션 시간으로 변경할 수 있습니다. <br/><br/> 노란색 점은 위성이 센서의 시야 내에 있는 시간을 나타냅니다. 빨간색 점은 위성이 센서의 시야 내에 없는 시간을 나타냅니다. 위성에 가장 가까운 점이 현재 시간입니다."}, "SensorFov": {"bottomIconLabel": "센서 시야각"}, "SensorSurvFence": {"bottomIconLabel": "센서 펜스"}, "SatelliteViewPlugin": {"bottomIconLabel": "위성 뷰"}, "SatelliteFov": {"bottomIconLabel": "위성 FOV", "title": "위성 시야각 메뉴", "helpBody": "위성 시야각 플러그인을 사용하면 위성의 시야각을 제어할 수 있습니다."}, "Planetarium": {"bottomIconLabel": "플라네타리움 뷰"}, "NightToggle": {"bottomIconLabel": "야간 토글"}, "SatConstellations": {"bottomIconLabel": "콘스텔레이션", "title": "콘스텔레이션 메뉴", "helpBody": "콘스텔레이션 메뉴를 사용하면 위성 그룹을 볼 수 있습니다. <br><br> 일부 콘스텔레이션의 경우, 콘스텔레이션 내 위성 간에 개념적 업링크/다운링크 및/또는 크로스링크가 그려집니다."}, "CountriesMenu": {"bottomIconLabel": "국가", "title": "국가 메뉴", "helpBody": "국가 메뉴를 사용하면 원산지 국가별로 위성을 필터링할 수 있습니다."}, "ColorMenu": {"bottomIconLabel": "색상 구성표", "title": "색상 구성표 메뉴", "helpBody": "색상 메뉴는 객체를 렌더링하는 데 사용되는 색상 테마를 변경하는 곳입니다. <br><br> 다양한 테마는 객체의 궤도, 객체의 특성 또는 객체와 태양 및/또는 지구와의 관계에 따라 색상을 변경할 수 있습니다."}, "Screenshot": {"bottomIconLabel": "사진 찍기"}, "LaunchCalendar": {"bottomIconLabel": "발사 캘린더"}, "TimeMachine": {"bottomIconLabel": "타임머신"}, "SatellitePhotos": {"bottomIconLabel": "위성 사진", "title": "위성 사진 메뉴", "helpBody": "위성 사진 메뉴는 선택된 위성에서 실시간 사진을 표시하는 데 사용됩니다. <br><br> 참고 - 이미지 API의 변경으로 인해 KeepTrack에서 잘못된 위성이 선택될 수 있습니다."}, "ScreenRecorder": {"bottomIconLabel": "비디오 녹화"}, "Astronomy": {"bottomIconLabel": "천문학"}, "Calculator": {"bottomIconLabel": "기준계 변환", "title": "기준계 변환 메뉴", "helpBody": "기준계 변환 메뉴는 서로 다른 기준계 간에 변환하는 데 사용됩니다. <br><br> 이 메뉴를 사용하면 다음 기준계 간에 변환할 수 있습니다: <ul style='margin-left: 40px;'> <li> ECI - 지구 중심 관성 </li> <li> ECEF - 지구 중심 지구 고정 </li> <li> 측지학적 </li> <li> 지역 중심 </li> </ul>"}, "AnalysisMenu": {"bottomIconLabel": "분석", "title": "분석 메뉴", "helpBody": "분석 메뉴는 현재 보기의 데이터를 분석하는 데 도움이 되는 여러 도구를 제공합니다. 도구는 다음과 같습니다: <ul style='margin-left: 40px;'> <li>공식 TLE 내보내기 - 실제 두 줄 요소 세트를 내보냅니다.</li> <li>3LES 내보내기 - 세 줄 요소 세트를 내보냅니다.</li> <li>KeepTrack TLE 내보내기 - 분석가를 포함한 모든 KeepTrack 두 줄 요소 세트를 내보냅니다.</li> <li>KeepTrack 3LES 내보내기 - 분석가를 포함한 모든 KeepTrack 세 줄 요소 세트를 내보냅니다.</li> <li>근접 객체 찾기 - 서로 가까운 객체를 찾습니다.</li> <li>재진입 찾기 - 대기권에 재진입할 가능성이 있는 객체를 찾습니다.</li> <li>최적의 패스 - 현재 선택된 센서를 기반으로 위성의 최적의 패스를 찾습니다.</li> </ul>"}, "SettingsMenuPlugin": {"bottomIconLabel": "설정", "title": "설정 메뉴", "helpBody": "설정 메뉴를 사용하면 애플리케이션을 구성할 수 있습니다."}, "VideoDirectorPlugin": {"bottomIconLabel": "비디오 디렉터", "title": "비디오 디렉터 메뉴", "helpBody": "비디오 디렉터 메뉴는 비디오를 만들기 위해 장면의 카메라와 객체를 조작하는 데 사용됩니다."}, "CreateSat": {"bottomIconLabel": "위성 생성", "title": "위성 생성", "helpBody": "위성 생성 메뉴는 케플러 요소에서 위성을 생성하는 데 사용됩니다"}, "DopsPlugin": {"bottomIconLabel": "DOP 보기", "title": "정밀도 희석 메뉴", "helpBody": "정밀도 희석(DOP) 메뉴는 위성 및 센서에 대한 DOP 값을 계산하는 데 사용됩니다. <br><br> DOP 값은 다음과 같습니다: <ul style='margin-left: 40px;'> <li> GDOP - 기하학적 정밀도 희석 </li> <li> PDOP - 위치 정밀도 희석 </li> <li> HDOP - 수평 정밀도 희석 </li> <li> VDOP - 수직 정밀도 희석 </li> <li> TDOP - 시간 정밀도 희석 </li> <li> NDOP - 위성 수 정밀도 희석 </li> </ul>"}, "EciPlot": {"bottomIconLabel": "ECI 플롯", "title": "ECI 플롯 메뉴", "helpBody": "ECI 플롯 메뉴는 지구 중심 관성(ECI) 기준계에서 위성의 위치를 플롯하는 데 사용됩니다."}, "EcfPlot": {"bottomIconLabel": "ECF 플롯", "title": "ECF 플롯 메뉴", "helpBody": "ECF 플롯 메뉴는 지구 중심 지구 고정(ECF) 기준계에서 위성의 위치를 플롯하는 데 사용됩니다."}, "RicPlot": {"bottomIconLabel": "RIC 플롯", "title": "RIC 플롯 메뉴", "helpBody": "RIC 플롯 메뉴는 방사상, 궤도 내, 궤도 교차(RIC) 기준계에서 위성의 위치를 플롯하는 데 사용됩니다."}, "Time2LonPlots": {"bottomIconLabel": "워터폴 플롯", "title": "워터폴 메뉴", "helpBody": "시간-경도(워터폴) 플롯 메뉴는 시간에 따른 위성의 경도를 플롯하는 데 사용됩니다."}, "Lat2LonPlots": {"bottomIconLabel": "위도 대 경도 플롯", "title": "위도-경도 플롯 메뉴", "helpBody": "위도-경도 플롯 메뉴는 GEO 벨트에서 위도 대 경도를 플롯하는 데 사용됩니다."}, "Inc2AltPlots": {"bottomIconLabel": "경사각 대 고도 플롯", "title": "경사각-고도 플롯 메뉴", "helpBody": "경사각-고도 플롯 메뉴는 위성의 경사각을 고도에 대해 플롯하는 데 사용됩니다."}, "Inc2LonPlots": {"bottomIconLabel": "경사각 대 경도 플롯", "title": "경사각-경도 플롯 메뉴", "helpBody": "경사각-경도 플롯 메뉴는 GEO 벨트에서 경사각 대 경도를 플롯하는 데 사용됩니다."}, "GraphicsMenuPlugin": {"bottomIconLabel": "그래픽 메뉴", "title": "그래픽 메뉴", "helpBody": "그래픽 메뉴는 애플리케이션의 그래픽 설정을 변경하는 데 사용됩니다."}}}