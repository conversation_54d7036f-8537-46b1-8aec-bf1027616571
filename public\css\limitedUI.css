#keeptrack-root {
  line-height: 1.15;
}

#keeptrack-canvas {
  /* position: absolute; */
  background: var(--colorBlack);
  /* Inconsistent brightness without this */
  z-index: 0;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  touch-action: auto;
}

#sat-hoverbox {
  display: none;
  background: black;
  cursor: default;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Open Sans', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  position: absolute;
  padding: 10px;
  border-radius: 5px;
  border: none;
  pointer-events: none;
}

#orbit-btn {
  display: block;
  background: red;
  position: relative;
  z-index: 99999;
  width: 5px;
  left: 0px;
  top: 500px;
  bottom: 0px;
  padding: 20px;
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

#countries-btn {
  display: block;
  background: green;
  position: relative;
  z-index: 99999;
  width: 5px;
  left: 0px;
  top: 500px;
  bottom: 0px;
  padding: 20px;
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

#time-machine-btn {
  display: block;
  background: yellow;
  position: relative;
  z-index: 99999;
  width: 5px;
  left: 0px;
  top: 500px;
  bottom: 0px;
  padding: 20px;
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

#toast-container {
  display: block;
  position: fixed;
  z-index: 10000;
}

.toast {
  border-radius: 2px;
  top: 35px;
  width: auto;
  margin-top: 10px;
  position: relative;
  max-width: 100%;
  height: auto;
  min-height: 48px;
  line-height: 1.5em;
  /* background-color: #323232; */
  background-color: rgba(0, 191, 255, 0.8);
  padding: 10px 25px;
  font-size: 1.1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Open Sans', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-weight: 300;
  color: white;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  cursor: default;
  border: none;
}

.toast .toast-action {
  color: #eeff41;
  font-weight: 500;
  margin-right: -25px;
  margin-left: 3rem;
}

.toast.rounded {
  border-radius: 24px;
}

.right-btn-menu {
  display: none;
  background: var(--color-dark-background);
  color: white;
  position: absolute;
  width: 165px;
  border-radius: 0px;
  border: 5px solid var(--color-dark-border);
  z-index: 10;
  /*Prevent fighting with the slide down button on mobile */
}

a,
.link {
  cursor: pointer;
}

a {
  text-decoration: none;
  color: #92cbff !important;
}

.link:hover {
  background: rgba(0, 0, 0, 0.2);
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

ul li {
  list-style: none;
  padding: 5px;
}

.right-btn-menu ul {
  width: 100%;
}

.right-btn-menu ul li {
  padding: 5px;
  height: 25px;
  cursor: pointer;
}

.right-btn-menu ul li:hover {
  background-color: var(--color-primary-dark);
}

.right-btn-menu ul li:hover a {
  color: var(--colorWhite) !important;
  background-color: rgba(0, 0, 0, 0);
}

.rmb-menu-item:hover {
  background-color: var(--colorPrimaryDarken3) !important;
}

.rmb-menu-item a {
  color: white !important;
}

/* MOBILE */
@media only screen and (max-width: 600px) {
  #toast-container {
    min-width: 100%;
    top: 50px;
  }

  .toast {
    width: 100%;
    border-radius: 0;
  }
}

/* TABLET */
@media only screen and (min-width: 601px) and (max-width: 992px) {
  #toast-container {
    left: 5%;
    bottom: 7%;
    max-width: 90%;
  }
}

/* DESKTOP */
@media only screen and (min-width: 993px) {
  #toast-container {
    top: 10%;
    right: 7%;
    max-width: 86%;
  }
}